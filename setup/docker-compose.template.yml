version: '3.1'

services:
  rental-db:
    image: postgis/postgis:15-master
    restart: always
    ports:
      - 5555:5432
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: secret
      POSTGRES_DB: rental-db
    volumes:
      - postgres-data:/var/lib/postgresql/data
  redis:
    image: bitnami/redis:7.0
    restart: always
    ports:
      - 6380:6379
    volumes:
      - redis-data:/bitnami/redis/data
    environment:
      - ALLOW_EMPTY_PASSWORD=yes

volumes:
  postgres-data:
  redis-data:
