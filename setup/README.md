# How to make CRS up and running in local environment?

Here are the guidelines that will help you make CRS up and running in local environment, step by step.

> Please refine this document if the information is incorrect or outdated.

## Steps

1.  You need to have sufficient permissions to clone the Car Rental Service repository from [![](https://github.githubassets.com/favicon.ico)https://github.com/BlueSG-2/rental-service](https://github.com/BlueSG-2/rental-service). Clone it to your local environment.
2.  Open the code repository with your favourite IDE, then set up required environment variables in CRS by simply run the command from the project root: `yarn local:setup`, `.env.local` file and `docker-compose.local.yml` will be created
3.  Open the `.env.local` file, most environment variables you don’t have to define again, just leave it be, only some of environment variables you will need to define yourself.

```
// You need to have sufficient permissions to get access to BlueSG’s ConfigCat, a feature flag service that is to help do A/B testing
CONFIG_CAT_MAIN_CONFIG_KEY
CONFIG_CAT_MIGRATION_CONFIG_KEY

// You need to have a AWS account with developer permissions to get access to BlueSG AWS Identity Center, then generate AWS credential yourself
AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY
AWS_SESSION_TOKEN
```

4. In the end, your environment variables in `env.local` should look like this

```
// In the end, full environment variable should look like this
NODE_ENV=local
PORT=3007
CRON_PORT=8890

DB_TYPE=postgres
DB_HOST=localhost
DB_PORT=5555
DB_USERNAME=postgres
DB_PASSWORD=secret
DB_NAME=rental-db

NEW_RELIC_LOG_ENABLED=true
NEW_RELIC_NO_CONFIG_FILE=true
NEW_RELIC_AUDIT_LOG_ENABLED=true
NEW_RELIC_APP_NAME=crs-newrelic

VULOG_URL=https://java-sta.vulog.com
VULOG_ADMIN_URL=https://java-sta.vulog.com
VULOG_SECOND_URL=https://vg-sta.vulog.net
VULOG_CLIENT_ID=BLUESG-SINGA_secure
VULOG_CLIENT_SECRET=
VULOG_FLEET_ID=BLUESG-SINGA
VULOG_CITY_ID=123
VULOG_SERVICE_ID=1fb65e82-69ca-4278-b890-9d48ebb8e93a
VULOG_PRICING_ID=mock-pricing-id
VULOG_DEFAULT_USERNAME=
VULOG_DEFAULT_PASSWORD=
VULOG_DEFAULT_USERS=
VULOG_CONTINUATION_TOKEN_TTL=86400
VULOG_API_KEY=

REDIS_HOST=localhost
REDIS_PORT=6380

STATION_SERVICE_HOST=localhost
STATION_SERVICE_PORT=3006
STATION_SERVICE_URL=http://localhost:3006

USER_SUBSCRIPTION_SERVICE_URL=http://localhost:3001

JWT_SECRET=
JWT_SECRET_ADMIN=

ENCRYPT_STRING=secretToBeRotated

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_SESSION_TOKEN=

AWS_REGION=ap-southeast-1
USING_CREDENTIAL=true

SNS_TOPIC_ARN=arn:aws:sns:ap-southeast-1:116431628221:developmentMessageBusTopic-cao

CRON_EXPRESSION_EXPIRE_PRE_RESERVATIONS=* * * * *
CRON_EXPRESSION_POP_PRE_RESERVATIONS=* * * * *

BILLING_SERVICE_URL=http://localhost:3003

CAR_SERVICE_URL=foobar

CONFIG_CAT_MAIN_CONFIG_KEY=
CONFIG_CAT_MIGRATION_CONFIG_KEY=

SQS_QUEUE_URL=https://sqs.ap-southeast-1.amazonaws.com/116431628221/developmentCRSQueue-cao
AIMA_ENDING_RENTAL_SQS_QUEUE_URL=https://sqs.ap-southeast-1.amazonaws.com/116431628221/staging-end-rental-aima

VULOG_CENTER_URL=abc

VULOG_DEFAULT_USERNAME_1=
VULOG_DEFAULT_PASSWORD_1=

VULOG_DEFAULT_USERNAME_2=
VULOG_DEFAULT_PASSWORD_2=

SKIP_ENDING_RENTAL_CONDITIONS=false
NEW_RELIC_LICENSE_KEY=abc

LOG_ENABLE=true
LOG_LEVEL=debug
```

5. Open the `docker-compose.local.yml` file, modify it so that the containers information matches your environment variables in your `.env.local` file. For example, host port for `rental-db` container should be `5555` which is matched to environment variable `DB_PORT` in the sample of previous step.

```
version: '3.1'

services:
  rental-db:
    image: postgis/postgis:15-master
    restart: always
    ports:
      - 5555:5432
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: secret
      POSTGRES_DB: rental-db
    volumes:
      - postgres-data:/var/lib/postgresql/data
  redis:
    image: bitnami/redis:7.0
    restart: always
    ports:
      - 6380:6379
    volumes:
      - redis-data:/bitnami/redis/data
    environment:
      - ALLOW_EMPTY_PASSWORD=yes

volumes:
  postgres-data:
  redis-data:
```

6.  Open your preferred CLI at current code repository working directory, run the command `yarn local:container:start`, let Docker provisions the containers for you
7.  Check if all containers regarding CRS are up and running or not by openning Docker Desktop
8.  Setting up database schema and table schemas by running the migration script, run the command `yarn migration:dev:run`
9.  Open your preferred CLI at current code repository working directory, run the command `yarn start:dev` to make CRS up and running
10. To make the behavior of local CRS aligned with staging CRS, you need to make sure:
    - local STS is up and running (Please refer the document that heps to make STS up and running, this is out of scope)
    - run the command `yarn start:update-cars:dev` so that CRS has car information persisted in database
    - run the command `yarn start:crons:dev` so that background jobs are up and running. For example, job to compute car availability, job to refresh Vulog car information, etc
