PORT=8881
CRON_PORT=8890
NEW_RELIC_ENABLED=false
NEW_RELIC_LICENSE_KEY=new_relic_license_key

DB_TYPE=postgres
DB_HOST=localhost
DB_PORT=5555
DB_USERNAME=postgres
DB_PASSWORD=secret
DB_NAME=rental-db

VULOG_URL=checkInTheWikiDoc
VULOG_API_KEY=checkInTheWikiDoc
VULOG_ADMIN_URL=checkInTheWikiDoc
VULOG_CLIENT_ID=checkInTheWikiDoc
VULOG_CLIENT_SECRET=checkInTheWikiDoc
VULOG_FLEET_ID=checkInTheWikiDoc
VULOG_CITY_ID=123
VULOG_SERVICE_ID=checkInTheWikiDoc
VULOG_PRICING_ID=checkInTheWikiDoc
VULOG_DEFAULT_USERNAME=testUsername
VULOG_DEFAULT_PASSWORD=testUsername
VULOG_CONTINUATION_TOKEN_TTL=86400

REDIS_HOST=localhost
REDIS_PORT=6380

JWT_SECRET=checkInTheWikiDoc
JWT_SECRET_ADMIN=checkInTheWikiDoc

# Fake SDK key with valid format, so that it wont be rejected by validation of ConfigCat
CONFIG_CAT_MAIN_CONFIG_KEY=3IHbCFPzjkqEnCW9xvj8kA/2ObIHlGaPE6OKFCna_FAKE
CONFIG_CAT_MIGRATION_CONFIG_KEY=3IHbCFPzjkqEnCW9xvj8kA/3UnukXGPD0udM5dY1_FAKE
CONFIG_CAT_RELEASE_CONFIG_KEY=3IHbCFPzjkqEnCW9xvj8kA/3UnukXGPD0udM5dY2_FAKE

STATION_SERVICE_HOST=checkInTheWikiDoc
STATION_SERVICE_PORT=8889

SENTRY_DSN=https://<EMAIL>/4505362339266560
SENTRY_ENVIRONMENT=fillHere

ENCRYPT_STRING=secretToBeRotated
BILLING_SERVICE_URL=http://localhost:3003

USER_SUBSCRIPTION_SERVICE_URL=http://localhost:3001

CAR_SERVICE_URL=foobar

## AWS
SNS_TOPIC_ARN=checkInTheWikiDoc

SQS_QUEUE_URL=sqs.url

VULOG_CENTER_URL=https://bluesg-test.vulog.center/BLUESG-SINGA/app

VULOG_DEFAULT_USERNAME_1=testUsername
VULOG_DEFAULT_PASSWORD_1=testUsername

VULOG_DEFAULT_USERNAME_2=testUsername
VULOG_DEFAULT_PASSWORD_2=testUsername

# lib-bo-auth-guard config
AUTH0_SERVER=
AUTHENTICATION_CONFIG_KEY=
