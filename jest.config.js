module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '.',
  setupFiles: ['<rootDir>/src/env.ts'],
  testRegex: '.*\\.spec\\.(t|j)s$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  coverageDirectory: './coverage',
  testEnvironment: 'node',
  moduleNameMapper: {
    '^~auth/(.*)': '<rootDir>/src/modules/auth/$1',
    '^~shared/(.*)': '<rootDir>/src/shared/$1',
    '^~models/(.*)': '<rootDir>/src/modules/models/$1',
    '^~base/(.*)': '<rootDir>/src/base/$1',
    '^~common/(.*)': '<rootDir>/src/common/$1',
    '^~test/(.*)': '<rootDir>/test/$1',
    '^~/(.*)': '<rootDir>/src/$1',
    '^src/(.*)': '<rootDir>/src/$1',
    '^test/(.*)': '<rootDir>/test/$1',
  },
  roots: ['<rootDir>'],
  moduleDirectories: ['node_modules'],
  // Needed for memory leak issue with NodeJS 16. See https://github.com/facebook/jest/issues/11956
  workerIdleMemoryLimit: '50M',
};
