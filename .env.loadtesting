CRON_PORT=8890

VULOG_URL=loadtest-env
VULOG_ADMIN_URL=loadtest-env
VULOG_CLIENT_ID=loadtest-env
VULOG_CLIENT_SECRET=loadtest-env
VULOG_FLEET_ID=loadtest-env
VULOG_CITY_ID=123
VULOG_SERVICE_ID=loadtest-env
VULOG_PRICING_ID=mock-pricing-id
VULOG_CONTINUATION_TOKEN_TTL=86400
VULOG_CENTER_URL=loadtest-env

STATION_SERVICE_HOST=sts.int-prod
STATION_SERVICE_PORT=3006

SENTRY_DSN=
SENTRY_ENVIRONMENT=loadtesting

AWS_REGION=ap-southeast-1

SNS_TOPIC_ARN=mockSnsArn

LOG_LEVEL=info

CRON_EXPRESSION_EXPIRE_PRE_RESERVATIONS=0 */5 * * * *
CRON_EXPRESSION_POP_PRE_RESERVATIONS=0 */5 * * * *

END_RENTAL_IN_AIMA_SQS_QUEUE_URL=

