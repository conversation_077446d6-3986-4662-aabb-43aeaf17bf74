import { CronExpression } from '@nestjs/schedule';
import { getEnv, getEnvBoolean } from './util';

class CronJobConfig {
  constructor(readonly expression: string, readonly enabled: boolean) {}
}

export class ScheduleConfig {
  expirePreReservations = new CronJobConfig(
    getEnv('CRON_EXPRESSION_EXPIRE_PRE_RESERVATIONS', {
      default: CronExpression.EVERY_SECOND,
    }),
    getEnvBoolean('CRON_EXPIRE_PRE_RESERVATIONS_ENABLED', {
      default: true,
    }),
  );

  popPreReservation = new CronJobConfig(
    getEnv('CRON_EXPRESSION_POP_PRE_RESERVATIONS', {
      default: CronExpression.EVERY_MINUTE,
    }),
    getEnvBoolean('CRON_POP_PRE_RESERVATIONS_ENABLED', { default: true }),
  );

  expireReservations = new CronJobConfig(
    getEnv('CRON_EXPRESSION_EXPIRE_RESERVATIONS', {
      default: CronExpression.EVERY_10_SECONDS,
    }),
    getEnvBoolean('CRON_EXPIRE_RESERVATIONS_ENABLED', {
      default: true,
    }),
  );

  fetchRealtimeVulogVehicles = new CronJobConfig(
    getEnv('CRON_EXPRESSION_FETCH_REAL_TIME_VULOG_VEHICLES', {
      default: '*/7 * * * * *',
    }),
    getEnvBoolean('CRON_FETCH_REAL_TIME_VULOG_VEHICLES_ENABLED', {
      default: true,
    }),
  );

  fetchVulogFleetServices = new CronJobConfig(
    getEnv('CRON_EXPRESSION_FETCH_VULOG_FLEET_SERVICES', {
      default: '*/7 * * * * *',
    }),
    getEnvBoolean('CRON_FETCH_VULOG_FLEET_SERVICES_ENABLED', {
      default: true,
    }),
  );

  getCarAvailability = new CronJobConfig(
    getEnv('CRON_EXPRESSION_GET_CAR_AVAILABILITY', {
      default: '*/7 * * * * *',
    }),
    getEnvBoolean('CRON_GET_CAR_AVAILABILITY_ENABLED', {
      default: true,
    }),
  );

  fetchAllStations = new CronJobConfig(
    getEnv('CRON_EXPRESSION_FETCH_ALL_STATIONS', {
      default: '*/7 * * * * *',
    }),
    getEnvBoolean('CRON_FETCH_ALL_STATIONS_ENABLED', {
      default: true,
    }),
  );

  fetchStaticVulogVehicles = new CronJobConfig(
    getEnv('CRON_EXPRESSION_FETCH_STATIC_VULOG_VEHICLES', {
      default: '*/7 * * * * *',
    }),
    getEnvBoolean('CRON_FETCH_STATIC_VULOG_VEHICLES_ENABLED', {
      default: true,
    }),
  );

  refreshSystemUsersTokens = new CronJobConfig(
    getEnv('CRON_EXPRESSION_REFRESH_SYSTEM_USERS_TOKENS', {
      default: CronExpression.EVERY_30_SECONDS,
    }),
    getEnvBoolean('CRON_REFRESH_SYSTEM_USERS_TOKENS_ENABLED', {
      default: true,
    }),
  );

  restickStationsWhenTerminatedByCronCron = new CronJobConfig(
    getEnv('CRON_EXPRESSION_RESTICK_STATIONS_WHEN_TERMINATED_BY_CRON_CRON', {
      default: CronExpression.EVERY_MINUTE,
    }),
    getEnvBoolean(
      'CRON_RESTICK_STATIONS_WHEN_TERMINATED_BY_CRON_CRON_ENABLED',
      {
        default: true,
      },
    ),
  );

  reportMetricReservationNumberOfOngoing = new CronJobConfig(
    getEnv('CRON_EXPRESSION_REPORT_METRIC_RESERVATION_NUMBER_OF_ONGOING_CRON', {
      default: CronExpression.EVERY_MINUTE,
    }),
    getEnvBoolean(
      'CRON_REPORT_METRIC_RESERVATION_NUMBER_OF_ONGOING_CRON_ENABLED',
      {
        default: true,
      },
    ),
  );

  reportMetricReservationNumberOfCooldown = new CronJobConfig(
    getEnv(
      'CRON_EXPRESSION_REPORT_METRIC_RESERVATION_NUMBER_OF_COOLDOWN_CRON',
      {
        default: CronExpression.EVERY_MINUTE,
      },
    ),
    getEnvBoolean(
      'CRON_REPORT_METRIC_RESERVATION_NUMBER_OF_COOLDOWN_CRON_ENABLED',
      {
        default: true,
      },
    ),
  );

  reportMetricRentalNumberOfBlueSGStartedButVulogBooking = new CronJobConfig(
    getEnv(
      'CRON_EXPRESSION_REPORT_METRIC_RENTAL_NUMBER_OF_BLUESG_STARTED_BUT_VULOG_BOOKING_CRON',
      {
        default: CronExpression.EVERY_MINUTE,
      },
    ),
    getEnvBoolean(
      'CRON_REPORT_METRIC_RENTAL_NUMBER_OF_BLUESG_STARTED_BUT_VULOG_BOOKING_CRON_ENABLED',
      {
        default: true,
      },
    ),
  );

  reportMetricRentalNumberOfOngoing = new CronJobConfig(
    getEnv('CRON_EXPRESSION_REPORT_METRIC_RENTAL_NUMBER_OF_ONGOING_CRON', {
      default: CronExpression.EVERY_MINUTE,
    }),
    getEnvBoolean('CRON_REPORT_METRIC_RENTAL_NUMBER_OF_ONGOING_CRON_ENABLED', {
      default: true,
    }),
  );

  reportMetricRentalNumberOfEndingRentalConditionsSatisfied = new CronJobConfig(
    getEnv(
      'CRON_EXPRESSION_REPORT_METRIC_RENTAL_NUMBER_OF_ENDING_RENTAL_CONDITIONS_SATISFIED_CRON',
      {
        default: CronExpression.EVERY_MINUTE,
      },
    ),
    getEnvBoolean(
      'CRON_REPORT_METRIC_RENTAL_NUMBER_OF_ENDING_RENTAL_CONDITIONS_SATISFIED_CRON_ENABLED',
      {
        default: true,
      },
    ),
  );

  reportMetricRentalNumberOfWaitingForInvoicing = new CronJobConfig(
    getEnv(
      'CRON_EXPRESSION_REPORT_METRIC_RENTAL_NUMBER_OF_WAITING_FOR_INVOICING_CRON',
      {
        default: CronExpression.EVERY_MINUTE,
      },
    ),
    getEnvBoolean(
      'CRON_REPORT_METRIC_RENTAL_NUMBER_OF_WAITING_FOR_INVOICING_CRON_ENABLED',
      {
        default: true,
      },
    ),
  );

  reportMetricCarNumberOfPlugged = new CronJobConfig(
    getEnv('CRON_EXPRESSION_REPORT_METRIC_CAR_NUMBER_OF_PLUGGED_CRON', {
      default: CronExpression.EVERY_MINUTE,
    }),
    getEnvBoolean('CRON_REPORT_METRIC_CAR_NUMBER_OF_PLUGGED_CRON_ENABLED', {
      default: true,
    }),
  );

  reportMetricCarNumberOfUnplugged = new CronJobConfig(
    getEnv('CRON_EXPRESSION_REPORT_METRIC_CAR_NUMBER_OF_UNPLUGGED_CRON', {
      default: CronExpression.EVERY_MINUTE,
    }),
    getEnvBoolean('CRON_REPORT_METRIC_CAR_NUMBER_OF_UNPLUGGED_CRON_ENABLED', {
      default: true,
    }),
  );

  reportMetricCarNumberOfCharging = new CronJobConfig(
    getEnv('CRON_EXPRESSION_REPORT_METRIC_CAR_NUMBER_OF_CHARGING_CRON', {
      default: CronExpression.EVERY_MINUTE,
    }),
    getEnvBoolean('CRON_REPORT_METRIC_CAR_NUMBER_OF_CHARGING_CRON_ENABLED', {
      default: true,
    }),
  );

  reportMetricCarNumberOfLockedAndImmobilized = new CronJobConfig(
    getEnv(
      'CRON_EXPRESSION_REPORT_METRIC_CAR_NUMBER_OF_LOCKED_AND_IMMOBILIZED_CRON',
      {
        default: CronExpression.EVERY_MINUTE,
      },
    ),
    getEnvBoolean(
      'CRON_REPORT_METRIC_CAR_NUMBER_OF_LOCKED_AND_IMMOBILIZED_CRON_ENABLED',
      {
        default: true,
      },
    ),
  );

  reportMetricCarNumberOfUnlockedAndMobilized = new CronJobConfig(
    getEnv(
      'CRON_EXPRESSION_REPORT_METRIC_CAR_NUMBER_OF_UNLOCKED_AND_MOBILIZED_CRON',
      {
        default: CronExpression.EVERY_MINUTE,
      },
    ),
    getEnvBoolean(
      'CRON_REPORT_METRIC_CAR_NUMBER_OF_UNLOCKED_AND_MOBILIZED_CRON_ENABLED',
      {
        default: true,
      },
    ),
  );

  reportMetricCarNumberOfOutOfZone = new CronJobConfig(
    getEnv('CRON_EXPRESSION_REPORT_METRIC_CAR_NUMBER_OF_OUT_OF_ZONE_CRON', {
      default: CronExpression.EVERY_MINUTE,
    }),
    getEnvBoolean('CRON_REPORT_METRIC_CAR_NUMBER_OF_OUT_OF_ZONE_CRON_ENABLED', {
      default: true,
    }),
  );

  reportMetricCarNumberOfInZone = new CronJobConfig(
    getEnv('CRON_EXPRESSION_REPORT_METRIC_CAR_NUMBER_OF_IN_ZONE_CRON', {
      default: CronExpression.EVERY_MINUTE,
    }),
    getEnvBoolean('CRON_REPORT_METRIC_CAR_NUMBER_OF_IN_ZONE_CRON_ENABLED', {
      default: true,
    }),
  );

  reportMetricCarNumberOfOutOfCustomerFleetService = new CronJobConfig(
    getEnv(
      'CRON_EXPRESSION_REPORT_METRIC_CAR_NUMBER_OF_OUT_OF_CUSTOMER_FLEET_SERVICE_CRON',
      {
        default: CronExpression.EVERY_MINUTE,
      },
    ),
    getEnvBoolean(
      'CRON_REPORT_METRIC_CAR_NUMBER_OF_OUT_OF_CUSTOMER_FLEET_SERVICE_CRON_ENABLED',
      {
        default: true,
      },
    ),
  );

  reportMetricCarNumberOfOutOfService = new CronJobConfig(
    getEnv('CRON_EXPRESSION_REPORT_METRIC_CAR_NUMBER_OF_OUT_OF_SERVICE_CRON', {
      default: CronExpression.EVERY_MINUTE,
    }),
    getEnvBoolean(
      'CRON_REPORT_METRIC_CAR_NUMBER_OF_OUT_OF_SERVICE_CRON_ENABLED',
      {
        default: true,
      },
    ),
  );

  reportMetricCarNumberOfAvailable = new CronJobConfig(
    getEnv('CRON_EXPRESSION_REPORT_METRIC_CAR_NUMBER_OF_AVAILABLE_CRON', {
      default: CronExpression.EVERY_MINUTE,
    }),
    getEnvBoolean('CRON_REPORT_METRIC_CAR_NUMBER_OF_AVAILABLE_CRON_ENABLED', {
      default: true,
    }),
  );

  reportMetricCarNumberOfSticky = new CronJobConfig(
    getEnv('CRON_EXPRESSION_REPORT_METRIC_CAR_NUMBER_OF_STICKY_CRON', {
      default: CronExpression.EVERY_MINUTE,
    }),
    getEnvBoolean('CRON_REPORT_METRIC_CAR_NUMBER_OF_STICKY_CRON_ENABLED', {
      default: true,
    }),
  );

  reportMetricCarNumberOfUnsticky = new CronJobConfig(
    getEnv('CRON_EXPRESSION_REPORT_METRIC_CAR_NUMBER_OF_UNSTICKY_CRON', {
      default: CronExpression.EVERY_MINUTE,
    }),
    getEnvBoolean('CRON_REPORT_METRIC_CAR_NUMBER_OF_UNSTICKY_CRON_ENABLED', {
      default: true,
    }),
  );
}
