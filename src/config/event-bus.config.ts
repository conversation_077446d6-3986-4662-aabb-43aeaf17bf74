import { SnsModuleConfig } from '@bluesg-2/sqs-event';
import { registerAs } from '@nestjs/config';
import {
  AWS_DEFAULT_REGION,
  AWS_DEFAULT_USING_CREDENTIAL,
  DEFAULT_SNS_MESSAGE_TIMEOUT,
  SERVICE_NAME,
} from 'src/common/constants/environments';
import { getEnv, getEnvBoolean, getEnvNumber } from './util';

export const eventBusConfig = registerAs(
  'eventBusConfig',
  (): SnsModuleConfig => ({
    sender: SERVICE_NAME,
    snsClientConfig: {
      usingCredential: getEnvBoolean('USING_CREDENTIAL', {
        default: AWS_DEFAULT_USING_CREDENTIAL,
      }),
      snsTopicArn: getEnv('SNS_TOPIC_ARN'),
      timeout: getEnvNumber('SNS_MESSAGE_TIMEOUT', {
        default: DEFAULT_SNS_MESSAGE_TIMEOUT,
      }),
      region: getEnv('AWS_REGION', {
        default: AWS_DEFAULT_REGION,
      }),
      accessKey: getEnv('AWS_ACCESS_KEY_ID', { isOptional: true }),
      accessKeySecret: getEnv('AWS_SECRET_ACCESS_KEY', { isOptional: true }),
    },
  }),
);
