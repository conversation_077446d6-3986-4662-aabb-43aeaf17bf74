import { VulogFleetId, VulogServiceId } from 'src/common/tiny-types';

import { getEnv, getEnvNumber } from './util';

export class VulogConfig {
  clientId: string = getEnv('VULOG_CLIENT_ID');
  clientSecret: string = getEnv('VULOG_CLIENT_SECRET');
  defaultUsers: { username: string; password: string }[] = process.env[
    'VULOG_DEFAULT_USERS'
  ]
    ? JSON.parse(getEnv('VULOG_DEFAULT_USERS'))
    : [
        {
          username: getEnv('VULOG_DEFAULT_USERNAME'),
          password: getEnv('VULOG_DEFAULT_PASSWORD'),
        },
        {
          username: getEnv('VULOG_DEFAULT_USERNAME_1'),
          password: getEnv('VULOG_DEFAULT_PASSWORD_1'),
        },
        {
          username: getEnv('VULOG_DEFAULT_USERNAME_2'),
          password: getEnv('VULOG_DEFAULT_PASSWORD_2'),
        },
      ];
  defaultUsername: string = getEnv('VULOG_DEFAULT_USERNAME');
  defaultPassword: string = getEnv('VULOG_DEFAULT_PASSWORD');
  apiUrl: string = getEnv('VULOG_URL');
  secondApiUrl: string = getEnv('VULOG_SECOND_URL', {
    default: 'https://vg-sta.vulog.net',
  });
  vvgApiUrl: string = getEnv('VULOG_ADMIN_URL');
  fleetId: VulogFleetId = new VulogFleetId(getEnv('VULOG_FLEET_ID'));
  cityId: string = getEnv('VULOG_CITY_ID');
  serviceId: VulogServiceId = new VulogServiceId(getEnv('VULOG_SERVICE_ID'));
  pricingId: string = getEnv('VULOG_PRICING_ID');
  continuationTokenTTL = getEnvNumber('VULOG_CONTINUATION_TOKEN_TTL');
  apiKey: string = getEnv('VULOG_API_KEY');
  emailPlaceholderPrefix: string = getEnv('EMAIL_PLACEHOLDER_PREFIX', {
    default: 'vulog-',
  });
  emailPlaceholderSuffix: string = getEnv('EMAIL_PLACEHOLDER_SUFFIX', {
    default: '',
  });
  firstNamePlaceholder: string = getEnv('FIRSTNAME_PLACEHOLDER', {
    default: 'PlaceholderFirstName',
  });
  lastNamePlaceholder: string = getEnv('LASTNAME_PLACEHOLDER', {
    default: 'PlaceholderLastName',
  });
  locale: string = getEnv('LOCALE', { default: 'en_GB' });
  membershipNumberPlaceholder = getEnv('MEMBERSHIP_NUMBER_PLACEHOLDER', {
    default: '',
  });
  phoneNumberPlaceholder: string = getEnv('PHONE_NUMBER_PLACEHOLDER', {
    default: '12345678',
  });
  birthdatePlaceholder: number = getEnvNumber('BIRTHDAY_PLACEHOLDER', {
    default: new Date(1990, 0, 1).getTime(),
  });
  vulogCenterUrl: string = getEnv('VULOG_CENTER_URL');
}
