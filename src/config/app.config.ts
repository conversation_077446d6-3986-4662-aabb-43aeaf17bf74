import { HttpServiceConfig } from 'src/config/http.config';
import { BatchConfig } from './batch.config';
import { CacheConfig } from './cacheConfig';
import { CronServerConfig } from './cron-server.config';
import { EncryptConfig } from './encrypt.config';
import { FeatureFlagConfig } from './feature-flag.config';
import { JWTConfig } from './jwt.config';
import { LogConfig } from './log.config';
import { NewRelicConfig } from './newrelic.config';
import { PaginationConfig } from './pagination.config';
import { RentalPricingConfig } from './rental-pricing.config';
import { RentalConfig } from './rental.config';
import { ScheduleConfig } from './schedule.config';
import { SentryConfig } from './sentry.config';
import { SqsConfig } from './sqs.config';
import { StationServiceConfig } from './station-service.config';
import { SwaggerConfig } from './swagger.config';
import { VulogConfig } from './vulog.config';
import { WebServerConfig } from './web-server.config';

export class AppConfig {
  webServer = new WebServerConfig();
  vulogConfig = new VulogConfig();
  swaggerConfig = new SwaggerConfig();
  redisConfig = new CacheConfig();
  stationServiceConfig = new StationServiceConfig();
  sentryConfig = new SentryConfig();
  jwtConfig = new JWTConfig();
  encryptConfig = new EncryptConfig();
  httpServiceConfig = new HttpServiceConfig();
  logConfig = new LogConfig();
  scheduleConfig = new ScheduleConfig();
  paginationConfig = new PaginationConfig();
  featureFlagConfig = new FeatureFlagConfig();
  sqs = new SqsConfig();
  rentalConfig = new RentalConfig();
  rentalPricingConfig = new RentalPricingConfig();
  cronServer = new CronServerConfig();
  newRelicConfig = new NewRelicConfig();
  batchConfig = new BatchConfig();
}
