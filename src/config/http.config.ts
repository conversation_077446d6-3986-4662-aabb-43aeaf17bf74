import { getEnv, getEnvNumber } from 'src/config/util';
import {
  STATION_API_SERVICE_KEY,
  USER_SUBSCRIPTION_API_SERVICE_KEY,
} from 'src/external/constants';

export class HttpServiceConfig {
  timeout = getEnvNumber('HTTP_TIMEOUT', { default: 20000 });
  billingUrl = getEnv('BILLING_SERVICE_URL');
  stationUrl = `http://${getEnv('STATION_SERVICE_HOST')}:${getEnv(
    'STATION_SERVICE_PORT',
  )}`;
  stationName = STATION_API_SERVICE_KEY;
  userSubscriptionUrl = getEnv('USER_SUBSCRIPTION_SERVICE_URL');
  userSubscriptionName = USER_SUBSCRIPTION_API_SERVICE_KEY;
  userAgent = getEnv('HTTP_USER_AGENT', { default: 'BlueSG/2.0 CRS' });
  carServiceUrl = getEnv('CAR_SERVICE_URL');
}
