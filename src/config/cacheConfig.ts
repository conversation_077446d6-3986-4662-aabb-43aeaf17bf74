import { getEnv, getEnvNumber } from './util';

export class ItemCacheConfig {
  cacheKey?: string;
  ttl: number;
}

export class CacheConfig {
  host: string = getEnv('REDIS_HOST');
  port: number = getEnvNumber('REDIS_PORT');
  /* used for availabilities, cache station api call result - medium impact */
  stations: ItemCacheConfig = {
    cacheKey: 'CACHE_STATION',
    ttl: getEnvNumber('CACHE_STATION_TTL', { default: 300 }),
  };
  zoneStationMap: ItemCacheConfig = {
    cacheKey: 'CACHE_ZONE_STATION_MAP',
    ttl: getEnvNumber('CACHE_ZONE_STATION_MAP_TTL', { default: 300 }),
  };
  /* seems unused ? */
  carModels: ItemCacheConfig = {
    cacheKey: 'CACHE_CAR_MODELS',
    ttl: getEnvNumber('CACHE_CAR_MODELS_TTL', { default: 300 }),
  };
  /* must be at least as high as realtimecar cache - multiple loops in code - cpu intensive, high impact in performance but no impact on vulog calls */
  carAvailabilities: ItemCacheConfig = {
    cacheKey: 'CACHE_AVAILABILITIES',
    ttl: getEnvNumber('CACHE_AVAILABILITIES_TTL', { default: 8 }),
  };
  carAvailabilityDetails: ItemCacheConfig = {
    cacheKey: 'CACHE_AVAILABILITY_DETAILS',
    ttl: getEnvNumber('CACHE_AVAILABILITY_DETAILS_TTL', { default: 8 }),
  };
  /* used in end rental to cache DB data. not much impact */
  staticCars: ItemCacheConfig = {
    cacheKey: 'CACHE_STATIC_CARS',
    ttl: getEnvNumber('CACHE_STATIC_CARS_TTL', { default: 300 }),
  };
  /* used in multiple places like ongoing rental status, reservation, packages availability, map availability of cars and BLOPS - huge impact on vehicle api calls to vulog */
  realTimeCars: ItemCacheConfig = {
    cacheKey: 'CACHE_REAL_TIME_CARS',
    ttl: getEnvNumber('CACHE_REAL_TIME_CARS_TTL', { default: 30 }),
  };
  realtimeCarPlateMap: ItemCacheConfig = {
    cacheKey: 'CACHE_REAL_TIME_CAR_PLATE_MAP',
    ttl: getEnvNumber('CACHE_REAL_TIME_CAR_PLATE_MAP_TTL', { default: 30 }),
  };
  configurations: ItemCacheConfig = {
    ttl: getEnvNumber('CACHE_CONFIGURATIONS_TTL', { default: 300 }),
  };
  vulogCarSwitchedOnGoingReservationByUserId: ItemCacheConfig = {
    cacheKey: 'CACHE_VULOG_CAR_SWITCHED_ON_GOING_RESERVATION_BY_BSG_USER_ID',
    ttl: getEnvNumber(
      'CACHE_VULOG_CAR_SWITCHED_ON_GOING_RESERVATION_BY_BSG_USER_ID_TTL',
      {
        default: 1800, // 30-minute duration
      },
    ),
  };
  /* cache DB list of cars, is it necessary ? */
  localAllCars: ItemCacheConfig = {
    cacheKey: 'CACHE_DB_ALL_CARS',
    ttl: getEnvNumber('CACHE_DB_ALL_CARS_TTL', { default: 300 }),
  };
  /* creation of map of cars for quick access in cache i believe ? */
  localVulogIdCarMap: ItemCacheConfig = {
    cacheKey: 'CACHE_VULOG_ID_CAR_MAP',
    ttl: getEnvNumber('CACHE_VULOG_ID_CAR_MAP_TTL', { default: 60 }),
  };
  fleetServices: ItemCacheConfig = {
    cacheKey: 'CACHE_FLEET_SERVICES',
    ttl: getEnvNumber('CACHE_FLEET_SERVICES_TTL', { default: 30 }),
  };
  /* Used for lock and unlock car. Calls to Vulog Vehicle gateway. */
  singleTelemetryCarInfo: ItemCacheConfig = {
    cacheKey: 'CACHE_SINGLE_TELEMETRY_CAR_INFO',
    ttl: getEnvNumber('CACHE_SINGLE_TELEMETRY_CAR_INFO_TTL', { default: 8 }),
  };
  vvgCarStatusInfo: ItemCacheConfig = {
    cacheKey: 'CACHE_VVG_CARS',
    ttl: getEnvNumber('CACHE_VVG_CARS_TTL', { default: 30 }),
  };
  localVinCarMap: ItemCacheConfig = {
    cacheKey: 'CACHE_VIN_CAR_MAP',
    ttl: getEnvNumber('CACHE_VULOG_VIN_CAR_MAP_TTL', { default: 60 }),
  };
  localPlateCarMap: ItemCacheConfig = {
    cacheKey: 'CACHE_PLATE_CAR_MAP',
    ttl: getEnvNumber('CACHE_VULOG_PLATE_CAR_MAP_TTL', { default: 60 }),
  };
}
