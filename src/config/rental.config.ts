import { getEnvBoolean, getEnv} from './util';

export type ConditionOptions = {
  disabled: boolean;
};

export class RentalConfig {
  EndRentalConditions = {
    DoorsAndWindowsClosed: {
      disabled: getEnvBoolean(
        'END_RENTAL_CONDITION__DOORS_AND_WINDOWS_DISABLED',
        {
          default: false,
        },
      ),
    } as ConditionOptions,
    EngineOff: {
      disabled: getEnvBoolean('END_RENTAL_CONDITION__ENGINE_OFF_DISABLED', {
        default: false,
      }),
    } as ConditionOptions,
    CarPlugged: {
      disabled: getEnvBoolean('END_RENTAL_CONDITION__CAR_PLUGGED_DISABLED', {
        default: false,
      }),
    } as ConditionOptions,
  };

  skipEndingRentalConditions = getEnvBoolean('SKIP_ENDING_RENTAL_CONDITIONS', {
    default: false,
  });

  blueChargeQrUrlPrefix = getEnv('BL<PERSON>CHARGE_QR_URL_PREFIX', {
    default: 'https://qrcode-chargingservices.totalenergies.sg/qrcode'
  });
}
