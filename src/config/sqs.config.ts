import { getEnv } from './util';

export class SqsConfig {
  url: string = getEnv('SQS_QUEUE_URL');
  region = getEnv('AWS_REGION', { default: 'ap-southeast-1' });

  EndRentalInAiMA = {
    name: getEnv('END_RENTAL_IN_AIMA_SQS_QUEUE', {
      default: 'END_RENTAL_IN_AIMA_SQS_QUEUE',
    }),
    region: getEnv('AWS_REGION', { default: 'ap-southeast-1' }),
    url: getEnv('END_RENTAL_IN_AIMA_SQS_QUEUE_URL', {
      default:
        'https://sqs.ap-southeast-1.amazonaws.com/116431628221/staging-end-rental-aima',
    }),
  };
}
