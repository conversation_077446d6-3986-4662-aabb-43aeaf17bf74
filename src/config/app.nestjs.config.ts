import { registerAs } from '@nestjs/config';
import { config } from 'src/config';
import { eventBusConfig } from './event-bus.config';

const httpServiceConfig = registerAs('httpServiceConfig', () => ({
  timeout: config.httpServiceConfig.timeout,
  stationUrl: config.httpServiceConfig.stationUrl,
  billingUrl: config.httpServiceConfig.billingUrl,
  stationName: config.httpServiceConfig.stationName,
  userSubscriptionUrl: config.httpServiceConfig.userSubscriptionUrl,
  userSubscriptionName: config.httpServiceConfig.userSubscriptionName,
  carServiceUrl: config.httpServiceConfig.carServiceUrl,
}));

export { eventBusConfig, httpServiceConfig };
