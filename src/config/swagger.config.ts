import { getEnv, getEnvBoolean } from './util';

export class SwaggerConfig {
  title: string = getEnv('SWAGGER_TITLE', {
    default: `BlueSG Rental Service`,
  });

  description: string = getEnv('SWAGGER_DESCRIPTION', {
    default:
      'Service in charge of all car rental related operations, including reservations and car availability',
  });

  version: string = getEnv('SWAGGER_VERSION', { default: '1.0' });

  isEnabled: boolean = getEnvBoolean('SWAGGER_ENABLED', { default: false });
}
