import { LogLevel } from 'src/common/constants/log-levels';
import { getEnv, getEnvBoolean } from './util';

export class SentryConfig {
  dsn: string = getEnv('SENTRY_DSN', { default: '' });
  debug: boolean = getEnvBoolean('SENTRY_DEBUG', { default: false });
  environment: string = getEnv('SENTRY_ENVIRONMENT', { default: '' });
  release: string = getEnv('SENTRY_RELEASE', { default: '' });
  logLevels: LogLevel[] = [LogLevel.DEBUG];
}
