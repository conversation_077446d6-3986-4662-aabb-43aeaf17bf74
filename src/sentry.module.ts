import { HttpException, Module } from '@nestjs/common';
import {
  SentryInterceptor,
  SentryModule as PkgSentryModule,
} from '@ntegral/nestjs-sentry';
import { ErrorDto } from './common/errors/error.dto';
import { config } from './config';

export const SENTRY_INTERCEPTOR = 'sentry-interceptor';

@Module({
  imports: [
    PkgSentryModule.forRoot({
      beforeSend(event, hint) {
        const exception = hint.originalException;
        const errorDto = ErrorDto.from(exception as Error);
        event.fingerprint = errorDto.sentry_fingerprint();
        return event;
      },
      dsn: config.sentryConfig.dsn,
      debug: config.sentryConfig.debug,
      environment: config.sentryConfig.environment,
      release: config.sentryConfig.release,
    }),
  ],
  providers: [
    {
      provide: SENTRY_INTERCEPTOR,
      useFactory: (): SentryInterceptor =>
        new SentryInterceptor({
          filters: [
            {
              type: HttpException,
              filter: (exception: HttpException) => exception.getStatus() < 500,
            },
          ],
        }),
    },
  ],
  exports: [SENTRY_INTERCEPTOR],
})
export class SentryModule {}
