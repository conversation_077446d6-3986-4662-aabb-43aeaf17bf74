import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import { config } from 'src/config';
import { BaseCronService } from 'src/cron/bases/base-cron.service';
import { AvailabilityService } from 'src/logic/availability.service';

@Injectable()
export class GetCarAvailabilityCronService extends BaseCronService {
  constructor(private readonly availabilityService: AvailabilityService) {
    super();
  }

  protected cronName = 'GET_CAR_AVAILABILITY_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.getCarAvailability.enabled;
  protected cronInterval: string = cronstrue
    .toString(config.scheduleConfig.getCarAvailability.expression)
    .toLowerCase();

  @Cron(config.scheduleConfig.getCarAvailability.expression, {
    disabled: !config.scheduleConfig.getCarAvailability.enabled,
  })
  async getCarAvailability() {
    try {
      this.log('Getting car availability...');

      await this.availabilityService.getCarAvailabilityForAllStations();
    } catch (err) {
      this.error(`Error when getting car availability: ${err.message}`, {
        origin: this.cronName,
        cause: err,
        stack: err.stack,
      });
    }
  }
}
