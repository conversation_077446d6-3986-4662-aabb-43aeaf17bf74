import { Injectable, OnModuleInit } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { config } from 'src/config';
import { BaseCronService } from 'src/cron/bases/base-cron.service';
import { ReservationService } from 'src/logic/reservation.service';
import { NewRelicReservationMetricService } from '~shared/newrelic/metric/category/reservation.metric.newrelic.service';
import { NewRelicMetricCarModel } from '~shared/newrelic/metric/enums/newrelic-metric.enum';

@Injectable()
export class ReportMetricReservationNumberOfOngoingCronService
  extends BaseCronService
  implements OnModuleInit
{
  constructor(
    private readonly reservationService: ReservationService,
    private readonly reservationMetricService: NewRelicReservationMetricService,
  ) {
    super();
  }

  protected cronName = 'REPORT_METRIC_RESERVATION_NUMBER_OF_ONGOING_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.reportMetricReservationNumberOfOngoing.enabled;
  protected cronInterval: string = cronstrue
    .toString(
      config.scheduleConfig.reportMetricReservationNumberOfOngoing.expression,
    )
    .toLowerCase();

  @Cron(
    config.scheduleConfig.reportMetricReservationNumberOfOngoing.expression,
    {
      disabled:
        !config.scheduleConfig.reportMetricReservationNumberOfOngoing.enabled,
    },
  )
  async reportMetricReservationNumberOfOngoing() {
    try {
      const countOfOnGoingBluecarReservations =
        await this.reservationService.countBsgOnGoingReservations(
          CarModelEnum.BlueCar,
        );

      const countOfOnGoingOpelCorsaEReservations =
        await this.reservationService.countBsgOnGoingReservations(
          CarModelEnum.OpelCorsaE,
        );

      const consolidatedDataPoints: {
        count: number;
        carModel: NewRelicMetricCarModel;
      }[] = [
        {
          count: countOfOnGoingBluecarReservations,
          carModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          count: countOfOnGoingOpelCorsaEReservations,
          carModel: NewRelicMetricCarModel.CorsaE,
        },
      ];

      return this.reservationMetricService.bulkSetNumberOfOnGoing(
        consolidatedDataPoints,
      );
    } catch (err) {
      this.error(
        `Error when reporting metric reservation.numberOfOngoing: ${err.message}`,
        {
          origin: this.cronName,
          cause: err,
          stack: err.stack,
        },
      );
    }
  }
}
