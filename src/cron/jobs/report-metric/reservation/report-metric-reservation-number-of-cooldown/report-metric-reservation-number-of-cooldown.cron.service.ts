import { Injectable, OnModuleInit } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { config } from 'src/config';
import { BaseCronService } from 'src/cron/bases/base-cron.service';
import { ReservationService } from 'src/logic/reservation.service';
import { NewRelicReservationMetricService } from '~shared/newrelic/metric/category/reservation.metric.newrelic.service';
import { NewRelicMetricCarModel } from '~shared/newrelic/metric/enums/newrelic-metric.enum';

@Injectable()
export class ReportMetricReservationNumberOfCooldownCronService
  extends BaseCronService
  implements OnModuleInit
{
  constructor(
    private readonly reservationService: ReservationService,
    private readonly reservationMetricService: NewRelicReservationMetricService,
  ) {
    super();
  }

  protected cronName = 'REPORT_METRIC_RESERVATION_NUMBER_OF_COOLDOWN_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.reportMetricReservationNumberOfCooldown.enabled;
  protected cronInterval: string = cronstrue
    .toString(
      config.scheduleConfig.reportMetricReservationNumberOfCooldown.expression,
    )
    .toLowerCase();

  @Cron(
    config.scheduleConfig.reportMetricReservationNumberOfCooldown.expression,
    {
      disabled:
        !config.scheduleConfig.reportMetricReservationNumberOfCooldown.enabled,
    },
  )
  async reportMetricReservationNumberOfCooldown() {
    try {
      const countOfCooldownBluecarReservations =
        await this.reservationService.countBsgCooldownReservations(
          CarModelEnum.BlueCar,
        );

      const countOfCooldownOpelCorsaEReservations =
        await this.reservationService.countBsgCooldownReservations(
          CarModelEnum.OpelCorsaE,
        );

      const consolidatedDataPoints: {
        count: number;
        carModel: NewRelicMetricCarModel;
      }[] = [
        {
          count: countOfCooldownBluecarReservations,
          carModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          count: countOfCooldownOpelCorsaEReservations,
          carModel: NewRelicMetricCarModel.CorsaE,
        },
      ];

      return this.reservationMetricService.bulkSetNumberOfCooldown(
        consolidatedDataPoints,
      );
    } catch (err) {
      this.error(
        `Error when reporting metric reservation.numberOfCooldown: ${err.message}`,
        {
          origin: this.cronName,
          cause: err,
          stack: err.stack,
        },
      );
    }
  }
}
