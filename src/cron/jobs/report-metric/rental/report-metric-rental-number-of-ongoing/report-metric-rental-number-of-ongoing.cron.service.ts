import { Injectable, OnModuleInit } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { config } from 'src/config';
import { BaseCronService } from 'src/cron/bases/base-cron.service';
import { ReservationService } from 'src/logic/reservation.service';
import { NewRelicRentalMetricService } from '~shared/newrelic/metric/category/rental.metric.newrelic.service';
import {
  NewRelicMetricCarModel,
  NewRelicMetricRentalType,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';

@Injectable()
export class ReportMetricRentalNumberOfOngoingCronService
  extends BaseCronService
  implements OnModuleInit
{
  constructor(
    private readonly reservationService: ReservationService,
    private readonly rentalMetricService: NewRelicRentalMetricService,
  ) {
    super();
  }

  protected cronName = 'REPORT_METRIC_RENTAL_NUMBER_OF_ONGOING_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.reportMetricRentalNumberOfOngoing.enabled;
  protected cronInterval: string = cronstrue
    .toString(
      config.scheduleConfig.reportMetricRentalNumberOfOngoing.expression,
    )
    .toLowerCase();

  @Cron(config.scheduleConfig.reportMetricRentalNumberOfOngoing.expression, {
    disabled: !config.scheduleConfig.reportMetricRentalNumberOfOngoing.enabled,
  })
  async reportMetricRentalNumberOfOngoing() {
    try {
      const countOfOnGoingBluecarSubscriptionRentals =
        await this.reservationService.countBsgOnGoingRentals(
          CarModelEnum.BlueCar,
          'subscription',
        );

      const countOfOnGoingBluecarPackageRentals =
        await this.reservationService.countBsgOnGoingRentals(
          CarModelEnum.BlueCar,
          'package',
        );

      const countOfOnGoingOpelCorsaESubscriptionRentals =
        await this.reservationService.countBsgOnGoingRentals(
          CarModelEnum.OpelCorsaE,
          'subscription',
        );

      const countOfOnGoingOpelCorsaEPackageRentals =
        await this.reservationService.countBsgOnGoingRentals(
          CarModelEnum.OpelCorsaE,
          'package',
        );

      const consolidatedDataPoints: {
        count: number;
        rentalType: NewRelicMetricRentalType;
        carModel: NewRelicMetricCarModel;
      }[] = [
        {
          count: countOfOnGoingBluecarSubscriptionRentals,
          rentalType: NewRelicMetricRentalType.Subscription,
          carModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          count: countOfOnGoingBluecarPackageRentals,
          rentalType: NewRelicMetricRentalType.RentalPackage,
          carModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          count: countOfOnGoingOpelCorsaESubscriptionRentals,
          rentalType: NewRelicMetricRentalType.Subscription,
          carModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          count: countOfOnGoingOpelCorsaEPackageRentals,
          rentalType: NewRelicMetricRentalType.RentalPackage,
          carModel: NewRelicMetricCarModel.CorsaE,
        },
      ];

      return this.rentalMetricService.bulkSetNumberOfOnGoing(
        consolidatedDataPoints,
      );
    } catch (err) {
      this.error(
        `Error when reporting metric rental.numberOfOngoing: ${err.message}`,
        {
          origin: this.cronName,
          cause: err,
          stack: err.stack,
        },
      );
    }
  }
}
