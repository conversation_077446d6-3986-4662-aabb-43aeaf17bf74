import { Injectable, OnModuleInit } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { config } from 'src/config';
import { BaseCronService } from 'src/cron/bases/base-cron.service';
import { ReservationService } from 'src/logic/reservation.service';
import { NewRelicRentalMetricService } from '~shared/newrelic/metric/category/rental.metric.newrelic.service';
import {
  NewRelicMetricCarModel,
  NewRelicMetricRentalType,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';

@Injectable()
export class ReportMetricRentalNumberOfWaitingForInvoicingCronService
  extends BaseCronService
  implements OnModuleInit
{
  constructor(
    private readonly reservationService: ReservationService,
    private readonly rentalMetricService: NewRelicRentalMetricService,
  ) {
    super();
  }

  protected cronName =
    'REPORT_METRIC_RENTAL_NUMBER_OF_WAITING_FOR_INVOICING_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.reportMetricRentalNumberOfWaitingForInvoicing.enabled;
  protected cronInterval: string = cronstrue
    .toString(
      config.scheduleConfig.reportMetricRentalNumberOfWaitingForInvoicing
        .expression,
    )
    .toLowerCase();

  @Cron(
    config.scheduleConfig.reportMetricRentalNumberOfWaitingForInvoicing
      .expression,
    {
      disabled:
        !config.scheduleConfig.reportMetricRentalNumberOfWaitingForInvoicing
          .enabled,
    },
  )
  async reportMetricRentalNumberOfWaitingForInvoicing() {
    try {
      const countOfWaitingForInvoicingBluecarSubscriptionRentals =
        await this.reservationService.countBsgEndedWaitingForInvoicing(
          CarModelEnum.BlueCar,
          'subscription',
        );
      const countOfWaitingForInvoicingBluecarPackageRentals =
        await this.reservationService.countBsgEndedWaitingForInvoicing(
          CarModelEnum.BlueCar,
          'package',
        );
      const countOfWaitingForInvoicingCorsaESubscriptionRentals =
        await this.reservationService.countBsgEndedWaitingForInvoicing(
          CarModelEnum.OpelCorsaE,
          'subscription',
        );
      const countOfWaitingForInvoicingCorsaEPackageRentals =
        await this.reservationService.countBsgEndedWaitingForInvoicing(
          CarModelEnum.OpelCorsaE,
          'package',
        );

      const consolidatedDataPoints: {
        count: number;
        rentalType: NewRelicMetricRentalType;
        carModel: NewRelicMetricCarModel;
      }[] = [
        {
          count: countOfWaitingForInvoicingBluecarSubscriptionRentals,
          rentalType: NewRelicMetricRentalType.Subscription,
          carModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          count: countOfWaitingForInvoicingBluecarPackageRentals,
          rentalType: NewRelicMetricRentalType.RentalPackage,
          carModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          count: countOfWaitingForInvoicingCorsaESubscriptionRentals,
          rentalType: NewRelicMetricRentalType.Subscription,
          carModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          count: countOfWaitingForInvoicingCorsaEPackageRentals,
          rentalType: NewRelicMetricRentalType.RentalPackage,
          carModel: NewRelicMetricCarModel.CorsaE,
        },
      ];

      return this.rentalMetricService.bulkSetNumberOfWaitingForInvoicing(
        consolidatedDataPoints,
      );
    } catch (err) {
      this.error(
        `Error when reporting metric rental.numberOfWaitingForInvoicing: ${err.message}`,
        {
          origin: this.cronName,
          cause: err,
          stack: err.stack,
        },
      );
    }
  }
}
