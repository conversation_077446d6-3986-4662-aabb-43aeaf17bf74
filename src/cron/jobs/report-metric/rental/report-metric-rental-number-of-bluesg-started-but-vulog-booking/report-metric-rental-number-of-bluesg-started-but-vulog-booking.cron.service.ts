import { Injectable, OnModuleInit } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import { MapUtils } from 'src/common/utils/map.util';
import { config } from 'src/config';
import { BaseCronService } from 'src/cron/bases/base-cron.service';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { ReservationService } from 'src/logic/reservation.service';
import { Reservation } from 'src/model/reservation.domain';
import { NewRelicRentalMetricService } from '~shared/newrelic/metric/category/rental.metric.newrelic.service';
import {
  NewRelicMetricCarModel,
  NewRelicMetricRentalType,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';

@Injectable()
export class ReportMetricRentalNumberOfBlueSGStartedButVulogBookingCronService
  extends BaseCronService
  implements OnModuleInit
{
  constructor(
    private readonly reservationService: ReservationService,
    private readonly rentalMetricService: NewRelicRentalMetricService,
    private readonly vulogCarService: VulogCarService,
  ) {
    super();
  }

  protected cronName =
    'REPORT_METRIC_RENTAL_NUMBER_OF_BLUESG_STARTED_BUT_VULOG_BOOKING_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.reportMetricRentalNumberOfBlueSGStartedButVulogBooking
      .enabled;
  protected cronInterval: string = cronstrue
    .toString(
      config.scheduleConfig
        .reportMetricRentalNumberOfBlueSGStartedButVulogBooking.expression,
    )
    .toLowerCase();

  @Cron(
    config.scheduleConfig.reportMetricRentalNumberOfBlueSGStartedButVulogBooking
      .expression,
    {
      disabled:
        !config.scheduleConfig
          .reportMetricRentalNumberOfBlueSGStartedButVulogBooking.enabled,
    },
  )
  async reportMetricRentalNumberOfBlueSGStartedButVulogBooking() {
    try {
      const bsgOnGoingRentals: Reservation[] =
        await this.reservationService.getBsgOnGoingRentals();

      const vulogOnGoingRentals = await this.vulogCarService.getOnGoingRentals(
        null,
        true,
      );

      const vulogOnGoingRentalMap = MapUtils.build(
        vulogOnGoingRentals,
        (rt) => rt.sourceId.value,
      );

      const metricDimMap = new Map<string, number>();

      const consolidatedDataPoints: {
        count: number;
        rentalType: NewRelicMetricRentalType;
        carModel: NewRelicMetricCarModel;
      }[] = [];

      bsgOnGoingRentals.map((bsgRt) => {
        if (!vulogOnGoingRentalMap.has(bsgRt.vulogTripId.value)) {
          const key = `${bsgRt.getMetricNewRelicRentalType()}/${bsgRt.getMetricNewRelicCarModel()}`;

          metricDimMap.set(
            key,
            metricDimMap.has(key) ? metricDimMap.get(key) + 1 : 1,
          );
        }
      });

      for (const [key, value] of metricDimMap.entries()) {
        const keySplit = key.split('/');
        const rentalType = keySplit[0] as NewRelicMetricRentalType;
        const carModel = keySplit[1] as NewRelicMetricCarModel;

        consolidatedDataPoints.push({ count: value, rentalType, carModel });
      }

      return this.rentalMetricService.bulkSetNumberOfBlueSGStartedButVulogBooking(
        consolidatedDataPoints,
      );
    } catch (err) {
      this.error(
        `Error when reporting metric reservation.numberOfBlueSGStartedButVulogBooking: ${err.message}`,
        {
          origin: this.cronName,
          cause: err,
          stack: err.stack,
        },
      );
    }
  }
}
