import { Injectable, OnModuleInit } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import {
  carInServiceZoneCond,
  carPluggedCond,
  doorsAndWindowsClosedCond,
  doorsLockedCond,
  engineOffCond,
  testingEndRentalConditions,
} from 'src/common/constants/ending-rental-conditions';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { config } from 'src/config';
import { BaseCronService } from 'src/cron/bases/base-cron.service';
import { ReservationService } from 'src/logic/reservation.service';
import { VulogTelemetryService } from 'src/logic/telemetry-repository/vulog/vulog-telemetry.service';
import { Reservation } from 'src/model/reservation.domain';
import { TelemetryCarInfo } from 'src/model/telemetry-car-info';
import { NewRelicRentalMetricService } from '~shared/newrelic/metric/category/rental.metric.newrelic.service';
import {
  NewRelicMetricCarModel,
  NewRelicMetricRentalType,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';

@Injectable()
export class ReportMetricRentalNumberOfEndingRentalConditionsSatisfiedCronService
  extends BaseCronService
  implements OnModuleInit
{
  constructor(
    private readonly reservationService: ReservationService,
    private readonly rentalMetricService: NewRelicRentalMetricService,
    private readonly vulogTelemetryService: VulogTelemetryService,
  ) {
    super();
  }

  protected cronName =
    'REPORT_METRIC_RENTAL_NUMBER_OF_ENDING_RENTAL_CONDITIONS_SATISFIED_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig
      .reportMetricRentalNumberOfEndingRentalConditionsSatisfied.enabled;
  protected cronInterval: string = cronstrue
    .toString(
      config.scheduleConfig
        .reportMetricRentalNumberOfEndingRentalConditionsSatisfied.expression,
    )
    .toLowerCase();

  @Cron(
    config.scheduleConfig
      .reportMetricRentalNumberOfEndingRentalConditionsSatisfied.expression,
    {
      disabled:
        !config.scheduleConfig
          .reportMetricRentalNumberOfEndingRentalConditionsSatisfied.enabled,
    },
  )
  async reportMetricNumberOfEndingRentalConditionsSatisfied() {
    try {
      const onGoingBluecarSubscriptionRentals: Reservation[] =
        await this.reservationService.getBsgOnGoingRentals(
          CarModelEnum.BlueCar,
          'subscription',
        );
      const onGoingBluecarPackageRentals: Reservation[] =
        await this.reservationService.getBsgOnGoingRentals(
          CarModelEnum.BlueCar,
          'package',
        );
      const onGoingOpelCorsaESubscriptionRentals: Reservation[] =
        await this.reservationService.getBsgOnGoingRentals(
          CarModelEnum.OpelCorsaE,
          'subscription',
        );
      const onGoingOpelCorsaEPackageRentals: Reservation[] =
        await this.reservationService.getBsgOnGoingRentals(
          CarModelEnum.OpelCorsaE,
          'package',
        );

      const carInServiceZoneCondMap = new Map<string, boolean>();
      const doorsAndWindowsClosedCondMap = new Map<string, boolean>();
      const doorsLockedCondMap = new Map<string, boolean>();
      const engineOffCondMap = new Map<string, boolean>();
      const carPluggedCondMap = new Map<string, boolean>();

      // 5 is our five conditions map above
      // If testingEndRentalConditions !== list of ending rental conditions in this service --> Throw error
      if (testingEndRentalConditions.length !== 5) {
        throw new Error(
          'Ending rental has been updated. Please update correspondingly in this service',
        );
      }

      const vvgCars: TelemetryCarInfo[] =
        await this.vulogTelemetryService.getListOfCarsStatusSessionCommand(
          true,
        );

      vvgCars.forEach((vvgCar) => {
        const carInServiceZone = carInServiceZoneCond.validate({
          telemetryCarInfo: vvgCar,
        });
        const doorsAndWindowsClosed = doorsAndWindowsClosedCond.validate({
          telemetryCarInfo: vvgCar,
        });
        const doorsLocked = doorsLockedCond.validate({
          telemetryCarInfo: vvgCar,
        });
        const engineOff = engineOffCond.validate({
          telemetryCarInfo: vvgCar,
        });
        const carPlugged = carPluggedCond.validate({
          telemetryCarInfo: vvgCar,
        });

        if (carInServiceZone.fulfilled) {
          carInServiceZoneCondMap.set(vvgCar.id.value, true);
        }

        if (doorsAndWindowsClosed.fulfilled) {
          doorsAndWindowsClosedCondMap.set(vvgCar.id.value, true);
        }

        if (doorsLocked.fulfilled) {
          doorsLockedCondMap.set(vvgCar.id.value, true);
        }

        if (engineOff.fulfilled) {
          engineOffCondMap.set(vvgCar.id.value, true);
        }

        if (carPlugged.fulfilled) {
          carPluggedCondMap.set(vvgCar.id.value, true);
        }
      });

      const reduceFunc = (prev, curr) => {
        if (
          !!(
            carInServiceZoneCondMap.has(curr.carId.value) &&
            doorsAndWindowsClosedCondMap.has(curr.carId.value) &&
            doorsLockedCondMap.has(curr.carId.value) &&
            engineOffCondMap.has(curr.carId.value) &&
            carPluggedCondMap.has(curr.carId.value)
          )
        ) {
          return prev + 1;
        }

        return prev;
      };

      const countOfSatisfiedOnGoingBluecarSubscriptionRentals =
        onGoingBluecarSubscriptionRentals.reduce(reduceFunc, 0);

      const countOfSatisfiedOnGoingBluecarPackageRentals =
        onGoingBluecarPackageRentals.reduce(reduceFunc, 0);

      const countOfSatisfiedOnGoingOpelCorsaESubscriptionRentals =
        onGoingOpelCorsaESubscriptionRentals.reduce(reduceFunc, 0);

      const countOfSatisfiedOnGoingOpelCorsaEPackageRentals =
        onGoingOpelCorsaEPackageRentals.reduce(reduceFunc, 0);

      const consolidatedDataPoints: {
        count: number;
        rentalType: NewRelicMetricRentalType;
        carModel: NewRelicMetricCarModel;
      }[] = [
        {
          count: countOfSatisfiedOnGoingBluecarSubscriptionRentals,
          rentalType: NewRelicMetricRentalType.Subscription,
          carModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          count: countOfSatisfiedOnGoingBluecarPackageRentals,
          rentalType: NewRelicMetricRentalType.RentalPackage,
          carModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          count: countOfSatisfiedOnGoingOpelCorsaESubscriptionRentals,
          rentalType: NewRelicMetricRentalType.Subscription,
          carModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          count: countOfSatisfiedOnGoingOpelCorsaEPackageRentals,
          rentalType: NewRelicMetricRentalType.RentalPackage,
          carModel: NewRelicMetricCarModel.CorsaE,
        },
      ];

      return this.rentalMetricService.bulkSetNumberOfEndingRentalConditionsSatisfied(
        consolidatedDataPoints,
      );
    } catch (err) {
      this.error(
        `Error when reporting metric rental.numberOfEndingRentalConditionsSatisfied: ${err.message}`,
        {
          origin: this.cronName,
          cause: err,
          stack: err.stack,
        },
      );

      return false;
    }
  }
}
