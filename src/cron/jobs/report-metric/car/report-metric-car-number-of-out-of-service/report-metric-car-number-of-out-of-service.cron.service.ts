import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import { config } from 'src/config';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { StationService } from 'src/logic/station/station.service';
import { VulogTelemetryService } from 'src/logic/telemetry-repository/vulog/vulog-telemetry.service';
import { VulogFleetService } from 'src/logic/vulog/vulog-fleet-service.service';
import { RealtimeCar } from 'src/model/realtime.car';
import { ConfigurationService } from '~shared/configuration/configuration.service';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { NewRelicCarMetricService } from '~shared/newrelic/metric/category/car.metric.newrelic.service';
import { NewRelicMetricCarModel } from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { CarMetricDataPoint } from '../base/car-metric.payload';
import { ReportMetricCarCronService } from '../base/report-metric-car.cron.service';

@Injectable()
export class ReportMetricCarNumberOfOutOfServiceCronService extends ReportMetricCarCronService {
  constructor(
    protected readonly vulogCarService: VulogCarService,
    protected readonly carMetricService: NewRelicCarMetricService,
    protected readonly stationService: StationService,
    protected readonly vulogFleetService: VulogFleetService,
    protected readonly vulogTelemetryService: VulogTelemetryService,
    protected readonly configurationService: ConfigurationService,
    protected readonly featureFlagService: FeatureFlagService,
  ) {
    super(
      vulogCarService,
      vulogFleetService,
      stationService,
      vulogTelemetryService,
      configurationService,
      featureFlagService,
    );
  }

  protected cronName = 'REPORT_METRIC_CAR_NUMBER_OF_OUT_OF_SERVICE_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.reportMetricCarNumberOfOutOfService.enabled;
  protected cronInterval: string = cronstrue
    .toString(
      config.scheduleConfig.reportMetricCarNumberOfOutOfService.expression,
    )
    .toLowerCase();

  private prepareMetricDataPoint(
    realTimeCarsWithStations: RealtimeCar[],
    carModel: NewRelicMetricCarModel,
  ): CarMetricDataPoint[] {
    const metricDataPoints: CarMetricDataPoint[] = [];

    this.sendMetricFullDimensions({
      dimensionMetadata: {
        carModel,
        stationFacetSupported: false,
      },
      realTimeCarsManipulationMetadata: {
        realTimeCarsWithStations,
        highOrderFunctionName: 'reduce',
        predicateFunc: (value, comparisonValues) =>
          value?.model?.value === (comparisonValues.carModel || undefined),
      },
      sendMetricFunc: (values) => {
        metricDataPoints.push({
          count: values.count,
          carModel: values.carModel,
        });
      },
    });

    return metricDataPoints;
  }

  @Cron(config.scheduleConfig.reportMetricCarNumberOfOutOfService.expression, {
    disabled:
      !config.scheduleConfig.reportMetricCarNumberOfOutOfService.enabled,
  })
  async reportMetricCarNumberOfOutOfService() {
    try {
      const realTimeCarsWithStations: RealtimeCar[] =
        await this.getRealtimeCarsWithStations({
          includeMissingStation: true,
          onlyAvailableCar: false,
          filterFunction: (value) => value.disabled,
        });

      const consolidatedMetricDataPoints: CarMetricDataPoint[] = [
        ...this.prepareMetricDataPoint(
          realTimeCarsWithStations,
          NewRelicMetricCarModel.Bluecar,
        ),
        ...this.prepareMetricDataPoint(
          realTimeCarsWithStations,
          NewRelicMetricCarModel.CorsaE,
        ),
        ...this.prepareMetricDataPoint(
          realTimeCarsWithStations,
          NewRelicMetricCarModel.NoCarModel,
        ),
      ];

      return this.carMetricService.bulkSetNumberOfOutOfService(
        consolidatedMetricDataPoints,
      );
    } catch (err) {
      this.error(
        `Error when reporting metric car.numberOfOutOfService: ${err.message}`,
        {
          origin: this.cronName,
          cause: err,
          stack: err.stack,
        },
      );
    }
  }
}
