import { Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as _ from 'lodash';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { VulogZoneId } from 'src/common/tiny-types';
import { MapUtils } from 'src/common/utils/map.util';
import { BaseCronService } from 'src/cron/bases/base-cron.service';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { StationService } from 'src/logic/station/station.service';
import { VulogTelemetryService } from 'src/logic/telemetry-repository/vulog/vulog-telemetry.service';
import { VulogFleetService } from 'src/logic/vulog/vulog-fleet-service.service';
import { StationDto } from 'src/model/dtos/station.dto';
import { VulogCarRealTimeDto } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogFleetServiceType } from 'src/model/dtos/vulog-fleet-service/vulog-fleet-service.dto';
import { RealtimeCar } from 'src/model/realtime.car';
import { StaticCar } from 'src/model/static.car';
import { ConfigurationService } from '~shared/configuration/configuration.service';
import { ConfigurationKey } from '~shared/configuration/constants/configuration';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { CarInfo } from '~shared/feature-flag/models/car-info';
import {
  NewRelicMetricCarModel,
  NewRelicMetricService,
  NewRelicMetricStation,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';

export class ReportMetricCarCronService extends BaseCronService {
  constructor(
    protected readonly vulogCarService: VulogCarService,
    protected readonly vulogFleetService: VulogFleetService,
    protected readonly stationService: StationService,
    protected readonly vulogTelemetryService: VulogTelemetryService,
    protected readonly configurationService: ConfigurationService,
    protected readonly featureFlagService: FeatureFlagService,
  ) {
    super();
  }

  private serviceDimensionalValueToApplicationValue(
    service: string,
  ): VulogFleetServiceType {
    let serviceEnum: VulogFleetServiceType;

    switch (service) {
      case NewRelicMetricService.CustomerTrip:
        serviceEnum = VulogFleetServiceType.FreeFloating;
        break;

      case NewRelicMetricService.ServiceTrip:
        serviceEnum = VulogFleetServiceType.ServiceTrip;
        break;

      default:
        serviceEnum = undefined; // Out Of Service
        break;
    }

    return serviceEnum;
  }

  private carModelDimensionalValueToCarModelApplicationValue(
    carModel: string,
  ): CarModelEnum {
    let carModelEnum: CarModelEnum;

    switch (carModel) {
      case NewRelicMetricCarModel.Bluecar:
        carModelEnum = CarModelEnum.BlueCar;
        break;

      case NewRelicMetricCarModel.CorsaE:
        carModelEnum = CarModelEnum.OpelCorsaE;
        break;

      default:
        carModelEnum = undefined; // No Car Model
        break;
    }

    return carModelEnum;
  }

  protected async getStaticCarPlateMap(): Promise<Map<string, StaticCar>> {
    const fleetServices = await this.vulogFleetService.getFleetServices(true);

    const fleetServiceMap = MapUtils.build(
      fleetServices,
      (fleetService) => fleetService.id.value,
    );

    const staticCars = (await this.vulogCarService.getAllStaticCars()).map(
      (staticCar) => {
        staticCar.serviceType = staticCar.serviceId
          ? fleetServiceMap.get(staticCar.serviceId.value).type
          : null;

        return staticCar;
      },
    );

    return MapUtils.build(staticCars, (staticCar) => staticCar.plate.value);
  }

  protected async getRealtimeCarsWithStations(options: {
    source?: 'aima' | 'vvg';
    filterFunction?: (value: RealtimeCar) => boolean;
    includeMissingStation?: boolean;
    onlyAvailableCar?: boolean;
  }): Promise<RealtimeCar[]> {
    const zoneStationMap = await this.stationService.getZoneStationMap();

    const warningLowBatterLevel = (
      await this.configurationService.findOne<object>(
        ConfigurationKey.WARNING_LOW_BATTERY_LEVEL_PER_CAR_MODEL,
      )
    ).value;

    const customerUsableFleetService =
      await this.vulogFleetService.getCustomerUsableFleetService();

    let realTimeCars: RealtimeCar[];

    switch (options.source) {
      case 'aima':
        realTimeCars = (
          await this.vulogCarService.getCarsInRealtime(options.onlyAvailableCar)
        ).filter(options.filterFunction || (() => true));

        break;

      case 'vvg':
        const realtimeCarPlateMap: {
          [plate: string]: VulogCarRealTimeDto;
        } = await this.vulogCarService.getRealtimeCarsPlateMap();

        realTimeCars =
          await this.vulogTelemetryService.getListOfCarsStatusSessionCommand(
            true,
          );

        if (options.onlyAvailableCar) {
          realTimeCars = await Promise.all(
            realTimeCars.filter(async (value) => {
              const warningLowBatteryLevelByModel =
                warningLowBatterLevel[value.model.value];

              const filterOutNonCustomerZones =
                await this.featureFlagService.filterOutNonCustomerZones(
                  CarInfo.fromRealtimeCar(value),
                );

              return realtimeCarPlateMap[value.plate.value].isAvailable(
                warningLowBatteryLevelByModel,
                customerUsableFleetService?.vehicleIds,
                filterOutNonCustomerZones
                  ? customerUsableFleetService.getZoneIdsAsMap()
                  : null,
              );
            }),
          );
        }

        realTimeCars = realTimeCars.filter(
          options.filterFunction || (() => true),
        );

        break;

      default:
        realTimeCars = (
          await this.vulogCarService.getCarsInRealtime(options.onlyAvailableCar)
        ).filter(options.filterFunction || (() => true));
    }

    const realTimeCarsWithStations: RealtimeCar[] = realTimeCars.reduce(
      (prev, curr) => {
        if (!curr.zoneIds?.length) {
          if (options.includeMissingStation) {
            curr.currentStation = null;

            prev.push(curr);
          }

          return prev;
        }

        const matchingZoneId: VulogZoneId = curr?.zoneIds?.find(
          (zoneId) => !!zoneStationMap[zoneId.value],
        );

        const currentStation: StationDto = matchingZoneId
          ? zoneStationMap[matchingZoneId.value]
          : null;

        if (!currentStation) {
          Logger.debug(
            `Car (Vulog Car ID: ${curr.id.value}) does not attach to a station`,
            {
              rltCar: curr,
            },
          );

          if (options.includeMissingStation) {
            curr.currentStation = null;

            prev.push(curr);
          }

          return prev;
        }

        curr.currentStation = plainToInstance(
          StationDto,
          currentStation,
        ).toStation();

        prev.push(curr);

        return prev;
      },
      [],
    );

    return realTimeCarsWithStations;
  }

  /**
   * Send metric with necessary dimensions
   * @param dimensionMetadata Dimensional metadata for your metric
   * @param realTimeCarsManipulationMetadata Metadata for real time car having current stations
   * @param realTimeCarsManipulationMetadata.realTimeCarsWithStations Data that high order function runs on
   * @param realTimeCarsManipulationMetadata.highOrderFunctionName The name of high order function that runs on data
   * @param realTimeCarsManipulationMetadata.predicateFunc Predicate function for high order function
   * @param sendMetricFunc the function should be the last one to be invoked
   */
  protected async sendMetricFullDimensions({
    dimensionMetadata,
    realTimeCarsManipulationMetadata,
    sendMetricFunc,
  }: {
    dimensionMetadata: {
      service?: NewRelicMetricService;
      carModel?: NewRelicMetricCarModel;
      stationFacetSupported?: boolean;
    };
    realTimeCarsManipulationMetadata: {
      realTimeCarsWithStations: RealtimeCar[];
      highOrderFunctionName: 'filter' | 'reduce';
      predicateFunc: (
        value: RealtimeCar,
        comparisonValues: {
          serviceType?: VulogFleetServiceType;
          carModel?: CarModelEnum;
          stationName?: string;
        },
      ) => boolean;
    };
    sendMetricFunc: (values: {
      count: number;
      serviceType?: NewRelicMetricService;
      carModel?: NewRelicMetricCarModel;
      stationName?: string;
    }) => void;
  }) {
    const serviceEnum: VulogFleetServiceType =
      this.serviceDimensionalValueToApplicationValue(dimensionMetadata.service);
    const carModelEnum: CarModelEnum =
      this.carModelDimensionalValueToCarModelApplicationValue(
        dimensionMetadata.carModel,
      );

    let carUnderDimensions: RealtimeCar[];
    let carUnderDimensionsCount: number;

    switch (realTimeCarsManipulationMetadata.highOrderFunctionName) {
      case 'filter':
        carUnderDimensions =
          realTimeCarsManipulationMetadata.realTimeCarsWithStations[
            realTimeCarsManipulationMetadata.highOrderFunctionName
          ]((value: RealtimeCar) =>
            realTimeCarsManipulationMetadata.predicateFunc(value, {
              serviceType: serviceEnum,
              carModel: carModelEnum,
            }),
          );

        break;

      case 'reduce':
        carUnderDimensionsCount =
          realTimeCarsManipulationMetadata.realTimeCarsWithStations[
            realTimeCarsManipulationMetadata.highOrderFunctionName
          ]((prev: number, curr: RealtimeCar) => {
            if (
              realTimeCarsManipulationMetadata.predicateFunc(curr, {
                serviceType: serviceEnum,
                carModel: carModelEnum,
              })
            ) {
              return prev + 1;
            }

            return prev;
          }, 0);

        break;
    }

    if (dimensionMetadata.stationFacetSupported) {
      const countByStationsHavingCarUnderCustomerTripServiceAndBlueCar: {
        [stationName: string]: number;
      } = _.countBy(
        carUnderDimensions,
        (el) =>
          el.currentStation?.displayName || NewRelicMetricStation.OutOfStation,
      );

      Object.entries(
        countByStationsHavingCarUnderCustomerTripServiceAndBlueCar,
      ).forEach(([stationName, count]) => {
        sendMetricFunc({
          count,
          serviceType: dimensionMetadata.service,
          carModel: dimensionMetadata.carModel,
          stationName: stationName,
        });
      });
    } else {
      sendMetricFunc({
        count: carUnderDimensionsCount,
        serviceType: dimensionMetadata.service,
        carModel: dimensionMetadata.carModel,
      });
    }
  }
}
