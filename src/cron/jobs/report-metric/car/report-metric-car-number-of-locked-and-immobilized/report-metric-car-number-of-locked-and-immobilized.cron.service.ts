import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import { config } from 'src/config';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { StationService } from 'src/logic/station/station.service';
import { VulogTelemetryService } from 'src/logic/telemetry-repository/vulog/vulog-telemetry.service';
import { VulogFleetService } from 'src/logic/vulog/vulog-fleet-service.service';
import { RealtimeCar } from 'src/model/realtime.car';
import { StaticCar } from 'src/model/static.car';
import { ConfigurationService } from '~shared/configuration/configuration.service';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { NewRelicCarMetricService } from '~shared/newrelic/metric/category/car.metric.newrelic.service';
import {
  NewRelicMetricCarModel,
  NewRelicMetricService,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { CarMetricDataPoint } from '../base/car-metric.payload';
import { ReportMetricCarCronService } from '../base/report-metric-car.cron.service';

@Injectable()
export class ReportMetricCarNumberOfLockedAndImmobilizedCronService extends ReportMetricCarCronService {
  constructor(
    protected readonly vulogCarService: VulogCarService,
    protected readonly carMetricService: NewRelicCarMetricService,
    protected readonly stationService: StationService,
    protected readonly vulogFleetService: VulogFleetService,
    protected readonly vulogTelemetryService: VulogTelemetryService,
    protected readonly configurationService: ConfigurationService,
    protected readonly featureFlagService: FeatureFlagService,
  ) {
    super(
      vulogCarService,
      vulogFleetService,
      stationService,
      vulogTelemetryService,
      configurationService,
      featureFlagService,
    );
  }

  protected cronName =
    'REPORT_METRIC_CAR_NUMBER_OF_LOCKED_AND_IMMOBILIZED_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.reportMetricCarNumberOfLockedAndImmobilized.enabled;
  protected cronInterval: string = cronstrue
    .toString(
      config.scheduleConfig.reportMetricCarNumberOfLockedAndImmobilized
        .expression,
    )
    .toLowerCase();

  private prepareMetricDataPoint(
    realTimeCarsWithStations: RealtimeCar[],
    staticCarPlateMap: Map<string, StaticCar>,
    service: NewRelicMetricService,
    carModel: NewRelicMetricCarModel,
  ): CarMetricDataPoint[] {
    const metricDataPoints: CarMetricDataPoint[] = [];

    this.sendMetricFullDimensions({
      dimensionMetadata: {
        service,
        carModel,
        stationFacetSupported: true,
      },
      realTimeCarsManipulationMetadata: {
        realTimeCarsWithStations,
        highOrderFunctionName: 'filter',
        predicateFunc: (value, comparisonValues) =>
          (staticCarPlateMap.get(value.plate?.value)?.serviceType ||
            undefined) === (comparisonValues.serviceType || undefined) &&
          value?.model?.value === (comparisonValues.carModel || undefined),
      },
      sendMetricFunc: (values) => {
        metricDataPoints.push({
          count: values.count,
          carModel: values.carModel,
          serviceType: values.serviceType || NewRelicMetricService.OutOfService,
          stationName: values.stationName,
        });
      },
    });

    return metricDataPoints;
  }

  @Cron(
    config.scheduleConfig.reportMetricCarNumberOfLockedAndImmobilized
      .expression,
    {
      disabled:
        !config.scheduleConfig.reportMetricCarNumberOfLockedAndImmobilized
          .enabled,
    },
  )
  async reportMetricCarNumberOfLockedAndImmobilized() {
    try {
      const staticCarPlateMap: Map<string, StaticCar> =
        await this.getStaticCarPlateMap();

      const realTimeCarsWithStations: RealtimeCar[] =
        await this.getRealtimeCarsWithStations({
          includeMissingStation: true,
          onlyAvailableCar: false,
          filterFunction: (value) => value.isDoorLocked && value.immobilizerOn,
        });

      const consolidatedMetricDataPoints: CarMetricDataPoint[] = [
        ...this.prepareMetricDataPoint(
          realTimeCarsWithStations,
          staticCarPlateMap,
          NewRelicMetricService.CustomerTrip,
          NewRelicMetricCarModel.Bluecar,
        ),
        ...this.prepareMetricDataPoint(
          realTimeCarsWithStations,
          staticCarPlateMap,
          NewRelicMetricService.CustomerTrip,
          NewRelicMetricCarModel.CorsaE,
        ),
        ...this.prepareMetricDataPoint(
          realTimeCarsWithStations,
          staticCarPlateMap,
          NewRelicMetricService.CustomerTrip,
          NewRelicMetricCarModel.NoCarModel,
        ),
        ...this.prepareMetricDataPoint(
          realTimeCarsWithStations,
          staticCarPlateMap,
          NewRelicMetricService.ServiceTrip,
          NewRelicMetricCarModel.Bluecar,
        ),
        ...this.prepareMetricDataPoint(
          realTimeCarsWithStations,
          staticCarPlateMap,
          NewRelicMetricService.ServiceTrip,
          NewRelicMetricCarModel.CorsaE,
        ),
        ...this.prepareMetricDataPoint(
          realTimeCarsWithStations,
          staticCarPlateMap,
          NewRelicMetricService.ServiceTrip,
          NewRelicMetricCarModel.NoCarModel,
        ),
        ...this.prepareMetricDataPoint(
          realTimeCarsWithStations,
          staticCarPlateMap,
          NewRelicMetricService.OutOfService,
          NewRelicMetricCarModel.Bluecar,
        ),
        ...this.prepareMetricDataPoint(
          realTimeCarsWithStations,
          staticCarPlateMap,
          NewRelicMetricService.OutOfService,
          NewRelicMetricCarModel.CorsaE,
        ),
        ...this.prepareMetricDataPoint(
          realTimeCarsWithStations,
          staticCarPlateMap,
          NewRelicMetricService.OutOfService,
          NewRelicMetricCarModel.NoCarModel,
        ),
      ];

      return this.carMetricService.bulkSetNumberOfLockedAndImmobilized(
        consolidatedMetricDataPoints,
      );
    } catch (err) {
      this.error(
        `Error when reporting metric car.numberOfLockedAndImmobilized: ${err.message}`,
        {
          origin: this.cronName,
          cause: err,
          stack: err.stack,
        },
      );
    }
  }
}
