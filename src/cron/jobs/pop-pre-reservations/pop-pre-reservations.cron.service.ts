import { PopPreReservationsJobService } from '@bluesg-2/queue-pop';
import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import { config } from 'src/config';
import { BaseCronService } from 'src/cron/bases/base-cron.service';

@Injectable()
export class PopPreReservationsCronService extends BaseCronService {
  constructor(
    private readonly popPreReservationsJobService: PopPreReservationsJobService,
  ) {
    super();
  }

  protected cronName = 'POP_PRE_RESERVATIONS_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.popPreReservation.enabled;
  protected cronInterval: string = cronstrue
    .toString(config.scheduleConfig.popPreReservation.expression)
    .toLowerCase();

  @Cron(config.scheduleConfig.popPreReservation.expression, {
    disabled: !config.scheduleConfig.popPreReservation.enabled,
  })
  async pop() {
    try {
      this.log('Popping pre-reservations...');

      await this.popPreReservationsJobService.pop();
    } catch (err) {
      this.error(`Error when pop pre-reservations: ${err.message}`, {
        origin: 'queue-pop-cron',
        cause: err,
        stack: err.stack,
      });
    }
  }
}
