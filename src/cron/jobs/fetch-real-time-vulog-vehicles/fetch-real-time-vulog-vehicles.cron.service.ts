import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import { config } from 'src/config';
import { BaseCronService } from 'src/cron/bases/base-cron.service';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';

@Injectable()
export class FetchRealTimeVulogVehiclesCronService extends BaseCronService {
  constructor(private readonly vulogCarService: VulogCarService) {
    super();
  }

  protected cronName = 'FETCH_REAL_TIME_VULOG_VEHICLES_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.fetchRealtimeVulogVehicles.enabled;
  protected cronInterval: string = cronstrue
    .toString(config.scheduleConfig.fetchRealtimeVulogVehicles.expression)
    .toLowerCase();

  @Cron(config.scheduleConfig.fetchRealtimeVulogVehicles.expression, {
    disabled: !config.scheduleConfig.fetchRealtimeVulogVehicles.enabled,
  })
  async fetchRealtimeVulogVehicles() {
    try {
      this.log('Fetching real-time Vulog vehicles...');

      await this.vulogCarService.fetchRealtimeVehicles(false);
    } catch (err) {
      this.error(
        `Error when fetching real-time Vulog vehicles: ${err.message}`,
        {
          origin: this.cronName,
          cause: err,
          stack: err.stack,
        },
      );
    }
  }
}
