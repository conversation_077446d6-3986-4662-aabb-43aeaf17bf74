import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import { config } from 'src/config';
import { BaseCronService } from 'src/cron/bases/base-cron.service';
import { StationService } from 'src/logic/station/station.service';

@Injectable()
export class FetchAllStationsCronService extends BaseCronService {
  constructor(private readonly stationService: StationService) {
    super();
  }

  protected cronName = 'FETCH_ALL_STATIONS_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.fetchAllStations.enabled;
  protected cronInterval: string = cronstrue
    .toString(config.scheduleConfig.fetchAllStations.expression)
    .toLowerCase();

  @Cron(config.scheduleConfig.fetchAllStations.expression, {
    disabled: !config.scheduleConfig.fetchAllStations.enabled,
  })
  async fetchAllStations() {
    try {
      this.log('Fetching stations...');

      await this.stationService.getAllStations();
    } catch (err) {
      this.error(`Error when fetching all stations: ${err.message}`, {
        origin: this.cronName,
        cause: err,
        stack: err.stack,
      });
    }
  }
}
