import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import {
  VULOG_ACCESS_TOKEN_KEY,
  VULOG_REFRESH_TOKEN_KEY,
} from 'src/common/constants/cache-keys';
import { VulogAuthError } from 'src/common/errors/vulog-auth-error';
import { config } from 'src/config';
import { BaseCronService } from 'src/cron/bases/base-cron.service';
import { CacheService } from 'src/logic/cache/cache.service';
import {
  VulogAuthResponse,
  VulogAuthService,
} from 'src/logic/vulog/vulog-auth.service';

@Injectable()
export class RefreshSystemUsersTokensCronService extends BaseCronService {
  constructor(
    private readonly vulogAuthService: VulogAuthService,
    private readonly cacheManager: CacheService,
  ) {
    super();
  }

  protected cronName = 'REFRESH_SYSTEM_USERS_TOKENS_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.refreshSystemUsersTokens.enabled;
  protected cronInterval: string = cronstrue
    .toString(config.scheduleConfig.refreshSystemUsersTokens.expression)
    .toLowerCase();

  async onModuleInit(): Promise<void> {
    super.onModuleInit();

    // Issue credentials for system users for the first time to avoid downtime when there is no access token available yet because job not run
    this.log("Issue system users' tokens on start");
    await this.refreshSystemUsersTokens();
  }

  private async doAuthentication(systemUser: {
    username: string;
    password: string;
  }): Promise<void> {
    const vulogAccessTokenKey = this.vulogAuthService.generateTokenKey(
      VULOG_ACCESS_TOKEN_KEY,
      '',
      { username: systemUser.username },
    );
    const vulogRefreshTokenKey = this.vulogAuthService.generateTokenKey(
      VULOG_REFRESH_TOKEN_KEY,
      '',
      { username: systemUser.username },
    );

    const cachedAccessToken = await this.cacheManager.get<string>(
      vulogAccessTokenKey,
    );
    const cachedRefreshToken = await this.cacheManager.get<string>(
      vulogRefreshTokenKey,
    );

    let response: VulogAuthResponse;

    if (cachedAccessToken) {
      this.log(
        `Access token for username ${systemUser.username} is still valid`,
        {
          vulogAccessTokenKey,
        },
      );

      return;
    }

    if (!cachedAccessToken) {
      this.warn(
        `Access token for username ${systemUser.username} is either not found or expired`,
        {
          vulogAccessTokenKey,
        },
      );

      if (cachedRefreshToken) {
        this.log(`Doing refreshing token for username ${systemUser.username}`, {
          vulogAccessTokenKey,
          vulogRefreshTokenKey,
        });

        try {
          response = await this.vulogAuthService.authWithRefreshToken(
            cachedRefreshToken,
            true,
          );
        } catch (error) {
          // Refresh token invalid, catch error and proceed with getting new tokens
          this.warn(
            `Refresh token invalid or expired, reobtaining for username ${systemUser.username}...`,
            error,
          );
        }
      }
    }

    if (!response) {
      this.log(
        `Login to get new credentials for username ${systemUser.username}`,
      );

      // Get new access token and refresh token with login credentials
      try {
        response = await this.vulogAuthService.authWithPassword(
          systemUser.username,
          systemUser.password,
          true,
        );
      } catch (error) {
        throw new VulogAuthError(error);
      }
    }

    const { access_token, expires_in, refresh_token, refresh_expires_in } =
      response;

    await this.cacheManager.set(vulogAccessTokenKey, access_token, {
      ttl: expires_in,
    });
    await this.cacheManager.set(vulogRefreshTokenKey, refresh_token, {
      ttl: refresh_expires_in,
    });

    this.log(
      `Doing authentication for username: ${systemUser.username} successfully`,
      {
        cacheAccessTokenKey: vulogAccessTokenKey,
        cacheAccessTokenTTL: expires_in,
        cacheRefreshTokenKey: vulogRefreshTokenKey,
        refreshAccessTokenTTL: refresh_expires_in,
      },
    );
  }

  @Cron(config.scheduleConfig.refreshSystemUsersTokens.expression, {
    disabled: !config.scheduleConfig.refreshSystemUsersTokens.enabled,
  })
  async refreshSystemUsersTokens() {
    try {
      this.log("Starting refreshing system users' tokens...");

      const systemUsers: {
        username: string;
        password: string;
      }[] = this.vulogAuthService.getSystemUsers();

      await Promise.all(
        systemUsers.map(async (sysUsr) =>
          this.doAuthentication({
            username: sysUsr.username,
            password: sysUsr.password,
          }),
        ),
      );
    } catch (err) {
      this.error(`Error when refreshing system users' tokens: ${err.message}`, {
        origin: this.cronName,
        cause: err,
        stack: err.stack,
      });
    }
  }
}
