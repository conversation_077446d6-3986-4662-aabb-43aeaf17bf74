import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import { config } from 'src/config';
import { BaseCronService } from 'src/cron/bases/base-cron.service';
import { ReservationService } from 'src/logic/reservation.service';

@Injectable()
export class ExpireReservationsCronService extends BaseCronService {
  constructor(private readonly reservationService: ReservationService) {
    super();
  }

  protected cronName = 'EXPIRE_RESERVATIONS_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.expireReservations.enabled;
  protected cronInterval: string = cronstrue
    .toString(config.scheduleConfig.expireReservations.expression)
    .toLowerCase();

  @Cron(config.scheduleConfig.expireReservations.expression, {
    disabled: !config.scheduleConfig.expireReservations.enabled,
  })
  async expireReservations() {
    try {
      this.log('🏁 Expiring reservations...');

      await this.reservationService.expireReservation();

      this.log('🚩 Expiring reservations...');
    } catch (err) {
      this.error(`Error when expiring reservations: ${err.message}`, {
        origin: this.cronName,
        cause: err,
        stack: err.stack,
      });
    }
  }
}
