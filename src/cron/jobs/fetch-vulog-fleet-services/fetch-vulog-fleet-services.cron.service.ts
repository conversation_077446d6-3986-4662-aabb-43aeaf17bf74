import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import { config } from 'src/config';
import { BaseCronService } from 'src/cron/bases/base-cron.service';
import { VulogFleetService } from 'src/logic/vulog/vulog-fleet-service.service';

@Injectable()
export class FetchVulogFleetServicesCronService extends BaseCronService {
  constructor(private readonly vulogFleetService: VulogFleetService) {
    super();
  }

  protected cronName = 'FETCH_VULOG_FLEET_SERVICES_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.fetchVulogFleetServices.enabled;
  protected cronInterval: string = cronstrue
    .toString(config.scheduleConfig.fetchVulogFleetServices.expression)
    .toLowerCase();

  @Cron(config.scheduleConfig.fetchVulogFleetServices.expression, {
    disabled: !config.scheduleConfig.fetchVulogFleetServices.enabled,
  })
  async fetchVulogFleetServices() {
    try {
      this.log('Fetching Vulog services...');

      await this.vulogFleetService.getFleetServices(false);
    } catch (err) {
      this.error(`Error when fetching Vulog services: ${err.message}`, {
        origin: this.cronName,
        cause: err,
        stack: err.stack,
      });
    }
  }
}
