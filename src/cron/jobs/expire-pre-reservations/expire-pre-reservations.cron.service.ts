import { ExpirePreReservationsJobService } from '@bluesg-2/queue-pop';
import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import { DateTime } from 'luxon';
import { config } from 'src/config';
import { BaseCronService } from 'src/cron/bases/base-cron.service';

@Injectable()
export class ExpirePreReservationsCronService extends BaseCronService {
  constructor(
    private readonly expirePreReservationJobService: ExpirePreReservationsJobService,
  ) {
    super();
  }

  protected cronName = 'EXPIRE_PRE_RESERVATIONS_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.expirePreReservations.enabled;
  protected cronInterval: string = cronstrue
    .toString(config.scheduleConfig.expirePreReservations.expression)
    .toLowerCase();

  @Cron(config.scheduleConfig.expirePreReservations.expression, {
    disabled: !config.scheduleConfig.expirePreReservations.enabled,
  })
  async expirePreReservations() {
    try {
      this.log('Expiring pre-reservations...');

      await this.expirePreReservationJobService.expire({
        pageSize: 200,
        expiredAtTo: DateTime.now(),
      });
    } catch (err) {
      this.error(`Error when expiring pre-reservations: ${err.message}`, {
        origin: 'queue-pop-cron',
        cause: err,
        stack: err.stack,
      });
    }
  }
}
