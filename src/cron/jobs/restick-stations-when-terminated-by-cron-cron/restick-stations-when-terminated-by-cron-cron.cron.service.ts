import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import { PaginationSortingOrder } from 'src/common/constants/pagination';
import { ReservationId } from 'src/common/tiny-types';
import { config } from 'src/config';
import { BaseCronService } from 'src/cron/bases/base-cron.service';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { RealtimeCar } from 'src/model/realtime.car';
import { ReservationRepository } from 'src/model/repositories/reservation.repository';
import { Reservation } from 'src/model/reservation.domain';

@Injectable()
export class RestickStationsWhenTerminatedByCronCronCronService
  extends BaseCronService
  implements OnModuleInit
{
  constructor(
    private readonly reservationRepository: ReservationRepository,
    private readonly vulogCarService: VulogCarService,
  ) {
    super();
  }

  protected cronName = 'RESTICK_STATIONS_WHEN_TERMINATED_BY_CRON_CRON_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.restickStationsWhenTerminatedByCronCron.enabled;
  protected cronInterval: string = cronstrue
    .toString(
      config.scheduleConfig.restickStationsWhenTerminatedByCronCron.expression,
    )
    .toLowerCase();

  @Cron(
    config.scheduleConfig.restickStationsWhenTerminatedByCronCron.expression,
    {
      disabled:
        !config.scheduleConfig.restickStationsWhenTerminatedByCronCron.enabled,
    },
  )
  async restickStationsWhenTerminatedByCronCron() {
    try {
      this.log(
        'Re-stick stations with car when rentals was terminated by CRON CRON...',
      );

      const { result, pagination } =
        await this.reservationRepository.getCronCronTerminatedReservations({
          page: 1,
          pageSize: 100,
          sortKey: 'startedAt',
          sortType: PaginationSortingOrder.DESCENDING,
        });

      if (pagination.total === 0) {
        return;
      }

      const reservationDetails: {
        reservation: Reservation;
        realtimeCar: RealtimeCar;
      }[] = (
        await Promise.all(
          result.map(async (rsrv) => {
            const rltCar: RealtimeCar =
              await this.vulogCarService.getSpecificCarInRealtime(rsrv.carId);

            const isCarNotInUse =
              !rltCar.isEngineOn && rltCar.immobilizerOn && rltCar.isPluggedIn;

            if (!isCarNotInUse) {
              Logger.warn(
                `The car [Vulog Car ID: ${rsrv.carId.value}] is In Use. Omit from sticking actions`,
                {
                  rltCar,
                },
              );
            }

            return isCarNotInUse
              ? {
                  reservation: rsrv,
                  realtimeCar: rltCar,
                }
              : null;
          }),
        ).catch((err) => {
          throw err;
        })
      ).filter(Boolean);

      await Promise.all(
        reservationDetails.map(async ({ reservation, realtimeCar }) => {
          const toBeStickedZoneId = realtimeCar.zoneIds?.length
            ? realtimeCar.zoneIds[0]
            : null;

          let softDeletedMessage = '';

          if (realtimeCar.allowedStickyZone) {
            Logger.log(
              'The car is sticky with station. No further action needed',
              { rltCar: realtimeCar, reservation },
            );

            softDeletedMessage =
              'CRS-CRON: the car is sticky. No need to make station sticky with car';
          } else if (!realtimeCar.allowedStickyZone && toBeStickedZoneId) {
            await this.vulogCarService.toggleStickyStationWithCar(
              realtimeCar.id,
              toBeStickedZoneId,
              true,
            );

            Logger.log(
              'The car is sticky with station after being terminated by CRON CRON',
              { rltCar: realtimeCar, reservation },
            );

            softDeletedMessage = 'CRS-CRON: make station sticky with car';
          } else {
            Logger.warn('The car is not in zone. No further action needed.', {
              rltCar: realtimeCar,
              reservation,
            });

            softDeletedMessage =
              'CRS-CRON: trying to stick station with car, but the car is not in zone. Require manual intervention from CCO agents';
          }

          reservation.setDeletedBy(softDeletedMessage);

          await this.reservationRepository.updateReservation(reservation);

          await this.reservationRepository.softDelete(
            new ReservationId(reservation.getId),
          );
        }),
      ).catch((err) => {
        throw err;
      });
    } catch (err) {
      this.error(
        `Error when re-sticking stations with car when rentals was terminated by CRON CRON: ${err.message}`,
        {
          origin: this.cronName,
          cause: err,
          stack: err.stack,
        },
      );
    }
  }
}
