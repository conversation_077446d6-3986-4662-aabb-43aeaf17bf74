import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import * as cronstrue from 'cronstrue';
import { config } from 'src/config';
import { BaseCronService } from 'src/cron/bases/base-cron.service';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';

@Injectable()
export class FetchStaticVulogVehiclesCronService extends BaseCronService {
  constructor(private readonly vulogCarService: VulogCarService) {
    super();
  }

  protected cronName = 'FETCH_STATIC_VULOG_VEHICLES_CRON';
  protected cronEnabled: boolean =
    config.scheduleConfig.fetchStaticVulogVehicles.enabled;
  protected cronInterval: string = cronstrue
    .toString(config.scheduleConfig.fetchStaticVulogVehicles.expression)
    .toLowerCase();

  @Cron(config.scheduleConfig.fetchStaticVulogVehicles.expression, {
    disabled: !config.scheduleConfig.fetchStaticVulogVehicles.enabled,
  })
  async fetchStaticVulogVehicles() {
    try {
      this.log('Fetching static Vulog vehicles...');

      await this.vulogCarService.getAllStaticCars();
    } catch (err) {
      this.error(`Error when fetching static Vulog vehicles: ${err.message}`, {
        origin: this.cronName,
        cause: err,
        stack: err.stack,
      });
    }
  }
}
