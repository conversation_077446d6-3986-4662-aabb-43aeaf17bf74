import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { ExpireReservationsCronModule } from './jobs/expire-reservations/expire-reservations.cron.module';
import { FetchAllStationsCronModule } from './jobs/fetch-all-stations/fetch-all-stations.cron.module';
import { FetchRealTimeVulogVehiclesCronModule } from './jobs/fetch-real-time-vulog-vehicles/fetch-real-time-vulog-vehicles.cron.module';
import { FetchStaticVulogVehiclesCronModule } from './jobs/fetch-static-vulog-vehicles/fetch-static-vulog-vehicles.cron.module';
import { FetchVulogFleetServicesCronModule } from './jobs/fetch-vulog-fleet-services/fetch-vulog-fleet-services..cron.module';
import { GetCarAvailabilityCronModule } from './jobs/get-car-availability/get-car-availability.cron.module';
import { RefreshSystemUsersTokensCronModule } from './jobs/refresh-system-users-tokens/refresh-system-users-tokens.cron.module';
import { ReportMetricReservationNumberOfOngoingCronModule } from './jobs/report-metric-reservation-number-of-ongoing/report-metric-reservation-number-of-ongoing.cron.module';
import { ReportMetricCarNumberOfAvailableCronModule } from './jobs/report-metric/car/report-metric-car-number-of-available/report-metric-car-number-of-available.cron.module';
import { ReportMetricCarNumberOfChargingCronModule } from './jobs/report-metric/car/report-metric-car-number-of-charging/report-metric-car-number-of-charging.cron.module';
import { ReportMetricCarNumberOfInZoneCronModule } from './jobs/report-metric/car/report-metric-car-number-of-in-zone/report-metric-car-number-of-in-zone.cron.module';
import { ReportMetricCarNumberOfLockedAndImmobilizedCronModule } from './jobs/report-metric/car/report-metric-car-number-of-locked-and-immobilized/report-metric-car-number-of-locked-and-immobilized.cron.module';
import { ReportMetricCarNumberOfCustomerFleetServiceCronModule } from './jobs/report-metric/car/report-metric-car-number-of-out-of-customer-fleet-service/report-metric-car-number-of-out-of-customer-fleet-service.cron.module';
import { ReportMetricCarNumberOfOutOfServiceCronModule } from './jobs/report-metric/car/report-metric-car-number-of-out-of-service/report-metric-car-number-of-out-of-service.cron.module';
import { ReportMetricCarNumberOfOutOfZoneCronModule } from './jobs/report-metric/car/report-metric-car-number-of-out-of-zone/report-metric-car-number-of-out-of-zone.cron.module';
import { ReportMetricCarNumberOfPluggedCronModule } from './jobs/report-metric/car/report-metric-car-number-of-plugged/report-metric-car-number-of-plugged.cron.module';
import { ReportMetricCarNumberOfStickyCronModule } from './jobs/report-metric/car/report-metric-car-number-of-sticky/report-metric-car-number-of-sticky.cron.module';
import { ReportMetricCarNumberOfUnlockedAndMobilizedCronModule } from './jobs/report-metric/car/report-metric-car-number-of-unlocked-and-mobilized/report-metric-car-number-of-unlocked-and-mobilized.cron.module';
import { ReportMetricCarNumberOfUnpluggedCronModule } from './jobs/report-metric/car/report-metric-car-number-of-unplugged/report-metric-car-number-of-unplugged.cron.module';
import { ReportMetricCarNumberOfUnstickyCronModule } from './jobs/report-metric/car/report-metric-car-number-of-unsticky/report-metric-car-number-of-unsticky.cron.module';
import { ReportMetricRentalNumberOfBlueSGStartedButVulogBookingCronModule } from './jobs/report-metric/rental/report-metric-rental-number-of-bluesg-started-but-vulog-booking/report-metric-rental-number-of-bluesg-started-but-vulog-booking.cron.module';
import { ReportMetricRentalNumberOfEndingRentalConditionsSatisfiedCronModule } from './jobs/report-metric/rental/report-metric-rental-number-of-ending-rental-conditions-satisfied/report-metric-rental-number-of-ending-rental-conditions-satisfied.cron.module';
import { ReportMetricRentalNumberOfOngoingCronModule } from './jobs/report-metric/rental/report-metric-rental-number-of-ongoing/report-metric-rental-number-of-ongoing.cron.module';
import { ReportMetricRentalNumberOfWaitingForInvoicingCronModule } from './jobs/report-metric/rental/report-metric-rental-number-of-waiting-for-invoicing/report-metric-rental-number-of-waiting-for-invocing.cron.module';
import { ReportMetricReservationNumberOfCooldownCronModule } from './jobs/report-metric/reservation/report-metric-reservation-number-of-cooldown/report-metric-reservation-number-of-cooldown.cron.module';
import { RestickStationsWhenTerminatedByCronCronCronModule } from './jobs/restick-stations-when-terminated-by-cron-cron/restick-stations-when-terminated-by-cron-cron.cron.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    // ExpirePreReservationsCronModule,
    // PopPreReservationsCronModule,

    ExpireReservationsCronModule,
    FetchAllStationsCronModule,
    FetchRealTimeVulogVehiclesCronModule,
    FetchVulogFleetServicesCronModule,
    GetCarAvailabilityCronModule,
    FetchStaticVulogVehiclesCronModule,
    RefreshSystemUsersTokensCronModule,
    RestickStationsWhenTerminatedByCronCronCronModule,
    ReportMetricReservationNumberOfOngoingCronModule,
    ReportMetricReservationNumberOfCooldownCronModule,
    ReportMetricRentalNumberOfBlueSGStartedButVulogBookingCronModule,
    ReportMetricRentalNumberOfOngoingCronModule,
    ReportMetricRentalNumberOfEndingRentalConditionsSatisfiedCronModule,
    ReportMetricRentalNumberOfWaitingForInvoicingCronModule,
    ReportMetricCarNumberOfPluggedCronModule,
    ReportMetricCarNumberOfUnpluggedCronModule,
    ReportMetricCarNumberOfChargingCronModule,
    ReportMetricCarNumberOfLockedAndImmobilizedCronModule,
    ReportMetricCarNumberOfUnlockedAndMobilizedCronModule,
    ReportMetricCarNumberOfOutOfZoneCronModule,
    ReportMetricCarNumberOfInZoneCronModule,
    ReportMetricCarNumberOfCustomerFleetServiceCronModule,
    ReportMetricCarNumberOfOutOfServiceCronModule,
    ReportMetricCarNumberOfAvailableCronModule,
    ReportMetricCarNumberOfStickyCronModule,
    ReportMetricCarNumberOfUnstickyCronModule,
  ],
})
export class CronModule {}
