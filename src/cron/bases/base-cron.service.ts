import { Logger, OnModuleInit } from '@nestjs/common';

export abstract class BaseCronService implements OnModuleInit {
  protected cronName: string;
  protected cronEnabled: boolean;
  protected cronInterval: string;

  onModuleInit() {
    Logger.log(
      `Cron [${this.cronName}] is ${
        this.cronEnabled ? '✅ ENABLED' : '❌ DISABLED'
      }, supposed to run ${this.cronInterval}`,
    );
  }

  private generateMessage(message): string {
    return `Cron [${this.cronName}]: ${message}`;
  }

  log(message: string, metadata?: unknown) {
    Logger.log(this.generateMessage(message), metadata);
  }

  debug(message: string, metadata?: unknown) {
    Logger.debug(this.generateMessage(message), metadata);
  }

  warn(message: string, metadata?: unknown) {
    Logger.warn(this.generateMessage(message), metadata);
  }

  error(message: string, metadata?: unknown) {
    Logger.error(this.generateMessage(message), metadata);
  }
}
