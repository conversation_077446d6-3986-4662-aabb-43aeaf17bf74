import { AuthGuardConfigOptions, AuthModule } from '@bluesg-2/bo-auth-guard';
import { CustomerAuthModule } from '@bluesg-2/customer-authentication';
import { MonitoringModule } from '@bluesg-2/monitoring';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { TypeOrmHealthIndicator } from '@nestjs/terminus';
import { ClsModule } from 'nestjs-cls';
import { getEnvFilePath } from 'src/common/utils/get-env-file-path.util';

import { EventBusModule } from '~shared/event-bus/event-bus.module';
import { FeatureFlagModule } from '~shared/feature-flag/feature-flag.module';
import { HttpLoggerModule } from '~shared/http-logger/http-logger.module';
import { LocalEventEmitterModule } from '~shared/local-event-emitter/local-event-emitter.module';
import { NewRelicModule } from '~shared/newrelic/newrelic.module';
import { MessageQueueName } from '~shared/queue-client/constants';
import { QueueClientModule } from '~shared/queue-client/queue-client.module';
import { CacheConfigModule } from './cache-config.module';
import { AllExceptionsFilter } from './common/filters/exception.filter';
import { config } from './config';
import * as AppNestConfig from './config/app.nestjs.config';
import { WebApi } from './controllers/web-api.module';
import { DatabaseModule } from './database/database.module';
import { EventListenersLocalModule } from './event/event-listeners.local.module';
import { EventExternalConsumerModule } from './event/external-consumer/event-external-consumer.module';
import { ApiResponseInterceptor } from './interceptors/api-response.interceptor';
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import { CacheService } from './logic/cache/cache.service';
import { LogicModule } from './logic/logic.module';
import { SentryModule, SENTRY_INTERCEPTOR } from './sentry.module';

@Module({
  imports: [
    FeatureFlagModule,
    // QueuePopModule.register({
    //   global: true,
    //   stationModule: {
    //     imports: [StationExternalModule],
    //     providers: [],
    //     requiredProvider: StationExternalService,
    //   },
    //   reservationModule: {
    //     imports: [LogicModule],
    //     providers: [],
    //     requiredProvider: ReservationServiceImpl,
    //     excludedFromRebuiltWithTransact: [
    //       HttpService,
    //       CorrelationIdService,
    //       ClsService,
    //       CacheService,
    //       HttpAdapterService,
    //       HttpLogger,
    //     ],
    //   },
    //   configurationModule: {
    //     imports: [LogicModule],
    //     providers: [],
    //     requiredProvider: ConfigurationServiceImpl,
    //   },
    // }),
    ClsModule.forRoot({
      global: true,
    }),
    MonitoringModule.registerAsync({
      imports: [LogicModule],
      useFactory: async (db: TypeOrmHealthIndicator, cache: CacheService) => ({
        health: {
          dependencies: [
            // TODO: check health of Vulog gateway
            () => db.pingCheck('database'),
            () => cache.checkIfHealthy(),
          ],
        },
      }),
      inject: [TypeOrmHealthIndicator, CacheService],
    }),
    SentryModule,
    WebApi,
    CacheConfigModule,
    DatabaseModule,
    AuthModule.registerAsync({
      useFactory: async (): Promise<AuthGuardConfigOptions> => ({
        secret: config.jwtConfig.adminSecret,
        auth0Server: config.jwtConfig.auth0Server,
        authenticationConfigKey:
          config.featureFlagConfig.configCatKey.authenticationConfig,
      }),
    }),
    CustomerAuthModule.register(),
    ConfigModule.forRoot({
      isGlobal: true,
      load: [...Object.values(AppNestConfig)],
      envFilePath: getEnvFilePath(),
    }),
    HttpLoggerModule,
    EventBusModule,
    LocalEventEmitterModule,
    EventExternalConsumerModule,
    QueueClientModule.registerAsync({
      queueName: MessageQueueName.EndRentalInAiMA,
      sqsConfig: {
        region: config.sqs.EndRentalInAiMA.region,
        queueUrl: config.sqs.EndRentalInAiMA.url,
      },
    }),
    EventListenersLocalModule,
    NewRelicModule,
  ],
  controllers: [],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ApiResponseInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useExisting: SENTRY_INTERCEPTOR,
    },
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
  ],
})
export class AppModule {}
