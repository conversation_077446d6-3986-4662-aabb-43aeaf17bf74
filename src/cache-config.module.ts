import { CacheModule } from '@nestjs/cache-manager';
import { CacheModuleOptions, Module } from '@nestjs/common';
import * as redisStore from 'cache-manager-ioredis';
import { config } from './config';

const cacheOptions: CacheModuleOptions = {
  isGlobal: true,
};

cacheOptions.store = redisStore;

cacheOptions.clusterConfig = {
  nodes: [
    {
      host: config.redisConfig.host,
      port: config.redisConfig.port,
    },
  ],
  options: {
    maxRedirections: 16,
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    dnsLookup: (address, callback) => callback(null, address),
    redisOptions: {
      showFriendlyErrorStack: true,
      tls: {
        servername: config.redisConfig.host,
        // https://www.reddit.com/r/aws/comments/s8ma5m/connecting_to_memorydb_from_ec2/
        checkServerIdentity: (_servername, _cert) =>
          // skip certificate hostname validation
          undefined,
      },
    },
    slotsRefreshTimeout: 3000,
  },
};

// disable redis cluster on local run and test envs
if (['local', 'test', 'e2e', undefined].includes(process.env.NODE_ENV)) {
  delete cacheOptions.clusterConfig;
  cacheOptions.host = config.redisConfig.host;
  cacheOptions.port = config.redisConfig.port;
}
@Module({
  imports: [CacheModule.register(cacheOptions)],
})
export class CacheConfigModule {}
