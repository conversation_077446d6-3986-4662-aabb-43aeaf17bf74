import { CorrelationIdService } from '@bluesg-2/monitoring';
import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { HealthCheckResult } from '@nestjs/terminus';
import { map, Observable } from 'rxjs';
import { ApiResponse } from 'src/api/dtos/api-response.dto';
import { BasePaginationResponseDto } from 'src/common/api/base.pagination.response.dto';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { PageResult } from 'src/common/api/page-result';
import { QueryPagination } from 'src/common/api/pagination';

@Injectable()
export class ApiResponseInterceptor implements NestInterceptor {
  constructor(private readonly correlationIdService: CorrelationIdService) {}
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ):
    | Observable<ApiResponse | HealthCheckResult>
    | Promise<Observable<ApiResponse | HealthCheckResult>> {
    const request = context.switchToHttp().getRequest<Request>();
    const correlationId = this.correlationIdService.getCorrelationId();

    return next.handle().pipe(
      map((data: unknown) => {
        // exclude for api health check
        if (request.url === '/health') return data as HealthCheckResult;

        //NOTE: if has pagination
        let pagination = (<BaseResponseDto>data)?.pagination;

        if (pagination instanceof QueryPagination) {
          const pageResult = data as PageResult<unknown>;

          pagination = BasePaginationResponseDto.from(
            pagination,
            pageResult.total,
            pageResult.totalPages,
          );
        }

        if (pagination) {
          return new ApiResponse(
            true,
            (<BaseResponseDto>data).result,
            correlationId,
            null,
            pagination,
          );
        }

        return new ApiResponse(true, data, correlationId);
      }),
    );
  }
}
