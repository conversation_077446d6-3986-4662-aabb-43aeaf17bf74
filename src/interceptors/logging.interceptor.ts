import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Response } from 'express';
import { Observable, tap } from 'rxjs';
import { ApiResponse } from 'src/api/dtos/api-response.dto';
import { config } from 'src/config';
import { HttpLogger } from 'src/shared/http-logger/http-logger';

const muteUrls = [
  '/api/v1/availability',
  '/api/v1/reservation?',
  '/api/v1/rental/history',
  '/api/v1/admin/reservations?',
  '/api/v1/admin/cars',
  '/api/v1/stations/available-cars',
];
@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(private readonly httpLogger: HttpLogger) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<unknown>> {
    const request = context.switchToHttp().getRequest<Request>();

    const apiURL = request.url;
    const apiMethod = request.method;

    this.httpLogger.logRequest({
      url: apiURL,
      method: apiMethod,
      body: request?.body as unknown,
    });

    return next.handle().pipe(
      tap((response: ApiResponse) => {
        const res = context.switchToHttp().getResponse<Response>();
        if (config.logConfig.logHttpResponse) {
          this.httpLogger.logResponse({
            url: apiURL,
            method: apiMethod,
            body: muteUrls.find((url) => apiURL.includes(url))
              ? '<TOO LONG TO DISPLAY>'
              : response,
            statusCode: res.statusCode,
          });
        }
      }),
    );
  }
}
