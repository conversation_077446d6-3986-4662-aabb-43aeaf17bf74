import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { localEventPattern } from 'src/common/constants/event';
import { ReservationError } from 'src/common/errors/reservation-error';
import { VulogCarId, VulogJourneyOrTripId, VulogUserId } from 'src/common/tiny-types';
import { LocalVgChargingCablePluggedEventPayload } from 'src/event/local/vulog/vg/local.vg-charging-cable-plugged';
import { LocalVgChargingCableUnpluggedEventPayload } from 'src/event/local/vulog/vg/local.vg-charging-cable-unplugged';
import { LocalVgVehicleSessionCancelEventPayload } from 'src/event/local/vulog/vg/local.vg-vehicle-session-cancel.event';
import { LocalVgVehicleSessionEndEventPayload } from 'src/event/local/vulog/vg/local.vg-vehicle-session-end.event';
import { LocalVgVehicleSessionStartByAdminEventPayload } from 'src/event/local/vulog/vg/local.vg-vehicle-session-start-by-admin.event';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { ReservationService } from 'src/logic/reservation.service';
import { StationService } from 'src/logic/station/station.service';
import { CancelReservationService } from 'src/logic/vulog/cancel-reservation.service';
import { CarPlugService } from 'src/logic/vulog/car-plug.service';
import { EndRentalService } from 'src/logic/vulog/end-rental.service';
import { StartReservationService } from 'src/logic/vulog/start-reservation.service';
import { RealtimeCar } from 'src/model/realtime.car';
import { Station } from 'src/model/station';
import { LOCAL_EVENT_EMITTER_TOKEN } from '~shared/local-event-emitter/constants';
import { LocalEventEmitterService } from '~shared/local-event-emitter/local-event-emitter.service';

@Injectable()
export class LocalVgEventListener {
  constructor(
    @Inject(LOCAL_EVENT_EMITTER_TOKEN)
    private readonly localEventEmitterService: LocalEventEmitterService,
    private readonly reservationService: ReservationService,

    private readonly endRentalService: EndRentalService,

    private readonly vulogCarService: VulogCarService,
    private readonly cancelReservationService: CancelReservationService,
    private readonly stationService: StationService,
    private readonly startReservationService: StartReservationService,

    private readonly carPlugService: CarPlugService,
  ) {}

  @OnEvent(localEventPattern.vg.vehicleSessionEnd, {
    async: true,
  })
  async onVehicleSessionEnd(
    eventPayload: LocalVgVehicleSessionEndEventPayload,
  ): Promise<void> {
    try {
      const vulogCar: RealtimeCar =
        await this.vulogCarService.getSpecificCarInRealtime(
          new VulogCarId(eventPayload.vehicleId),
        );

      if (!vulogCar) {
        throw new Error(
          `Vulog car (Vulog ID: ${eventPayload.vehicleId}) not found`,
        );
      }

      if (!vulogCar.zoneIds?.length) {
        throw new Error(
          `Vulog car (Vulog ID: ${eventPayload.vehicleId}) has empty zones`,
        );
      }

      await this.endRentalService.makeRentalEnded(
        eventPayload.sessionId,
        vulogCar.zoneIds.map((z) => z.value),
        eventPayload.finishDate,
      );
    } catch (e) {
      this.localEventEmitterService.throwError(e, eventPayload.correlationId);
    }
  }

  @OnEvent(localEventPattern.vg.vehicleSessionCancel, {
    async: true,
  })
  async onVehicleSessionCancel({
    sessionId,
    finishDate,
    correlationId,
    trigger,
  }: LocalVgVehicleSessionCancelEventPayload): Promise<void> {
    try {
      const reservation = await this.reservationService.getOneByVulogId(
        new VulogJourneyOrTripId(sessionId),
      );

      if (!reservation) {
        throw new ReservationError('Reservation not found');
      }

      const startStation: Station = await this.stationService.getStation(
        reservation.startStationId,
      );

      // Ending BlueSG rental
      if (reservation.isOnGoingRental()) {
        await this.endRentalService.makeRentalEnded(
          sessionId,
          [startStation.zoneId],
          finishDate,
          { nrTriggerDimension: trigger },
          correlationId,
        );
      } else {
        await this.cancelReservationService.makeReservationCancelled(
          reservation,
          correlationId,
        );
      }
    } catch (e) {
      this.localEventEmitterService.throwError(e, correlationId);
    }
  }

  @OnEvent(localEventPattern.vg.vehicleSessionStart, {
    async: true,
  })
  async onVehicleSessionStartByAdmin(
    eventPayload: LocalVgVehicleSessionStartByAdminEventPayload,
  ): Promise<void> {
    try {
      await this.startReservationService.startReservationByAdmin(
        eventPayload.sessionId,
        eventPayload.correlationId,
        eventPayload.vehicleId,
        eventPayload.startDate,
      );
    } catch (e) {
      this.localEventEmitterService.throwError(e, eventPayload.correlationId);
    }
  }

  @OnEvent(localEventPattern.vg.chargingCablePlugged, {
    async: true,
  })
  async onChargingCablePlugged(
    eventPayload: LocalVgChargingCablePluggedEventPayload,
  ): Promise<void> {
    try {
      await this.carPlugService.plugCable(
        new VulogCarId(eventPayload.vehicleId),
      );
    } catch (error) {
      this.localEventEmitterService.throwError(
        error,
        eventPayload.correlationId,
      );
    }
  }

  @OnEvent(localEventPattern.vg.chargingCableUnplugged, {
    async: true,
  })
  async onChargingCableUnplugged(
    eventPayload: LocalVgChargingCableUnpluggedEventPayload,
  ): Promise<void> {
    try {
      await this.carPlugService.unplugCable(
        new VulogCarId(eventPayload.vehicleId),
      );
    } catch (error) {
      this.localEventEmitterService.throwError(
        error,
        eventPayload.correlationId,
      );
    }
  }
}
