import { LocalPostStartRentalEventPayloadV1 } from '@bluesg-2/queue-pop';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { localEventPattern } from 'src/common/constants/event';
import { RentalError } from 'src/common/errors/rental-error';
import {
  VulogCarId,
  VulogJourneyOrTripId,
  VulogUserId,
} from 'src/common/tiny-types';
import { LocalVulogEndTripEventV1Payload } from 'src/event/local/vulog/aima/local.v1.vulog-end-trip.event';
import { LocalVulogTripEndRfidEventV1Payload } from 'src/event/local/vulog/aima/local.v1.vulog-trip-end-rfid.event';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { CarService } from 'src/logic/car/car.service';
import { ReservationService } from 'src/logic/reservation.service';
import { VulogTelemetryService } from 'src/logic/telemetry-repository/vulog/vulog-telemetry.service';
import { VulogUserService } from 'src/logic/vulog/vulog-user.service';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { LOCAL_EVENT_EMITTER_TOKEN } from '~shared/local-event-emitter/constants';
import { LocalEventEmitterService } from '~shared/local-event-emitter/local-event-emitter.service';
import { NewRelicRentalEventService } from '~shared/newrelic/event/event-type/rental.event.newrelic.service';
import { NewRelicMetricTrigger } from '~shared/newrelic/metric/enums/newrelic-metric.enum';

const MAX_DESTICK_ATTEMPTS = 3;

const isVgZoneSticky = (vgZone: VulogZone) => vgZone.isSticky();
@Injectable()
export class LocalRentalListenerV1 {
  constructor(
    @Inject(LOCAL_EVENT_EMITTER_TOKEN)
    private readonly localEventEmitterService: LocalEventEmitterService,
    private readonly carService: CarService,
    private readonly vulogUserService: VulogUserService,
    private readonly reservationService: ReservationService,
    private readonly vulogCarService: VulogCarService,

    private readonly vulogTelemetryService: VulogTelemetryService,
    private readonly rentalEventService: NewRelicRentalEventService,
  ) {}

  /** Comment this event handler as we could not rely on Vulog Event to detect if the trip is started with RFID or not
  @OnEvent(localEventPattern.rental.tripStartRfid, {
    async: true,
  })
  async onTripStartedWithRfid(
    dto: LocalVulogTripStartRfidEventV1Payload,
  ): Promise<void> {
    try {
      const { authorized, rfid, tripId, vehicleId, vulogUserId, date } = dto;

      if (!authorized) {
        throw new RentalError(
          'Customer failed to tap RFID card to start a rental',
        );
      }

      const car = await this.carService.getOneByVulogId(
        new VulogCarId(vehicleId),
      );

      if (!car) {
        throw new RentalError('The vehicle is not found in our database');
      }

      const vulogUser = await this.vulogUserService.findOneByVulogUserId(
        new VulogUserId(vulogUserId),
      );

      if (!vulogUser) {
        throw new RentalError('The user is not existed in our database');
      }

      if (!vulogUser.isEqualToUserRfid(rfid)) {
        throw new RentalError(
          `The RFID of the user (${vulogUser.formattedRfid()}) does not match with provided RFID card (${rfid})`,
        );
      }

      const vulogTripId = new VulogJourneyOrTripId(tripId);

      const bsgOwnOnGoingReservation =
        await this.reservationService.getBsgOnGoingReservationByVulogTripId(
          vulogTripId,
        );

      if (!bsgOwnOnGoingReservation || !bsgOwnOnGoingReservation.isReserved) {
        throw new RentalError(
          `There is no on-going BlueSG reservation with Vulog Trip ID (${tripId})`,
        );
      }

      const vulogOnGoingRental =
        await this.vulogCarService.getSpecificOnGoingRental(car, false);

      if (!vulogOnGoingRental) {
        throw new RentalError(
          `There is no on-going Vulog rental with Vulog Trip ID (${tripId}) with provided RFID (${rfid})`,
        );
      }

      let convertedRental = await this.rentalService.convertReservationToRental(
        bsgOwnOnGoingReservation,
        date ? new VulogDate(date) : vulogOnGoingRental.startDate,
      );

      convertedRental = await this.reservationService.saveOne(convertedRental);

      let latestRentalInfo: Reservation;

      try {
        await asyncRetry<Reservation>(
          async () => {
            latestRentalInfo = await this.reservationService.getOneByVulogId(
              vulogTripId,
            );

            throw new Error('Retries until zero reached');
          },
          {
            factor: 2,
            retries: 2,
          },
        );
      } catch (err) {}

      if (
        latestRentalInfo.startedAt
          .toDateTime()
          .equals(convertedRental.startedAt.toDateTime())
      ) {
        // dispatch event to update queue pop
        this.localEventEmitterService.dispatchEvent(
          new LocalPostStartRentalEventV1({
            bsgUserId: vulogUser.bsgUserId.value,
            correlationId: dto.correlationId,
            carMetadata: {
              id: car.id.value,
              model: car.model as unknown as QueuePopCarModel,
              stationId: convertedRental.startStationId.value,
              vulogId: car.vulogId.value,
            },
          }),
        );

        this.eventBusService.nonBlockingEmit(
          new StartRentalEventPayloadDto(
            new StartRentalRequestDto(
              convertedRental.startStationId.value,
              vulogUser.bsgUserId.value,
              {
                id: car.id.value,
                model: convertedRental.carModel,
                vulogId: car.vulogId.value,
              },
            ),
            dto.correlationId,
          ),
        );

        // Find the StartTrip event in `vulog-event` table, if it also exists then the rental was started by CRC
        const startTripEvent = await this.vulogEventService.getOneByConditions({
          tripId: vulogTripId,
          type: VulogEventNotifierType.StartTrip,
        });

        if (startTripEvent) {
          Logger.warn(
            'CRC started a rental on behalf of customer (These 2 events should be persisted into database `TripStart` and `StartTrip`)',
            {
              details: convertedRental,
            },
          );

          await this.rentalEventService.emitStarted(
            NewRelicMetricTrigger.Admin,
            convertedRental.getMetricNewRelicRentalType(),
            convertedRental.getMetricNewRelicCarModel(),
          );
        } else {
          Logger.warn('Customer started a rental by using RFID card', {
            details: convertedRental,
          });

          await this.rentalEventService.emitStarted(
            NewRelicMetricTrigger.RFID,
            convertedRental.getMetricNewRelicRentalType(),
            convertedRental.getMetricNewRelicCarModel(),
          );
        }
      }
    } catch (e) {
      this.localEventEmitterService.throwError(e, dto.correlationId);
    }
  }
  */

  @OnEvent(localEventPattern.rental.postStartTrip, {
    async: true,
  })
  async onPostStartTrip(
    event: LocalPostStartRentalEventPayloadV1,
  ): Promise<void> {
    const { carMetadata, correlationId } = event;

    try {
      const vgCarId = new VulogCarId(carMetadata.vulogId);

      await this.performDestick(vgCarId);
    } catch (e) {
      this.localEventEmitterService.throwError(e, correlationId);
    }
  }

  private async performDestick(vgCarId: VulogCarId): Promise<void> {
    let realtimeCar =
      await this.vulogTelemetryService.getCarStatusSessionCommand(vgCarId);

    let stickyZones = realtimeCar?.currentZones?.filter(isVgZoneSticky);

    const POST_START_TRIP = 'POST_START_TRIP';

    Logger.log(
      'Post Start Trip: Checking if there are sticky zones attached with car',
      {
        realtimeCar,
        stickyZones,
        process: POST_START_TRIP,
      },
    );

    let destickPerformed = false;
    let remainingAttempts = MAX_DESTICK_ATTEMPTS;

    while (stickyZones.length && remainingAttempts) {
      destickPerformed = true;

      Logger.log('Post Start Trip: Removing sticky zones from car', {
        stickyZones,
        remainingAttempts,
        process: POST_START_TRIP,
      });
      await Promise.all(
        stickyZones.map(
          async (vgZone) =>
            await this.vulogCarService.toggleStickyStationWithCar(
              vgCarId,
              vgZone.vulogZoneId,
              false,
            ),
        ),
      ).catch((err) => {
        throw err;
      });
      remainingAttempts--;

      realtimeCar = await this.vulogTelemetryService.getCarStatusSessionCommand(
        vgCarId,
      );

      stickyZones = realtimeCar?.currentZones?.filter(isVgZoneSticky);
    }

    if (!destickPerformed) {
      return;
    }

    if (!stickyZones.length) {
      Logger.log('Post Start Trip: All sticky zones are removed from car', {
        realtimeCar,
        stickyZones,
        noStickyZone: !stickyZones.length,
        process: POST_START_TRIP,
      });
      return;
    }

    Logger.warn(
      `Post Start Trip: Remaining sticky zones after ${MAX_DESTICK_ATTEMPTS} attempts`,
      {
        realtimeCar,
        stickyZones,
        process: POST_START_TRIP,
      },
    );
  }

  @OnEvent(localEventPattern.rental.endTrip, {
    async: true,
  })
  /**
   *  This EndTrip event is triggered by admin, for customer, it is by Trip Termination
   */
  async onEndTrip(event: LocalVulogEndTripEventV1Payload): Promise<void> {
    const { correlationId, tripId, vehicleId, vulogUserId } = event;

    try {
      const rental = await this.reservationService.getOneByCarIdAndTripId(
        new VulogCarId(vehicleId),
        new VulogJourneyOrTripId(tripId),
      );

      if (!rental) {
        throw new RentalError(
          `Rental with trip ID [${tripId}] and vulog vehicle ID [${vehicleId}] not found.`,
        );
      }

      if (!rental.isOnGoingRental()) {
        throw new RentalError(
          `Rental with trip ID [${tripId}] is not on-going`,
        );
      }

      const vulogUser = await this.vulogUserService.findOneByVulogUserId(
        new VulogUserId(vulogUserId),
      );

      if (!vulogUser) {
        throw new RentalError(
          `User with Vulog User ID [${vulogUserId}] not found`,
        );
      }

      if (!rental.bsgUserId.equals(vulogUser.bsgUserId)) {
        throw new RentalError(
          'Mismatched user information between BlueSG and Vulog',
        );
      }

      await this.rentalEventService.emitTriggeredWaitingForInvoicing(
        NewRelicMetricTrigger.Admin,
        rental.getMetricNewRelicRentalType(),
        rental.getMetricNewRelicCarModel(),
      );
    } catch (e) {
      this.localEventEmitterService.throwError(e, correlationId);
    }
  }

  @OnEvent(localEventPattern.rental.tripEndRfid, {
    async: true,
  })
  async onTripEndedWithRfid(
    dto: LocalVulogTripEndRfidEventV1Payload,
  ): Promise<void> {
    try {
      const { authorized, rfid, tripId, vehicleId, vulogUserId } = dto;

      if (!authorized) {
        throw new RentalError(
          'Customer failed to tap RFID card to end a rental',
        );
      }

      const car = await this.carService.getOneByVulogId(
        new VulogCarId(vehicleId),
      );

      if (!car) {
        throw new RentalError('The vehicle is not found in our database');
      }

      const vulogUser = await this.vulogUserService.findOneByVulogUserId(
        new VulogUserId(vulogUserId),
      );

      if (!vulogUser) {
        throw new RentalError('The user is not existed in our database');
      }

      if (!vulogUser.isEqualToUserRfid(rfid)) {
        throw new RentalError(
          `The RFID of the user (${vulogUser.formattedRfid()}) does not match with provided RFID card (${rfid})`,
        );
      }

      const vulogTripId = new VulogJourneyOrTripId(tripId);

      const rental = await this.reservationService.findOneByTripId(vulogTripId);

      if (!rental) {
        throw new RentalError(
          `There is no BlueSG rental with Vulog Trip ID (${tripId})`,
        );
      }

      await this.rentalEventService.emitTriggeredWaitingForInvoicing(
        NewRelicMetricTrigger.RFID,
        rental.getMetricNewRelicRentalType(),
        rental.getMetricNewRelicCarModel(),
      );
    } catch (e) {
      this.localEventEmitterService.throwError(e, dto.correlationId);
    }
  }
}
