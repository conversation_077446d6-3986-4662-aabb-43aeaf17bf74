import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { isDefined, isEmpty } from 'class-validator';
import { DateTime } from 'luxon';
import { ApiResponseSchema } from 'src/api/api-response.decorator';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { StationNotFoundError } from 'src/common/errors/external/station-not-found.error';
import { MissingArgumentsError } from 'src/common/errors/missing-arguments-error';
import { StationId } from 'src/common/tiny-types';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import {
  InternalCarNotChargingMetadata,
  InternalCarService,
} from 'src/logic/internal.car.service';
import { StationService } from 'src/logic/station/station.service';
import { RealtimeCar } from 'src/model/realtime.car';
import { Station } from 'src/model/station';
import { InternalGetRealtimeCarQueryParamsDtoV1 } from './dto/request/internal.get-realtime-car.query.v1.dto';
import { InternalRealtimeCarResponseDtoV1 } from './dto/response/internal.realtime-car.response.dto.v1';

@ApiTags(RouteSegment.Car.Index)
@Controller({
  version: ApiVersion.V1,
  path: `${RentalServiceRootRouteSegment.Internal}/${RouteSegment.Car.Index}`,
})
export class InternalCarControllerV1 {
  constructor(
    private readonly vulogCarService: VulogCarService,
    private readonly stationService: StationService,
    private readonly internalCarService: InternalCarService,
  ) {}

  @ApiResponseSchema(InternalRealtimeCarResponseDtoV1)
  @ApiOperation({
    summary: 'Get information of real-time car',
  })
  @Get(RouteSegment.Car.Realtime)
  async getSpecificCar(
    @Query() queryParams: InternalGetRealtimeCarQueryParamsDtoV1,
  ): Promise<InternalRealtimeCarResponseDtoV1> {
    const { atStation, charging, stsPluggedAt } = queryParams;

    if (isDefined(charging) && !charging && isEmpty(stsPluggedAt)) {
      throw new MissingArgumentsError(
        '`stsPluggedAt` is required when `charging` is set to `false`',
      );
    }

    const matchedStation: Station = await this.stationService.getStation(
      new StationId(atStation),
    );

    if (!matchedStation) {
      throw new StationNotFoundError();
    }

    let rltCar: RealtimeCar;
    let suitablePluggedMetadata: InternalCarNotChargingMetadata;
    let carNotChargingAndPluggedAt: DateTime;

    if (charging) {
      rltCar = await this.vulogCarService.getLatestCarInChargingModeByZoneId(
        matchedStation.zoneId,
      );
    } else {
      suitablePluggedMetadata =
        await this.internalCarService.getCarNotInChargingMode(
          matchedStation.id,
          DateTime.fromISO(stsPluggedAt),
        );

      if (suitablePluggedMetadata) {
        rltCar = suitablePluggedMetadata.realtimeCar;
        carNotChargingAndPluggedAt = suitablePluggedMetadata.event.date;
      }
    }

    if (!rltCar) {
      return null;
    }

    const { bsgCar, fleetService, stickyStation } =
      await this.internalCarService.getCarMetadata(rltCar);

    bsgCar.realtimeMetadata = {
      disabled: rltCar.disabled,
      batteryPercentage: rltCar.energyLevel,
      pluggedAt: carNotChargingAndPluggedAt,
    };

    return InternalRealtimeCarResponseDtoV1.from(
      bsgCar,
      stickyStation,
      fleetService,
    );
  }
}
