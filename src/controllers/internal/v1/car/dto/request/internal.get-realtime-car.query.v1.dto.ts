import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsISO8601,
  IsNotEmpty,
  IsOptional,
  IsUUID,
} from 'class-validator';
import { ToBoolean } from 'src/common/transformers/to-boolean.transformer';

export class InternalGetRealtimeCarQueryParamsDtoV1 {
  @ApiProperty({
    type: 'string',
    description: 'The UUID of station',
    format: 'uuid',
  })
  @IsUUID()
  @IsNotEmpty()
  atStation: string;

  @ApiProperty({
    type: 'boolean',
    default: true,
    description: 'Whether the car is charging or not charging',
  })
  @ToBoolean()
  @IsBoolean()
  @IsOptional()
  charging?: boolean = true;

  @ApiProperty({
    type: 'string',
    format: 'datetime',
    description:
      'Timestamp that an EVSE is being plugged into by a car. This field is required when query parameter `charging` is set to `false`',
  })
  @IsISO8601()
  @IsOptional()
  stsPluggedAt?: string;
}
