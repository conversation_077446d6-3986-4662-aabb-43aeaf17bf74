import { ApiProperty } from '@nestjs/swagger';
import { Car } from 'src/logic/car/domain/car';
import { FleetService } from 'src/model/fleet-service';
import { Station } from 'src/model/station';
import { AdminStickyCarStationResponseDtoV1 } from '../../../../../admin/v1/car/dto/response/admin.sticky-car-station.response.dto.v1';
import { StickyServiceResponseDto } from '../../../../../admin/v1/car/dto/response/sticky-service.response.dto';
import { StickyStationResponseDto } from '../../../../../admin/v1/car/dto/response/sticky-station.response.dto';

export class InternalRealtimeCarResponseDtoV1 extends AdminStickyCarStationResponseDtoV1 {
  constructor(props: InternalRealtimeCarResponseDtoV1) {
    super(props);

    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    description: 'Battery percentage of a car',
  })
  batteryPercentage: number;

  @ApiProperty({
    type: 'string',
    format: 'datetime',
    description: 'A charging cable has been plugged in to the vehicle',
  })
  pluggedAt: string;

  static from(
    car: Car,
    station: Station,
    service: FleetService,
  ): InternalRealtimeCarResponseDtoV1 {
    const { id, plate, vin, model, vulogId, realtimeMetadata } = car;

    return {
      id: id.value,
      vulogCarId: vulogId.value,
      plateNumber: plate.value,
      vin: vin,
      category: model,
      disabled: realtimeMetadata?.disabled || undefined,
      stickyStation: station
        ? StickyStationResponseDto.from(station)
        : undefined,
      service: service ? StickyServiceResponseDto.from(service) : undefined,
      batteryPercentage: realtimeMetadata?.batteryPercentage,
      pluggedAt: realtimeMetadata?.pluggedAt?.toISO(),
    };
  }
}
