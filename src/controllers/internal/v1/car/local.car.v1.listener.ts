import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { localEventPattern } from 'src/common/constants/event';
import { VulogCarId } from 'src/common/tiny-types';
import { LocalVulogChargingCablePluggedEventV1Payload } from 'src/event/local/vulog/aima/local.v1.vulog-charging-cable-plugged.event';
import { LocalVulogChargingCableUnpluggedEventV1Payload } from 'src/event/local/vulog/aima/local.v1.vulog-charging-cable-unplugged.event';
import { CarPlugService } from 'src/logic/vulog/car-plug.service';
import { LOCAL_EVENT_EMITTER_TOKEN } from '~shared/local-event-emitter/constants';
import { LocalEventEmitterService } from '~shared/local-event-emitter/local-event-emitter.service';

@Injectable()
export class LocalCarListenerV1 {
  constructor(
    @Inject(LOCAL_EVENT_EMITTER_TOKEN)
    private readonly localEventEmitterService: LocalEventEmitterService,
    private readonly carPlugService: CarPlugService,
  ) {}

  @OnEvent(localEventPattern.car.chargingCablePlugged, {
    async: true,
  })
  async onChargingCablePlugged(
    dto: LocalVulogChargingCablePluggedEventV1Payload,
  ): Promise<void> {
    try {
      await this.carPlugService.plugCable(new VulogCarId(dto.vehicleId));
    } catch (e) {
      this.localEventEmitterService.throwError(e, dto.correlationId);
    }
  }

  @OnEvent(localEventPattern.car.chargingCableUnplugged, {
    async: true,
  })
  async onChargingCableUnplugged(
    dto: LocalVulogChargingCableUnpluggedEventV1Payload,
  ): Promise<void> {
    try {
      await this.carPlugService.unplugCable(new VulogCarId(dto.vehicleId));
    } catch (e) {
      this.localEventEmitterService.throwError(e, dto.correlationId);
    }
  }
}
