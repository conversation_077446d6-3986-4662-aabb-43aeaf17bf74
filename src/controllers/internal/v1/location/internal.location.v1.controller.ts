import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { DateTime } from 'luxon';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { ReservationService } from 'src/logic/reservation.service';
import { InternalLocationQueryParamsDtoV1 } from './dto/request/internal.location.query.v1.dto';
import { InternalLocationResponseDtoV1 } from './dto/response/internal.location.response.dto.v1';

@ApiTags(RouteSegment.Location.Index)
@Controller({
  version: ApiVersion.V1,
  path: `${RentalServiceRootRouteSegment.Internal}/${RouteSegment.Location.Index}`,
})
export class InternalLocationControllerV1 {
  constructor(private readonly reservationService: ReservationService) {}

  @Get()
  @ApiOperation({
    summary: 'Get locations of ended rentals',
    description:
      'Returns locations where rentals were ended after the provided timestamp',
  })
  @ApiResponse({
    status: 200,
    description: 'List of locations with rental information',
    type: [InternalLocationResponseDtoV1],
  })
  async getLocations(
    @Query() queryParams: InternalLocationQueryParamsDtoV1,
  ): Promise<InternalLocationResponseDtoV1[]> {
    const { from } = queryParams;

    const reservations = await this.reservationService.getReservationsEndedFrom(
      DateTime.fromISO(from),
    );

    return reservations.map((reservation) =>
      InternalLocationResponseDtoV1.from(reservation),
    );
  }
}
