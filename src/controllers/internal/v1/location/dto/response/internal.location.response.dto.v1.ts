import { ApiProperty } from '@nestjs/swagger';
import { Reservation } from 'src/model/reservation.domain';

export class InternalLocationResponseDtoV1 {
  constructor(props: InternalLocationResponseDtoV1) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    description: 'The UUID of the car in vulog',
    format: 'uuid',
  })
  vulogCarId: string;

  @ApiProperty({
    type: 'string',
    description: 'The UUID of the station STS',
    format: 'uuid',
  })
  stationId: string;

  @ApiProperty({
    type: 'string',
    description: 'The timestamp when the rental ended',
    format: 'date-time',
  })
  locatedAt: string;

  static from(reservation: Reservation): InternalLocationResponseDtoV1 {
    return new InternalLocationResponseDtoV1({
      vulogCarId: reservation.carId.value,
      stationId: reservation.endStationId.value,
      locatedAt: reservation.endedAt.toDateTime().toUTC().toISO(),
    });
  }
}
