import { ApiProperty } from '@nestjs/swagger';
import { IsISO8601NotInFuture } from 'src/common/decorator/is-not-in-future.decorator';

export class InternalLocationQueryParamsDtoV1 {
  @ApiProperty({
    required: true,
    type: 'string',
    format: 'datetime',
    description:
      'Timestamp of the oldest acceptable location update, must not be in the future',
  })
  @IsISO8601NotInFuture()
  from: string;
}
