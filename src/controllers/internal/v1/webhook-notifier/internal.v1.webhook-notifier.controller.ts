import {
  CorrelationId,
  CorrelationIdService,
} from '@bluesg-2/monitoring/dist/correlation-id/correlation-id.service';
import { Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { AllowNonWhitelistedBody } from 'src/common/decorator/allow-non-whitelist-body.decorator';
import { VulogEventDto } from 'src/logic/vulog/dto/vulog.dto';
import { EventbridgeEventDto } from 'src/logic/vulog/dto/webhook/eventbridge-event.dto';
import { VulogEventNotifierService } from 'src/logic/vulog/vulog-event-notifier.service';
import { BaseVulogEvent } from 'src/logic/vulog/vulog.event';

@ApiTags(RouteSegment.EventNotifier.Index)
@Controller({
  version: ApiVersion.V1,
  path: `${RentalServiceRootRouteSegment.Internal}/${RouteSegment.EventNotifier.Index}`,
})
export class InternalWebhookNotifierControllerV1 {
  constructor(
    private readonly vulogEventNotifierService: VulogEventNotifierService,
    private readonly correlationIdService: CorrelationIdService,
  ) {}

  // Status code 400, the event is rejected and musn't be sent again
  // Status code 500, unexpected server error, Vulog will retry to send the event later.
  @Post(RouteSegment.EventNotifier.EventBridge)
  async handleEventBridgeEvent(
    @AllowNonWhitelistedBody()
    dto: EventbridgeEventDto,
  ) {
    const correlationId: CorrelationId =
      await this.correlationIdService.getCorrelationId();

    const vulogEvent: BaseVulogEvent = BaseVulogEvent.fromDto(
      dto.detail as VulogEventDto,
      correlationId,
    );

    await this.vulogEventNotifierService.handleWebhookCall(vulogEvent);
  }
}
