import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { DateTime } from 'luxon';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { CarPlateNumber } from 'src/common/tiny-types';
import { CarService } from 'src/logic/car/car.service';
import { ReservationService } from 'src/logic/reservation.service';
import { GetReservationsByPlateNoAndTimeDtoV1 } from './dto/request/get-reservations-by-plate-no.v1.dto';
import { InternalReservationQueryParamsDtoV1 } from './dto/request/internal.reservation.query.v1.dto';
import { InternalReservationResponseDtoV1 } from './dto/response/internal.reservation.response.dto.v1';

@ApiTags(RouteSegment.Reservation.Index)
@Controller({
  version: ApiVersion.V1,
  path: `${RentalServiceRootRouteSegment.Internal}/${RouteSegment.Reservation.Index}`,
})
export class InternalReservationControllerV1 {
  constructor(
    private readonly reservationService: ReservationService,
    private readonly carService: CarService,
  ) {}

  @Post(RouteSegment.Reservation.Search)
  async searchReservationsAndCarsByPlateNumbersAndTime(
    @Body() dto: GetReservationsByPlateNoAndTimeDtoV1,
  ) {
    const reservations =
      await this.reservationService.getReservationsByPlateNumberAndTime(dto);
    // In case where there are no reservations for particular plate numbers on a particular date,
    // we need to get car details for those plate numbers
    const plateNumbersToSearch: CarPlateNumber[] =
      dto.plateNumberAndTxnDate.map(
        (reservation) => new CarPlateNumber(reservation.plateNumber),
      );
    const cars = await this.carService.getCarsByPlateNumbers(
      plateNumbersToSearch,
    );

    return {
      reservations,
      cars,
    };
  }

  @Get(RouteSegment.Reservation.RecentlyUpdated)
  @ApiOperation({
    summary: 'Get recently updated reservations',
    description:
      'Returns reservations that were updated after the provided timestamp',
  })
  @ApiResponse({
    status: 200,
    description:
      'List of reservations that were updated after the provided timestamp',
    type: [InternalReservationResponseDtoV1],
  })
  async getRecentlyUpdatedReservations(
    @Query() queryParams: InternalReservationQueryParamsDtoV1,
  ): Promise<InternalReservationResponseDtoV1[]> {
    const { 'updated-from': updatedFrom } = queryParams;

    const reservations =
      await this.reservationService.getRecentlyUpdatedReservations(
        DateTime.fromISO(updatedFrom),
      );

    return reservations.map((reservation) =>
      InternalReservationResponseDtoV1.from(reservation),
    );
  }
}
