import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { localEventPattern } from 'src/common/constants/event';
import { ReservationError } from 'src/common/errors/reservation-error';
import { LocalVulogCancelTripReportEventV1Payload } from 'src/event/local/vulog/aima/local.v1.vulog-cancel-trip-report.event';
import { LocalVulogEndTripReportEventV1Payload } from 'src/event/local/vulog/aima/local.v1.vulog-end-trip-report.event';
import { LocalVulogStartTripEventV1Payload } from 'src/event/local/vulog/aima/local.v1.vulog-start-trip.event';
import { CarService } from 'src/logic/car/car.service';
import { RentalService } from 'src/logic/rental.service';
import { ReservationService } from 'src/logic/reservation.service';
import { VulogEventService } from 'src/logic/vulog-event.service';
import { CancelReservationService } from 'src/logic/vulog/cancel-reservation.service';
import { EndRentalService } from 'src/logic/vulog/end-rental.service';
import { StartReservationService } from 'src/logic/vulog/start-reservation.service';
import { VulogUserService } from 'src/logic/vulog/vulog-user.service';
import { ReservationRepository } from 'src/model/repositories/reservation.repository';
import { EventBusService } from '~shared/event-bus/event-bus.service';
import { LOCAL_EVENT_EMITTER_TOKEN } from '~shared/local-event-emitter/constants';
import { LocalEventEmitterService } from '~shared/local-event-emitter/local-event-emitter.service';
import { NewRelicRentalEventService } from '~shared/newrelic/event/event-type/rental.event.newrelic.service';
import { VulogJourneyOrTripId } from '../../../../common/tiny-types';

@Injectable()
export class LocalReservationListenerV1 {
  constructor(
    private readonly reservationService: ReservationService,
    @Inject(LOCAL_EVENT_EMITTER_TOKEN)
    private readonly localEventEmitterService: LocalEventEmitterService,

    private readonly endRentalService: EndRentalService,
    private readonly cancelReservationService: CancelReservationService,
    private readonly startReservationService: StartReservationService,
  ) {}

  @OnEvent(localEventPattern.reservation.endTripReport, {
    async: true,
  })
  async onRentalEnded(
    dto: LocalVulogEndTripReportEventV1Payload,
  ): Promise<void> {
    try {
      await this.endRentalService.makeRentalEnded(
        dto.tripId,
        dto.endZones,
        dto.finishDate,
        { nrTriggerDimension: dto.trigger, qrCode: dto.qrCode },
      );
    } catch (e) {
      this.localEventEmitterService.throwError(e, dto.correlationId);
    }
  }

  @OnEvent(localEventPattern.reservation.cancelTripReport, {
    async: true,
  })
  async onReservationCanceled(
    dto: LocalVulogCancelTripReportEventV1Payload,
  ): Promise<void> {
    try {
      const reservation = await this.reservationService.getOneByVulogId(
        new VulogJourneyOrTripId(dto.vulogTripId),
      );

      if (!reservation) {
        throw new ReservationError('Reservation not found');
      }

      // Ending BlueSG rental
      if (reservation.isOnGoingRental()) {
        await this.endRentalService.makeRentalEnded(
          dto.vulogTripId,
          dto.endZoneIds,
          dto.cancelledAt,
          { nrTriggerDimension: dto.trigger },
          dto.correlationId,
        );
      } else {
        await this.cancelReservationService.makeReservationCancelled(
          reservation,
          dto.correlationId,
        );
      }
    } catch (e) {
      this.localEventEmitterService.throwError(e, dto.correlationId);
    }
  }

  @OnEvent(localEventPattern.rental.adminStartTrip, {
    async: true,
  })
  async onStartRentalByAdmin(
    dto: LocalVulogStartTripEventV1Payload,
  ): Promise<void> {
    const { tripId, correlationId, vehicleId, date } = dto;
    try {
      await this.startReservationService.startReservationByAdmin(
        tripId,
        correlationId,
        vehicleId,
        date,
      );
    } catch (e) {
      this.localEventEmitterService.throwError(e, dto.correlationId);
    }
  }
}
