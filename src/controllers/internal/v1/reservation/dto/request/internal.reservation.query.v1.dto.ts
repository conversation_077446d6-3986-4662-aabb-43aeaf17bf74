import { ApiProperty } from '@nestjs/swagger';
import { IsISO8601NotInFuture } from 'src/common/decorator/is-not-in-future.decorator';

export class InternalReservationQueryParamsDtoV1 {
  @ApiProperty({
    required: true,
    type: 'string',
    format: 'datetime',
    description:
      'Timestamp of the oldest acceptable reservation update, must not be in the future',
  })
  @IsISO8601NotInFuture()
  'updated-from': string;
}
