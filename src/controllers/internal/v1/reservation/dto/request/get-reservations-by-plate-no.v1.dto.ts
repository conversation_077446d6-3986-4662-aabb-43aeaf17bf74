import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayNotEmpty, IsNotEmpty, ValidateNested } from 'class-validator';
import { DateTime } from 'luxon';
import { IsValidLuxonISO8601 } from 'src/common/decorator/is-valid-luxon-iso-8601.decorator';

export class RentalPlateNumberAndTimeDtoV1 {
  @ApiProperty({
    required: true,
    type: 'string',
  })
  @IsNotEmpty()
  plateNumber: string;

  @ApiProperty({
    required: true,
    type: 'string',
    format: 'date-time',
    example: DateTime.now().toISO(),
  })
  @IsValidLuxonISO8601()
  @IsNotEmpty()
  time: Date;
}

export class GetReservationsByPlateNoAndTimeDtoV1 {
  @ApiProperty({
    required: true,
    isArray: true,
    type: RentalPlateNumberAndTimeDtoV1,
  })
  @IsNotEmpty()
  @ArrayNotEmpty()
  @ValidateNested()
  @Type(() => RentalPlateNumberAndTimeDtoV1)
  plateNumberAndTxnDate: RentalPlateNumberAndTimeDtoV1[];
}
