import { ApiProperty } from '@nestjs/swagger';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { Reservation } from 'src/model/reservation.domain';

export class InternalReservationResponseDtoV1 {
  constructor(props: InternalReservationResponseDtoV1) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    description: 'The UUID of the reservation',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    type: 'string',
    description: 'The status of the reservation',
    enum: ReservationStatus,
  })
  status: ReservationStatus;

  @ApiProperty({
    type: 'string',
    description: 'The UUID of the user',
    format: 'uuid',
  })
  userId: string;

  @ApiProperty({
    type: 'string',
    description: 'The UUID of the car in vulog',
    format: 'uuid',
  })
  vulogCarId: string;

  @ApiProperty({
    type: 'string',
    description: 'The timestamp when the reservation was created',
    format: 'date-time',
  })
  createdAt: string;

  @ApiProperty({
    type: 'string',
    description: 'The timestamp when the reservation was last modified',
    format: 'date-time',
  })
  modifiedAt: string;

  static from(reservation: Reservation): InternalReservationResponseDtoV1 {
    return new InternalReservationResponseDtoV1({
      id: reservation.reservationId.value,
      status: reservation.status,
      userId: reservation.bsgUserId.value,
      vulogCarId: reservation.carId?.value,
      createdAt: reservation.getCreatedAt?.toUTC().toISO(),
      modifiedAt: reservation.getModifiedAt?.toUTC().toISO(),
    });
  }
}
