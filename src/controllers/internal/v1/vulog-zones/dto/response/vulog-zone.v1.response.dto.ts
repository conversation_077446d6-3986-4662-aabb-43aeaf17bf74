import { ApiProperty } from '@nestjs/swagger';
import { VulogZoneType } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { VulogGeoZone } from 'src/model/vulog-geo-zone';

export class VulogZoneResponseDtoV1 {
  constructor(props?: VulogZoneResponseDtoV1) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    example: 'Woodlands/Woodland Drive 50/Blk 892C/Deck 2A',
    required: true,
  })
  name: string;

  @ApiProperty({
    type: 'number',
    example: 1,
    required: true,
  })
  version: number;

  @ApiProperty({
    type: 'string',
    enum: VulogZoneType,
    required: true,
  })
  type: VulogZoneType;

  @ApiProperty({
    type: 'string',
    example: '000117cb6dac46f986f16e0dbcc27d3f',
    required: true,
  })
  zoneId: string;

  static fromVulogGeoZone(vulogGeoZone: VulogGeoZone): VulogZoneResponseDtoV1 {
    return new VulogZoneResponseDtoV1({
      name: vulogGeoZone.name,
      version: vulogGeoZone.version,
      type: vulogGeoZone.type,
      zoneId: vulogGeoZone.zoneId,
    });
  }
}
