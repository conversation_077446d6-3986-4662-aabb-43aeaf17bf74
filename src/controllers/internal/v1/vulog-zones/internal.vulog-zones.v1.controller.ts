import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ApiResponseSchema } from 'src/api/api-response.decorator';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { VulogZoneService } from 'src/logic/vulog/vulog-zone.service';
import { VulogZoneResponseDtoV1 } from './dto/response/vulog-zone.v1.response.dto';

@ApiTags(RouteSegment.VulogZone.Index)
@Controller({
  version: ApiVersion.V1,
  path: `${RentalServiceRootRouteSegment.Internal}/${RouteSegment.VulogZone.Index}`,
})
export class InternalVulogZonesControllerV1 {
  constructor(private readonly vulogZoneService: VulogZoneService) {}

  @Get(RouteSegment.VulogZone.List)
  @ApiResponseSchema(VulogZoneResponseDtoV1, true)
  async getVulogZones(): Promise<VulogZoneResponseDtoV1[]> {
    const vulogGeoZones = await this.vulogZoneService.getVulogGeoZones();

    return vulogGeoZones?.length
      ? vulogGeoZones?.map((vulogGeoZone) =>
          VulogZoneResponseDtoV1.fromVulogGeoZone(vulogGeoZone),
        )
      : [];
  }
}
