import { ApiResponseSchema } from 'src/api/api-response.decorator';
import { ApiVersion } from 'src/common/constants/api-versions';
import { AvailabilityService } from 'src/logic/availability.service';

import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { CarAvailabilityResponseDto } from './dtos/car-availability.response.dto';

@ApiTags('availability')
@Controller({
  path: 'availability',
  version: ApiVersion.V1,
})
export class AvailabilityController {
  constructor(private availabilityService: AvailabilityService) {}

  @ApiResponseSchema(CarAvailabilityResponseDto)
  @Get()
  async getAvailableCars(): Promise<CarAvailabilityResponseDto> {
    return CarAvailabilityResponseDto.from(
      await this.availabilityService.getCarAvailabilityForAllStations(),
    );
  }
}
