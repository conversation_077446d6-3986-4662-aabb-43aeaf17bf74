import { GuardOn, PermissionAction } from '@bluesg-2/bo-auth-guard';
import { ADMIN_BEARER_AUTH } from '@bluesg-2/queue-pop';
import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Logger,
  Param,
  Put,
  Query,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ApiResponseSchema } from 'src/api/api-response.decorator';
import { PageResult } from 'src/common/api/page-result';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { ResourceName } from 'src/common/constants/resource';
import { CarError } from 'src/common/errors/car.error';
import { StationError } from 'src/common/errors/station.error';
import {
  CarPlateNumber,
  StationId,
  VulogCarId,
  VulogZoneId,
} from 'src/common/tiny-types';
import { MapUtils } from 'src/common/utils/map.util';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { CarService } from 'src/logic/car/car.service';
import { Car } from 'src/logic/car/domain/car';
import { ReservationService } from 'src/logic/reservation.service';
import { StationService } from 'src/logic/station/station.service';
import { VulogFleetService } from 'src/logic/vulog/vulog-fleet-service.service';
import { VulogCarRealTimeDto } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { FleetService } from 'src/model/fleet-service';
import { RealtimeCar } from 'src/model/realtime.car';
import { Station } from 'src/model/station';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { CarInfo } from '~shared/feature-flag/models/car-info';
import { AdminCarQueryParamsDtoV1 } from './dto/request/admin.car.query.v1.dto';
import { AdminSpecificCarInfoParamDtoV1 } from './dto/request/admin.specific-car-info.param.dto.v1';
import { AdminMakeStationStickyWithCarParamDtoV1 as AdminStationStickyWithCarParamDtoV1 } from './dto/request/admin.station-sticky-with-car.param.dto.v1';
import { AdminMakeStationStickyWithCarRequestDtoV1 as AdminStationStickyWithCarRequestDtoV1 } from './dto/request/admin.station-sticky-with-car.request.dto.v1';
import { AdminCarWithMetadataResponseDtoV1 } from './dto/response/admin.car-with-metadata.response.dto.v1';
import { AdminStickyCarStationResponseDtoV1 } from './dto/response/admin.sticky-car-station.response.dto.v1';
import { EventBusService } from '~shared/event-bus/event-bus.service';
import { StationStickyWithCarEventDto } from 'src/event/station/station.car.sticky.event';
import { v4 } from 'uuid';

@ApiBearerAuth(ADMIN_BEARER_AUTH)
@ApiTags(RouteSegment.Car.Index)
@ApiBearerAuth('admin-jwt')
@Controller({
  version: ApiVersion.V1,
  path: `${RentalServiceRootRouteSegment.Admin}/${RouteSegment.Car.Index}`,
})
export class AdminCarControllerV1 {
  constructor(
    private readonly carService: CarService,
    private readonly reservationService: ReservationService,
    private readonly vulogCarService: VulogCarService,
    private readonly vulogFleetService: VulogFleetService,
    private readonly stationService: StationService,
    private readonly featureFlagService: FeatureFlagService,
    private readonly eventBusService: EventBusService,
  ) {}

  private async performValidation(
    plateNumber: string,
    stationId: string,
  ): Promise<{
    vgCarId: VulogCarId;
    station: Station;
    bsgCar: Car;
  }> {
    let bsgCar;

    try {
      bsgCar = await this.carService.getOneByPlateNumber(
        new CarPlateNumber(plateNumber),
      );
    } catch (err) {
      throw new CarError(
        `Car [Plate: ${plateNumber}] is not found`,
        HttpStatus.BAD_REQUEST,
      );
    }

    const vgCarId = bsgCar.vulogId;
    const bsgStationId = new StationId(stationId);

    const station: Station = await this.stationService.getStation(bsgStationId);

    if (!station) {
      throw new StationError(
        `Station [ID: ${stationId}] is not found`,
        HttpStatus.BAD_REQUEST,
      );
    }

    const onGoingReservation =
      await this.reservationService.getBsgOnGoingReservationByVulogCarId(
        vgCarId,
      );

    const onGoingRental =
      await this.reservationService.getBsgOnGoingRentalByVulogCarId(vgCarId);

    if (onGoingReservation || onGoingRental) {
      throw new CarError(
        `The car [Vulog Car Id: ${vgCarId.value}] is currently in use. Could not perform attaching station to car`,
        HttpStatus.BAD_REQUEST,
      );
    }

    return {
      vgCarId,
      station,
      bsgCar,
    };
  }

  private async toggleStationSticky(
    rltCar: RealtimeCar,
    station: Station,
    on: boolean,
    forceCarDisabled = false,
  ): Promise<{
    carService: FleetService;
    stickyStation: Station;
    car: RealtimeCar;
  }> {
    // const outOfServiceReason = 'Car Station Attachment/Detachment';

    // if (!rltCar.disabled) {
    //   if (forceCarDisabled) {
    //     Logger.log('[ADMIN] Force-disable car when attaching station to car', {
    //       vulogCarId: rltCar.id.value,
    //       stationId: station.id.value,
    //       forceCarDisabled,
    //     });

    //     await this.vulogCarService.disableCar(rltCar.id, outOfServiceReason);
    //   } else {
    //     throw new CarError(
    //       `The car [Vulog Car Id: ${rltCar.id.value}] is required to be disabled before performing attaching station to car`,
    //       HttpStatus.BAD_REQUEST,
    //     );
    //   }
    // }

    // Logger.log(
    //   `[ADMIN] The car [Vulog Car Id: ${rltCar.id.value}] is disabled successfully with reason: "${outOfServiceReason}"`,
    //   {
    //     disabled: true,
    //     outOfServiceReason: outOfServiceReason,
    //   },
    // );

    try {
      await this.vulogCarService.toggleStickyStationWithCar(
        rltCar.id,
        new VulogZoneId(station.zoneId),
        on,
      );
    } catch (err) {
      throw new CarError(
        'Failed to attach car at Vulog',
        HttpStatus.INTERNAL_SERVER_ERROR, // Should not return status code 502 (Bad Gateway) as BlueSG uses CloudFlare, and Cloudflare has logic to redirect customer to Cloudflare's Bad Request page. See: https://developers.cloudflare.com/support/troubleshooting/cloudflare-errors/troubleshooting-cloudflare-5xx-errors/#502504-from-your-origin-web-server
      );
    }

    rltCar = await this.vulogCarService.getSpecificCarInRealTimeDirectly(
      rltCar.id,
    );

    Logger.log(
      `[ADMIN] The result after performing ${on ? 'attachment' : 'detachment'}`,
      {
        carInfo: rltCar,
        station,
      },
    );

    const staticCar = await this.vulogCarService.getStaticCar(rltCar.id);

    Logger.log(
      `[ADMIN] Car is ${
        on ? 'sticky with' : 'detached from'
      }  station in BlueSG`,
      {
        carInfo: rltCar,
        station,
      },
    );

    const fleetServices = await this.vulogFleetService.getFleetServices(true);

    const carService = fleetServices.find((fleetSvc) =>
      fleetSvc.id.equals(staticCar.serviceId),
    );

    return {
      carService,
      car: rltCar,
      stickyStation: on ? station : null,
    };
  }

  @GuardOn({
    action: PermissionAction.Read,
    resource: ResourceName.Car,
  })
  @ApiResponseSchema(AdminCarWithMetadataResponseDtoV1, true)
  @ApiOperation({
    summary: 'Get list of cars with metadata',
  })
  @Get(RouteSegment.Car.List)
  async getCarsWithMetadata(
    @Query() queryParams: AdminCarQueryParamsDtoV1,
  ): Promise<PageResult<AdminCarWithMetadataResponseDtoV1[]>> {
    const pageResult = await this.carService.getPaginatedCars(
      queryParams.toPagination(),
      { search: queryParams?.search },
    );

    const newRealTimeCarsPlateMap: {
      [plate: string]: VulogCarRealTimeDto;
    } = await this.vulogCarService.getRealtimeCarsPlateMap();

    const fleetServices = await this.vulogFleetService.getFleetServices(true);

    const fleetServicesIdMap = MapUtils.build(
      fleetServices,
      (fltSvc) => fltSvc.id.value,
    );

    const staticCars = (
      (await this.vulogCarService.getAllStaticCars()) || []
    ).filter((stCar) => !!stCar.plate);

    const staticCarsPlateMap = MapUtils.build(
      staticCars,
      (stCar) => stCar.plate.value,
    );

    const zoneIdStationMap = await this.stationService.getZoneStationMap();

    const responses: AdminCarWithMetadataResponseDtoV1[] = await Promise.all(
      (pageResult.result || []).map(async (car: Car) => {
        const staticCar = staticCarsPlateMap.get(car.plate?.value);

        const fleetService = fleetServicesIdMap.get(
          staticCar?.serviceId?.value,
        );

        const filterOutNonCustomerZones =
          await this.featureFlagService.filterOutNonCustomerZones(
            staticCar ? CarInfo.fromStaticCar(staticCar) : null,
          );

        const realtimeCar = newRealTimeCarsPlateMap[
          car.plate?.value
        ]?.toRealtimeCar(
          staticCar.model,
          filterOutNonCustomerZones ? fleetService?.getZoneIdsAsMap() : null,
        );

        const stickyStation: Station =
          zoneIdStationMap[realtimeCar?.allowedStickyZone?.zoneId]?.toStation();

        return AdminCarWithMetadataResponseDtoV1.from(
          car,
          stickyStation,
          fleetService,
          realtimeCar,
        );
      }),
    );

    return PageResult.from(
      responses,
      pageResult.pagination,
      pageResult.total,
      pageResult.sortKey,
      pageResult.sortType,
    );
  }

  @GuardOn({
    action: PermissionAction.Read,
    resource: ResourceName.Car,
  })
  @ApiResponseSchema(AdminStickyCarStationResponseDtoV1)
  @ApiOperation({
    summary: 'Get information of specific car',
  })
  @Get(RouteSegment.Car.SpecificCarInformation)
  async getSpecificCarInformation(
    @Param() pathParams: AdminSpecificCarInfoParamDtoV1,
  ): Promise<AdminStickyCarStationResponseDtoV1> {
    const { plateNumber } = pathParams;

    let bsgCar: Car;

    try {
      bsgCar = await this.carService.getOneByPlateNumber(
        new CarPlateNumber(plateNumber),
      );
    } catch (err) {
      throw new CarError(
        `Car [Plate: ${plateNumber}] is not found`,
        HttpStatus.BAD_REQUEST,
      );
    }

    const rltCar = await this.vulogCarService.getSpecificCarInRealTimeDirectly(
      bsgCar.vulogId,
    );

    const zoneIdStationMap = await this.stationService.getZoneStationMap();

    const stickyStation: Station =
      zoneIdStationMap[rltCar.allowedStickyZone?.zoneId]?.toStation();

    const staticCar = await this.vulogCarService.getStaticCar(rltCar.id);

    const fleetServices = await this.vulogFleetService.getFleetServices(true);

    const carService = fleetServices.find((fleetSvc) =>
      fleetSvc.id.equals(staticCar.serviceId),
    );

    bsgCar.realtimeMetadata = {
      disabled: rltCar.disabled,
    };

    return AdminStickyCarStationResponseDtoV1.from(
      bsgCar,
      stickyStation,
      carService,
    );
  }

  @GuardOn({
    action: PermissionAction.Update,
    resource: ResourceName.Car,
  })
  @ApiResponseSchema(AdminStickyCarStationResponseDtoV1)
  @ApiOperation({
    summary: 'Make station sticky with car',
  })
  @Put(RouteSegment.Car.AttachStationToCar)
  async makeStationStickyWithCar(
    @Param() pathParams: AdminStationStickyWithCarParamDtoV1,
    @Body() requestDto: AdminStationStickyWithCarRequestDtoV1,
  ): Promise<AdminStickyCarStationResponseDtoV1> {
    const { plateNumber, stationId } = pathParams;

    const { forceCarDisabled } = requestDto;

    const {
      vgCarId,
      bsgCar,
      station: expectedStation,
    } = await this.performValidation(plateNumber, stationId);

    let rltCar = await this.vulogCarService.getSpecificCarInRealTimeDirectly(
      vgCarId,
    );

    const invalidStickyZones: VulogZone[] = rltCar.invalidZones?.filter(
      (zone) => zone.isSticky(),
    );

    const customerUsableFleetService: FleetService =
      await this.vulogFleetService.getCustomerUsableFleetService();

    if (
      !customerUsableFleetService.getZoneIdsAsMap().has(expectedStation.zoneId)
    ) {
      throw new CarError(
        `Expected station ${expectedStation.id.value} is not under customer fleet service. Pinning is not allowed`,
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!rltCar.allowedStickyZone && !!invalidStickyZones?.length) {
      Logger.warn(
        `Sticky zones ${invalidStickyZones.map(
          (zone) => zone.zoneId,
        )} attached with the car (${
          rltCar.plate.value
        }) are non-customer zones. There could be something wrong with our logic or forgotten manual API calls (For example, CURL, Postman). Perform detaching car from non-customer zones.`,
      );

      await Promise.all(
        invalidStickyZones.map(async (zone) => {
          await this.vulogCarService.toggleStickyStationWithCar(
            rltCar.id,
            new VulogZoneId(zone.zoneId),
            false,
          );
        }),
      ).catch(() => {
        throw new CarError(
          'Detected non-customer sticky zones, but failed to detach them from car. Please try again.',
          HttpStatus.INTERNAL_SERVER_ERROR, // Should not return status code 502 (Bad Gateway) as BlueSG uses CloudFlare, and Cloudflare has logic to redirect customer to Cloudflare's Bad Request page. See: https://developers.cloudflare.com/support/troubleshooting/cloudflare-errors/troubleshooting-cloudflare-5xx-errors/#502504-from-your-origin-web-server
        );
      });

      rltCar = await this.vulogCarService.getSpecificCarInRealTimeDirectly(
        rltCar.id,
      );
    }

    if (rltCar.allowedStickyZone) {
      throw new CarError(
        `The car [Vulog Car ID: ${vgCarId.value}] is currently attached with station [Station ID: ${stationId}]. Please detach it from current station before station attachment.`,
        HttpStatus.BAD_REQUEST,
      );
    }

    const { carService, stickyStation } = await this.toggleStationSticky(
      rltCar,
      expectedStation,
      true,
      forceCarDisabled,
    );

    this.eventBusService.nonBlockingEmit(
      new StationStickyWithCarEventDto(
        {
          carId: vgCarId.value,
          stationId: expectedStation.id.value,
          plateNumber: plateNumber,
          isSticky: true,
        },
        v4(),
      ),
    );

    return AdminStickyCarStationResponseDtoV1.from(
      bsgCar,
      stickyStation,
      carService,
    );
  }

  @GuardOn({
    action: PermissionAction.Update,
    resource: ResourceName.Car,
  })
  @ApiResponseSchema(AdminStickyCarStationResponseDtoV1)
  @ApiOperation({
    summary: 'Remove sticky station from car',
  })
  @Put(RouteSegment.Car.DetachStationFromCar)
  async removeStickyStationFromCar(
    @Param() pathParams: AdminStationStickyWithCarParamDtoV1,
    @Body() requestDto: AdminStationStickyWithCarRequestDtoV1,
  ): Promise<AdminStickyCarStationResponseDtoV1> {
    const { plateNumber, stationId } = pathParams;

    const { forceCarDisabled } = requestDto;

    const { vgCarId, bsgCar, station } = await this.performValidation(
      plateNumber,
      stationId,
    );

    const rltCar = await this.vulogCarService.getSpecificCarInRealTimeDirectly(
      vgCarId,
    );

    if (!rltCar.allowedStickyZone) {
      throw new CarError(
        `The car [Vulog Car ID: ${vgCarId.value}] is currently not attached to any station. No further action needed.`,
        HttpStatus.BAD_REQUEST,
      );
    }

    const currentAttachedStation = await this.stationService.getStationByZones([
      rltCar.allowedStickyZone,
    ]);

    if (currentAttachedStation?.id?.value !== stationId) {
      throw new CarError(
        `The input station [Station ID: ${stationId}] must be equal to current attached station [Station ID: ${currentAttachedStation.id.value}]`,
        HttpStatus.BAD_REQUEST,
      );
    }

    const { carService, stickyStation } = await this.toggleStationSticky(
      rltCar,
      station,
      false,
      forceCarDisabled,
    );

    this.eventBusService.nonBlockingEmit(
      new StationStickyWithCarEventDto(
        {
          carId: vgCarId.value,
          stationId: station.id.value,
          plateNumber: plateNumber,
          isSticky: false,
        },
        v4(),
      ),
    );

    return AdminStickyCarStationResponseDtoV1.from(
      bsgCar,
      stickyStation,
      carService,
    );
  }
}
