import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';

export class AdminMakeStationStickyWithCarParamDtoV1 {
  @ApiProperty({
    type: 'string',
    example: 'SLV12345L',
    required: true,
  })
  @IsNotEmpty()
  plateNumber: string;

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  stationId: string;
}
