import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { BasePaginationRequestDto } from 'src/common/api/base.pagination.request.dto';
import { QueryPagination } from 'src/common/api/pagination';

export type CarQuerySortKey = 'id' | 'plateNumber' | 'vin' | 'category';

export const CarQuerySortKey: CarQuerySortKey[] = [
  'id',
  'plateNumber',
  'vin',
  'category',
];

export type AdminCarQueryPaginationV1 = QueryPagination & {
  sortKey?: CarQuerySortKey;
};

export class AdminCarQueryParamsDtoV1 extends BasePaginationRequestDto {
  @ApiProperty({
    type: 'enum',
    enum: CarQuerySortKey,
    default: 'plateNumber',
    required: false,
  })
  @IsEnum(CarQuerySortKey)
  @IsOptional()
  sortKey?: CarQuerySortKey = 'plateNumber';

  @ApiProperty({
    type: 'string',
    required: false,
  })
  @IsString()
  @IsOptional()
  search?: string;

  toPagination(): AdminCarQueryPaginationV1 {
    return super.toPagination() as AdminCarQueryPaginationV1;
  }
}
