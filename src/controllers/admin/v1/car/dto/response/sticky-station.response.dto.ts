import { faker } from '@faker-js/faker';
import { ApiProperty } from '@nestjs/swagger';
import { Station } from 'src/model/station';
import { Md5 } from 'ts-md5';

export class StickyStationResponseDto {
  constructor(props: StickyStationResponseDto) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  public id: string;

  @ApiProperty({
    type: 'string',
  })
  public sourceId: string;

  @ApiProperty({
    type: 'string',
    example: 'Bukit Merah Avenue/Block 2A/Park 2C',
    required: true,
  })
  public displayName: string;

  @ApiProperty({
    type: 'string',
    example: '23.424076',
    required: true,
  })
  public latitude: string;

  @ApiProperty({ type: 'string', example: '53.847818', required: true })
  public longitude: string;

  @ApiProperty({ type: 'string' })
  public street: string;

  @ApiProperty({ type: 'string' })
  public postalCode: string;

  @ApiProperty({
    type: 'string',
    example: Md5.hashStr(faker.string.alphanumeric()),
    required: false,
  })
  public zoneId?: string;

  static from(station: Station) {
    return new StickyStationResponseDto({
      id: station.id.value,
      sourceId: station?.sourceId,
      displayName: station.displayName,
      latitude: station.latitude?.toString(),
      longitude: station.longitude?.toString(),
      street: station.street,
      postalCode: station.postalCode,
      zoneId: station.zoneId,
    });
  }
}
