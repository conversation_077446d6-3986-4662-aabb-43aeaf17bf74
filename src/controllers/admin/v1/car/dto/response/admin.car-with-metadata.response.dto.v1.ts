import { ApiProperty } from '@nestjs/swagger';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { Car } from 'src/logic/car/domain/car';
import { FleetService } from 'src/model/fleet-service';
import { RealtimeCar } from 'src/model/realtime.car';
import { Station } from 'src/model/station';
import { StickyServiceResponseDto } from './sticky-service.response.dto';
import { StickyStationResponseDto } from './sticky-station.response.dto';

export class AdminCarWithMetadataResponseDtoV1 {
  constructor(props: AdminCarWithMetadataResponseDtoV1) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    description: 'The UUID of the car',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    type: 'string',
    example: 'Available',
    description: 'The status in descriptive text format',
  })
  status: string;

  @ApiProperty({
    type: 'string',
    description: 'The Vulog UUID of the car',
    format: 'uuid',
  })
  vulogCarId: string;

  @ApiProperty({
    type: 'string',
    description: 'The plate number of the car',
    example: 'SVX20543',
  })
  plateNumber: string;

  @ApiProperty({
    type: 'string',
    description: 'The vin of the car',
    example: 'VIN0312345234',
  })
  vin: string;

  @ApiProperty({
    type: 'string',
    enum: CarModelEnum,
    description: 'The model of the car',
  })
  category: CarModelEnum;

  @ApiProperty({
    type: 'boolean',
    description: 'Car is disabled. In other words, "Out Of Service" in Vulog',
  })
  disabled: boolean;

  @ApiProperty({
    type: StickyStationResponseDto,
    description: 'Current station that the car is sticky with',
    required: false,
  })
  stickyStation?: StickyStationResponseDto;

  @ApiProperty({
    type: StickyServiceResponseDto,
    description: 'Current service that the car is belonged to',
    required: false,
  })
  service?: StickyServiceResponseDto;

  static from(
    car: Car,
    station: Station,
    service: FleetService,
    realtimeCar: RealtimeCar,
  ): AdminCarWithMetadataResponseDtoV1 {
    const { id, plate, vin, model, vulogId, realtimeMetadata } = car;

    return {
      id: id.value,
      status: realtimeCar?.statusLabel,
      vulogCarId: vulogId.value,
      plateNumber: plate.value,
      vin: vin,
      category: model,
      disabled: realtimeMetadata?.disabled,
      stickyStation: station ? StickyStationResponseDto.from(station) : null,
      service: service ? StickyServiceResponseDto.from(service) : null,
    };
  }
}
