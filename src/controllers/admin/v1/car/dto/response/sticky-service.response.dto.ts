import { ApiProperty } from '@nestjs/swagger';
import {
  VulogFleetServiceStatus,
  VulogFleetServiceType,
} from 'src/model/dtos/vulog-fleet-service/vulog-fleet-service.dto';
import { FleetService } from 'src/model/fleet-service';

export class StickyServiceResponseDto {
  constructor(props: StickyServiceResponseDto) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  id: string;

  @ApiProperty({
    type: 'string',
    required: true,
  })
  name: string;

  @ApiProperty({
    type: 'string',
    enum: VulogFleetServiceStatus,
    required: true,
  })
  status: VulogFleetServiceStatus;

  @ApiProperty({
    type: 'string',
    enum: VulogFleetServiceType,
    required: true,
  })
  type: VulogFleetServiceType;

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  fleetId: string;

  static from(fleetService: FleetService) {
    return new StickyServiceResponseDto({
      id: fleetService.id.value,
      name: fleetService.name,
      status: fleetService.status,
      type: fleetService.type,
      fleetId: fleetService.fleetId.value,
    });
  }
}
