import {
  <PERSON><PERSON><PERSON><PERSON>,
  GuardOn,
  PermissionAction,
  TokenPayloadV1,
} from '@bluesg-2/bo-auth-guard';
import { ADMIN_BEARER_AUTH } from '@bluesg-2/queue-pop';
import { Body, Controller, Logger, Param, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { DateTime } from 'luxon';
import { ApiResponseSchema } from 'src/api/api-response.decorator';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { ResourceName } from 'src/common/constants/resource';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { PackageNotAvailableError } from 'src/common/errors/external/package-not-available.error';
import { StationNotFoundError } from 'src/common/errors/external/station-not-found.error';
import { InternalError } from 'src/common/errors/internal-error';
import {
  BsgUserId,
  CarId,
  ReservationId,
  StationId,
} from 'src/common/tiny-types';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { BillingExternalService } from 'src/external/billing/billing.external.service';
import { InternalCheckRentalPackageAvailableResponseDtoV1 } from 'src/external/billing/dtos/response/internal.check-rental-package-available.v1.response.dto';
import { UserSubscriptionExternalService } from 'src/external/user-subscription/user-subscription.external.service';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { CarService } from 'src/logic/car/car.service';
import { Car } from 'src/logic/car/domain/car';
import { CarNotFoundError } from 'src/logic/car/errors/car-not-found.error';
import { CarNotMatchingExpectedModelError } from 'src/logic/car/errors/car-not-matching-model.error';
import { RentalService } from 'src/logic/rental.service';
import { ReservationService } from 'src/logic/reservation.service';
import { StationService } from 'src/logic/station/station.service';
import { VulogTelemetryService } from 'src/logic/telemetry-repository/vulog/vulog-telemetry.service';
import { VulogCarConnectionProblemService } from 'src/logic/vulog/vulog-car-connection-problem.service';
import { RealtimeCar } from 'src/model/realtime.car';
import { RentalInfo } from 'src/model/rental-info';
import { ReservationRepository } from 'src/model/repositories/reservation.repository';
import { Reservation } from 'src/model/reservation.domain';
import { StartRental } from 'src/model/start-rental';
import { Station } from 'src/model/station';
import { NewRelicMetricStepInFlow } from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { AdminCustomerIdParamDtoV1 } from './dto/request/admin.customer-id.param.dto.v1';
import { AdminStartRentalImmediatelyRequestDtoV1 } from './dto/request/admin.start-rental-immediately.request.dto.v1';
import { AdminStartRentalImmediatelyResponseDtoV1 } from './dto/response/admin.start-rental-immediately.response.dto.v1';

@ApiBearerAuth(ADMIN_BEARER_AUTH)
@ApiTags(RouteSegment.Customer.Index)
@ApiBearerAuth('admin-jwt')
@Controller({
  version: ApiVersion.V1,
  path: `${RentalServiceRootRouteSegment.Admin}/${RouteSegment.Customer.Index}`,
})
export class AdminCustomerControllerV1 {
  constructor(
    private readonly userSubscriptionExternalService: UserSubscriptionExternalService,
    private readonly carService: CarService,
    private readonly vulogCarService: VulogCarService,
    private readonly billingExternalService: BillingExternalService,
    private readonly stationService: StationService,
    private readonly reservationService: ReservationService,
    private readonly rentalService: RentalService,
    private readonly vulogCarConnectionProblemService: VulogCarConnectionProblemService,
    private readonly vulogTelemetryService: VulogTelemetryService,
    private readonly reservationRepository: ReservationRepository,
  ) {}

  @GuardOn({
    resource: ResourceName.Rental,
    action: PermissionAction.Create,
  })
  @ApiResponseSchema(AdminStartRentalImmediatelyResponseDtoV1)
  @ApiOperation({
    summary: 'Start rental immediately on behalf of customer',
  })
  @Post(RouteSegment.Customer.StartRental)
  async startRentalImmediately(
    @Param() params: AdminCustomerIdParamDtoV1,
    @Body() requestDto: AdminStartRentalImmediatelyRequestDtoV1,
    @AdminUser() staffInfo: TokenPayloadV1,
  ): Promise<AdminStartRentalImmediatelyResponseDtoV1> {
    const { customerId } = params;

    const bsgUserId: BsgUserId = new BsgUserId(customerId);

    const { carId: targetBsgCarId, stationId, rentalPackage } = requestDto;

    let reservation: Reservation;

    const commonQueryDimensions = {
      feature: 'advance-reservation',
      backOfficeUserId: staffInfo.userId,
      bsgUserId: bsgUserId.value,
      requestDto,
    };

    function logError(message: string, context: object): void {
      Logger.error(`❌ ${message}`, {
        ...commonQueryDimensions,
        ...context,
      });
    }

    function logSuccess(message: string, context: object): void {
      Logger.log(`✅ ${message}`, {
        ...commonQueryDimensions,
        ...context,
      });
    }

    function logWarning(message: string, context: object): void {
      Logger.warn(`⚠️ ${message}`, {
        ...commonQueryDimensions,
        ...context,
      });
    }

    let targetCar: Car;

    try {
      // Validate customer constraints
      await this.userSubscriptionExternalService.validateUssUser(bsgUserId);

      // Validate if the car is existing in database (throw inside the function call)
      targetCar = await this.carService.getOneById(new CarId(targetBsgCarId));

      // The POC is only for "Opel Corsa-e"
      if (targetCar.model !== CarModelEnum.OpelCorsaE) {
        throw new CarNotMatchingExpectedModelError(
          `The car (ID: ${targetCar.id.value}) does not match the expected car model (${CarModelEnum.OpelCorsaE})`,
        );
      }

      const realtimeCar: RealtimeCar =
        await this.vulogCarService.getSpecificCarInRealtime(
          targetCar.vulogId,
          true,
        );

      // Check if expected car is available for booking on Vulog
      if (!realtimeCar) {
        throw new CarNotFoundError(
          `The expected car (Vulog ID: ${targetCar.vulogId.value}, Car ID: ${targetCar.id.value}) is not available for booking`,
        );
      }

      // Derived from "ReservationService.attachRentalPackage" function
      const time = DateTime.now()
        .plus({
          minutes: 5,
        })
        .toISO();

      const queryParams = {
        ...rentalPackage,
        time,
        carModel: targetCar.model,
      };

      // Check if expected rental package is available
      const checkPackageRes: InternalCheckRentalPackageAvailableResponseDtoV1 =
        await this.billingExternalService.internalApi.checkPackageAvailable(
          queryParams,
        );

      if (!checkPackageRes.isAvailable) {
        logError('Package is not available', {
          checkRentalPackageAvailableParams: queryParams,
        });

        throw new PackageNotAvailableError();
      }

      const station: Station = await this.stationService.getStation(
        new StationId(stationId),
      );

      if (!station) {
        logError('Station is not found in database', {
          stationId,
        });

        throw new StationNotFoundError();
      }

      try {
        reservation = await this.reservationService.allocateCar(
          station.id.value,
          bsgUserId,
          targetCar.model,
          false,
          staffInfo.userId,
        );
      } catch (err) {
        logError('Failed at "Allocate Car" step', {
          error: err,
        });

        throw err;
      }

      logSuccess('"Allocate Car" step is executed successfully', {
        newBookedReservation: reservation,
      });

      let switchedReservation: Reservation;

      const currentAllocatedCar = await this.carService.getOneByVulogId(
        reservation.carId,
      );

      // If the automatically-allocated car is not the expected one
      if (reservation.carId.value !== targetCar.vulogId.value) {
        try {
          logWarning(
            'The allocated car is not tally with the expected car, trying to switch to the expected one...',
            { targetCar, currentAllocatedCar },
          );

          switchedReservation =
            await this.reservationService.switchAllocationCar(
              bsgUserId,
              reservation,
              targetCar.vulogId,
              true,
            );
        } catch (err) {
          logError('Failed at "Switch Car" step', { error: err, reservation });

          throw err;
        }
      }

      if (!!switchedReservation) {
        logSuccess('"Switch Car" step is executed successfully', {
          switchedReservation,
        });
      }

      reservation = switchedReservation || reservation;

      // Attach the rental package to the reservation
      try {
        reservation = await this.reservationService.attachRentalPackage(
          reservation,
          rentalPackage,
        );
      } catch (err) {
        logError('Failed at "Attach Rental Package" step', {
          error: err,
          reservation,
        });

        throw err;
      }

      logSuccess('"Attach Rental Package" step is executed successfully', {
        attachedRentalPackageReservation: reservation,
      });

      // Start rental in BlueSG
      let startedBlueSGRental: StartRental;

      try {
        startedBlueSGRental = await this.rentalService.startRental(
          reservation,
          bsgUserId,
        );
      } catch (err) {
        logError('Failed at "Start Rental in BlueSG" step', {
          error: err,
          reservation,
        });

        throw err;
      }

      logSuccess('"Start Rental in BlueSG" step is executed successfully', {
        rental: startedBlueSGRental,
      });

      // Unlock the car on Vulog Vehicle Gateway
      try {
        await this.vulogCarConnectionProblemService.retryWhenEncounterCarConnectionProblem<void>(
          {
            vulogCarId: targetCar.vulogId,
            callback: async () => {
              await this.vulogTelemetryService.unlockAndMobilizeCar(
                targetCar.vulogId,
              );
            },
            step: NewRelicMetricStepInFlow.UnlockAndMobilize,
          },
        );
      } catch (err) {
        logError('Failed at "Unlock Car" step', {
          error: err,
          startedBlueSGRental,
        });

        throw err;
      }

      logSuccess('"Unlock Car" step is executed successfully', {
        startedBlueSGRental,
      });

      // Start Rental on Vulog
      let rentalInfo: RentalInfo;

      try {
        rentalInfo = await this.vulogCarService.startRental(
          reservation.bsgUserId,
          reservation.vulogTripId,
        );
      } catch (err) {
        logError('Failed at "Start Rental on Vulog" step', {
          error: err,
          startedBlueSGRental,
        });

        throw err;
      }

      logSuccess('"Start Rental on Vulog" step is executed successfully', {
        rentalInfo,
      });

      reservation = await this.reservationService.getOne(
        new ReservationId(reservation.getId),
      );

      logSuccess(
        'Starting rental immediately on behalf of customer executed successfully!',
        {
          reservation,
        },
      );

      return AdminStartRentalImmediatelyResponseDtoV1.from(reservation, {
        packageId: rentalPackage.packageId,
        isDynamic: checkPackageRes.isDynamic,
      });
    } catch (error) {
      if (!!reservation) {
        logWarning(
          '[Error Mitigation] Cancelling reservation and terminating Vulog journey/trip...',
          { reservation },
        );

        try {
          reservation = await this.reservationRepository.updateStatus(
            reservation,
            ReservationStatus.Cancelled,
            [
              ReservationStatus.Reserved,
              ReservationStatus.Converted,
              ReservationStatus.Cancelled, // Canceled in "ReservationService.switchAllocationCar"
            ],
            {
              canceledAt: VulogDate.now(),
              deletedBy: `Cancelled by back office staff ${staffInfo.userId} because failed to start rental immediately on behalf of customer`,
            },
          );
        } catch (error) {
          logWarning(
            '❌ [Error Mitigation] Failed to cancel reservation. Requires engineers to perform manual intervention',
            { error, reservation },
          );

          throw new InternalError(
            'MITIGATION_FAILED_TO_CANCEL_RESERVATION_ERROR',
            `Failed to cancel reservation ${reservation.getId}. Requires engineers to perform manual intervention`,
          );
        }

        logWarning('✅ [Error Mitigation] Cancelled reservation successfully', {
          error,
          reservation,
        });

        try {
          await this.vulogCarService.terminateTrip(targetCar.vulogId);
        } catch (error) {
          logWarning(
            '❌ [Error Mitigation] Failed to terminate Vulog journey/trip. Requires CRC to manually terminate on Vulog AiMA',
            {
              error,
              reservation,
            },
          );

          throw new InternalError(
            'MITIGATION_FAILED_TO_TERMINATE_VULOG_JOURNEY_TRIP_ERROR',
            `Failed to terminate Vulog journey/trip ${reservation.vulogTripId.value}. Requires CRC to manually terminate on Vulog AiMA`,
          );
        }

        logWarning(
          '✅ [Error Mitigation] Terminated Vulog journey/trip successfully',
          {
            reservation,
          },
        );
      }

      logWarning('Throw error (reach end line of code)', {
        error,
        reservation,
      });

      throw error;
    }
  }
}
