import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsObject, IsUUID, ValidateNested } from 'class-validator';
import { AttachRentalPackageRequestDtoV1 } from 'src/controllers/dtos/rental/request/attach-rental-package.v1.request.dto';

export class AdminStartRentalImmediatelyRequestDtoV1 {
  @ApiProperty({
    type: 'string',
    format: 'uuid',
    description: 'BlueSG Car ID',
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  carId: string;

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    description: 'BlueSG Station ID',
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  stationId: string;

  @ApiProperty({
    type: AttachRentalPackageRequestDtoV1,
    description:
      'Rental package information, these parameters are used to find the package and attach it with the reservation',
    required: true,
  })
  @ValidateNested()
  @IsObject()
  @Type(() => AttachRentalPackageRequestDtoV1)
  rentalPackage: AttachRentalPackageRequestDtoV1;
}
