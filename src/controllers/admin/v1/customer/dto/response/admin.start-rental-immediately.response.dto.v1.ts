import { ApiProperty } from '@nestjs/swagger';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { Reservation } from 'src/model/reservation.domain';

export class AdminStartRentalImmediatelyResponseDtoV1 {
  constructor(props: AdminStartRentalImmediatelyResponseDtoV1) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    description: 'The UUID of the rental',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    type: 'string',
    description: 'The UUID of the station where the car started at',
    format: 'uuid',
  })
  startStationId: string;

  @ApiProperty({
    type: 'string',
    format: 'date-time',
    description: 'The timestamp when the rental started',
  })
  startedAt: string;

  @ApiProperty({
    type: 'string',
    description: 'Car model of the rental',
    example: CarModelEnum.OpelCorsaE,
  })
  carCategory: string;

  @ApiProperty({
    type: 'string',
    description: 'The UUID of the BlueSG car',
    format: 'uuid',
  })
  carId: string;

  @ApiProperty({
    type: 'string',
    description: 'The UUID of the BlueSG user',
    format: 'uuid',
  })
  userId: string;

  @ApiProperty({
    type: 'string',
    description: 'The UUID of the rental package',
    format: 'uuid',
  })
  rentalPackageId: string;

  @ApiProperty({
    type: 'string',
    description: 'The UUID of the rental package dynamic (if present)',
    format: 'uuid',
  })
  rentalPackageDynamicId: string;

  static from(
    reservation: Reservation,
    rentalPackage: { packageId: string; isDynamic: boolean },
  ): AdminStartRentalImmediatelyResponseDtoV1 {
    const isRentalPackageDynamicAvailable: boolean = rentalPackage.isDynamic;

    return new AdminStartRentalImmediatelyResponseDtoV1({
      id: reservation.getId,
      startStationId: reservation.startStationId.value,
      startedAt: reservation.startedAt.toDateTime().toUTC().toISO(),
      carCategory: reservation.carModel,
      carId: reservation.carId.value,
      userId: reservation.bsgUserId.value,
      rentalPackageId: !isRentalPackageDynamicAvailable
        ? rentalPackage.packageId
        : null,
      rentalPackageDynamicId: isRentalPackageDynamicAvailable
        ? rentalPackage.packageId
        : null,
    });
  }
}
