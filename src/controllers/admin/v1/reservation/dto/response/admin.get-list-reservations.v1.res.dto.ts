import { ApiProperty } from '@nestjs/swagger';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { config as AppConfig } from 'src/config';
import { SubscriptionUssDto } from 'src/external/user-subscription/dtos/response/subscription.uss.dto';
import { CarDescription } from 'src/logic/car/domain/car';
import { Reservation } from 'src/model/reservation.domain';
import { Station, StationDescription } from 'src/model/station';

export class AdminGetListReservationResDtoV1 {
  @ApiProperty({
    description: 'Reservation id',
    required: true,
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Car info',
  })
  car: CarDescription;

  @ApiProperty({
    description: 'Reservation status',
  })
  status: ReservationStatus;

  @ApiProperty({
    description: 'BlueSG user',
  })
  userId: string;

  @ApiProperty({
    description: 'Link to vulog center',
  })
  vulog: {
    trip: string;
    user: string;
  };

  @ApiProperty({
    description: 'BlueSG user id',
  })
  bsgUserId: string;

  @ApiProperty({
    description: 'Bsg user full name',
  })
  bsgUserFullName: string;

  @ApiProperty({
    description: 'Subscription detail of the user',
  })
  subscription: SubscriptionUssDto;

  @ApiProperty()
  rentalStartTime: string;

  @ApiProperty()
  rentalEndTime: string;

  @ApiProperty({
    description: 'Rental duration, unit is minute',
  })
  rentalDuration: number;

  @ApiProperty()
  startStation: StationDescription;

  @ApiProperty()
  endStation: StationDescription;

  @ApiProperty({
    description: 'Rental package information',
  })
  rentalPackageInfo: unknown;

  @ApiProperty({
    description: 'Vulog trip id',
  })
  vulogTripId: string;

  static from(
    reserves: Reservation[],
    stationMap?: Map<string, Station>,
    subs?: SubscriptionUssDto[],
  ): AdminGetListReservationResDtoV1[] {
    const subMap = new Map<string, SubscriptionUssDto>();
    subs?.forEach((sub) => {
      subMap.set(sub.userId, sub);
    });

    return reserves.map((reserve) => {
      const newRes = new AdminGetListReservationResDtoV1();
      newRes.vulog = {
        user: reserve?.vulogUser?.id
          ? `${AppConfig.vulogConfig.vulogCenterUrl}/users/${reserve.vulogUser.id.value}`
          : null,
        trip: null,
      };
      newRes.car = reserve.car?.getPropsCopy();
      newRes.id = reserve.getId;
      newRes.status = reserve.status;
      newRes.userId = reserve.vulogUser?.id?.value || null;
      newRes.bsgUserId = reserve.bsgUserId?.value || null;
      newRes.rentalPackageInfo = reserve.rentalPackageData;
      newRes.rentalDuration = reserve.getBilledDuration();
      newRes.rentalStartTime = reserve.startedAt?.value || null;
      newRes.rentalEndTime = reserve.endedAt?.value || null;
      newRes.vulogTripId = reserve.vulogTripId?.value || null;

      newRes.endStation = reserve.endStationId
        ? stationMap.get(reserve.endStationId.value)?.toObject() || null
        : null;
      newRes.startStation = reserve.startStationId
        ? stationMap.get(reserve.startStationId.value)?.toObject() || null
        : null;
      newRes.subscription = reserve.bsgUserId?.value
        ? subMap.get(reserve.bsgUserId.value) || null
        : null;
      newRes.bsgUserFullName = newRes.subscription?.user
        ? `${newRes.subscription?.user.firstName} ${newRes.subscription?.user.lastName}`
        : null;

      if (reserve.vulogTripId) {
        if (
          [ReservationStatus.Reserved, ReservationStatus.Converted].includes(
            newRes.status,
          )
        ) {
          newRes.vulog.trip = `${AppConfig.vulogConfig.vulogCenterUrl}/trips/ongoing/${reserve.vulogTripId.value}`;
        } else if (
          [
            ReservationStatus.EndTrip,
            ReservationStatus.EndedWaitingForInvoicing,
            ReservationStatus.Ended,
          ].includes(newRes.status)
        ) {
          newRes.vulog.trip = `${AppConfig.vulogConfig.vulogCenterUrl}/trips/completed/${reserve.vulogTripId.value}`;
        }
      }

      if (newRes.subscription?.user) delete newRes.subscription.user;

      return newRes;
    });
  }
}
