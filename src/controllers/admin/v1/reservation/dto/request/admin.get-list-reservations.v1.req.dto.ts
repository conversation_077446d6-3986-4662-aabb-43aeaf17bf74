import { PaginationSortingOrder } from '@bluesg-2/queue-pop/dist/constants/pagination';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsISO8601,
  IsOptional,
  IsUUID,
  <PERSON><PERSON><PERSON>th,
  <PERSON><PERSON><PERSON>th,
} from 'class-validator';
import { BasePaginationRequestDto } from 'src/common/api/base.pagination.request.dto';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ToBoolean } from 'src/common/transformers/to-boolean.transformer';
import { Trim } from 'src/common/transformers/trim.transformer';

type AdminGetListReservationReqDtoV1SortKey =
  | 'createdAt'
  | 'startedAt'
  | 'endedAt';
const sortKeys: AdminGetListReservationReqDtoV1SortKey[] = [
  'createdAt',
  'startedAt',
  'endedAt',
];

export class AdminGetListReservationReqDtoV1 extends BasePaginationRequestDto {
  @ApiProperty({
    type: 'string',
    required: false,
    enum: sortKeys,
    default: 'startedAt',
  })
  @IsOptional()
  sortKey?: string = 'startedAt';

  @ApiProperty({
    type: 'enum',
    enum: PaginationSortingOrder,
    default: PaginationSortingOrder.DESCENDING,
    required: false,
  })
  @IsEnum(PaginationSortingOrder)
  @IsOptional()
  sortType?: PaginationSortingOrder = PaginationSortingOrder.DESCENDING;

  @ApiProperty({
    description:
      'CREATING, RESERVED, FAILED, CANCELLED, CONVERTED, ENDED_WAITING_FOR_INVOICING, END_TRIP,EXPIRED,ENDED ',
    required: false,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(ReservationStatus, {
    each: true,
  })
  status?: ReservationStatus[];

  @ApiProperty({
    description: 'Blue SG User Id',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  bsgUserId?: string;

  @ApiProperty({
    description: 'User with CreatedAt >= from will be valid',
    required: false,
  })
  @IsOptional()
  @IsISO8601()
  from?: Date;

  @ApiProperty({
    description: 'User with CreatedAt <= to will be valid',
    required: false,
  })
  @IsOptional()
  @IsISO8601()
  to?: Date;

  @ApiProperty({
    description: 'BlueSG Rental Id, not Vulog rental ID',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  rentalId?: string;

  @ApiProperty({
    description: 'Name or part of name of the station',
    required: false,
  })
  @IsOptional()
  startStationName?: string;

  @ApiProperty({
    description: 'Name or part of name of the station',
    required: false,
  })
  @IsOptional()
  endStationName?: string;

  @ApiProperty({
    description: 'Load user info, subscription or not',
    required: false,
    type: 'boolean',
  })
  @ToBoolean()
  @IsOptional()
  @IsBoolean()
  loadUserAndSubscription?: boolean;

  @ApiProperty({
    description: 'Load station or not',
    required: false,
    type: 'boolean',
  })
  @ToBoolean()
  @IsOptional()
  @IsBoolean()
  loadStation?: boolean;

  @ApiProperty({
    description: 'Nearly match plate number',
    required: false,
    type: 'string',
  })
  @Trim()
  @IsOptional()
  plateNumber?: string;

  @ApiProperty({
    description: 'Vulog trip id',
    required: false,
    type: 'string',
  })
  @Trim()
  @IsOptional()
  vulogTripId?: string;

  @ApiProperty({
    description: 'Nearly match customer full name',
    required: false,
    type: 'string',
  })
  @MaxLength(255)
  @MinLength(1)
  @Trim()
  @IsOptional()
  userFullName?: string;

  @ApiProperty({
    description: 'Nearly match customer email',
    required: false,
    type: 'string',
  })
  @MaxLength(320)
  @MinLength(1)
  @Trim()
  @IsOptional()
  userEmail?: string;
}
