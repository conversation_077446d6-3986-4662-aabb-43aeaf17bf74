import { GuardOn, PermissionAction } from '@bluesg-2/bo-auth-guard';
import { Controller, Get, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ApiResponseSchema } from 'src/api/api-response.decorator';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { ResourceName } from 'src/common/constants/resource';
import { SupaPagination } from 'src/common/utils/supa-pagination.util';
import { SubscriptionStatus } from 'src/external/user-subscription/constants';
import { InternalGetListSubscriptionReqDescriptionV1 } from 'src/external/user-subscription/dtos/request/internal.get-list-subscriptions.v1.req.dto';
import { SubscriptionUssDto } from 'src/external/user-subscription/dtos/response/subscription.uss.dto';
import { UserSubscriptionExternalService } from 'src/external/user-subscription/user-subscription.external.service';
import { ReservationService } from 'src/logic/reservation.service';
import { StationService } from 'src/logic/station/station.service';
import { Station } from 'src/model/station';
import { AdminGetListReservationReqDtoV1 } from './dto/request/admin.get-list-reservations.v1.req.dto';
import { AdminGetListReservationResDtoV1 } from './dto/response/admin.get-list-reservations.v1.res.dto';

@ApiTags(RouteSegment.Reservation.Index)
@ApiBearerAuth('admin-jwt')
@Controller({
  version: ApiVersion.V1,
  path: `${RentalServiceRootRouteSegment.Admin}/${RouteSegment.Reservation.Index}`,
})
export class AdminReservationControllerV1 {
  constructor(
    private readonly reservationService: ReservationService,
    private readonly ussService: UserSubscriptionExternalService,
    private readonly stationService: StationService,
  ) {}

  @GuardOn({
    action: PermissionAction.Read,
    resource: ResourceName.Reservation,
  })
  @ApiResponseSchema(AdminGetListReservationResDtoV1, true)
  @ApiOperation({
    description: '/api/v1/admin/reservations',
    summary: 'Get list reservation for admin',
  })
  @Get(RouteSegment.Reservation.List)
  async getListReservation(@Query() dto: AdminGetListReservationReqDtoV1) {
    let relatedSubs: SubscriptionUssDto[] = [];
    let stationLists: Map<string, Station> = new Map<string, Station>();

    const [result, total] =
      await this.reservationService.getListReservationForAdmin(dto);

    if (dto.loadUserAndSubscription) {
      relatedSubs = await this.ussService.getListSubscriptions(
        new InternalGetListSubscriptionReqDescriptionV1({
          userIds: result.map((reserve) => reserve.bsgUserId.value || null),
          status: [SubscriptionStatus.ACTIVE],
        }),
      );
    }

    if (dto.loadStation) {
      stationLists = await this.stationService.getStationsMap();
    }

    return BaseResponseDto.success(
      AdminGetListReservationResDtoV1.from(result, stationLists, relatedSubs),
      SupaPagination.fromBasePaginationRequest(dto).toPaginationResponse(total),
    );
  }
}
