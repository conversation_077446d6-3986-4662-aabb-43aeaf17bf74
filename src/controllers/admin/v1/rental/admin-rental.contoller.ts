import { ApiResponseSchema } from 'src/api/api-response.decorator';
import { ApiVersion } from 'src/common/constants/api-versions';
import { ResourceName } from '../../../../common/constants/resource';

import { GuardOn, PermissionAction } from '@bluesg-2/bo-auth-guard';
import { Controller, Get, Param } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';

import { ReservationId } from 'src/common/tiny-types';
import { AdminReservationResponseDtoV1 } from 'src/controllers/dtos/admin.reservation.response.v1.dto';
import { RentalService } from 'src/logic/rental.service';
import { RentalListResponseDto } from '../../../dtos/rental-list.response.dto';
import { ValidateUUIDParamDto } from '../../../dtos/rental/request/validate-uuid-param.dto';

@ApiTags('admin-rental')
@ApiBearerAuth('admin-jwt')
@Controller({
  path: 'admin/rental',
  version: ApiVersion.V1,
})
export class AdminRentalController {
  constructor(private rentalService: RentalService) {}

  @ApiResponseSchema(RentalListResponseDto)
  @Get('all-finished')
  async getAllFinishedRentals(): Promise<RentalListResponseDto> {
    const ongoingRentals =
      await this.rentalService.adminGetAllFinishedRentals();

    return RentalListResponseDto.from(ongoingRentals);
  }

  @ApiResponseSchema(AdminReservationResponseDtoV1)
  @Get(':id')
  @GuardOn({
    action: PermissionAction.Read,
    resource: ResourceName.Rental,
  })
  @ApiOperation({
    summary: 'For admin to get a rental by reservation_id',
  })
  async getOneRentalById(
    @Param() dto: ValidateUUIDParamDto,
  ): Promise<AdminReservationResponseDtoV1> {
    const rentalId = new ReservationId(dto.id);
    const result = await this.rentalService.getDetailRentalById(rentalId);
    return AdminReservationResponseDtoV1.fromReservation(result);
  }
}
