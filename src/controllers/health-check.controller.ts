import { Controller, Get, VERSION_NEUTRAL } from '@nestjs/common';
import {
  HealthCheck,
  HealthCheckResult,
  HealthCheckService,
  HealthIndicatorResult,
  MicroserviceHealthIndicator,
  TypeOrmHealthIndicator,
} from '@nestjs/terminus';
import { CacheService } from 'src/logic/cache/cache.service';

const TYPEORM_DB = 'database';

@Controller({
  path: 'health',
  version: VERSION_NEUTRAL,
})
export class HealthCheckController {
  constructor(
    private health: HealthCheckService,
    private microservice: MicroserviceHealthIndicator,
    private db: TypeOrmHealthIndicator,
    private cacheService: CacheService,
  ) {}

  @Get()
  @HealthCheck()
  async check(): Promise<HealthCheckResult | HealthIndicatorResult> {
    return this.health.check([
      // TODO: check health of Vulog gateway
      () => this.db.pingCheck(TYPEORM_DB),
      () => this.cacheService.checkIfHealthy(),
    ]);
  }
}
