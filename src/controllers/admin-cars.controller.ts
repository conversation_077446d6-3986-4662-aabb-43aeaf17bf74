import { ApiResponseSchema } from 'src/api/api-response.decorator';
import { ApiVersion } from 'src/common/constants/api-versions';
import { VulogCarId } from 'src/common/tiny-types';

import { GuardOn, PermissionAction } from '@bluesg-2/bo-auth-guard';
import { Controller, Get, Param, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';

import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { VulogTelemetryService } from 'src/logic/telemetry-repository/vulog/vulog-telemetry.service';
import { ParamCarId } from './api-params.decorator';
import { CarAdminInfoResponseDto } from './dtos/car-admin-info.response.dto';
import { CarAwakeResponseDto } from './dtos/car-awake.response.dto';

@ApiTags('admin-cars')
@ApiBearerAuth('admin-jwt')
@Controller({
  path: 'admin/cars',
  version: ApiVersion.V1,
})
export class AdminCarsController {
  constructor(
    private vulogTelemetryClient: VulogTelemetryService,
    private vulogCarService: VulogCarService,
  ) {}

  @ApiOperation({
    deprecated: true,
  })
  @ParamCarId
  @ApiResponseSchema(CarAdminInfoResponseDto)
  @Get(':carId')
  @GuardOn({
    action: PermissionAction.Read,
    resource: 'cars',
  })
  async getCarInformationInRealTime(
    @Param('carId') carId: string,
  ): Promise<CarAdminInfoResponseDto> {
    const allCarsInRealTime = await this.vulogCarService.getCarsInRealtime();

    const foundOne = allCarsInRealTime.find(
      (carInRealTime) => carInRealTime.id.value === carId,
    );

    return foundOne ? CarAdminInfoResponseDto.from(foundOne) : null;
  }

  @ApiOperation({
    deprecated: true,
    description:
      'This API is marked as `DEPRECATED` as we discard the usage of Vulog Vehicle Gateway API and there is no equivalent API in AiMA',
  })
  @ParamCarId
  @ApiResponseSchema(CarAwakeResponseDto)
  @Post('/:carId/ping')
  @GuardOn({
    action: PermissionAction.Read,
    resource: 'cars',
  })
  async pingCar(@Param('carId') carId: string): Promise<CarAwakeResponseDto> {
    return {
      isAwake: (
        await this.vulogTelemetryClient.wakeUpCar(new VulogCarId(carId))
      ).isAwake,
    };
  }

  @ApiOperation({
    deprecated: true,
    description:
      'This API is marked as `DEPRECATED` as we discard the usage of Vulog Vehicle Gateway API and there is no equivalent API in AiMA',
  })
  @ParamCarId
  @ApiResponseSchema(null)
  @Post('/:carId/lock')
  @GuardOn({
    action: PermissionAction.Update,
    resource: 'cars',
  })
  async lockCar(@Param('carId') carId: string): Promise<void> {
    await this.vulogTelemetryClient.lockCar(new VulogCarId(carId));
  }

  @ApiOperation({
    deprecated: true,
    description:
      'This API is marked as `DEPRECATED` as we discard the usage of Vulog Vehicle Gateway API and there is no equivalent API in AiMA',
  })
  @ParamCarId
  @ApiResponseSchema(null)
  @Post('/:carId/unlock')
  @GuardOn({
    action: PermissionAction.Update,
    resource: 'cars',
  })
  async unlockCar(@Param('carId') carId: string): Promise<void> {
    await this.vulogTelemetryClient.unlockCar(new VulogCarId(carId));
  }

  @ApiOperation({
    deprecated: true,
    description:
      'This API is marked as `DEPRECATED` as we discard the usage of Vulog Vehicle Gateway API and there is no equivalent API in AiMA',
  })
  @ParamCarId
  @ApiResponseSchema(null)
  @Post('/:carId/immobilize')
  @GuardOn({
    action: PermissionAction.Update,
    resource: 'cars',
  })
  async immobilizeCar(@Param('carId') carId: string): Promise<void> {
    await this.vulogTelemetryClient.immobilizeCar(new VulogCarId(carId));
  }

  @ApiOperation({
    deprecated: true,
    description:
      'This API is marked as `DEPRECATED` as we discard the usage of Vulog Vehicle Gateway API and there is no equivalent API in AiMA',
  })
  @ParamCarId
  @ApiResponseSchema(null)
  @Post('/:carId/mobilize')
  @GuardOn({
    action: PermissionAction.Update,
    resource: 'cars',
  })
  async mobilizeCar(@Param('carId') carId: string): Promise<void> {
    await this.vulogTelemetryClient.mobilizeCar(new VulogCarId(carId));
  }

  @ApiOperation({
    deprecated: true,
    description:
      'This API is marked as `DEPRECATED` as we discard the usage of Vulog Vehicle Gateway API and there is no equivalent API in AiMA',
  })
  @ParamCarId
  @ApiResponseSchema(CarAwakeResponseDto)
  @Post('/:carId/standby')
  @GuardOn({
    action: PermissionAction.Update,
    resource: 'cars',
  })
  async standbyCar(
    @Param('carId') carId: string,
  ): Promise<CarAwakeResponseDto> {
    return {
      isAwake: (
        await this.vulogTelemetryClient.standbyCar(new VulogCarId(carId))
      ).isAwake,
    };
  }
}
