import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { BasePaginationRequestDto } from 'src/common/api/base.pagination.request.dto';

type RentalQuerySortKey = 'createdAt';

export const RentalQuerySortKey: RentalQuerySortKey[] = ['createdAt'];

export class RentalQueryDtoV1 extends BasePaginationRequestDto {
  @ApiProperty({
    type: 'enum',
    enum: RentalQuerySortKey,
    default: null,
    required: false,
  })
  @IsEnum(RentalQuerySortKey)
  @IsOptional()
  sortKey?: RentalQuerySortKey = 'createdAt';
}
