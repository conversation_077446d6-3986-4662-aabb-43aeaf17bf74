import { faker } from '@faker-js/faker';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsLatitude,
  IsLongitude,
  IsNotEmpty,
  IsObject,
  IsString,
  ValidateNested,
} from 'class-validator';

export class MobileGpsLocationDto {
  @ApiProperty({
    type: 'string',
    example: faker.location.latitude(),
    nullable: false,
  })
  @IsLatitude()
  @IsString()
  @IsNotEmpty()
  lat: string;

  @ApiProperty({
    type: 'string',
    example: faker.location.longitude(),
    nullable: false,
  })
  @IsLongitude()
  @IsString()
  @IsNotEmpty()
  long: string;
}

export class AttachLocationRequestDtoV1 {
  @ApiProperty({
    type: MobileGpsLocationDto,
    nullable: false,
    description: 'Mobile GPS location',
  })
  @ValidateNested()
  @IsObject()
  @Type(() => MobileGpsLocationDto)
  location: MobileGpsLocationDto;
}
