import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';

export class StartRentalRequestDto {
  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @IsUUID()
  stationId: string;

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    type: 'object',
    required: true,
  })
  carMetadata: {
    id: string;
    vulogId: string;
    model: string;
  };

  constructor(stationId: string, userId: string, carMetadata) {
    this.stationId = stationId;
    this.userId = userId;
    this.carMetadata = carMetadata;
  }
}
