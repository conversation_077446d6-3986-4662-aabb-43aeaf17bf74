import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsUUID } from 'class-validator';

export class AttachPricingPolicyRequestDtoV1 {
  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  pricingPolicyId: string;

  @ApiProperty({
    type: 'number',
    required: true,
  })
  @IsNumber()
  @IsNotEmpty()
  rentalRate: number;
}
