import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsInt,
  IsISO8601,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsUUID,
  Max,
  Min,
  ValidateNested,
} from 'class-validator';
import { CarModelName } from 'src/logic/car-availability';

export class SubscriptionPlanInfoEventDto {
  constructor(props?: SubscriptionPlanInfoEventDto) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    type: 'string',
    required: true,
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    type: 'string',
    required: true,
  })
  @IsNotEmpty()
  description: string;
}

export class TripInfoEventDto {
  constructor(props?: TripInfoEventDto) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    type: 'string',
    format: 'date-time',
    required: true,
  })
  @IsISO8601({ strict: true })
  @IsNotEmpty()
  startingTime: string;

  @ApiProperty({
    type: 'string',
    format: 'date-time',
    required: true,
  })
  @IsISO8601({ strict: true })
  @IsNotEmpty()
  endingTime: string;

  @ApiProperty({
    type: 'number',
    required: true,
  })
  @IsInt()
  @IsNumber()
  @Min(1)
  @Max(86400)
  @IsNotEmpty()
  duration: number;
}

export class CarModelInfoEventDto {
  constructor(props?: CarModelInfoEventDto) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    type: 'string',
    enum: CarModelName,
    required: true,
    default: CarModelName.BlueCar,
  })
  @IsEnum(CarModelName)
  @IsNotEmpty()
  model: CarModelName;
}

export class UserInfoEventDto {
  constructor(props?: UserInfoEventDto) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  id: string;
}

export class SubscriptionInfoEventDto {
  constructor(props?: SubscriptionInfoEventDto) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    type: SubscriptionPlanInfoEventDto,
    required: true,
  })
  @ValidateNested()
  @IsObject()
  @Type(() => SubscriptionPlanInfoEventDto)
  @IsNotEmpty()
  subscriptionPlan: SubscriptionPlanInfoEventDto;
}

export class StationInfoEventDto {
  constructor(props?: StationInfoEventDto) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    type: 'string',
    required: true,
  })
  @IsNotEmpty()
  name: string;
}

export class RentalPackageSimulationDto {
  constructor(props?: RentalPackageSimulationDto) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    type: 'string',
    required: true,
  })
  @IsNotEmpty()
  name: string;

  // No need duration and price because Billing will handle it using rental_package_id
}

export class EndRentalSimulationRequestDto {
  constructor(props?: EndRentalSimulationRequestDto) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: TripInfoEventDto,
    required: true,
  })
  @ValidateNested()
  @IsObject()
  @Type(() => TripInfoEventDto)
  tripInfo: TripInfoEventDto;

  @ApiProperty({
    type: CarModelInfoEventDto,
    required: true,
  })
  @ValidateNested()
  @IsObject()
  @Type(() => CarModelInfoEventDto)
  carModelInfo: CarModelInfoEventDto;

  @ApiProperty({
    type: UserInfoEventDto,
    required: true,
  })
  @ValidateNested()
  @IsObject()
  @Type(() => UserInfoEventDto)
  userInfo: UserInfoEventDto;

  @ApiProperty({
    type: SubscriptionInfoEventDto,
    required: true,
  })
  @ValidateNested()
  @IsObject()
  @Type(() => SubscriptionInfoEventDto)
  subscriptionInfo: SubscriptionInfoEventDto;

  @ApiProperty({
    type: StationInfoEventDto,
    required: true,
  })
  @ValidateNested()
  @IsObject()
  @Type(() => StationInfoEventDto)
  startingStationInfo: StationInfoEventDto;

  @ApiProperty({
    type: StationInfoEventDto,
    required: true,
  })
  @ValidateNested()
  @IsObject()
  @Type(() => StationInfoEventDto)
  endingStationInfo: StationInfoEventDto;

  @ApiProperty({
    type: RentalPackageSimulationDto,
    required: false,
  })
  @ValidateNested()
  @IsObject()
  @Type(() => RentalPackageSimulationDto)
  @IsOptional()
  rentalPackageInfo?: RentalPackageSimulationDto;
}
