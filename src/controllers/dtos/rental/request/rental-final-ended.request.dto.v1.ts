import { Car } from 'src/logic/car/domain/car';
import { Reservation } from 'src/model/reservation.domain';

export class RentalFinalEndedPayloadDto {
  reservation: ReservationForRentalFinalEndedDto;
  car: CarForRentalFinalEndedDto;
  isRentalPackage: boolean;

  constructor(reservation: Reservation, car: Car, isRentalPackage: boolean) {
    this.reservation = ReservationForRentalFinalEndedDto.from(reservation);
    this.car = CarForRentalFinalEndedDto.from(car);
    this.isRentalPackage = isRentalPackage;
  }
}

export class CarForRentalFinalEndedDto {
  model: string;
  id: string;
  plateNumber: string;
  vin: string;
  public static from(car: Car): CarForRentalFinalEndedDto {
    const carDto = new CarForRentalFinalEndedDto();

    carDto.model = car.model;
    carDto.id = car.id.value;
    carDto.plateNumber = car.plate.value;
    carDto.vin = car.vin;

    return carDto;
  }
}

export class ReservationForRentalFinalEndedDto {
  bsgUserId: string;
  reservationId: string;
  amount?: number;
  /**
   * Duration in seconds
   */
  duration?: number;
  startStation?: string;
  startedAt: string;
  endStation?: string;
  endedAt?: string;

  public static from(
    reservation: Reservation,
  ): ReservationForRentalFinalEndedDto {
    const result = new ReservationForRentalFinalEndedDto();
    result.bsgUserId = reservation.bsgUserId.value;
    result.reservationId = reservation.reservationId.value;
    result.amount = reservation.amount;
    result.duration = reservation.getBilledDuration();
    result.startStation = reservation.startStationId?.value;
    result.startedAt = reservation.startedAt
      .toDateTime()
      .toJSDate()
      .toISOString();
    result.endStation = reservation.endStationId?.value;
    result.endedAt = reservation.endedAt?.toDateTime().toJSDate().toISOString();
    return result;
  }
}
