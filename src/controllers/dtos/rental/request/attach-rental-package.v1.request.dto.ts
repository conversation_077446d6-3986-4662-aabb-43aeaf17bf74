import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsString,
  IsUUID,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { MAX_DECIMAL_VALUE } from 'src/common/constants/normal';
import { ToBigNumber } from 'src/common/decorator/to-big-number.decorator';

export class AttachRentalPackageRequestDtoV1 {
  @ApiProperty({
    type: 'string',
    example: 'rt-pkg-202310260000xxx1 or rt-dyn-202310260000xxx1',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  hrid: string;

  @ApiProperty({
    type: 'string',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  packageId: string;

  @ApiProperty({
    type: 'string',
    required: true,
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    type: 'string',
    required: true,
  })
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    type: 'number',
    default: 1,
    required: true,
  })
  @Type(() => Number)
  @IsInt()
  @IsNotEmpty()
  duration: number;

  @ApiProperty({
    type: 'number',
    minimum: 1,
    maximum: MAX_DECIMAL_VALUE,
    required: true,
  })
  @ToBigNumber()
  @Min(1)
  @Max(MAX_DECIMAL_VALUE)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsNotEmpty()
  price: number;

  @ApiProperty({
    type: 'number',
    minimum: 0,
    maximum: 100,
    required: true,
  })
  @ToBigNumber()
  @Min(0)
  @Max(100)
  @IsNumber()
  @IsNotEmpty()
  minAllowedBatteryPercentage: number;
}
