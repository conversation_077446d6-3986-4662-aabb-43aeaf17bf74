import { ApiProperty } from '@nestjs/swagger';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { Reservation } from 'src/model/reservation.domain';

export class StationResponseDtoV1 {
  constructor(props: StationResponseDtoV1) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  id: string;

  @ApiProperty({
    type: 'string',
    format: 'md5',
    required: true,
  })
  sourceId: string;

  @ApiProperty({
    type: 'string',
    required: true,
  })
  name: string;
}

export class RentalResponseDtoV1 {
  constructor(props: RentalResponseDtoV1) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  id: string;

  @ApiProperty({
    type: 'string',
    format: 'datetime',
    required: true,
  })
  startDate: string;

  @ApiProperty({
    type: 'string',
    format: 'datetime',
    required: true,
  })
  endDate: string;

  @ApiProperty({
    type: 'number',
    required: true,
    description: 'Duration in seconds',
  })
  dnDuration: number;

  @ApiProperty({
    type: 'number',
    required: true,
    description: 'Billed duration in seconds',
  })
  billedDuration: number;

  @ApiProperty({
    type: 'number',
    required: true,
  })
  amount: number;

  @ApiProperty({
    type: 'string',
    enum: CarModelEnum,
    required: true,
  })
  carCategory: CarModelEnum;

  @ApiProperty({
    type: StationResponseDtoV1,
    required: true,
  })
  startStation: StationResponseDtoV1;

  @ApiProperty({
    type: StationResponseDtoV1,
    required: true,
  })
  endStation: StationResponseDtoV1;

  @ApiProperty({
    type: 'string',
    required: true,
  })
  rentalId: string;

  static fromReservation(
    reservation: Reservation,
    startStation: StationResponseDtoV1,
    endStation?: StationResponseDtoV1,
  ): RentalResponseDtoV1 {
    return new RentalResponseDtoV1({
      id: reservation.getId,
      startDate: reservation.startedAt?.value,
      endDate: reservation.endedAt?.value,
      dnDuration: reservation.getCurrentDuration(),
      billedDuration: reservation.getBilledDuration(),
      amount: reservation.amount,
      carCategory: reservation.carModel,
      startStation,
      endStation,
      rentalId: reservation.vulogTripId?.value,
    });
  }
}
