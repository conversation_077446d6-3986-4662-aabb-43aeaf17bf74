import { ApiProperty } from '@nestjs/swagger';
import { MapUtils } from 'src/common/utils/map.util';
import { StartRental } from 'src/model/start-rental';

export class AvailabilityResponseDto {
  constructor(props: AvailabilityResponseDto) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    description: 'UUID of a station',
  })
  id: string;

  @ApiProperty({
    type: 'object',
    example: { bluecar: 3, opel_ecorsa: 1 },
    description: 'Vehicles available per car model',
  })
  carsCounter: { [carModel: string]: number };

  @ApiProperty({
    type: 'number',
    description: 'Number of parking slots available',
  })
  slots: number; // TODO: Will return value when OCPI testing is stable (@Triston)

  @ApiProperty({
    type: 'object',
    example: {
      carReservation: 1,

      parkReservation: 2,

      rentalStart: 3,

      rentalEnd: 4,
    },
    description: 'Pricing information that is relevant to rental/trip',
  })
  pricingInformation: {
    carReservation: number; // TODO: Will have value when yield management is ready

    parkReservation: number; // TODO: Will have value when yield management is ready

    rentalStart: number; // TODO: Will have value when yield management is ready

    rentalEnd: number; // TODO: Will have value when yield management is ready
  };
}

export class StartRentalResponseDtoV1 {
  constructor(props: StartRentalResponseDtoV1) {
    Object.assign(this, props);
  }

  // @ApiProperty({
  //   type: 'string',
  //   description: 'Vulog ID of the rental/trip in MD5 format',
  //   example: 'C8B8A49945E856C86FD99DECEB490CF8',
  // })
  // id: string;

  @ApiProperty({
    type: 'string',
    description: 'UUID of the reservation in our db',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    type: 'string',
    description: 'UUID of the start station',
    format: 'uuid',
  })
  startStationId: string;

  @ApiProperty({
    type: 'string',
    description: 'Model of a vehicle',
    example: 'blue-car',
  })
  carCategory: string;

  @ApiProperty({
    type: 'string',
    description: 'UUID of the car of the rental',
    format: 'uuid',
  })
  carId: string;

  @ApiProperty({
    type: AvailabilityResponseDto,
    description: 'Current availability of start station',
  })
  stationAvailability: AvailabilityResponseDto;

  @ApiProperty({
    type: 'boolean',
    description: 'A flag to check whether the reservation is local or not',
  })
  local: boolean;

  static from(startRental: StartRental) {
    const { carId, carModel, startStationId, stationAvailability, local } =
      startRental;

    return new StartRentalResponseDtoV1({
      id: startRental?.id?.value,
      startStationId: startStationId.value,
      carCategory: carModel.value,
      carId: carId.value,
      stationAvailability: new AvailabilityResponseDto({
        id: startStationId.value,
        carsCounter: MapUtils.toObject(stationAvailability.carsCounter),
        slots: null,
        pricingInformation: null,
      }),
      local,
    });
  }
}
