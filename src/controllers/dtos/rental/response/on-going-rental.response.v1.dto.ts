import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { isBoolean, isNotEmpty } from 'class-validator';
import { ChargingKiosk } from 'src/model/charing-kiosk';
import { OnGoingRental } from 'src/model/on-going-rental';
import { RealtimeCar } from 'src/model/realtime.car';
import { CarRealtimeMetadata } from 'src/model/rental-info';
import { RentalPackage } from 'src/model/rental-package';
import { StaticCar } from 'src/model/static.car';
import { Station } from 'src/model/station';
import { Car } from '../../../../logic/car/domain/car';

export enum DoorsStatus {
  Locked = 'LOCKED',
  Unlocked = 'UNLOCKED',
  None = 'NONE',
}

export class CharingPointDto {
  constructor(props: CharingPointDto) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    description: 'The UUID of the charing kiosk',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    type: 'string',
    description:
      'The physcial reference on the charing kiosk. Could be observed by eyes',
    example: '01233',
  })
  physicalReference: string;

  static fromCharingKiosk(charingKiosk: ChargingKiosk): CharingPointDto {
    return new CharingPointDto({
      id: charingKiosk.id.value,
      physicalReference: charingKiosk.physicalReference,
    });
  }
}

export class CharingStationDto {
  constructor(props: CharingStationDto) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    description: 'The UUID of the station',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    type: 'string',
    description: 'The display name of the station',
  })
  name: string;

  static fromStation(station: Station): CharingStationDto {
    return new CharingStationDto({
      id: station.id.value,
      name: station.displayName,
    });
  }
}

export class CarDto {
  constructor(props: CarDto) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    description: 'The UUID of the car',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    type: 'string',
    description: 'The plate number of the car',
    example: 'SVX20543',
  })
  plateNumber: string;

  @ApiProperty({
    type: 'string',
    description: 'The vin of the car',
    example: 'VIN0312345234',
  })
  vin: string;

  @ApiProperty({
    type: 'string',
    description: 'The model of the car',
    example: 'blue-car',
  })
  category: string;

  @ApiProperty({
    type: 'number',
    description: 'The energy level of the car',
  })
  batteryLevel: number;

  @ApiProperty({
    type: 'enum',
    enum: DoorsStatus,
    description: 'Indicate the status of all doors',
  })
  doors: DoorsStatus;

  @ApiProperty({
    type: 'boolean',
    description:
      'To indicate if the battery level of the vehicle is above the minimum allowed battery level of the current-attached package',
  })
  availableForPackage: boolean;

  static fromVulogCar(
    car: RealtimeCar | StaticCar,
    autonomy: number,
    locked?: boolean,
    rentalPackageMinAllowedBatteryLevel = null,
  ): CarDto {
    return new CarDto({
      id: car.id.value,
      plateNumber: car.plate.value,
      vin: car.vin,
      category: car.model.value,
      batteryLevel: autonomy,
      doors: isBoolean(locked)
        ? locked
          ? DoorsStatus.Locked
          : DoorsStatus.Unlocked
        : DoorsStatus.None,
      availableForPackage: isNotEmpty(rentalPackageMinAllowedBatteryLevel)
        ? autonomy >= rentalPackageMinAllowedBatteryLevel
        : undefined,
    });
  }

  static fromCar(
    car: Car,
    realtimeInfo: CarRealtimeMetadata,
    rentalPackageMinAllowedBatteryLevel = null,
  ): CarDto {
    return new CarDto({
      id: car.vulogId.value,
      plateNumber: car.plate.value,
      vin: car.vin,
      category: car.model,
      batteryLevel: realtimeInfo.battery,
      doors: isBoolean(realtimeInfo.isDoorLocked)
        ? realtimeInfo.isDoorLocked
          ? DoorsStatus.Locked
          : DoorsStatus.Unlocked
        : DoorsStatus.None,
      availableForPackage: isNotEmpty(rentalPackageMinAllowedBatteryLevel)
        ? realtimeInfo.battery >= rentalPackageMinAllowedBatteryLevel
        : undefined,
    });
  }
}

export class RentalPackageDto {
  constructor(props: RentalPackageDto) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  id: string;

  @ApiProperty({
    type: 'string',
    description: 'Human-readable ID of the rental package',
    example: 'rpg-2023',
  })
  hrid: string;

  @ApiProperty({
    type: 'string',
    description: 'The name of the rental package',
    example: 'Rental package for 1h30m',
  })
  name: string;

  @ApiProperty({
    type: 'number',
    description: 'The price for the rental package',
  })
  price: number;

  @ApiProperty({
    type: 'number',
    description: 'The driving minutes of this rental package',
  })
  drivingMinutes: number;

  @ApiProperty({
    type: 'number',
    description:
      'The minimum allowed battery level of the vehicle to use this package',
  })
  minAllowedBatteryPercentage: number;

  @ApiProperty({
    type: 'boolean',
    description:
      'To indicate if the battery level of the vehicle is above the minimum allowed battery level of the current-attached package',
  })
  availableAtStation: boolean;

  static fromRentalPackage(
    rentalPackage: RentalPackage,
    currentRealtimeCarInfo?: CarRealtimeMetadata,
  ): RentalPackageDto {
    return new RentalPackageDto({
      id: rentalPackage.packageId?.value,
      hrid: rentalPackage.hrid,
      name: rentalPackage.name,
      price: rentalPackage.price,
      drivingMinutes: rentalPackage.duration,
      minAllowedBatteryPercentage: rentalPackage.minAllowedBatteryPercentage,
      availableAtStation:
        isNotEmpty(rentalPackage.minAllowedBatteryPercentage) &&
        isNotEmpty(currentRealtimeCarInfo)
          ? currentRealtimeCarInfo.battery >=
            rentalPackage.minAllowedBatteryPercentage
          : undefined,
    });
  }
}

export class OnGoingRentalResponseDtoV1 {
  constructor(props: OnGoingRentalResponseDtoV1) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    description: 'Reservation ID of the rental/trip in UUID format',
    example: 'd607d930-350f-43a6-a434-221d0835de49',
  })
  id: string;

  @ApiProperty({
    type: 'string',
    format: 'datetime',
    description: 'Start date of the rental',
  })
  startDate: string;

  @ApiProperty({
    type: 'string',
    format: 'datetime',
    description:
      'End date of the rental (End date could be equal to current time if the rental is not actually ended)',
  })
  endDate: string;

  @ApiProperty({
    type: 'number',
    description: 'Current driving duration of the rental in seconds',
  })
  duration: number;

  @ApiProperty({
    type: 'number',
    description: 'Current billed duration of the rental in seconds',
  })
  billedDuration: number;

  @ApiProperty({
    type: 'number',
    description: 'Current Kilometers (Km) the trip has made',
  })
  distance: number;

  @ApiProperty({
    type: 'number',
    description:
      'Pricing policy applied for this rental. Note that the value should be `null` when rental package is applied',
  })
  price?: number;

  @ApiProperty({
    type: CharingStationDto,
    description: 'Start station of the rental',
  })
  @Type(() => CharingStationDto)
  startStation: CharingStationDto;

  @ApiProperty({
    type: CharingStationDto,
    description:
      'End station of the rental. Note that the value should be `null` when the rental is not actually ended yet',
  })
  @Type(() => CharingStationDto)
  endStation: CharingStationDto;

  @ApiProperty({
    type: CarDto,
    description: 'The car that is used with the rental',
  })
  @Type(() => CarDto)
  car: CarDto;

  @ApiProperty({
    type: RentalPackageDto,
    description:
      'Rental package applied for this rental. Note that the value should be `null` when pricing policy is applied',
  })
  @Type(() => RentalPackageDto)
  package?: RentalPackageDto;

  static from(onGoingRental: OnGoingRental) {
    const {
      id,
      startDate,
      endDate,
      duration,
      billedDuration,
      distance,
      price,
      startStation,
      endStation,
      car,
      rentalPackage,
      realtimeInfo,
    } = onGoingRental;

    return new OnGoingRentalResponseDtoV1({
      id: id?.value,
      startDate: startDate?.value,
      endDate: endDate?.value,
      duration,
      billedDuration,
      distance: distance,
      price,
      startStation: CharingStationDto.fromStation(startStation),
      endStation: endStation ? CharingStationDto.fromStation(endStation) : null,
      car: CarDto.fromCar(car, realtimeInfo),
      package: rentalPackage
        ? RentalPackageDto.fromRentalPackage(rentalPackage)
        : null,
    });
  }
}
