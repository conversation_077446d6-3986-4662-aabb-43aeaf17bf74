import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  TestingEndRentalConditionResult,
  TestingEndRentalReport,
} from 'src/model/testing-end-rental.report';
import {
  EndRentalConditionResult,
  EndRentalReport,
} from '../../../../model/end-rental.report';
import { ReservationResponseDtoV1 } from '../../reservation.response.v1.dto';

export class EndRentalConditionResultResponseV1Dto {
  @ApiProperty({
    description: 'The identifier of the pre-condition',
    example: 'doors-windows-closed',
  })
  id: string;

  @ApiProperty({
    description: 'The result of the check. Tru is the condition was fulfilled.',
    example: true,
  })
  fulfilled: boolean;

  @ApiProperty({
    description: 'The label of the condition',
    example: 'Doors and windows closed',
  })
  label: string;

  @ApiProperty({
    description: 'The text to display to the user',
    example: 'The doors and windows were closed properly',
  })
  message: string;

  static from(
    conditionResult: EndRentalConditionResult,
  ): EndRentalConditionResultResponseV1Dto {
    const dto = new EndRentalConditionResultResponseV1Dto();

    dto.id = conditionResult.conditionId;
    dto.fulfilled = conditionResult.fulfilled;
    dto.label = conditionResult.label;
    dto.message = conditionResult.message;

    return dto;
  }

  static fromTesting(
    conditionResult: TestingEndRentalConditionResult,
  ): EndRentalConditionResultResponseV1Dto {
    const dto = new EndRentalConditionResultResponseV1Dto();

    dto.id = conditionResult.conditionId;
    dto.fulfilled = conditionResult.fulfilled;
    dto.label = conditionResult.label;
    dto.message = conditionResult.message;

    return dto;
  }
}

export class EndRentalResponseV1Dto {
  @ApiProperty({
    description:
      'True if the rental has started. False if it was not possible to end the rental',
  })
  hasEnded: boolean;

  @ApiProperty({
    type: ReservationResponseDtoV1,
    description: 'The reservation on which we did the end of rental',
  })
  reservation: ReservationResponseDtoV1;

  @ApiProperty({
    isArray: true,
    type: EndRentalConditionResultResponseV1Dto,
    description:
      'The list of all the pre-conditions that were checked before to process to the end of rental.',
  })
  conditions: EndRentalConditionResultResponseV1Dto[];

  @ApiPropertyOptional({
    isArray: true,
    type: EndRentalConditionResultResponseV1Dto,
    description:
      'The list of all QR scan pre-conditions that were checked before to process to the end of rental.',
  })
  qrConditions?: EndRentalConditionResultResponseV1Dto[];

  @ApiPropertyOptional({
    description: 'The total price of the rental.',
  })
  price?: number;
  @ApiPropertyOptional({
    description: 'The duration of the rental in seconds.',
  })
  durationInSeconds?: number;

  static from(report: EndRentalReport): EndRentalResponseV1Dto {
    const dto = new EndRentalResponseV1Dto();

    dto.hasEnded = report.endRentalSuccess;
    dto.reservation = ReservationResponseDtoV1.fromReservation(
      report.reservation,
    );
    dto.conditions = report.conditions.map((c) =>
      EndRentalConditionResultResponseV1Dto.from(c),
    );
    dto.price = report.price;
    dto.durationInSeconds = report.durationInSeconds;
    return dto;
  }

  static fromTesting(report: TestingEndRentalReport): EndRentalResponseV1Dto {
    const dto = new EndRentalResponseV1Dto();

    dto.hasEnded = report.endRentalSuccess;
    dto.reservation = ReservationResponseDtoV1.fromReservation(
      report.reservation,
    );
    dto.conditions = report.conditions?.map((c) =>
      EndRentalConditionResultResponseV1Dto.fromTesting(c),
    ) ?? [];
    dto.qrConditions = report.qrConditions?.map((c) =>
      EndRentalConditionResultResponseV1Dto.fromTesting(c),
    ) ?? [];
    dto.durationInSeconds = report.durationInSeconds ?? undefined;
    dto.price = report.totalPrice ?? undefined;
    return dto;
  }
}
