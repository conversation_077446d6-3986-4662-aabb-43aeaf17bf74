import { VulogCarId } from 'src/common/tiny-types';
import { RentalInfo } from 'src/model/rental-info';

import { ApiProperty } from '@nestjs/swagger';

export class RentalResponseDto {
  @ApiProperty({
    description: 'UUID of the car of the rental',
    format: 'uuid',
    example: 'd91ce6fe-c968-49a5-8d28-87dd3ed4e90d',
  })
  public carId: string;

  constructor(carId: VulogCarId) {
    this.carId = carId.value;
  }

  static from(rentalInfo: RentalInfo) {
    return new RentalResponseDto(rentalInfo.carId);
  }
}
