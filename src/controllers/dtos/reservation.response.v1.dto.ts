import { ApiProperty } from '@nestjs/swagger';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ReservationInfo } from 'src/model/reservation-info';
import { Reservation } from 'src/model/reservation.domain';

export class ReservationResponseDtoV1 {
  constructor(props: ReservationResponseDtoV1) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  id: string;

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    required: true,
  })
  stationId: string;

  @ApiProperty({
    type: 'string',
    required: true,
  })
  carId: string;

  @ApiProperty({
    type: 'string',
    required: true,
  })
  plateNumber: string;

  @ApiProperty({
    type: 'boolean',
    required: true,
  })
  local: boolean;

  @ApiProperty({
    type: 'string',
    format: 'datetime',
    required: true,
  })
  reservedAt: string;

  @ApiProperty({
    type: 'string',
    format: 'datetime',
    required: true,
  })
  expiresAt: string;

  @ApiProperty({
    type: 'string',
    format: 'datetime',
    required: false,
  })
  canceledAt?: string;

  @ApiProperty({
    type: 'string',
    format: 'datetime',
    required: false,
  })
  abuseReleasesAt?: string;

  @ApiProperty({
    type: 'string',
    enum: ReservationStatus,
    required: true,
  })
  status: ReservationStatus;

  @ApiProperty({
    type: 'string',
    enum: CarModelEnum,
    required: true,
  })
  carCategory: CarModelEnum;

  @ApiProperty({
    type: 'uuid',
    required: false,
  })
  bsgUserId?: string;

  static fromReservation(
    reservation: Reservation,
    carModel?: CarModelEnum,
  ): ReservationResponseDtoV1 {
    return new ReservationResponseDtoV1({
      id: reservation.getId,
      stationId: reservation?.startStationId?.value,
      reservedAt: reservation?.reservedAt?.value,
      expiresAt: reservation?.expiresAt?.value,
      canceledAt: reservation?.canceledAt?.value,
      abuseReleasesAt: reservation?.abuseReleasesAt?.value,
      status: reservation?.status,
      carCategory: carModel || reservation.carModel,
      bsgUserId: reservation?.bsgUserId?.value,
      carId: reservation?.carId?.value,
      local: reservation?.local,
      plateNumber: reservation?.plateNumber?.value,
    });
  }

  static fromReservationInfo(
    reservationInfo: ReservationInfo,
  ): ReservationResponseDtoV1 {
    const {
      carModel,
      startStationId,
      status,
      reservedAt,
      expiresAt,
      canceledAt,
      abuseReleasesAt,
      entityId,
      bsgUserId,
    } = reservationInfo;

    return new ReservationResponseDtoV1({
      id: entityId?.value,
      stationId: startStationId.value,
      reservedAt: reservedAt.value,
      expiresAt: expiresAt.value,
      canceledAt: canceledAt?.value,
      abuseReleasesAt: abuseReleasesAt?.value,
      status: status,
      carCategory: carModel,
      carId: reservationInfo?.carId?.value,
      local: reservationInfo?.local,
      plateNumber: reservationInfo?.plateNumber?.value,
      bsgUserId: bsgUserId?.value,
    });
  }
}
