import { ApiProperty } from '@nestjs/swagger';
import { CarAvailability } from 'src/logic/car-availability';
import { Station } from 'src/model/station';

export class CarAvailabilityResponseDto {
  @ApiProperty({
    description: `JSON object representing station availabilities 
      - each key is a station ID, and each value is an object that 
      contains the station information, and the number of available cars`,
    example: {
      'd91ce6fe-c968-49a5-8d28-87dd3ed4e90d': {
        station: {
          id: 'd91ce6fe-c968-49a5-8d28-87dd3ed4e90d',
          displayName: 'Blk 123 Test Street',
          latitude: '-79.132557',
          longitude: '80.880444',
          street: 'Test Street',
          postalCode: '123456',
        },
        carAvailability: {
          Bluecar: 2,
          'Opel Corsa-e': 1,
        },
      },
    },
  })
  public stations: {
    [stationZoneId: string]: {
      station: Station;
      carAvailability: { [model: string]: number };
    };
  };

  constructor(stations: {
    [stationZoneId: string]: {
      station: Station;
      carAvailability: { [model: string]: number };
    };
  }) {
    this.stations = stations;
  }

  static from(carAvailability: CarAvailability) {
    return {
      stations: carAvailability,
    };
  }
}
