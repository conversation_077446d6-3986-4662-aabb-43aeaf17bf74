import { ApiProperty } from '@nestjs/swagger';
import { AvailableCarsAtStation } from 'src/model/available-cars-at-station';
import { AvailableCarsAtStationMap } from 'src/model/available-cars-at-station-map';
import { Station } from 'src/model/station';
import { CarDto } from '../../rental/response/on-going-rental.response.v1.dto';

export class StationDto {
  public id: string;

  public displayName: string;

  public zoneId?: string;

  public latitude: string;

  public longitude: string;

  public street: string;

  public postalCode: string;

  constructor(props: StationDto) {
    Object.assign(this, props);
  }

  static from(domain: Station): StationDto {
    return new StationDto({
      id: domain.id.value,
      displayName: domain.displayName,
      latitude: domain.latitude.toString(),
      longitude: domain.longitude.toString(),
      street: domain.street,
      postalCode: domain.postalCode,
      zoneId: domain.zoneId,
    });
  }
}

export class AvailableCarsAtStationDto {
  @ApiProperty({
    type: StationDto,
    required: true,
    description: 'The detail of a station',
  })
  station: StationDto;

  @ApiProperty({
    type: CarDto,
    isArray: true,
    required: true,
    description: 'List of available cars at the station',
  })
  availableCars: CarDto[];

  constructor(props: AvailableCarsAtStationDto) {
    Object.assign(this, props);
  }

  static from(domain: AvailableCarsAtStation): AvailableCarsAtStationDto {
    return new AvailableCarsAtStationDto({
      station: StationDto.from(domain.station),
      availableCars: domain.availableCars.map((availCar) =>
        CarDto.fromVulogCar(availCar, availCar.energyLevel),
      ),
    });
  }
}

export class ListAvailableCarsAtStationsResponseDtoV1 {
  [stationId: string]: AvailableCarsAtStationDto;

  static from(
    availableCarsAtStationMap: AvailableCarsAtStationMap,
  ): ListAvailableCarsAtStationsResponseDtoV1 {
    const responseDto: ListAvailableCarsAtStationsResponseDtoV1 = {};

    for (const [stationId, availableCarsAtStation] of Object.entries(
      availableCarsAtStationMap,
    )) {
      responseDto[stationId] = AvailableCarsAtStationDto.from(
        availableCarsAtStation,
      );
    }

    return responseDto;
  }
}
