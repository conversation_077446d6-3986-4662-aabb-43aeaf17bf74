import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { BasePaginationRequestDto } from 'src/common/api/base.pagination.request.dto';
import { ReservationStatus } from 'src/common/enum/reservation.enum';

type ReservationQuerySortKey = 'createdAt';

export const ReservationQuerySortKey: ReservationQuerySortKey[] = ['createdAt'];

export class ReservationQueryDtoV1 extends BasePaginationRequestDto {
  @ApiProperty({
    type: 'enum',
    enum: ReservationQuerySortKey,
    default: null,
    required: false,
  })
  @IsEnum(ReservationQuerySortKey)
  @IsOptional()
  sortKey?: ReservationQuerySortKey = 'createdAt';

  @ApiProperty({
    enum: ReservationStatus,
    required: false,
    isArray: true,
  })
  @IsOptional()
  @IsEnum(ReservationStatus, { each: true })
  @IsArray()
  status?: ReservationStatus[];
}
