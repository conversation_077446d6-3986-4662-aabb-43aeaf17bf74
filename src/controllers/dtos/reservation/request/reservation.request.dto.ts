import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsOptional } from 'class-validator';
import { CarModelEnum } from 'src/common/enum/car-model.enum';

export class ReservationRequestDto {
  @ApiProperty({
    type: 'string',
    required: false,
    enum: CarModelEnum,
  })
  @IsEnum(CarModelEnum)
  @IsOptional()
  carModel?: CarModelEnum;

  @ApiProperty({
    type: 'booealn',
    required: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  local?: boolean;
}
