import { ApiProperty } from '@nestjs/swagger';
import { Car } from 'src/logic/car/domain/car';
import { CarRealtimeMetadata } from 'src/model/rental-info';
import { Reservation } from 'src/model/reservation.domain';
import { CarDto } from '../../rental/response/on-going-rental.response.v1.dto';
import { ReservationResponseDtoV1 } from '../../reservation.response.v1.dto';

export class AvailableCarsForReservationResponseDtoV1 {
  constructor(props: AvailableCarsForReservationResponseDtoV1) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: CarDto,
    required: true,
  })
  currentAllocatedCar: CarDto;

  @ApiProperty({
    type: CarDto,
    required: true,
    isArray: true,
  })
  availableCars: CarDto[];

  @ApiProperty({
    type: ReservationResponseDtoV1,
    required: true,
  })
  currentReservation: ReservationResponseDtoV1;

  static from(
    currentAllocatedCar: Car,
    availableCars: Car[],
    currentReservation: Reservation,
  ) {
    const currentRentalPackageMinAllowedBatLevel =
      currentReservation.getRentalPackageData()?.minAllowedBatteryPercentage;

    return new AvailableCarsForReservationResponseDtoV1({
      currentAllocatedCar: CarDto.fromCar(
        currentAllocatedCar,
        {
          battery: currentAllocatedCar.realtimeMetadata.batteryPercentage,
          isDoorLocked: currentAllocatedCar.realtimeMetadata.isLocked,
        } as CarRealtimeMetadata,
        currentRentalPackageMinAllowedBatLevel,
      ),
      availableCars: availableCars.map((car) =>
        CarDto.fromCar(
          car,
          {
            battery: car.realtimeMetadata.batteryPercentage,
            isDoorLocked: car.realtimeMetadata.isLocked,
          } as CarRealtimeMetadata,
          currentRentalPackageMinAllowedBatLevel,
        ),
      ),
      currentReservation: ReservationResponseDtoV1.fromReservation(
        currentReservation,
        currentReservation.carModel,
      ),
    });
  }
}
