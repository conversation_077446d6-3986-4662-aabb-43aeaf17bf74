import { ApiProperty } from '@nestjs/swagger';
import { Reservation } from 'src/model/reservation.domain';
import { RealtimeCar } from '../../../../model/realtime.car';
import { CarDto } from '../../rental/response/on-going-rental.response.v1.dto';
import { ReservationResponseDtoV1 } from '../../reservation.response.v1.dto';

export class AvailableCarsForReservationResponseDtoV1 {
  constructor(props: AvailableCarsForReservationResponseDtoV1) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: CarDto,
    required: true,
  })
  currentAllocatedCar: CarDto;

  @ApiProperty({
    type: CarDto,
    required: true,
    isArray: true,
  })
  availableCars: CarDto[];

  @ApiProperty({
    type: ReservationResponseDtoV1,
    required: true,
  })
  currentReservation: ReservationResponseDtoV1;

  static from(
    currentAllocatedCar: RealtimeCar,
    availableCars: RealtimeCar[],
    currentReservation: Reservation,
  ) {
    const currentRentalPackageMinAllowedBatLevel =
      currentReservation.getRentalPackageData()?.minAllowedBatteryPercentage;

    return new AvailableCarsForReservationResponseDtoV1({
      currentAllocatedCar: CarDto.fromVulogCar(
        currentAllocatedCar,
        currentAllocatedCar.energyLevel,
        currentAllocatedCar.isDoorLocked,
        currentRentalPackageMinAllowedBatLevel,
      ),
      availableCars: availableCars.map((car) =>
        CarDto.fromVulogCar(
          car,
          car.energyLevel,
          car.isDoorLocked,
          currentRentalPackageMinAllowedBatLevel,
        ),
      ),
      currentReservation: ReservationResponseDtoV1.fromReservation(
        currentReservation,
        currentReservation.carModel,
      ),
    });
  }
}
