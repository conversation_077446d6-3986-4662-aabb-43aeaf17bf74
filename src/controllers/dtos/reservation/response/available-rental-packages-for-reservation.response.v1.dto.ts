import { ApiProperty } from '@nestjs/swagger';
import { RealtimeCar } from 'src/model/realtime.car';
import { CarRealtimeMetadata } from 'src/model/rental-info';
import { RentalPackage } from 'src/model/rental-package';
import { Reservation } from 'src/model/reservation.domain';
import { RentalPackageDto } from '../../rental/response/on-going-rental.response.v1.dto';
import { ReservationResponseDtoV1 } from '../../reservation.response.v1.dto';

export class AvailableRentalPackagesForReservationResponseDtoV1 {
  constructor(props: AvailableRentalPackagesForReservationResponseDtoV1) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: RentalPackageDto,
    required: true,
  })
  currentAttachedRentalPackage: RentalPackageDto;

  @ApiProperty({
    type: RentalPackageDto,
    required: true,
    isArray: true,
  })
  availableRentalPackages: RentalPackageDto[];

  @ApiProperty({
    type: ReservationResponseDtoV1,
    required: true,
  })
  currentReservation: ReservationResponseDtoV1;

  static from(
    currentAttachedRentalPackage: RentalPackage,
    availableRentalPackages: RentalPackage[],
    currentReservation: Reservation,
    currentAllocatedRealtimeCar: RealtimeCar,
  ) {
    const currentAllocatedCarRealtimeInfo: CarRealtimeMetadata = {
      battery: currentAllocatedRealtimeCar.energyLevel,
      isCharging: currentAllocatedRealtimeCar.isCharging,
      isDoorClosed: currentAllocatedRealtimeCar.isDoorClosed,
      isDoorLocked: currentAllocatedRealtimeCar.isDoorLocked,
      isEngineOn: currentAllocatedRealtimeCar.isEngineOn,
    };

    return new AvailableRentalPackagesForReservationResponseDtoV1({
      currentAttachedRentalPackage: currentAttachedRentalPackage
        ? RentalPackageDto.fromRentalPackage(
            currentAttachedRentalPackage,
            currentAllocatedCarRealtimeInfo,
          )
        : null,
      availableRentalPackages: availableRentalPackages.map((rentalPackage) =>
        RentalPackageDto.fromRentalPackage(
          rentalPackage,
          currentAllocatedCarRealtimeInfo,
        ),
      ),
      currentReservation: ReservationResponseDtoV1.fromReservation(
        currentReservation,
        currentReservation.carModel,
      ),
    });
  }
}
