import { ReservationInfo } from 'src/model/reservation-info';

import { ApiProperty } from '@nestjs/swagger';
import { ReservationAdminInfo } from 'src/model/reservation-admin-info';

export class ReservationResponseDto {
  constructor(props: ReservationResponseDto) {
    Object.assign(this, props);
  }

  @ApiProperty({
    description: 'UUID representing the car that has been reserved',
    format: 'uuid',
    example: 'd91ce6fe-c968-49a5-8d28-87dd3ed4e90d',
  })
  reservedCarId: string;

  static from(reservationInfo: ReservationInfo | ReservationAdminInfo) {
    return new ReservationResponseDto({
      reservedCarId: reservationInfo.carId.value,
    });
  }
}
