import { RealtimeCar } from '../../model/realtime.car';

import { ApiProperty } from '@nestjs/swagger';

export class CarAdminInfoResponseDto {
  @ApiProperty({
    description: 'UUID representing the car',
    format: 'uuid',
    example: 'd91ce6fe-c968-49a5-8d28-87dd3ed4e90d',
  })
  public id: string;

  @ApiProperty({
    description: 'Model of the car',
  })
  public model: string;

  @ApiProperty({
    description: 'Plate number of the car',
  })
  public plate: string;

  @ApiProperty({
    description:
      'UUID representing the car assigned by the telemetry box vendor',
    format: 'uuid',
    example: 'd91ce6fe-c968-49a5-8d28-87dd3ed4e90d',
  })
  public sourceId: string;

  @ApiProperty({
    description: 'Battery level of the car',
    example: 50,
  })
  public energyLevel: number;

  @ApiProperty({
    description: 'GPS coordinates of the location of the car',
    example: {
      latitude: -79.132557,
      longitude: 80.880444,
    },
  })
  public location: {
    latitude: number;
    longitude: number;
  };

  @ApiProperty({
    description:
      'Indicates if the doors and windows of the car are closed or not',
  })
  public areDoorsAndWindowsClosed: boolean;

  @ApiProperty({
    description:
      'Indicates if any of the alerts of the car have been triggered',
    example: {
      hasFlatTire: false,
      hasTireUnderPressure: true,
      hasCrash: false,
    },
  })
  public alerts: {
    hasFlatTire: boolean;
    hasTireUnderPressure: boolean;
    hasCrash: boolean;
  };

  @ApiProperty({
    description: 'Indicates if the car is currently charging',
  })
  public isCharging: boolean;

  @ApiProperty({
    description: 'Indicates if the car is currently plugged in',
  })
  public isPluggedIn: boolean;

  constructor(
    id: string,
    model: string,
    plate: string,
    sourceId: string,
    energyLevel: number,
    location: {
      latitude: number;
      longitude: number;
    },
    areDoorsAndWindowsClosed: boolean,
    alerts: {
      hasFlatTire: boolean;
      hasTireUnderPressure: boolean;
      hasCrash: boolean;
    },
    isCharging: boolean,
    isPluggedIn: boolean,
  ) {
    this.id = id;
    this.model = model;
    this.plate = plate;
    this.sourceId = sourceId;
    this.energyLevel = energyLevel;
    this.location = location;
    this.areDoorsAndWindowsClosed = areDoorsAndWindowsClosed;
    this.alerts = alerts;
    this.isCharging = isCharging;
    this.isPluggedIn = isPluggedIn;
  }

  static from(car: RealtimeCar): CarAdminInfoResponseDto {
    return new CarAdminInfoResponseDto(
      car.id.value,
      car?.model?.value,
      car.plate?.value,
      car.sourceId.value,
      car.energyLevel,
      car.location,
      car.areDoorsAndWindowsClosed,
      car.alerts,
      car.isCharging,
      car.isPluggedIn,
    );
  }
}
