import { ApiProperty } from '@nestjs/swagger';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { PricingPolicy } from 'src/model/pricing-policy';
import { ReservationInfo } from 'src/model/reservation-info';
import { Reservation } from 'src/model/reservation.domain';
import { ReservationResponseDtoV1 } from './reservation.response.v1.dto';

export class AdminReservationResponseDtoV1 extends ReservationResponseDtoV1 {
  constructor(props: AdminReservationResponseDtoV1) {
    super(props);

    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'decimal',
    required: false,
  })
  amount?: number;

  @ApiProperty({
    type: 'decimal',
    required: false,
  })
  duration?: number;

  @ApiProperty({
    type: 'jsonb',
    required: false,
  })
  pricingPolicy?: PricingPolicy;

  @ApiProperty({
    type: 'uuid',
    required: false,
  })
  bsgUserId?: string;

  static fromReservation(
    reservation: Reservation,
    carModel?: CarModelEnum,
  ): AdminReservationResponseDtoV1 {
    return new AdminReservationResponseDtoV1({
      id: reservation.getId,
      stationId: reservation?.startStationId?.value,
      reservedAt: reservation?.reservedAt?.value,
      expiresAt: reservation?.expiresAt?.value,
      canceledAt: reservation?.canceledAt?.value,
      abuseReleasesAt: reservation?.abuseReleasesAt?.value,
      status: reservation?.status,
      carCategory: carModel || reservation.carModel,
      amount: reservation?.amount,
      duration: reservation?.getBilledDuration(),
      pricingPolicy: reservation?.pricingPolicy,
      bsgUserId: reservation?.bsgUserId?.value,
      carId: reservation?.carId?.value,
      local: reservation?.local,
      plateNumber: reservation?.plateNumber?.value,
    });
  }

  static fromReservationInfo(
    reservationInfo: ReservationInfo,
  ): AdminReservationResponseDtoV1 {
    const {
      carModel,
      startStationId,
      status,
      reservedAt,
      expiresAt,
      canceledAt,
      abuseReleasesAt,
      entityId,
    } = reservationInfo;

    return new AdminReservationResponseDtoV1({
      id: entityId.value,
      stationId: startStationId.value,
      reservedAt: reservedAt.value,
      expiresAt: expiresAt.value,
      canceledAt: canceledAt?.value,
      abuseReleasesAt: abuseReleasesAt?.value,
      status: status,
      carCategory: carModel,
      carId: reservationInfo?.carId?.value,
      local: reservationInfo?.local,
      plateNumber: reservationInfo?.plateNumber?.value,
    });
  }
}
