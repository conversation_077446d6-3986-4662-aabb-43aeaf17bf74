import { ApiProperty } from '@nestjs/swagger';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationInfo } from 'src/model/reservation-info';
import { Reservation } from 'src/model/reservation.domain';
import { ReservationResponseDtoV1 } from './reservation.response.v1.dto';

export class FeedbackReservationResponseDtoV1 extends ReservationResponseDtoV1 {
  constructor(props: FeedbackReservationResponseDtoV1) {
    super(props);

    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    required: false,
  })
  rating?: string = null;

  @ApiProperty({
    type: 'string',
    required: false,
  })
  carCleanliness?: string = null;

  static fromReservation(
    reservation: Reservation,
    carModel?: CarModelEnum,
  ): FeedbackReservationResponseDtoV1 {
    return new FeedbackReservationResponseDtoV1({
      id: reservation.getId,
      stationId: reservation?.startStationId?.value,
      reservedAt: reservation?.reservedAt?.value,
      expiresAt: reservation?.expiresAt?.value,
      canceledAt: reservation?.canceledAt?.value,
      abuseReleasesAt: reservation?.abuseReleasesAt?.value,
      status: reservation?.status,
      carCategory: carModel || reservation.carModel,
      rating: reservation.rating || null,
      carCleanliness: reservation.carCleanliness || null,
      bsgUserId: reservation?.bsgUserId?.value,
      carId: reservation?.carId?.value,
      local: reservation?.local,
      plateNumber: reservation?.plateNumber?.value,
    });
  }

  static fromReservationInfo(
    reservationInfo: ReservationInfo,
  ): FeedbackReservationResponseDtoV1 {
    const {
      carModel,
      startStationId,
      status,
      reservedAt,
      expiresAt,
      canceledAt,
      abuseReleasesAt,
      entityId,
    } = reservationInfo;

    return new FeedbackReservationResponseDtoV1({
      id: entityId.value,
      stationId: startStationId.value,
      reservedAt: reservedAt.value,
      expiresAt: expiresAt.value,
      canceledAt: canceledAt?.value,
      abuseReleasesAt: abuseReleasesAt?.value,
      status: status,
      carCategory: carModel,
      carId: reservationInfo?.carId?.value,
      local: reservationInfo?.local,
      plateNumber: reservationInfo?.plateNumber?.value,
    });
  }
}
