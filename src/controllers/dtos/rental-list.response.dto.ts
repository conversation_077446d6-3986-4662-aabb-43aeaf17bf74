import { ApiProperty } from '@nestjs/swagger';
import { RentalInfo } from 'src/model/rental-info';
import { RentalResponseDto } from './rental.response.dto';

export class RentalListResponseDto {
  @ApiProperty({
    description: 'List of rentals',
    type: [RentalResponseDto],
  })
  public rentals: RentalResponseDto[];

  constructor(rentals: RentalResponseDto[]) {
    this.rentals = rentals;
  }

  static from(rentalInfos: RentalInfo[]) {
    return new RentalListResponseDto(
      rentalInfos.map((rentalInfo) => RentalResponseDto.from(rentalInfo)),
    );
  }
}
