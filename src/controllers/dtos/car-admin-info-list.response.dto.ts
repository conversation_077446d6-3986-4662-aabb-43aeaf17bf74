import { ApiProperty } from '@nestjs/swagger';
import { CarAdminInfoResponseDto } from './car-admin-info.response.dto';

export class CarAdminInfoListResponseDto {
  @ApiProperty({
    description: 'List of car information objects',
    type: [CarAdminInfoResponseDto],
  })
  public content: CarAdminInfoResponseDto[];

  @ApiProperty({
    description: 'Cursor token that points to the next page of cars',
  })
  public continuationToken: string;

  constructor(content: CarAdminInfoResponseDto[], continuationToken: string) {
    this.content = content;
    this.continuationToken = continuationToken;
  }
}
