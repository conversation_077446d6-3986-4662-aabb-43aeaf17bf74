import { ApiVersion } from 'src/common/constants/api-versions';

import { Controller } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { GuardOn, PermissionAction } from '@bluesg-2/bo-auth-guard';
import { ADMIN_BEARER_AUTH } from '@bluesg-2/queue-pop';
import { Get } from '@nestjs/common';
import { ResourceName } from 'src/common/constants/resource';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { ReservationResponseDto } from './dtos/reservation/response/reservation.response.dto';

@ApiTags('admin-reservations')
@ApiBearerAuth(ADMIN_BEARER_AUTH)
@Controller({
  path: 'admin/reservations',
  version: ApiVersion.V1,
})
export class AdminReservationController {
  constructor(private vulogCarClient: VulogCarService) {}

  @GuardOn({
    action: PermissionAction.Read,
    resource: ResourceName.Reservation,
  })
  @Get()
  async getReservations(): Promise<ReservationResponseDto[]> {
    const result =
      await this.vulogCarClient.getAllFinishedReservationsAndAllocations();

    return result.map((r) => ReservationResponseDto.from(r));
  }
}
