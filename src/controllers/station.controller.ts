import { ApiVersion } from 'src/common/constants/api-versions';

import { Body, Controller, Post } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { ApiResponseSchema } from 'src/api/api-response.decorator';

import { RouteSegment } from 'src/common/constants/api-path';
import { AvailabilityService } from 'src/logic/availability.service';
import { ListAvailableCarsAtStationsRequestDtoV1 } from './dtos/station/request/list-available-cars-at-stations.request.v1.dto';
import { ListAvailableCarsAtStationsResponseDtoV1 } from './dtos/station/response/list-available-cars-at-stations.response.v1.dto';

@ApiTags(RouteSegment.Station.Index)
@Controller({
  path: RouteSegment.Station.Index,
  version: ApiVersion.V1,
})
export class StationController {
  constructor(private readonly availabilityService: AvailabilityService) {}

  @ApiOperation({
    description: 'API to list available cars at stations',
  })
  @Post(RouteSegment.Station.AvailableCars)
  @ApiResponseSchema()
  async listAvailableCarsAtStations(
    @Body() requestDto: ListAvailableCarsAtStationsRequestDtoV1,
  ): Promise<ListAvailableCarsAtStationsResponseDtoV1> {
    const { atStations } = requestDto;

    const availableCarsAtStationMap =
      await this.availabilityService.getAvailableCarsAtStations();

    for (const key of Object.keys(availableCarsAtStationMap)) {
      if (!atStations.includes(key)) {
        delete availableCarsAtStationMap[key];
      }
    }

    return ListAvailableCarsAtStationsResponseDtoV1.from(
      availableCarsAtStationMap,
    );
  }
}
