import { Controller, Post, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ApiResponse } from '../api/dtos/api-response.dto';
import { ApiVersion } from '../common/constants/api-versions';
import { UserSubscriptionExternalService } from '../external/user-subscription/user-subscription.external.service';
import { VulogUserEntity } from '../model/repositories/entities/vulog-user.entity';
import { VulogUserRepository } from '../model/repositories/vulog-user.repository';
import { VulogUser } from '../model/vulog-user';

@ApiTags('Migrations')
@Controller({ path: 'internal/migrations', version: ApiVersion.V1 })
export class MigrationController {
  constructor(
    private readonly ussService: UserSubscriptionExternalService,
    private readonly vulogUserService: VulogUserRepository,
  ) {}

  @Post('/vulog-users')
  async createVulogUsers(
    @Query('batch_size') batchSize = 20,
  ): Promise<ApiResponse<{ totalMigrated: number }>> {
    let totalMigrated = 0;
    let isLast = false;
    let page = 1;
    while (!isLast) {
      const ussUserPage = await this.ussService.internalApi.getAllActiveUsers(
        page,
        batchSize,
      );

      const result = await this.vulogUserService.save(
        ussUserPage.result.map((u) =>
          VulogUserEntity.from(VulogUser.generateFrom(u.toUser())),
        ),
      );

      totalMigrated += result.length;
      isLast = ussUserPage.isLast;
      page++;
    }

    return new ApiResponse(true, { totalMigrated });
  }
}
