import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, MaxLength } from 'class-validator';

export class CreateCommentDtoV1 {
  @ApiProperty({
    nullable: false,
    type: 'text',
    description: 'User comment',
  })
  @MaxLength(200)
  @IsNotEmpty()
  comment: string;

  @ApiProperty({
    nullable: false,
    type: 'boolean',
    description: 'Hide user identity or not',
    default: false,
  })
  @IsNotEmpty()
  isAnonymous = false;
}
