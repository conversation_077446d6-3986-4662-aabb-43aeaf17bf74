import { ApiProperty } from '@nestjs/swagger';
import { Comment } from 'src/logic/comment/domain/comment.domain';

export class ConsumerCommentResponseDtoV1 {
  constructor(props?: ConsumerCommentResponseDtoV1) {
    Object.assign(this, props);
  }

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    description: 'Comment ID',
    required: true,
  })
  id: string;

  @ApiProperty({
    type: 'string',
    format: 'uuid',
    description: 'BlueSG User ID',
    required: false,
  })
  bsgUserId?: string;

  @ApiProperty({
    type: 'string',
    description: 'Comment made by the user',
    required: true,
  })
  comment: string;

  @ApiProperty({
    type: 'boolean',
    description: 'Is this user anonymous?',
    required: false,
  })
  isAnonymous?: boolean;

  @ApiProperty({
    type: 'string',
    format: 'datetime',
    required: true,
  })
  createdAt: string;

  @ApiProperty({
    type: 'string',
    format: 'datetime',
    required: true,
  })
  modifiedAt: string;

  static fromDomain(domain: Comment) {
    const { id, bsgUserId, comment, isAnonymous, createdAt, modifiedAt } =
      domain.getPropsCopy();

    return new ConsumerCommentResponseDtoV1({
      id: id.value,
      bsgUserId: bsgUserId.value,
      comment: comment,
      isAnonymous: isAnonymous,
      createdAt: createdAt.toUTC().toISO(),
      modifiedAt: modifiedAt.toUTC().toISO(),
    });
  }
}
