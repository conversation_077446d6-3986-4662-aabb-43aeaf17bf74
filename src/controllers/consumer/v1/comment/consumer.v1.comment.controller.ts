import {
  CustomerJwtGuard,
  GetUserInfo,
  UserInfo,
} from '@bluesg-2/customer-authentication';
import { CUSTOMER_BEARER_AUTH } from '@bluesg-2/queue-pop';
import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ApiResponseSchema } from 'src/api/api-response.decorator';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { BsgUserId } from 'src/common/tiny-types';
import { CommentService } from 'src/logic/comment/comment.service';
import { Comment } from 'src/logic/comment/domain/comment.domain';
import { CreateCommentDtoV1 } from './dto/request/create-comment.v1.dto';
import { ConsumerCommentResponseDtoV1 } from './dto/response/consumer.comment.response.dto.v1';

@ApiBearerAuth(CUSTOMER_BEARER_AUTH)
@UseGuards(CustomerJwtGuard)
@ApiTags(RouteSegment.Comment.Index)
@Controller({
  version: ApiVersion.V1,
  path: `${RentalServiceRootRouteSegment.Consumer}/${RouteSegment.Comment.Index}`,
})
export class ConsumerCommentControllerV1 {
  constructor(private readonly commentService: CommentService) {}

  @ApiOperation({
    description: 'Create new comment',
  })
  @ApiResponseSchema(ConsumerCommentResponseDtoV1, true)
  @Post(RouteSegment.Comment.Create)
  async createComment(
    @GetUserInfo() userInfo: UserInfo,
    @Body() dto: CreateCommentDtoV1,
  ): Promise<ConsumerCommentResponseDtoV1> {
    const commentDomain = new Comment({
      bsgUserId: new BsgUserId(userInfo.id),
      comment: dto.comment,
      isAnonymous: dto.isAnonymous,
    });

    const comment = await this.commentService.createComment(commentDomain);

    return ConsumerCommentResponseDtoV1.fromDomain(comment);
  }
}
