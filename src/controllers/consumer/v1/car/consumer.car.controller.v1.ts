import {
  CustomerJwtGuard,
  GetUserInfo,
  UserInfo,
} from '@bluesg-2/customer-authentication';
import { CorrelationIdService } from '@bluesg-2/monitoring';
import {
  CUSTOMER_BEARER_AUTH,
  LocalPostStartRentalEventV1,
  QueuePopCarModel,
} from '@bluesg-2/queue-pop';
import {
  Controller,
  HttpStatus,
  Logger,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiParam, ApiTags } from '@nestjs/swagger';
import * as asyncRetry from 'async-retry';
import { ApiResponseSchema } from 'src/api/api-response.decorator';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { RentalError } from 'src/common/errors/rental-error';
import { RentalTechnicalError } from 'src/common/errors/rental-technical-error';
import { BsgUserId, VulogCarId } from 'src/common/tiny-types';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { CarService } from 'src/logic/car/car.service';
import { Car } from 'src/logic/car/domain/car';
import { CarNotFoundError } from 'src/logic/car/errors/car-not-found.error';
import { ReservationService } from 'src/logic/reservation.service';
import { VulogTelemetryService } from 'src/logic/telemetry-repository/vulog/vulog-telemetry.service';
import { VulogCarConnectionProblemService } from 'src/logic/vulog/vulog-car-connection-problem.service';
import { Reservation } from 'src/model/reservation.domain';
import { TelemetryCarInfo } from 'src/model/telemetry-car-info';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { CarInfo } from '~shared/feature-flag/models/car-info';
import { LocalEventEmitterService } from '~shared/local-event-emitter/local-event-emitter.service';
import { NewRelicRentalEventService } from '~shared/newrelic/event/event-type/rental.event.newrelic.service';
import { NewRelicReservationEventService } from '~shared/newrelic/event/event-type/reservation.event.newrelic.service';
import {
  NewRelicMetricStepInFlow,
  NewRelicMetricTrigger,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { CarIdParamDto } from './dto/car-id.param.dto';
import { ConsumerLockAndImmobilizeResponseDtoV1 } from './dto/consumer.lock-and-immobilize.response.dto.v1';

@ApiBearerAuth(CUSTOMER_BEARER_AUTH)
@UseGuards(CustomerJwtGuard)
@ApiTags(RouteSegment.Car.Index)
@Controller({
  path: `${RentalServiceRootRouteSegment.Consumer}/${RouteSegment.Car.Index}`,
  version: ApiVersion.V1,
})
export class ConsumerCarControllerV1 {
  constructor(
    private readonly vulogTelemetryService: VulogTelemetryService,
    private readonly carService: CarService,
    private readonly reservationService: ReservationService,
    private readonly vulogCarService: VulogCarService,
    private readonly vulogCarConnectionProblemService: VulogCarConnectionProblemService,
    private readonly localEventEmitterService: LocalEventEmitterService,
    private readonly correlationIdService: CorrelationIdService,
    private readonly reservationEventService: NewRelicReservationEventService,
    private readonly rentalEventService: NewRelicRentalEventService,
    private readonly featureFlagService: FeatureFlagService,
  ) {}

  private generalizeCarLoggingInfo({
    locked,
    immobilized,
    vulogCarId,
    sideEffect = false,
  }: {
    locked: boolean;
    immobilized: boolean;
    vulogCarId: VulogCarId;
    sideEffect?: boolean;
  }) {
    Logger.log(
      `The vehicle is ${locked ? 'LOCKED' : 'UNLOCKED'} and ${
        immobilized ? 'IMMOBILIZED' : 'MOBILIZED'
      }`,
      {
        details: {
          locked,
          immobilized,
          sideEffect,
        },
        vulogCarId: vulogCarId.value,
      },
    );
  }

  private async performCarChecks(
    vulogCarId: VulogCarId,
    bsgUserId: BsgUserId,
  ): Promise<{
    car: Car;
    locked: boolean;
    immobilized: boolean;
    ownOnGoingRental: Reservation;
  }> {
    const car = await this.carService.getOneByVulogId(vulogCarId);

    if (!car) {
      throw new CarNotFoundError(
        `The car (Vulog Car ID: ${vulogCarId.value}) is not found in BlueSG system`,
      );
    }

    const ownOnGoingRental: Reservation =
      await this.reservationService.getOwnOnGoingRental(bsgUserId);

    if (!ownOnGoingRental) {
      throw new RentalError(
        `The customer (BSG User Id: ${bsgUserId.value}) does not have on-going rental in BlueSG`,
        HttpStatus.BAD_REQUEST,
      );
    }

    const { carId: onGoingVulogCarId } = ownOnGoingRental;

    if (!onGoingVulogCarId.equals(vulogCarId)) {
      throw new RentalError(
        `The target car (Vulog Car ID: ${vulogCarId.value}) is not the same as the car in user-current on-going rental (Vulog Car ID: ${onGoingVulogCarId.value})`,
        HttpStatus.BAD_REQUEST,
      );
    }

    const telemetryCarInfo: TelemetryCarInfo =
      await this.vulogTelemetryService.getCarStatusSessionCommand(
        car.vulogId,
        true,
      );

    return {
      car,
      locked: telemetryCarInfo.locked,
      immobilized: telemetryCarInfo.immobilizerOn,
      ownOnGoingRental: ownOnGoingRental,
    };
  }

  private async retryFetchingTelemetryCarInfo(
    vulogCarId: VulogCarId,
    conditionCb: (telemetryCarInfo: TelemetryCarInfo) => boolean,
  ): Promise<TelemetryCarInfo> {
    const telemetryCarInfo: TelemetryCarInfo =
      await asyncRetry<TelemetryCarInfo>(
        async () => {
          const telemetryCarInfo =
            await this.vulogTelemetryService.getCarStatusSessionCommand(
              vulogCarId,
            );

          if (conditionCb(telemetryCarInfo)) {
            throw new Error('Not expected response, retrying again');
          }

          return telemetryCarInfo;
        },
        {
          factor: 2,
          retries: 6,
        },
      );

    return telemetryCarInfo;
  }

  @Post(RouteSegment.Car.Lock)
  @ApiParam({
    name: 'carId',
    description: 'Vulog Car ID',
  })
  @ApiResponseSchema(ConsumerLockAndImmobilizeResponseDtoV1)
  async lockAndImmobilizeCar(
    @Param() { carId }: CarIdParamDto,
    @GetUserInfo() userInfo: UserInfo,
  ): Promise<ConsumerLockAndImmobilizeResponseDtoV1> {
    const vulogCarId = new VulogCarId(carId);

    const { locked, immobilized, car } = await this.performCarChecks(
      vulogCarId,
      new BsgUserId(userInfo.id),
    );

    if (locked && immobilized) {
      return {
        locked,
        immobilized,
      };
    }

    try {
      await this.vulogCarConnectionProblemService.retryWhenEncounterCarConnectionProblem(
        {
          vulogCarId,
          callback: async () => {
            await this.vulogTelemetryService.lockAndImmobilizeCar(car.vulogId);
          },
          step: NewRelicMetricStepInFlow.LockAndImmobilize,
        },
      );
    } catch (err) {
      throw new RentalError(
        `Failed to lock and immobilize the vehicle ${vulogCarId.value}. Please retry again.`,
        HttpStatus.GONE,
      );
    }

    let telemetryCarInfo: TelemetryCarInfo;

    try {
      telemetryCarInfo = await this.retryFetchingTelemetryCarInfo(
        car.vulogId,
        (telemetryCarInfo) =>
          !telemetryCarInfo.locked || !telemetryCarInfo.immobilizerOn,
      );
    } catch (err) {
      throw new RentalError(
        'The car is either unlocked or mobilized. Please retry again to lock and immobilize the car',
        HttpStatus.BAD_REQUEST,
      );
    }

    this.generalizeCarLoggingInfo({
      locked: telemetryCarInfo.locked,
      immobilized: telemetryCarInfo.immobilizerOn,
      vulogCarId: telemetryCarInfo.id,
    });

    return {
      locked: telemetryCarInfo.locked,
      immobilized: telemetryCarInfo.immobilizerOn,
    };
  }

  private async startRental(ownOnGoingRental: Reservation, car: Car) {
    try {
      await this.vulogCarService.startRental(
        ownOnGoingRental.bsgUserId,
        ownOnGoingRental.vulogTripId,
      );

      await this.rentalEventService.emitStartedAtVulog(
        NewRelicMetricTrigger.Mobile,
        ownOnGoingRental.getMetricNewRelicRentalType(),
        ownOnGoingRental.getMetricNewRelicCarModel(),
      );

      this.localEventEmitterService.dispatchEvent(
        new LocalPostStartRentalEventV1({
          bsgUserId: ownOnGoingRental.bsgUserId.value,
          correlationId: this.correlationIdService.getCorrelationId().value,
          carMetadata: {
            id: car.id.value,
            model: ownOnGoingRental.carModel as unknown as QueuePopCarModel,
            stationId: ownOnGoingRental.startStationId.value,
            vulogId: car.vulogId.value,
          },
        }),
      );
    } catch (err) {
      if (err instanceof RentalTechnicalError) {
        await this.reservationEventService.emitTerminatedByTechnicalError(
          ownOnGoingRental.getMetricNewRelicCarModel(),
          NewRelicMetricStepInFlow.StartRental,
        );
      }

      throw err;
    }
  }

  private async triggerStartingRentalWhenNotExist(
    car: Car,
    ownOnGoingRental: Reservation,
  ): Promise<void> {
    const vulogOnGoingRental =
      await this.vulogCarService.getSpecificOnGoingRental(car);

    if (!vulogOnGoingRental) {
      await this.startRental(ownOnGoingRental, car);
    }
  }

  @Post(RouteSegment.Car.Unlock)
  @ApiParam({
    name: 'carId',
    description: 'Vulog Car ID',
  })
  @ApiResponseSchema(ConsumerLockAndImmobilizeResponseDtoV1)
  async unlockAndMobilizeCar(
    @Param() { carId }: CarIdParamDto,
    @GetUserInfo() userInfo: UserInfo,
  ): Promise<ConsumerLockAndImmobilizeResponseDtoV1> {
    const bsgUserId = new BsgUserId(userInfo.id);

    const { locked, immobilized, car, ownOnGoingRental } =
      await this.performCarChecks(new VulogCarId(carId), bsgUserId);

    const startRentalAutoUnlocked: boolean =
      await this.featureFlagService.startRentalAutoUnlocked(
        CarInfo.fromCar(car),
      );

    /**
     * CRS received car unlocking acknowledgement
     */
    if (!startRentalAutoUnlocked && !locked && !immobilized) {
      Logger.log('The car is already unlocked and mobilized', {
        car,
      });

      await this.triggerStartingRentalWhenNotExist(car, ownOnGoingRental);

      return {
        locked,
        immobilized,
      };
    }

    if (startRentalAutoUnlocked) {
      await this.startRental(ownOnGoingRental, car);

      this.generalizeCarLoggingInfo({
        locked: false,
        immobilized: false,
        vulogCarId: car.vulogId,
        sideEffect: true,
      });

      // Return `false` immediately because Vulog Starting Rental API has side effect "auto unlocking car"
      return {
        locked: false,
        immobilized: false,
      };
    } else {
      try {
        await this.vulogCarConnectionProblemService.retryWhenEncounterCarConnectionProblem<void>(
          {
            vulogCarId: car.vulogId,
            callback: async () => {
              await this.vulogTelemetryService.unlockAndMobilizeCar(
                car.vulogId,
              );
            },
            step: NewRelicMetricStepInFlow.UnlockAndMobilize,
          },
        );
      } catch (err) {
        Logger.error(
          `(1) Failed to unlock and mobilize the vehicle, err details: `,
          err,
        );

        Logger.error(
          `(1) Failed to unlock and mobilize the vehicle, err stack: `,
          err.stack,
        );

        throw new RentalError(
          `(1) Failed to unlock and mobilize the vehicle ${car.vulogId.value}. Please retry again until successful response is returned.`,
          HttpStatus.INTERNAL_SERVER_ERROR, // Should not return status code 502 (Bad Gateway) as BlueSG uses CloudFlare, and Cloudflare has logic to redirect customer to Cloudflare's Bad Request page. See: https://developers.cloudflare.com/support/troubleshooting/cloudflare-errors/troubleshooting-cloudflare-5xx-errors/#502504-from-your-origin-web-server
        );
      }
    }

    let telemetryCarInfo: TelemetryCarInfo;

    try {
      telemetryCarInfo = await this.retryFetchingTelemetryCarInfo(
        car.vulogId,
        (telemetryCarInfo) =>
          telemetryCarInfo.locked || telemetryCarInfo.immobilizerOn,
      );
    } catch (err) {
      Logger.error(
        `(2) Failed to unlock and mobilize the vehicle, err details: `,
        err,
      );

      Logger.error(
        `(2) Failed to unlock and mobilize the vehicle, err stack: `,
        err.stack,
      );

      throw new RentalError(
        `(2) Failed to unlock and mobilize the vehicle ${car.vulogId.value}. Please retry again until successful response is returned.`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    this.generalizeCarLoggingInfo({
      locked: telemetryCarInfo.locked,
      immobilized: telemetryCarInfo.immobilizerOn,
      vulogCarId: telemetryCarInfo.id,
    });

    /**
     * CRS received car unlocking acknowledgement
     */
    await this.triggerStartingRentalWhenNotExist(car, ownOnGoingRental);

    return {
      locked: telemetryCarInfo.locked,
      immobilized: telemetryCarInfo.immobilizerOn,
    };
  }
}
