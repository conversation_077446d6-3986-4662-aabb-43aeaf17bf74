import { ApiParam, ApiQuery } from '@nestjs/swagger';

export const QueryUserId = ApiQuery({
  name: 'userId',
  description: 'BlueSG UUID representing the user',
});

export const ParamCarId = ApiParam({
  name: 'carId',
  description: 'UUID representing the target car',
});

export const ParamStationId = ApiParam({
  name: 'stationId',
  description: 'UUID representing the target station',
});

export const ParamReservationId = ApiParam({
  name: 'reservationId',
  description: 'UUID representing the target reservation',
});
