import { Module } from '@nestjs/common';
import { UserSubscriptionExternalModule } from 'src/external/user-subscription/user-subscription.external.module';
import { LogicModule } from 'src/logic/logic.module';
import { AdminCarsController } from './admin-cars.controller';
import { AdminCarControllerV1 } from './admin/v1/car/admin.car.controller.v1';
import { AdminCustomerControllerV1 } from './admin/v1/customer/admin.customer.controller.v1';
import { AdminRentalController } from './admin/v1/rental/admin-rental.contoller';
import { AdminReservationControllerV1 } from './admin/v1/reservation/admin.v1.reservation.controller';
import { AvailabilityController } from './availability.controller';
import { AvailabilityControllerV2 } from './availability.controller.v2';
import { ConsumerCarControllerV1 } from './consumer/v1/car/consumer.car.controller.v1';
import { ConsumerCommentControllerV1 } from './consumer/v1/comment/consumer.v1.comment.controller';
import { InternalCarControllerV1 } from './internal/v1/car/internal.car.v1.controller';
import { InternalLocationControllerV1 } from './internal/v1/location/internal.location.v1.controller';
import { InternalReservationControllerV1 } from './internal/v1/reservation/internal.reservation.v1.controller';
import { InternalVulogZonesControllerV1 } from './internal/v1/vulog-zones/internal.vulog-zones.v1.controller';
import { InternalWebhookNotifierControllerV1 } from './internal/v1/webhook-notifier/internal.v1.webhook-notifier.controller';
import { MigrationController } from './migration.controller';
import { RentalController } from './rental.controller';
import { ReservationController } from './reservation.controller';
import { StationController } from './station.controller';

@Module({
  imports: [LogicModule, UserSubscriptionExternalModule],
  controllers: [
    AvailabilityController,
    AvailabilityControllerV2,
    AdminCarsController,
    ReservationController,
    RentalController,
    AdminRentalController,
    InternalReservationControllerV1,
    InternalWebhookNotifierControllerV1,
    MigrationController,
    StationController,
    ConsumerCommentControllerV1,
    AdminReservationControllerV1,
    ConsumerCarControllerV1,
    InternalVulogZonesControllerV1,
    AdminCarControllerV1,
    InternalCarControllerV1,
    AdminCustomerControllerV1,
    InternalLocationControllerV1,
    InternalReservationControllerV1,
  ],
  providers: [],
})
export class WebApi {}
