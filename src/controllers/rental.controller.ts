import { ApiResponseSchema } from 'src/api/api-response.decorator';
import { ApiVersion } from 'src/common/constants/api-versions';
import { BsgUserId, ReservationId } from 'src/common/tiny-types';
import { RentalService } from 'src/logic/rental.service';

import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import {
  CustomerJwtGuard,
  GetUserInfo,
  UserInfo,
} from '@bluesg-2/customer-authentication';
import { CUSTOMER_BEARER_AUTH } from '@bluesg-2/queue-pop';
import { PageResult } from '@bluesg-2/queue-pop/dist/common/controllers/domains/page-result';
import { RouteSegment } from 'src/common/constants/api-path';
import { Latitude } from 'src/common/tiny-types/latitude';
import { Longitude } from 'src/common/tiny-types/longitude';
import { StationService } from 'src/logic/station/station.service';
import { GpsCoordinates } from 'src/model/gps-coordinates';
import { Station } from 'src/model/station';
import { TestingEndRentalConditionResult } from 'src/model/testing-end-rental.report';
import { AttachLocationRequestDtoV1 } from './dtos/rental/request/attach-location.request.dto.v1';
import { EndRentalSimulationRequestDto } from './dtos/rental/request/end-rental-simulation.request.dto';
import { RentalQueryDtoV1 } from './dtos/rental/request/rental.query.v1.dto';
import { ReservationIdParamDto } from './dtos/rental/request/reservation-id.param.dto';
import {
  EndRentalConditionResultResponseV1Dto,
  EndRentalResponseV1Dto,
} from './dtos/rental/response/end-rental.response.v1.dto';
import { OnGoingRentalResponseDtoV1 } from './dtos/rental/response/on-going-rental.response.v1.dto';
import {
  RentalResponseDtoV1,
  StationResponseDtoV1,
} from './dtos/rental/response/rental-response.v1.dto';
import { ParamReservationId } from './api-params.decorator';
import { EndRentalWithQRRequestDtoV1 } from './dtos/rental/request/end-rental-with-qr.request.dto.v1';
import { StationError } from 'src/common/errors/station.error';
import { StationNotFoundNewError } from "../common/errors/external/station-not-found-new.error";
import { StationStatus } from "../external/station/dtos/station-status";
import { StationDisabledError } from "../common/errors/external/station-disabled.error";

@ApiBearerAuth(CUSTOMER_BEARER_AUTH)
@UseGuards(CustomerJwtGuard)
@ApiTags(RouteSegment.Rental.Index)
@Controller({
  path: RouteSegment.Rental.Index,
  version: ApiVersion.V1,
})
export class RentalController {
  constructor(
    private rentalService: RentalService,
    private readonly stationService: StationService,
  ) {}

  @ApiResponseSchema(OnGoingRentalResponseDtoV1)
  @Get('on-going')
  async getOngoingRental(
    @GetUserInfo() userInfo: UserInfo,
  ): Promise<OnGoingRentalResponseDtoV1> {
    const ongoingRental = await this.rentalService.getCurrentRentalDetail(
      new BsgUserId(userInfo.id),
    );

    return ongoingRental
      ? OnGoingRentalResponseDtoV1.from(ongoingRental)
      : null;
  }

  @ApiResponseSchema(EndRentalConditionResultResponseV1Dto, true)
  @Get(':rentalId/conditions')
  async getRentalConditions(
    @Param('rentalId') rentalId: string,
  ): Promise<EndRentalConditionResultResponseV1Dto[]> {
    const conditions: TestingEndRentalConditionResult[] =
      await this.rentalService.getRentalConditions(new ReservationId(rentalId));

    return conditions.map((item) =>
      EndRentalConditionResultResponseV1Dto.fromTesting(item),
    );
  }

  @ApiOperation({
    description:
      'End the current rental of a user, returns the list of conditions checked before to end the rental.',
  })
  @ApiResponseSchema(EndRentalResponseV1Dto)
  @Delete()
  async endRental(
    @GetUserInfo() userInfo: UserInfo,
  ): Promise<EndRentalResponseV1Dto> {
    return EndRentalResponseV1Dto.fromTesting(
      await this.rentalService.endRentalTesting(new BsgUserId(userInfo.id)),
    );
  }

  // NOTE: Currently, we don't have implementation for webhook to receive END_RENTAL returned by VULOG
  // So I create a http request to simulate the action of receiving data from VULOG with similar payload returned by VULOG
  @ApiOperation({
    description: 'Just for testing purpose',
  })
  @Post('end/simulate')
  async endRentalSimulation(
    @Body() requestDto: EndRentalSimulationRequestDto,
  ): Promise<void> {
    await this.rentalService.endRentalSimulation(requestDto);
  }

  @ParamReservationId
  @ApiOperation({
    summary: 'End rental using a QR code',
    description: 'Ends an ongoing rental by scanning a QR code at a parking station',
  })
  @ApiResponse({ status: HttpStatus.OK, description: 'Rental ended successfully' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid QR code or parking station' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'No ongoing rental found for the user' })
  @ApiResponse({ status: HttpStatus.INTERNAL_SERVER_ERROR, description: 'An unexpected error occurred' })
  @ApiResponseSchema(EndRentalResponseV1Dto)
  @Post(':reservationId/end')
  async endRentalWithQR(
    @GetUserInfo() userInfo: UserInfo,
    @Body() requestDto: EndRentalWithQRRequestDtoV1,
  ): Promise<EndRentalResponseV1Dto> {
    try {
      const bsgUserId = new BsgUserId(userInfo.id);

      const station = await this.stationService.getStationByQRCode(
        requestDto.qrCode,
      );

      if (!station) throw new StationNotFoundNewError();
      if (station.status === StationStatus.Closed)
        throw new StationDisabledError();

      const ongoingRental = await this.rentalService.getCurrentRentalDetail(bsgUserId);

      if (!ongoingRental) {
        throw new NotFoundException('No ongoing rental found for the user');
      }
      const endRentalReport = await this.rentalService.endRentalUsingQR(
        ongoingRental,
        bsgUserId,
        requestDto.qrCode
      );
      return EndRentalResponseV1Dto.fromTesting(endRentalReport);
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof StationNotFoundNewError ||
        error instanceof StationDisabledError
      ) {
        throw error;
      } else if (error instanceof StationError) {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException('An unexpected error occurred while ending the rental');
    }
  }

  @ApiOperation({
    description: 'Attach Mobile GPS location to the the rental',
  })
  @Patch(':reservationId/location')
  async attachLocation(
    @Param() params: ReservationIdParamDto,
    @Body() requestDto: AttachLocationRequestDtoV1,
    @GetUserInfo() userInfo: UserInfo,
  ): Promise<void> {
    await this.rentalService.attachLocation(
      new ReservationId(params.reservationId),
      new GpsCoordinates({
        longitude: new Longitude(requestDto.location.long),
        latitude: new Latitude(requestDto.location.lat),
      }),
      userInfo,
    );
  }

  @ApiOperation({
    description: 'To get all rentals for current user',
  })
  // Currently, reservation and rental has the same format, it should be separated once we split Reservation table into Reservation and Rental table
  @ApiResponseSchema(RentalResponseDtoV1, true)
  @Get(RouteSegment.Rental.History)
  async getAllRentals(
    @GetUserInfo() userInfo: UserInfo,
    @Query()
    queryParams: RentalQueryDtoV1,
  ): Promise<PageResult<RentalResponseDtoV1[]>> {
    // For Bff and mobile purposes, stations are mapped in controller instead of service
    const stations = await this.stationService.getAllStations();

    const stationsMap = new Map<string, Station>(
      stations
        .filter((s) => !!s.id)
        .map((station) => [station.id.value, station]),
    );

    const pageResult = await this.rentalService.getAllRentals(
      queryParams.toPagination(),
      new BsgUserId(userInfo.id),
    );

    const responses = pageResult.result.map((item) => {
      const startStation = item.startStationId
        ? stationsMap.get(item.startStationId.value)
        : null;
      const endStation = item.endStationId
        ? stationsMap.get(item?.endStationId.value)
        : null;

      return RentalResponseDtoV1.fromReservation(
        item,
        startStation
          ? new StationResponseDtoV1({
              id: startStation.id.value,
              sourceId: startStation.sourceId,
              name: startStation.displayName,
            })
          : null,
        endStation
          ? new StationResponseDtoV1({
              id: endStation.id.value,
              sourceId: endStation.sourceId,
              name: endStation.displayName,
            })
          : null,
      );
    });

    return PageResult.from(
      responses,
      pageResult.pagination,
      pageResult.total,
      pageResult.sortKey,
      pageResult.sortType,
    );
  }
}
