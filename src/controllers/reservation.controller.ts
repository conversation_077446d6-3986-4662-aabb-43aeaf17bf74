import {
  CustomerJwtGuard,
  GetUserInfo,
  UserInfo,
} from '@bluesg-2/customer-authentication';
import { CUSTOMER_BEARER_AUTH } from '@bluesg-2/queue-pop';
import {
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { ApiResponseSchema } from 'src/api/api-response.decorator';
import { RouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationError } from 'src/common/errors/reservation-error';
import {
  BsgUserId,
  CarModel,
  CarPlateNumber,
  ReservationId,
  VulogCarId,
} from 'src/common/tiny-types';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { RentalService } from 'src/logic/rental.service';
import { ReservationService } from 'src/logic/reservation.service';
import { ReservationInfo } from 'src/model/reservation-info';
import { Reservation } from 'src/model/reservation.domain';
import { ParamReservationId, ParamStationId } from './api-params.decorator';
import { AttachRentalPackageRequestDtoV1 } from './dtos/rental/request/attach-rental-package.v1.request.dto';
import { ReservationIdParamDtoV1 } from './dtos/rental/request/reservation-id.v1.param.dto';
import { SwitchCarRequestDtoV1 } from './dtos/rental/request/switch-car.request.dto.v1';

import * as _ from 'lodash';
import { PageResult } from 'src/common/api/page-result';
import { ReservationNotFoundError } from 'src/common/errors/reservation/reservation-not-found.error';
import { MapUtils } from 'src/common/utils/map.util';
import { BillingExternalService } from 'src/external/billing/billing.external.service';
import { InternalGetRentalPackageV1ResponseDto } from 'src/external/billing/dtos/response/internal.get-rental-packges.v1.response.dto';
import { CarExternalService } from 'src/external/car/car.external.service';
import { InternalCarsRequestV1 } from 'src/external/car/dtos/internal.cars.request.v1.dto';
import { InternalCarResponseV1 } from 'src/external/car/dtos/internal.cars.response.v1.dto';
import { AvailabilityService } from 'src/logic/availability.service';
import { Car } from 'src/logic/car/domain/car';
import { VulogFleetService } from 'src/logic/vulog/vulog-fleet-service.service';
import { VulogCarRealTimeDto } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { FleetService } from 'src/model/fleet-service';
import { RentalPackage } from 'src/model/rental-package';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { CarInfo } from '~shared/feature-flag/models/car-info';
import { NewRelicRentalEventService } from '~shared/newrelic/event/event-type/rental.event.newrelic.service';
import { InternalError } from '../common/errors/internal-error';
import { CarService } from '../logic/car/car.service';
import { RealtimeCar } from '../model/realtime.car';
import { FeedbackReservationResponseDtoV1 } from './dtos/feedback-reservation.response.v1.dto';
import { ReservationIdParamDto } from './dtos/rental/request/reservation-id.param.dto';
import { UpdateRentalFeedBackRequestDtoV1 } from './dtos/rental/request/update-rental-feedback.v1.request.dto';
import { StartRentalResponseDtoV1 } from './dtos/rental/response/start-rental.response.v1.dto';
import { ReservationResponseDtoV1 } from './dtos/reservation.response.v1.dto';
import { ReservationQueryDtoV1 } from './dtos/reservation/request/reservation.query.v1.dto';
import { ReservationRequestDto } from './dtos/reservation/request/reservation.request.dto';
import { AvailableCarsForReservationResponseDtoV1 } from './dtos/reservation/response/available-cars-for-reservation.response.v1.dto';
import { AvailableRentalPackagesForReservationResponseDtoV1 } from './dtos/reservation/response/available-rental-packages-for-reservation.response.v1.dto';

@ApiBearerAuth(CUSTOMER_BEARER_AUTH)
@UseGuards(CustomerJwtGuard)
@ApiTags('reservation')
@Controller({
  path: 'reservation',
  version: ApiVersion.V1,
})
export class ReservationController {
  constructor(
    private reservationService: ReservationService,
    private vulogCarClient: VulogCarService,
    private rentalService: RentalService,
    private availabilityService: AvailabilityService,
    private billingExternalService: BillingExternalService,
    private readonly carService: CarService,
    private readonly carExternalService: CarExternalService,
    private readonly rentalEventService: NewRelicRentalEventService,
    private readonly vulogFleetService: VulogFleetService,
    private readonly featureFlagService: FeatureFlagService,
  ) {}

  @ApiOperation({
    description: 'API to change car that is attached to a reservation',
  })
  @Put(':reservationId/cars/:plateNumber')
  @ApiResponseSchema(ReservationResponseDtoV1)
  async switchAllocationCar(
    @Param() params: SwitchCarRequestDtoV1,
    @GetUserInfo() userInfo: UserInfo,
  ): Promise<ReservationResponseDtoV1> {
    const bsgUserId = new BsgUserId(userInfo.id);

    const ownReservation: Reservation = await this.reservationService.getOne(
      new ReservationId(params.reservationId),
    );

    this.checkOnGoingReservation(ownReservation);

    const ownTargetCar = await this.carService.getOneByPlateNumber(
      new CarPlateNumber(params.plateNumber),
    );

    if (!ownTargetCar) {
      throw new ReservationError(
        `The target car (Plate: ${params.plateNumber}) is not found`,
        HttpStatus.BAD_REQUEST,
      );
    }

    // The case when mobile app reserves the same currently allocated vehicle
    if (ownTargetCar.vulogId.equals(ownReservation.carId)) {
      return ReservationResponseDtoV1.fromReservation(
        ownReservation,
        ownReservation.carModel,
      );
    }

    const updatedReservation: Reservation =
      await this.reservationService.switchAllocationCar(
        bsgUserId,
        ownReservation,
        ownTargetCar.vulogId,
      );

    return ReservationResponseDtoV1.fromReservation(
      updatedReservation,
      updatedReservation.carModel as CarModelEnum,
    );
  }

  @ParamStationId
  @Post('/allocate/:stationId')
  @ApiResponseSchema(ReservationResponseDtoV1)
  async allocateCar(
    @Param('stationId') stationId: string,
    @GetUserInfo() userInfo: UserInfo,
    @Body() requestDto: ReservationRequestDto,
  ): Promise<ReservationResponseDtoV1> {
    const result = await this.reservationService.allocateCar(
      stationId,
      userInfo,
      requestDto?.carModel,
      requestDto?.local,
    );

    return ReservationResponseDtoV1.fromReservation(result, result.carModel);
  }

  @Get('on-going')
  @ApiResponseSchema(ReservationResponseDtoV1)
  async getOnGoingReservation(
    @GetUserInfo() userInfo: UserInfo,
  ): Promise<ReservationResponseDtoV1> {
    const reservationInfos =
      await this.vulogCarClient.getUserCurrentReservations(
        new BsgUserId(userInfo.id),
      );

    if (!reservationInfos || !reservationInfos.length) {
      return null;
    }

    const ownReservation = await this.reservationService.findOneByTripId(
      reservationInfos[0].sourceId,
    );

    const firstReservationInfo = {
      ...reservationInfos[0],
      startStationId: ownReservation.startStationId,
      status: ownReservation.status,
      plateNumber: ownReservation.plateNumber,
      local: ownReservation.local,
      carModel: ownReservation.carModel,
      entityId: new ReservationId(ownReservation.getId),
      bsgUserId: new BsgUserId(userInfo.id),
    } as Omit<ReservationInfo, 'toDomain'>;

    return ReservationResponseDtoV1.fromReservation(
      new ReservationInfo(firstReservationInfo).toDomain(),
      firstReservationInfo.carModel,
    );
  }

  private checkOnGoingReservation(ownReservation: Reservation) {
    if (!ownReservation.isReserved || ownReservation.isExpired) {
      throw new ReservationError(
        'Client-specified reservation is not valid',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @ApiOperation({
    description: 'Get available cars for a reservation',
  })
  @ApiParam({
    name: 'reservationId',
  })
  @Get(':reservationId/available-cars')
  @ApiResponseSchema(AvailableCarsForReservationResponseDtoV1)
  async getAvailableCarsForReservation(
    @Param() params: ReservationIdParamDto,
    @GetUserInfo() userInfo: UserInfo,
  ): Promise<AvailableCarsForReservationResponseDtoV1> {
    const ownReservation = await this.reservationService.findOneById(
      new ReservationId(params.reservationId),
    );

    if (!ownReservation) {
      throw new ReservationError(
        'No reservation is found',
        HttpStatus.BAD_REQUEST,
      );
    }

    this.checkOnGoingReservation(ownReservation);

    const currentAllocatedCar =
      (await this.featureFlagService.isEnableCsCarAvailability(userInfo))
        ? await this.getCurrentAllocatedCarFromCs(ownReservation.carId)
        : await this.getCurrentAllocatedCarFromVulog(ownReservation.carId);

    const startStationId = ownReservation.startStationId;

    const availableCarsAtStation =
      await this.availabilityService.getAvailableCarsForSpecificStation(
        startStationId,
        userInfo,
        ownReservation.local ? ownReservation.carModel : null,
      );

    return AvailableCarsForReservationResponseDtoV1.from(
      currentAllocatedCar,
      _.uniqBy(
        availableCarsAtStation.concat(currentAllocatedCar),
        function (e) {
          return e.vulogId.value;
        },
      ),
      ownReservation,
    );
  }

  private async getCurrentAllocatedCarFromCs(
    vulogCarId: VulogCarId,
  ): Promise<Car> {
    return InternalCarResponseV1.toCar(
      (
        await this.carExternalService.internalApi.getCars(
          new InternalCarsRequestV1({
            externalCarId: vulogCarId.value,
          }),
        )
      )[0],
    );
  }

  private async getCurrentAllocatedCarFromVulog(
    vulogCarId: VulogCarId,
  ): Promise<Car> {
    const realtimeCars = await this.vulogCarClient.getCarsInRealtime();

    const realtimeCarsMap: Map<string, RealtimeCar> = MapUtils.build<
      string,
      RealtimeCar
    >(realtimeCars, (rltCar) => rltCar.id.value);

    return realtimeCarsMap.get(vulogCarId.value).toCar();
  }

  @ApiOperation({
    description: 'Get available rental packages for a reservation',
  })
  @ApiParam({
    name: 'reservationId',
  })
  @Get(':reservationId/available-rental-packages')
  @ApiResponseSchema(AvailableRentalPackagesForReservationResponseDtoV1)
  async getAvailableRentalPackagesForReservation(
    @Param() params: ReservationIdParamDto,
  ): Promise<AvailableRentalPackagesForReservationResponseDtoV1> {
    const ownReservation = await this.reservationService.findOneById(
      new ReservationId(params.reservationId),
    );

    if (!ownReservation) {
      throw new ReservationError(
        'No reservation is found',
        HttpStatus.BAD_REQUEST,
      );
    }

    this.checkOnGoingReservation(ownReservation);

    // Commented because BFF makes request to this API even though the reservation is non-local
    // if (!ownReservation.local) {
    //   throw new NonLocalReservationError(
    //     'Your on-going reservation is non-local. Could not get available rental packages',
    //   );
    // }

    const currentAttachedRentalPackage = !ownReservation.isNormalRental()
      ? ownReservation.getRentalPackageData()
      : null;

    const billingAvailRentalPackages: InternalGetRentalPackageV1ResponseDto[] =
      await this.billingExternalService.internalApi.getAvailableRentalPackages(
        ownReservation.carModel,
      );

    const realtimeCarsMap: {
      [plate: string]: VulogCarRealTimeDto;
    } = await this.vulogCarClient.getRealtimeCarsPlateMap();

    const car: Car = await this.carService.getOneByPlateNumber(
      ownReservation.plateNumber,
    );

    const filterOutNonCustomerZones =
      await this.featureFlagService.filterOutNonCustomerZones(
        CarInfo.fromCar(car),
      );

    const customerUsableFleetService: FleetService =
      await this.vulogFleetService.getCustomerUsableFleetService();

    const currentAllocatedRealtimeCar: RealtimeCar = realtimeCarsMap[
      ownReservation.plateNumber.value
    ].toRealtimeCar(
      car.model ? new CarModel(car.model) : null,
      filterOutNonCustomerZones
        ? customerUsableFleetService.getZoneIdsAsMap()
        : null,
    );

    const availableRentalPackages: RentalPackage[] =
      billingAvailRentalPackages.map((rentalPackage) =>
        rentalPackage.toRentalPackage(),
      );

    return AvailableRentalPackagesForReservationResponseDtoV1.from(
      currentAttachedRentalPackage,
      availableRentalPackages,
      ownReservation,
      currentAllocatedRealtimeCar,
    );
  }

  @Delete()
  @ApiResponseSchema(ReservationResponseDtoV1, true)
  async cancelAllReservations(
    @GetUserInfo() userInfo: UserInfo,
  ): Promise<ReservationResponseDtoV1[]> {
    const bsgUserId = new BsgUserId(userInfo.id);

    const bsgOnGoingRental = await this.reservationService.getOwnOnGoingRental(
      bsgUserId,
    );

    if (bsgOnGoingRental) {
      throw new ReservationError(
        `Customer (ID: ${bsgUserId}) does not have on-going reservation, but having on-going rental`,
        HttpStatus.BAD_REQUEST,
      );
    }

    const bsgOnGoingReservation =
      await this.reservationService.getBsgOnGoingReservationByBsgUserId(
        bsgUserId,
      );

    if (!bsgOnGoingReservation) {
      throw new ReservationError(
        `Customer (ID: ${bsgUserId}) does not have on-going reservation`,
        HttpStatus.BAD_REQUEST,
      );
    }

    const canceledReservationList =
      await this.reservationService.cancelCurrentReservation(
        new BsgUserId(userInfo.id),
      );

    return canceledReservationList.map((reservation) =>
      ReservationResponseDtoV1.fromReservation(
        reservation,
        reservation.carModel,
      ),
    );
  }

  @ApiOperation({
    description: 'Attach a rental package to a reservation',
  })
  @ApiParam({
    name: 'reservationId',
  })
  @Put(RouteSegment.Reservation.AttachRentalPackage)
  async attachRentalPackage(
    @Body() body: AttachRentalPackageRequestDtoV1,
    @Param() param: ReservationIdParamDtoV1,
  ): Promise<Partial<ReservationResponseDtoV1>> {
    const ownReservation = await this.reservationService.getOne(
      new ReservationId(param.reservationId),
    );

    if (!ownReservation) {
      throw new ReservationError(
        'No reservation is found',
        HttpStatus.BAD_REQUEST,
      );
    }

    this.checkOnGoingReservation(ownReservation);
    // TODO: This Error is blocking the current rental flow.
    /*    if (!ownReservation.local) {
      throw new NonLocalReservationError(
        'Your on-going reservation is non-local. Could not attach rental package',
      );
    }*/

    const reserve = await this.reservationService.attachRentalPackage(
      ownReservation,
      body,
    );

    return ReservationResponseDtoV1.fromReservation(reserve, reserve.carModel);
  }

  @ApiOperation({
    description: 'Detach a rental package from a reservation',
  })
  @ApiParam({
    name: 'reservationId',
  })
  @Delete(RouteSegment.Reservation.DetachRentalPackage)
  async detachRentalPackage(
    @Param() param: ReservationIdParamDtoV1,
  ): Promise<ReservationResponseDtoV1> {
    const reservation = await this.reservationService.getOne(
      new ReservationId(param.reservationId),
    );

    if (!reservation) {
      throw new ReservationError(
        'No reservation is found',
        HttpStatus.BAD_REQUEST,
      );
    }

    this.checkOnGoingReservation(reservation);

    const updatedReservation =
      await this.reservationService.detachRentalPackage(reservation);
    return ReservationResponseDtoV1.fromReservation(updatedReservation);
  }

  @ApiOperation({
    description: 'Update the reservation with rating and car clean status',
  })
  @ApiParam({
    name: 'reservationId',
  })
  @Put(RouteSegment.Reservation.UpdateRentalFeedBack)
  async updateRentalFeedBack(
    @Body() { carCleanliness, rating }: UpdateRentalFeedBackRequestDtoV1,
    @Param() { reservationId }: ReservationIdParamDtoV1,
  ): Promise<Partial<FeedbackReservationResponseDtoV1>> {
    let reservation = await this.reservationService.findOneById(
      new ReservationId(reservationId),
    );

    if (!reservation) {
      throw new ReservationNotFoundError();
    }

    if (reservation.isRated()) {
      return FeedbackReservationResponseDtoV1.fromReservation(reservation);
    }

    reservation.setRating = rating;
    reservation.setCarCleanliness = carCleanliness;
    reservation = await this.reservationService.updateReservation(reservation);

    await this.rentalEventService.emitRated(
      reservation.getMetricNewRelicRentalType(),
      reservation.getMetricNewRelicCarModel(),
    );

    return FeedbackReservationResponseDtoV1.fromReservation(reservation);
  }

  @ParamReservationId
  @ApiResponseSchema(StartRentalResponseDtoV1)
  @Post(':reservationId/start-rental')
  async startRental(
    @Param('reservationId') reservationId: string,
    @GetUserInfo() userInfo: UserInfo,
  ): Promise<StartRentalResponseDtoV1> {
    const ownReservation = await this.reservationService.findOneById(
      new ReservationId(reservationId),
    );

    if (!ownReservation) {
      throw new ReservationError(
        'No reservation found in database',
        HttpStatus.BAD_REQUEST,
      );
    }

    this.checkOnGoingReservation(ownReservation);

    const startRental = await this.rentalService.startRental(
      ownReservation,
      new BsgUserId(userInfo.id),
    );

    return StartRentalResponseDtoV1.from(startRental);
  }

  @ApiResponseSchema(ReservationResponseDtoV1, true)
  @Get(RouteSegment.Reservation.List)
  async getAllReservation(
    @GetUserInfo() userInfo: UserInfo,
    @Query() queryParams: ReservationQueryDtoV1,
  ): Promise<PageResult<ReservationResponseDtoV1[]>> {
    const pageResult = await this.reservationService.getAllReservations(
      queryParams.toPagination(),
      new BsgUserId(userInfo.id),
      queryParams.status || [],
    );

    const responses = pageResult.result.map((item) =>
      ReservationResponseDtoV1.fromReservation(item, item.carModel),
    );

    return PageResult.from(
      responses,
      pageResult.pagination,
      pageResult.total,
      pageResult.sortKey,
      pageResult.sortType,
    );
  }

  @ApiOperation({
    deprecated: true,
    description: 'Do not use this API endpoint. This is unfinished.',
  })
  @Post(RouteSegment.Reservation.EndRental)
  async endRental(
    @GetUserInfo() user: UserInfo,
    @Param('reservationId') reservationId: string,
  ) {
    let reservation: Reservation;
    try {
      reservation = await this.reservationService.getOne(
        new ReservationId(reservationId),
      );
    } catch (e) {
      if (e instanceof InternalError && e.cause === 'No reservation is found')
        throw new ReservationNotFoundError();
      throw e;
    }

    if (reservation.bsgUserId.value !== user.id)
      throw new ForbiddenException('You do not have access to this resource');
  }
}
