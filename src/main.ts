import 'newrelic';
import './env';

import { AUTHORIZATION_HEADER } from '@bluesg-2/customer-authentication/dist/guards/customer-jwt.guard';
import { AppLogger, CorrelationIdClsMiddleware } from '@bluesg-2/monitoring';
import { SqsEventTransportStrategy } from '@bluesg-2/sqs-event';
import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { ClsMiddleware } from 'nestjs-cls';
import { ApiSetup } from './api/api-setup';
import { AppModule } from './app.module';
import { config } from './config';
import { SwaggerSetup } from './swagger-setup';

const api = new ApiSetup();

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    bufferLogs: true,
  });

  app.use(
    new ClsMiddleware({
      setup: (cls, req, res): void => {
        CorrelationIdClsMiddleware.setup(cls, req, res);

        const authorizationHeader = (req.headers[
          AUTHORIZATION_HEADER.toLowerCase()
        ] || req.headers[AUTHORIZATION_HEADER]) as string;

        cls.set(AUTHORIZATION_HEADER, authorizationHeader);
      },
    }).use,
  );

  app.useLogger(app.get(AppLogger));

  api.setup(app);

  const swagger = new SwaggerSetup();
  swagger.setup(app);

  app.enableCors();

  app.connectMicroservice({
    strategy: new SqsEventTransportStrategy({
      queueUrl: config.sqs.url,
    }),
  });

  await app.startAllMicroservices();

  const PORT = config.webServer.port;

  await app.listen(PORT, () =>
    Logger.log(`Rental Service is listening at port ${PORT}`, 'WebServer'),
  );
}

bootstrap().catch((error) => {
  Logger.error(`Error while starting the application: ${error.message}`, {
    cause: error,
  });
});
