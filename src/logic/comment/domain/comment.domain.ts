import { DateTime } from 'luxon';
import { BaseDomain } from 'src/common/ddd/domain/class/base.domain';
import { BsgUserId, CommentId } from 'src/common/tiny-types';
import { CommentEntity } from '../entity/comment.entity';

export interface CommentDescription {
  bsgUserId: BsgUserId;
  comment: string;
  isAnonymous: boolean;
  createdAt?: DateTime;
  updatedAt?: DateTime;
}

export class Comment extends BaseDomain<CommentDescription, CommentEntity> {
  constructor(payload: CommentDescription, id?: CommentId) {
    super({
      props: payload,
      id,
    });
  }

  protected equalWith(
    obj: BaseDomain<CommentDescription, CommentEntity>,
  ): boolean {
    throw new Error('Method not implemented.');
  }

  public toEntity(): Partial<CommentEntity> {
    const commentEntity = new CommentEntity();
    commentEntity.bsgUserId = this.props.bsgUserId.value;
    commentEntity.comment = this.props.comment;
    commentEntity.isAnonymous = this.props.isAnonymous;
    commentEntity.createdAt = this.getCreatedAt.toJSDate();
    commentEntity.createdBy = this.getCreatedBy;
    return commentEntity;
  }

  /* eslint-disable-next-line*/
  public validate(): void {}
}
