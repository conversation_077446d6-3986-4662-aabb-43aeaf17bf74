import { DateTime } from 'luxon';
import { BsgUserId, CommentId } from 'src/common/tiny-types';
import { BaseEntity } from 'src/model/repositories/entities/base.entity';
import { Column, Entity } from 'typeorm';
import { Comment } from '../domain/comment.domain';

@Entity({ name: 'comment' })
export class CommentEntity extends BaseEntity {
  @Column({
    type: 'uuid',
    nullable: false,
  })
  bsgUserId: string;

  @Column({
    type: 'text',
    nullable: false,
  })
  comment: string;

  @Column({
    type: 'boolean',
    default: false,
    nullable: false,
  })
  isAnonymous: boolean;

  toDomain(): Comment {
    return new Comment(
      {
        bsgUserId: new BsgUserId(this.bsgUserId),
        comment: this.comment,
        isAnonymous: this.isAnonymous,
        createdAt: this.createdAt ? DateTime.fromJSDate(this.createdAt) : null,
        updatedAt: this.modifiedAt
          ? DateTime.fromJSDate(this.modifiedAt)
          : null,
      },
      new CommentId(this.id),
    );
  }
}
