import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Comment } from './domain/comment.domain';
import { CommentEntity } from './entity/comment.entity';

@Injectable()
export class CommentService {
  constructor(
    @InjectRepository(CommentEntity)
    private readonly commentRepo: Repository<CommentEntity>,
  ) {}

  async createComment(commentDomain: Comment): Promise<Comment> {
    const comment = await this.commentRepo.save(commentDomain.toEntity());
    return comment.toDomain();
  }
}
