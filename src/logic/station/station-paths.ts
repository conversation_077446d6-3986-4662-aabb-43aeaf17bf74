import { config } from 'src/config';
import { Url } from '../url';

export class StationPaths {
  static urlPrefix(): Url {
    return new Url(
      `http://${config.stationServiceConfig.host}:${config.stationServiceConfig.port}`,
    );
  }

  static stations(): Url {
    return new Url(`${this.urlPrefix()}/api/v1/stations`);
  }

  static getStation(stationId: string): Url {
    return new Url(`${this.stations()}/${stationId}`);
  }
}
