import { Injectable } from '@nestjs/common';
import { AxiosRequestConfig } from 'axios';
import { StationApiError } from 'src/common/errors/station-api-error';
import { PaginationQuery } from '../../common/query/pagination-query';
import { HttpAdapterService, ObjectOrVoid } from '../http-adapter.service';
import { Url } from '../url';

@Injectable()
export class StationAdapterService {
  constructor(private httpAdapter: HttpAdapterService) {}

  async get<T extends object>(
    url: Url,
    expectedDto: new () => T,
    customConfig: AxiosRequestConfig = {},
  ): Promise<ObjectOrVoid<T>> {
    return this.httpAdapter.get<T>(
      url,
      expectedDto,
      StationApiError,
      null,
      customConfig,
    );
  }

  async getAll<T extends object>(
    url: Url,
    expectedDto: new () => T,
    customConfig: AxiosRequestConfig = {},
  ): Promise<T[]> {
    return await this.httpAdapter.getAllPagesAsArray<T>(
      url,
      expectedDto,
      StationApiError,
      null,
      customConfig,
      null,
      new PaginationQuery(1, 200, undefined, undefined, true),
    );
  }
}
