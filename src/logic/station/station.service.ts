import { Nullable } from '@bluesg-2/bo-auth-guard/dist/types/nullable';
import { Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { StationId } from 'src/common/tiny-types';
import { MapUtils } from 'src/common/utils/map.util';
import { StationDto } from 'src/model/dtos/station.dto';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { Station } from 'src/model/station';
import { config } from '../../config';
import { StationExternalService } from '../../external/station/station.external.service';
import { CacheService } from '../cache/cache.service';
import { StationAdapterService } from './station-adapter.service';
import { StationPaths } from './station-paths';

@Injectable()
export class StationService {
  constructor(
    private stationAdapter: StationAdapterService,
    private cacheService: CacheService,
    private stationExternalService: StationExternalService,
  ) {}

  async getAllStations(): Promise<Station[]> {
    const cacheResult = await this.cacheService.get<Record<string, unknown>[]>(
      config.redisConfig.stations.cacheKey,
    );

    let allStations = cacheResult?.map((item) =>
      plainToInstance(StationDto, item),
    );

    if (!allStations) {
      allStations = await this.stationAdapter.getAll(
        StationPaths.stations(),
        StationDto,
      );

      await this.cacheService.set(
        config.redisConfig.stations.cacheKey,
        allStations,
        {
          ttl: config.redisConfig.stations.ttl,
        },
      );
    }

    const zoneStationMap = MapUtils.build(
      allStations,
      (station) => station.zoneId,
    );

    await this.cacheService.set(
      config.redisConfig.zoneStationMap.cacheKey,
      MapUtils.toObject(zoneStationMap),
      {
        ttl: config.redisConfig.zoneStationMap.ttl,
      },
    );

    return allStations ? allStations.map((stnDto) => stnDto.toStation()) : [];
  }

  async getZoneStationMap(): Promise<{ [zoneId: string]: StationDto }> {
    let cacheResult = await this.cacheService.get<{
      [zoneId: string]: StationDto;
    }>(config.redisConfig.zoneStationMap.cacheKey);

    if (!cacheResult || !Object.keys(cacheResult).length) {
      await this.getAllStations();
    }

    cacheResult = await this.cacheService.get<{ [zoneId: string]: StationDto }>(
      config.redisConfig.zoneStationMap.cacheKey,
    );

    const data: { [zoneId: string]: StationDto } = {};

    Object.entries(cacheResult).forEach(([key, stationDto]) => {
      data[key] = plainToInstance(StationDto, stationDto);
    });

    return data;
  }

  async getStation(stationId: StationId): Promise<Station> {
    const stations = await this.getAllStations();

    const expectedStation = stations.find((sts) => sts.id.equals(stationId));

    return expectedStation;
  }

  async getStationsMap(): Promise<Map<string, Station>> {
    const stations = await this.getAllStations();

    const map = new Map<string, Station>();
    stations.forEach((station) => {
      map.set(station.id.value, station);
    });

    return map;
  }

  async getStationByName(name: string): Promise<Nullable<Station[]>> {
    const stations = await this.getAllStations();
    const matchedStations: Station[] = [];
    stations.forEach((station) => {
      if (station.displayName.includes(name)) matchedStations.push(station);
    });
    return matchedStations.length ? matchedStations : null;
  }

  async getStationByZones(zones: VulogZone[]): Promise<Station> {
    if (!zones || !zones.length || zones.some((vgZone) => !vgZone)) return null;

    const zoneIds = zones.map((zone) => zone.zoneId);
    const stations = await this.getAllStations();

    // NOTE: Cao had implied even when a trip detail have many zones
    // There can only be one station which the vehicle locates
    return stations.find((station) => zoneIds.includes(station.zoneId));
  }

  async getStationByQRCode(qrCode: string): Promise<Station> {
    const parkingLotResponse =
      await this.stationExternalService.internalApi.getParkingLotByQR(qrCode);

    const parkingLot = parkingLotResponse?.toParkingLot();
    return parkingLot?.station;
  }
}
