import { Injectable, OnModuleInit } from '@nestjs/common';
import { BsgUserId } from 'src/common/tiny-types';
import { config } from 'src/config';
import { ItemCacheConfig } from 'src/config/cacheConfig';
import { ReservationInfo } from 'src/model/reservation-info';
import { ICacheService } from '../cache/cache.interface';
import { CacheService } from '../cache/cache.service';

@Injectable()
class VulogCarSwitchedOnGoingReservationByUserIdCacheService
  implements ICacheService<boolean>
{
  constructor(private readonly cacheService: CacheService) {}

  private cacheConfig: ItemCacheConfig =
    config.redisConfig.vulogCarSwitchedOnGoingReservationByUserId;

  cachingKey(bsgUserId: BsgUserId): string {
    return `${this.cacheConfig.cacheKey}/BSG_USER_ID/${bsgUserId.value}/CAR_SWITCHING_PERFORMED`;
  }

  async retrieve(bsgUserId: BsgUserId): Promise<boolean> {
    return await this.cacheService.get(this.cachingKey(bsgUserId));
  }

  async update(
    bsgUserId: BsgUserId,
    latestVulogReservation: ReservationInfo,
  ): Promise<void> {
    await this.cacheService.set(this.cachingKey(bsgUserId), true, {
      ttl: Math.ceil(
        latestVulogReservation.expiresAt.toDateTime().diffNow(['seconds'])
          .seconds,
      ),
    });
  }

  async delete(bsgUserId: BsgUserId): Promise<void> {
    await this.cacheService.del(this.cachingKey(bsgUserId));
  }
}

@Injectable()
export class ReservationCacheService implements OnModuleInit {
  constructor(private readonly cacheService: CacheService) {}

  private vulogCarSwitchedOnGoingReservationByUserIdCacheService: VulogCarSwitchedOnGoingReservationByUserIdCacheService;

  onModuleInit() {
    this.vulogCarSwitchedOnGoingReservationByUserIdCacheService =
      new VulogCarSwitchedOnGoingReservationByUserIdCacheService(
        this.cacheService,
      );
  }

  public get vulogCarSwitchedOnGoingReservationByUserId(): VulogCarSwitchedOnGoingReservationByUserIdCacheService {
    return this.vulogCarSwitchedOnGoingReservationByUserIdCacheService;
  }
}
