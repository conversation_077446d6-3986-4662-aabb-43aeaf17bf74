import {
  BsgUserId,
  ReservationId,
  SubscriptionPlanId,
  VulogZoneId,
} from 'src/common/tiny-types';
import { CarRealtimeMetadata, RentalInfo } from 'src/model/rental-info';

import { HttpStatus, Injectable, Logger } from '@nestjs/common';

import { CorrelationIdService } from '@bluesg-2/monitoring';

import { UserInfo } from '@bluesg-2/customer-authentication';
import { PageResult } from '@bluesg-2/queue-pop/dist/common/controllers/domains/page-result';
import { QueryPagination } from '@bluesg-2/queue-pop/dist/common/controllers/domains/pagination';

import { CorrelationId } from '@bluesg-2/monitoring/dist/correlation-id/correlation-id.service';
import { ModuleRef } from '@nestjs/core';
import { DateTime } from 'luxon';
import { TransactionFor } from 'nest-transact';
import {
  EndRentalConditionId,
  endRentalWithQrConditions,
  testingEndRentalConditions,
} from 'src/common/constants/ending-rental-conditions';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { BlueSGUserNotFound } from 'src/common/errors/external/blue-sg-user-not-found.error';
import { PricingPolicyNotFound } from 'src/common/errors/external/pricing-policy-not-found.error';
import { RentalError } from 'src/common/errors/rental-error';
import { ReservationNotFoundError } from 'src/common/errors/reservation/reservation-not-found.error';
import { StationError } from 'src/common/errors/station.error';
import { LoggerUtils } from 'src/common/utils/logger.util';
import { config } from 'src/config';
import { EndRentalSimulationRequestDto } from 'src/controllers/dtos/rental/request/end-rental-simulation.request.dto';
import { StationDto } from 'src/controllers/dtos/station/response/list-available-cars-at-stations.response.v1.dto';
import { EndRentalSimulationEventPayloadDto } from 'src/event/rental/end-rental-simulation.event';
import { BillingExternalService } from 'src/external/billing/billing.external.service';
import { StationExternalService } from 'src/external/station/station.external.service';
import { UserSubscriptionExternalService } from 'src/external/user-subscription/user-subscription.external.service';
import { GpsCoordinates } from 'src/model/gps-coordinates';
import { OnGoingRental } from 'src/model/on-going-rental';
import { ParkingLot, ParkingLotValidationStatus } from 'src/model/parking-lot';
import { PricingPolicy } from 'src/model/pricing-policy';
import { RentalPackage } from 'src/model/rental-package';
import { ReservationRepository } from 'src/model/repositories/reservation.repository';
import { StartRental } from 'src/model/start-rental';
import { TelemetryCarInfo } from 'src/model/telemetry-car-info';
import {
  TestingEndRentalConditionResult,
  TestingEndRentalReport,
} from 'src/model/testing-end-rental.report';
import { EventBusService } from 'src/shared/event-bus/event-bus.service';
import { FeatureFlagName } from '~shared/feature-flag/feature-flag-name';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { CarInfo } from '~shared/feature-flag/models/car-info';
import { StationInfo } from '~shared/feature-flag/models/station-info';
import { NewRelicRentalEventService } from '~shared/newrelic/event/event-type/rental.event.newrelic.service';
import { NewRelicMetricTrigger } from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { VulogDate } from '../common/tiny-types/vulog-date.type';
import { Reservation } from '../model/reservation.domain';
import { AvailabilityService } from './availability.service';
import { CarsCounter } from './car-availability';
import { VulogCarService } from './car-repository/vulog/vulog-car.service';
import { CarService } from './car/car.service';
import { QrProviderName } from './qr/domain/qr-provider-name';
import { ParkingQrValidation } from './qr/domain/qr-validation';
import { ReservationService } from './reservation.service';
import { StationService } from './station/station.service';
import { VulogTelemetryService } from './telemetry-repository/vulog/vulog-telemetry.service';

import { RentalStartedEvent } from '@bluesg-2/event-library';
import { SERVICE_NAME } from 'src/common/constants/environments';
import { v4 } from 'uuid';
import { CancelReservationService } from './vulog/cancel-reservation.service';
import { EndRentalService } from './vulog/end-rental.service';

export interface ValidateParkingLotResult {
  validationStatus: ParkingLotValidationStatus;
  validationResultMetadata?: {
    parkingSlot?: ParkingLot;
    qrCodeData?: ParkingQrValidation;
  };
}

@Injectable()
export class RentalService extends TransactionFor<RentalService> {
  constructor(
    private vulogCarClient: VulogCarService,
    private reservationService: ReservationService,
    private stationExternalService: StationExternalService,
    private readonly eventBusService: EventBusService,
    private readonly corIdService: CorrelationIdService,
    private availabilityService: AvailabilityService,
    private billingApiService: BillingExternalService,
    private userSubscriptionApiService: UserSubscriptionExternalService,
    private flag: FeatureFlagService,
    private stationService: StationService,
    private carService: CarService,
    private vulogTelemetryService: VulogTelemetryService,
    private readonly rentalEventService: NewRelicRentalEventService,
    private readonly reservationRepository: ReservationRepository,
    private readonly correlationIdService: CorrelationIdService,
    private readonly endRentalService: EndRentalService,
    private readonly cancelReservationService: CancelReservationService,

    // This is the needed thing for [TransactionFor<T>] logic working
    moduleRef: ModuleRef,
  ) {
    super(moduleRef);
  }

  async convertReservationToRental(
    reservation: Reservation,
    vulogStartDate?: VulogDate,
  ): Promise<{ startedAt: VulogDate; pricingPolicy: PricingPolicy }> {
    reservation.status = ReservationStatus.Converted;
    reservation.startedAt = vulogStartDate || VulogDate.now();

    const user =
      await this.userSubscriptionApiService.internalApi.getUserDetail(
        reservation.bsgUserId.value,
      );

    if (!user) {
      throw new BlueSGUserNotFound();
    }

    const [pricingPolicy] =
      await this.billingApiService.internalApi.getPricingPolicy(
        reservation.carModel,
        new SubscriptionPlanId(user.currentSubscription.subscriptionPlanId),
        reservation.startedAt,
      );

    if (!pricingPolicy) {
      throw new PricingPolicyNotFound();
    }

    reservation.attachPricingPolicy(pricingPolicy.toDomain());

    return {
      startedAt: reservation.startedAt,
      pricingPolicy: reservation.pricingPolicy,
    };
  }

  async startRental(
    reservation: Reservation,
    bsgUserId: BsgUserId,
  ): Promise<StartRental> {
    const { startedAt, pricingPolicy } = await this.convertReservationToRental(
      reservation,
    );

    const convertedRental = await this.reservationRepository.updateStatus(
      reservation,
      ReservationStatus.Converted,
      [ReservationStatus.Reserved],
      {
        startedAt,
        pricingPolicy,
      },
    );

    Logger.debug('Reservation updated', {
      reservation: convertedRental.toLogs(),
    });

    if (
      await this.flag.unlockDoorsWhenStartRental(
        new UserInfo(bsgUserId.value, null, null),
      )
    ) {
      const vulogJourneyId = reservation.vulogTripId;

      // Start Vulog trip with allocation booking ID
      const rentalResponse: RentalInfo = await this.vulogCarClient.startRental(
        bsgUserId,
        vulogJourneyId,
      );

      Logger.verbose('Rental started in Vulog', {
        reservation: reservation.toLogs(),
      });

      if (rentalResponse.carId.value !== reservation.carId.value)
        Logger.warn(
          'The rental started with a different car than booked in the reservation',
          {
            reservation: reservation.toLogs(),
            rental: {
              car: {
                id: rentalResponse.carId.value,
              },
            },
          },
        );
    }

    const stationRespDto =
      await this.stationExternalService.internalApi.getStationDetail(
        convertedRental.startStationId.value,
      );

    Logger.debug('Get Station information', {
      station: {
        id: stationRespDto.id,
        zoneId: stationRespDto.zoneId,
        name: stationRespDto.displayName,
      },
    });

    const car = await this.carService.getOneByVulogId(convertedRental.carId);

    Logger.debug('Car information received', {
      car: {
        id: car.id,
        model: car.model,
        vin: car.vin,
      },
    });

    const carsCounter: CarsCounter =
      await this.availabilityService.getCarsCounterForSpecificStation(
        convertedRental.startStationId,
      );

    this.eventBusService.nonBlockingEmit(
      new RentalStartedEvent({
        correlationId: this.corIdService.getCorrelationId()?.value || v4(),
        sender: SERVICE_NAME,
        sentAt: DateTime.now().toMillis(),
        payload: {
          id: convertedRental.getId,
          carId: convertedRental.carId.value,
          createdAt: convertedRental.getCreatedAt.toISO(),
          reservedAt: convertedRental.reservedAt.value,
          startedAt: convertedRental.startedAt.value,
          startStationId: convertedRental.startStationId.value,
          status: convertedRental.status,
          userId: convertedRental.bsgUserId.value,
          // legacy fields
          stationId: convertedRental.startStationId.value,
          carMetadata: {
            id: car.id.value, // CRS CarId
            model: convertedRental.carModel,
            vulogId: car.vulogId.value,
          },
        },
      }),
    );

    await this.rentalEventService.emitStartedAtBlueSG(
      NewRelicMetricTrigger.Mobile,
      convertedRental.getMetricNewRelicRentalType(),
      convertedRental.getMetricNewRelicCarModel(),
    );

    return StartRental.from(
      convertedRental,
      stationRespDto.toStation(),
      car,
      carsCounter,
    );
  }

  async getRentalConditions(
    rentalId: ReservationId,
  ): Promise<TestingEndRentalConditionResult[]> {
    const existingReservation = await this.reservationService.findOneById(
      rentalId,
    );

    if (!existingReservation) {
      throw new RentalError(`No rental found for id ${rentalId.value}`);
    }

    if (existingReservation.status !== ReservationStatus.Converted) {
      throw new RentalError(
        `Rental ${rentalId.value} is not an ongoing rental`,
      );
    }

    const currentRental = await this.getCurrentRentalDetail(
      existingReservation.bsgUserId,
      false,
    );

    if (!currentRental) {
      throw new RentalError('No on-going rental is found');
    }

    let carInfo: TelemetryCarInfo;

    try {
      carInfo = await this.vulogTelemetryService.getCarStatusSessionCommand(
        currentRental.car.vulogId,
      );

      Logger.log('Retrieved current car realtime INFO', {
        statusDetails: LoggerUtils.removeLongAttributes(carInfo, [
          'zoneIds',
          'zoneMaps',
        ]),
      });
    } catch (err) {
      Logger.error(
        'Getting vehicle INFO from Vulog Vehicle Gateway failed...',
        {
          errorDetails: err,
        },
      );

      carInfo = null;
    }

    const conditionsValidationResults: TestingEndRentalConditionResult[] =
      testingEndRentalConditions.map((c) =>
        c.validate({
          onGoingRental: currentRental,
          telemetryCarInfo: carInfo,
          telemetryCarStatus: null,
        }),
      );

    return conditionsValidationResults;
  }

  async getCurrentRentalDetail(
    bsgUserId: BsgUserId,
    useCache = true,
  ): Promise<OnGoingRental> {
    const bsgOnGoingRental = await this.reservationService.getOwnOnGoingRental(
      bsgUserId,
    );

    if (!bsgOnGoingRental) {
      return null;
    }

    const car = await this.carService.getOneByVulogId(bsgOnGoingRental.carId);

    // Is the reservation applied with pricing policy or rental package?
    const isNormalRental = bsgOnGoingRental.isNormalRental();

    const vulogOnGoingRental =
      await this.vulogCarClient.getSpecificOnGoingRental(car, useCache);

    if (!vulogOnGoingRental) {
      Logger.log(
        'The customer has on-going rental but the rental has not been started in Vulog AiMA yet',
        {
          ownOnGoingRental: bsgOnGoingRental,
          bsgUserId: bsgUserId.value,
        },
      );
    }
    let vulogZones = vulogOnGoingRental?.currentZones || [];

    const startStation = await this.stationService.getStation(
      bsgOnGoingRental.startStationId,
    );

    let currentStation = await this.stationService.getStationByZones(
      vulogZones,
    );

    if (!currentStation) {
      const carInfo: TelemetryCarInfo =
        await this.vulogTelemetryService.getCarStatusSessionCommand(
          bsgOnGoingRental.carId,
        );

      vulogZones = carInfo?.currentZones || [];
      currentStation = await this.stationService.getStationByZones(vulogZones);

      Logger.log(
        'Zones from Vulog AiMA response are empty, fetching from VVG instead',
        {
          onGoingRental: vulogOnGoingRental,
          zones: vulogZones,
          station: currentStation,
        },
      );
    }

    let rentalPackage: RentalPackage = null;

    if (!isNormalRental) {
      rentalPackage = bsgOnGoingRental.getRentalPackageData();
    }

    const vulogVehicle = await this.vulogCarClient.getSpecificCarInRealtime(
      car.vulogId,
    );

    const vulogVehicleRealtimeInfo: CarRealtimeMetadata = vulogVehicle
      ? {
          battery: vulogVehicle.energyLevel,
          isCharging: vulogVehicle.isCharging,
          isDoorClosed: vulogVehicle.isDoorClosed,
          isDoorLocked: vulogVehicle.isDoorLocked,
          isEngineOn: vulogVehicle.isEngineOn,
        }
      : null;

    car.vuboxId = vulogOnGoingRental?.vuboxId || vulogVehicle?.vuboxId;

    return new OnGoingRental({
      id: new ReservationId(bsgOnGoingRental.getId),
      vulogTripId: bsgOnGoingRental.vulogTripId,
      startDate: bsgOnGoingRental.startedAt,
      endDate: bsgOnGoingRental.endedAt,
      duration: bsgOnGoingRental.getCurrentDuration(),
      billedDuration: bsgOnGoingRental.getBilledDuration(),
      distance: vulogOnGoingRental?.distance?.value || 0,
      price: null, // No need to return this as it does not indicate the real amount after invoice is generated
      startStation: startStation,
      endStation: null,
      currentStation,
      car,
      rentalPackage,
      hasStartedTripInVulog: !!vulogOnGoingRental?.hasTripStarted,
      realtimeInfo:
        vulogOnGoingRental?.carRealtimeMetadata || vulogVehicleRealtimeInfo,
      currentGpsCoordinates: vulogOnGoingRental?.currentGpsCoordinates,
      vulogZones: vulogZones,
    });
  }

  async adminGetAllFinishedRentals(): Promise<RentalInfo[]> {
    return await this.vulogCarClient.getAllFinishedRentals();
  }

  async endRentalTesting(
    bsgUserId: BsgUserId,
  ): Promise<TestingEndRentalReport> {
    const currentRental = await this.getCurrentRentalDetail(bsgUserId, false);

    if (!currentRental) {
      throw new RentalError('No on-going rental is found');
    }

    const existingReservation = await this.reservationService.findOneByTripId(
      currentRental.vulogTripId,
    );

    if (!existingReservation) {
      throw new RentalError(`No on-going rental for customer ${bsgUserId}`);
    }

    return this.endCurrentRental(existingReservation, currentRental, bsgUserId);
  }

  async endCurrentRental(
    existingReservation: Reservation,
    currentRental: OnGoingRental,
    bsgUserId: BsgUserId,
    qrCode?: string,
  ): Promise<TestingEndRentalReport> {
    let carInfo: TelemetryCarInfo;

    try {
      carInfo = await this.vulogTelemetryService.getCarStatusSessionCommand(
        currentRental.car.vulogId,
      ); // This will always return `parkingBrakeOn` as `false` as the under-the-hood API does not return this info. Rely on `getVehicleStatusDirectCommand`

      Logger.log('Retrieved current car realtime INFO', {
        statusDetails: LoggerUtils.removeLongAttributes(carInfo, [
          'zoneIds',
          'zoneMaps',
        ]),
      });
    } catch (err) {
      Logger.error(
        'Getting vehicle INFO from Vulog Vehicle Gateway is failed...',
        {
          errorDetails: err,
        },
      );

      carInfo = null;
    }

    Logger.log('Current station when ending the rental', {
      details: currentRental.currentStation,
    });

    const car = await this.carService.getOneByVulogId(
      currentRental.car.vulogId,
    );

    let applicableConditions = [...testingEndRentalConditions];

    const requireLockCarWhenEndRental =
      await this.flag.isRequireLockCarWhenEndRental(CarInfo.fromCar(car));

    if (!requireLockCarWhenEndRental) {
      applicableConditions = applicableConditions.filter(
        (condition) => condition.id !== EndRentalConditionId.DOORS_LOCKED,
      );
    }

    if (qrCode) {
      const qrValidation = await this.parseAndValidateQRFormat(qrCode);
      const isChargelessEnabled = await this.flag.isChargelessEnabled();
      if (
        qrValidation.type === QrProviderName.CHARGELESS &&
        isChargelessEnabled
      ) {
        applicableConditions = applicableConditions.filter(
          (condition) => condition.id !== EndRentalConditionId.CAR_PLUGGED,
        );
      }
    }

    const conditionsValidationResults: TestingEndRentalConditionResult[] =
      applicableConditions.map((c) =>
        c.validate({
          onGoingRental: currentRental,
          telemetryCarInfo: carInfo,
          telemetryCarStatus: null,
        }),
      );

    const allConditionsAreFulfilled = conditionsValidationResults.reduce(
      (acc, cr) => !!(acc && cr.fulfilled),
      true,
    );

    const isRentalEnded = allConditionsAreFulfilled;

    if (!allConditionsAreFulfilled) {
      await this.rentalEventService.emitFailedToSatisfyEndingConditions(
        NewRelicMetricTrigger.Mobile,
        existingReservation.getMetricNewRelicRentalType(),
        existingReservation.getMetricNewRelicCarModel(),
        conditionsValidationResults,
        currentRental.currentStation.displayName,
        currentRental.currentStation.id.value,
      );
    }
    let durationSeconds: number | null = null;
    let rentalPrice: number | null = null;
    Logger.log('ending rental conditions', {
      skipEndingRentalConditions:
        config.rentalConfig.skipEndingRentalConditions,
      allConditionsAreFulfilled,
    });
    if (
      config.rentalConfig.skipEndingRentalConditions ||
      allConditionsAreFulfilled
    ) {
      existingReservation.vulogEndGpsCoordinates =
        currentRental?.currentGpsCoordinates ?? null;
      durationSeconds = existingReservation.getBilledDuration();
      rentalPrice = existingReservation.getRentalPrice();
      existingReservation.amount = rentalPrice;

      await this.reservationService.saveOne(existingReservation);

      const correlationId: CorrelationId =
        await this.correlationIdService.getCorrelationId();

      // if vulog trip is started, use current station, else use the starting one
      const effectiveEndZoneId = currentRental.hasStartedTripInVulog
        ? currentRental.currentStation.zoneId
        : currentRental.startStation.zoneId;

      await this.endRentalService.makeRentalEnded(
        currentRental.vulogTripId.value,
        [effectiveEndZoneId],
        DateTime.now().toUTC().toISO(),
        { qrCode, nrTriggerDimension: NewRelicMetricTrigger.Mobile },
        correlationId?.value,
      );
    }

    return new TestingEndRentalReport(
      existingReservation,
      isRentalEnded,
      [],
      conditionsValidationResults,
      rentalPrice,
      durationSeconds,
    );
  }

  // Temporary service for receiving data for ending rental returned by VULOG
  async endRentalSimulation(
    requestDto: EndRentalSimulationRequestDto,
  ): Promise<void> {
    this.eventBusService.nonBlockingEmit(
      new EndRentalSimulationEventPayloadDto(
        { ...requestDto },
        this.corIdService.getCorrelationId().value,
      ),
    );
  }

  async getAllRentals(
    queryPagination: QueryPagination,
    bsgUserId?: BsgUserId,
  ): Promise<PageResult<Reservation[]>> {
    return await this.reservationService.getAllReservations(
      queryPagination,
      bsgUserId,
      [ReservationStatus.Ended],
    );
  }

  async getDetailRentalById(rentalId: ReservationId): Promise<Reservation> {
    const reservation = await this.reservationService.findOneById(rentalId);
    if (!reservation) {
      throw new ReservationNotFoundError();
    }
    return reservation;
  }

  async endRentalUsingQR(
    currentRental: OnGoingRental,
    userId: BsgUserId,
    qrCode: string,
  ): Promise<TestingEndRentalReport> {
    // Emit attempt metric
    await this.rentalEventService.emitQREndRentalAttempt(
      currentRental.car?.plate.value,
      userId.value,
      qrCode,
    );
    const existingReservation = await this.reservationService.findOneByTripId(
      currentRental.vulogTripId,
    );

    if (!existingReservation) {
      throw new RentalError(`No on-going rental for customer ${userId.value}`);
    }

    try {
      const { validationStatus, validationResultMetadata } =
        await this.validateParkingLot(qrCode, currentRental);

      Logger.log('[Rental End] validateParkingLot', {
        reservationId: currentRental.id.value,
        isAssociatedWithStation: validationStatus.isAssociatedWithStation(),
        isQRCodeValid: validationStatus.isQRCodeValid(),
        hasAvailableSpace: validationStatus.hasAvailableSpace(),
        qrCode,
      });
      Logger.log('[Rental End] validateParkingLot', {
        reservationId: currentRental.id.value,
        isAssociatedWithStation: validationStatus.isAssociatedWithStation(),
        isQRCodeValid: validationStatus.isQRCodeValid(),
        qrCode,
      });
      const qrConditionsValidationResults: TestingEndRentalConditionResult[] =
        endRentalWithQrConditions.map((c) =>
          c.validate({
            parkingLotValidationStatus: validationStatus,
          }),
        );
      const allQrConditionsAreFulfilled = qrConditionsValidationResults.reduce(
        (acc, cr) => !!(acc && cr.fulfilled),
        true,
      );

      // Emit metrics for each QR condition
      for (const result of qrConditionsValidationResults) {
        await result.emitValidationEvent(
          currentRental.car?.plate.value,
          userId.value,
          qrCode,
          this.rentalEventService,
        );
      }

      if (!allQrConditionsAreFulfilled) {
        return new TestingEndRentalReport(
          existingReservation,
          false,
          qrConditionsValidationResults,
        );
      }

      const qrStation = validationResultMetadata.parkingSlot?.station;

      const enforceQRStationSameAsEndingStation: boolean =
        await this.flag.enforceQRStationSameAsEndingStation(
          !!qrStation ? StationInfo.fromStation(qrStation) : null,
        );

      Logger.log(
        `The value of flag ${FeatureFlagName.EndingRental_WithQrCode_Enable_QRStationSameAsEndingStation}: `,
        {
          flagValue: enforceQRStationSameAsEndingStation,
          metadata: { qrStation },
        },
      );

      if (enforceQRStationSameAsEndingStation) {
        // For the QR ending rental case, ending station must be the same as QR station
        currentRental.currentStation =
          validationResultMetadata.parkingSlot.station;
      }

      const endCurrentRentalResponse = await this.endCurrentRental(
        existingReservation,
        currentRental,
        userId,
        qrCode,
      );

      if (endCurrentRentalResponse.endRentalSuccess) {
        // Emit success
        await this.rentalEventService.emitQREndRentalSuccess(
          currentRental.car?.plate.value,
          userId.value,
          qrCode,
        );

        const zoneStationMap: {
          [zoneId: string]: StationDto;
        } = await this.stationService.getZoneStationMap();

        const vulogZonesWithStationInfo = currentRental.vulogZones.map(
          (vgZone) => {
            return {
              ...vgZone,
              station: zoneStationMap[vgZone.zoneId],
            };
          },
        );

        Logger.log('Passed all QR code checks and ending rental checks', {
          reservationId: currentRental.id.value,
          qrCodeMetadata: {
            valid: validationStatus.isQRCodeValid(),
            associatedStation: validationResultMetadata.parkingSlot.station,
            isMatchedWithCurrentZones:
              validationStatus.isAssociatedWithStation(),
          },
          zones: vulogZonesWithStationInfo,
          zoneIds: vulogZonesWithStationInfo.map((vgZone) => vgZone.zoneId),
          endStation: currentRental.currentStation,
        });
      }

      return new TestingEndRentalReport(
        endCurrentRentalResponse.reservation,
        endCurrentRentalResponse.endRentalSuccess,
        qrConditionsValidationResults,
        endCurrentRentalResponse.conditions,
        endCurrentRentalResponse.totalPrice,
        endCurrentRentalResponse.durationInSeconds,
      );
    } catch (error) {
      // Emit failure
      await this.rentalEventService.emitQREndRentalError(
        currentRental.car?.plate.value,
        userId.value,
        qrCode,
      );
      Logger.error('Failed to end rental with QR', {
        BsgUserId: userId.value,
        RentalId: currentRental?.id.value,
        error,
      });
      if (error instanceof StationError) {
        throw error;
      } else {
        throw new RentalError(
          'An unexpected error occurred while ending the rental',
          HttpStatus.INTERNAL_SERVER_ERROR,
          error,
        );
      }
    }
  }

  async validateParkingLot(
    qrCode: string,
    currentRental: OnGoingRental,
  ): Promise<ValidateParkingLotResult> {
    Logger.log('[Rental End] QR code validation initiated', {
      rentalId: currentRental.id.value,
      qrCode,
    });

    const result: ValidateParkingLotResult = {
      validationStatus: new ParkingLotValidationStatus(),
      validationResultMetadata: {},
    };
    try {
      // Step 1: Validate QR Format
      const qrValidationResponse = await this.validateQRFormat(qrCode);

      result.validationStatus.setIsQRCodeValid(qrValidationResponse.success);
      result.validationResultMetadata.qrCodeData = qrValidationResponse.data;

      if (!qrValidationResponse.success) {
        return result;
      }

      // Step 2: Validate Parking Location
      const parkingLotResponse = await this.validateParkingLocation(
        qrCode,
        currentRental,
      );

      result.validationStatus.setIsAssociatedWithStation(
        parkingLotResponse.success,
      );
      result.validationResultMetadata.parkingSlot = parkingLotResponse.data;

      if (!parkingLotResponse.success) {
        return result;
      }

      // Step 3: Check Parking Availability
      const hasAvailableSpace = await this.checkParkingAvailability(
        parkingLotResponse.data,
      );

      result.validationStatus.setHasAvailableSpace(hasAvailableSpace);

      if (!hasAvailableSpace) {
        return result;
      }

      return result;
    } catch (error) {
      Logger.error('[Rental End] Validation pipeline failed', {
        error,
        rentalId: currentRental.id.value,
        qrCode,
      });
      throw error;
    }
  }

  private async validateQRFormat(qrCode: string): Promise<{
    success: boolean;
    data: ParkingQrValidation;
  }> {
    const qrValidation = await this.parseAndValidateQRFormat(qrCode);

    if (!qrValidation.isValid) {
      Logger.log('[Rental End] QR format validation failed', { qrCode });
      return { success: false, data: qrValidation };
    }

    return { success: true, data: qrValidation };
  }

  async parseAndValidateQRFormat(qrCode: string): Promise<ParkingQrValidation> {
    // Check for BlueSG format (chargeless)
    if (qrCode.startsWith('BSG-')) {
      return {
        isValid: true,
        parkingId: qrCode,
        type: QrProviderName.CHARGELESS,
      };
    }
    // Check for BlueCharge format
    if (qrCode.startsWith(config.rentalConfig.blueChargeQrUrlPrefix)) {
      const parkingId = qrCode.split('?emi3=')[1] || '';
      return {
        isValid: true,
        parkingId,
        type: QrProviderName.BLUECHARGE,
      };
    }
    return {
      isValid: false,
      parkingId: '',
      type: QrProviderName.CHARGELESS,
    };
  }

  private async validateParkingLocation(
    qrCode: string,
    currentRental: OnGoingRental,
  ): Promise<{
    success: boolean;
    data: ParkingLot;
  }> {
    try {
      const parkingLotResponse =
        await this.stationExternalService.internalApi.getParkingLotByQR(qrCode);
      const parkingLot = parkingLotResponse?.toParkingLot();

      if (!parkingLot?.station?.zoneId) {
        Logger.warn(
          '[Rental End] QR code validation warning - Missing zone ID',
          {
            reason: 'missing_zone_id',
            stationId: parkingLot.station.id.value,
          },
        );
        return { success: false, data: parkingLot };
      }

      Logger.log('[Rental End] is car zone same as end station zone', {
        station_zone_id: parkingLot.station.zoneId,
        car_id: currentRental.car?.id?.value,
        car_vulog_zones: currentRental.vulogZones?.map((zone) => ({
          zone_id: zone.vulogZoneId.value,
          is_sticky: zone.isSticky(),
        })),
      });
      const isAssociatedWithStation = this.isStationInRentalZones(
        new VulogZoneId(parkingLot.station.zoneId),
        currentRental.vulogZones,
      );
      if (!isAssociatedWithStation) {
        Logger.log('[Rental End] Station not in rental zones', {
          stationZoneId: parkingLot.station.zoneId,
          rentalId: currentRental.id.value,
          car_id: currentRental.car?.id?.value,
          car_plate_no: currentRental.car?.plate?.value,
          car_model: currentRental.car?.model,
          car_vin: currentRental.car?.vin,
        });
        const qrValidation = await this.parseAndValidateQRFormat(qrCode);
        if (await this.flag.isQrEndRentalDisabled(qrValidation.parkingId)) {
          Logger.log('[Rental End] QR code - station disabled', {
            reason: 'station_qr_disabled',
            data: qrValidation,
            qrCode,
          });
          return { success: true, data: parkingLot };
        }
        return { success: false, data: parkingLot };
      }

      return { success: true, data: parkingLot };
    } catch (error) {
      if (error instanceof StationError) {
        return { success: false, data: null };
      }
      throw error;
    }
  }

  private async checkParkingAvailability(
    parkingLot: ParkingLot,
  ): Promise<boolean> {
    const hasAvailableSpace =
      parkingLot?.isAvailable !== undefined
        ? parkingLot.isAvailable
        : parkingLot.station.parkingSpacesAvailable !== null &&
          parkingLot.station.parkingSpacesAvailable > 0;

    if (!hasAvailableSpace) {
      Logger.log('[Rental End] No parking spaces available', {
        stationId: parkingLot.station.id.value,
        spacesAvailable: parkingLot.station.parkingSpacesAvailable,
      });
    }

    return hasAvailableSpace;
  }

  private isStationInRentalZones(
    stationZoneId: VulogZoneId,
    rentalZones: { vulogZoneId: VulogZoneId }[],
  ): boolean {
    return rentalZones.some((zone) => zone.vulogZoneId.equals(stationZoneId));
  }

  async attachLocation(
    reservationId: ReservationId,
    gpsCoordinates: GpsCoordinates,
    userInfo?: UserInfo,
  ): Promise<void> {
    const existingReservation = await this.reservationService.findOneById(
      reservationId,
    );

    if (!existingReservation) {
      throw new RentalError(
        `Rental [${reservationId.value}] is not found`,
        HttpStatus.BAD_REQUEST,
      );
    }

    if (
      userInfo &&
      !existingReservation.bsgUserId.equals(new BsgUserId(userInfo.id))
    ) {
      throw new RentalError(
        "You could not attach GPS coordinates to the reservation you don't own",
        HttpStatus.BAD_REQUEST,
      );
    }

    // // Have a case when CRC ends the trip forcefully
    // if (!existingReservation.isRentalEnded()) {
    //   throw new RentalError(
    //     `Cannot attach GPS coordinates as the trip (ID: ${existingReservation.getId}) is not ended`,
    //     HttpStatus.BAD_REQUEST,
    //   );
    // }

    if (existingReservation.mobileEndGpsCoordinates) {
      throw new RentalError(
        `This rental was attached with mobile GPS coordinates. Hence, no further updates allowed`,
        HttpStatus.BAD_REQUEST,
      );
    }

    existingReservation.mobileEndGpsCoordinates = gpsCoordinates;

    await this.reservationService.updateReservation(existingReservation);
  }
}
