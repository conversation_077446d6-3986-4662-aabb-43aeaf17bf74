import { VulogCarId } from 'src/common/tiny-types';
import { RealtimeCar } from '../../model/realtime.car';

export interface TelemetryRepositoryInterface {
  getCarInformation: (carId: VulogCarId) => Promise<RealtimeCar>;
  getAllCars: (
    internalContToken: string,
  ) => Promise<{ content: RealtimeCar[]; continuationToken: string }>;
  wakeUpCar: (carId: VulogCarId) => Promise<{ isAwake: boolean }>;
  standbyCar: (carId: VulogCarId) => Promise<{ isAwake: boolean }>;
  lockCar: (carId: VulogCarId) => Promise<{ success: boolean }>;
  unlockCar: (carId: VulogCarId) => Promise<{ success: boolean }>;
  immobilizeCar: (carId: VulogCarId) => Promise<{ success: boolean }>;
  mobilizeCar: (carId: VulogCarId) => Promise<{ success: boolean }>;
}
