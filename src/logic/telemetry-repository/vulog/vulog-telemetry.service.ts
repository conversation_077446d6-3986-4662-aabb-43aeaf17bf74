import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import {
  VULOG_IMMOBILIZE_INVALID_STATUS_CODE,
  VULOG_LOCK_INVALID_STATUS_CODE,
  VULOG_MOBILIZE_INVALID_STATUS_CODE,
  VULOG_UNLOCK_INVALID_STATUS_CODE,
} from 'src/common/constants/messages';
import { VulogApiError } from 'src/common/errors/vulog-api-error';
import { VulogCarId } from 'src/common/tiny-types';
import { ContinuationTokenManager } from 'src/common/utils/continuation-token-manager';
import { LoggerUtils } from 'src/common/utils/logger.util';
import { validateStatusCode } from 'src/common/utils/validate-status-codes';
import { config } from 'src/config';
import { CacheService } from 'src/logic/cache/cache.service';
import { CarService } from 'src/logic/car/car.service';
import { Car } from 'src/logic/car/domain/car';
import { HttpEmptyDataResponse } from 'src/logic/http-adapter.service';
import { StationService } from 'src/logic/station/station.service';
import { VulogClientService } from 'src/logic/vulog/vulog-client.service';
import { VulogFleetService } from 'src/logic/vulog/vulog-fleet-service.service';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { StationDto } from 'src/model/dtos/station.dto';
import { VvgCarListDto } from 'src/model/dtos/vvg-car/vvg-car-list.dto';
import { VvgCarDto } from 'src/model/dtos/vvg-car/vvg-car.dto';
import { VvgVehicleStatusDto } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { TelemetryCarInfo } from 'src/model/telemetry-car-info';
import { TelemetryCarStatus } from 'src/model/telemetry-car-status';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { CarInfo } from '~shared/feature-flag/models/car-info';
import { RealtimeCar } from '../../../model/realtime.car';
import { TelemetryRepositoryInterface } from '../telemetry-repository.interface';

/**
 * Service communicates with Vulog Vehicle Gateway
 */
@Injectable()
export class VulogTelemetryService implements TelemetryRepositoryInterface {
  constructor(
    private client: VulogClientService,
    private readonly cacheService: CacheService,
    private readonly carService: CarService,
    private readonly vulogFleetService: VulogFleetService,
    private readonly stationService: StationService,
    private readonly featureFlagService: FeatureFlagService,
  ) {}

  async getCarInformation(carId: VulogCarId): Promise<RealtimeCar> {
    return (
      await this.client.get(
        VulogPaths.vvgVehicles(config.vulogConfig.fleetId, carId),
        VvgCarDto,
      )
    ).toRealTimeCar();
  }

  async getAllCars(
    internalContToken?: string,
  ): Promise<{ content: RealtimeCar[]; continuationToken: string }> {
    let url = VulogPaths.vvgVehicles(config.vulogConfig.fleetId);
    if (internalContToken) {
      const vulogContToken: string = await this.cacheService.get(
        internalContToken,
      );
      url = url.addQueryParams({ continuationToken: vulogContToken });
    }

    const response = await this.client.get(url, VvgCarListDto);

    // Cache continuationToken if exists
    const nextPageVulogContToken = response.continuationToken;
    if (nextPageVulogContToken) {
      const nextPageInternalContToken =
        ContinuationTokenManager.generateInternalContinuationToken();

      await this.cacheService.set(
        nextPageInternalContToken,
        nextPageVulogContToken,
        { ttl: config.vulogConfig.continuationTokenTTL },
      );
    }
    return response.toCarList();
  }

  async wakeUpCar(
    vgCarId: VulogCarId,
  ): Promise<{ isAwake: boolean; message?: string }> {
    let responseStatus;

    try {
      responseStatus = (
        await this.client.post<null>(
          VulogPaths.vvgWakeUpVehicle(config.vulogConfig.fleetId, vgCarId),
          null,
        )
      ).statusCode;
    } catch (err) {
      responseStatus = HttpStatus.GATEWAY_TIMEOUT;
    }

    // If status code is equal to `202` (Accepted), "wakeup discarded because the box is already awake" as described by Vulog
    return { isAwake: responseStatus === HttpStatus.ACCEPTED };
  }

  async pingCar(
    vgCarId: VulogCarId,
  ): Promise<{ isOnline: boolean; message?: string }> {
    try {
      const response = await this.client.get<null>(
        VulogPaths.vvgPingVehicle(config.vulogConfig.fleetId, vgCarId),
        null,
      );

      return { isOnline: response.statusCode === HttpStatus.OK };
    } catch (err) {
      if (err instanceof VulogApiError) {
        const vulogApiError = err as VulogApiError;

        // returned if the vehicle isn't connected and need to be awaken
        if (vulogApiError.statusCode === HttpStatus.GONE) {
          return { isOnline: false, message: vulogApiError.data.message };
        } else {
          throw vulogApiError;
        }
      } else {
        throw err;
      }
    }
  }

  async lockCar(carId: VulogCarId): Promise<{ success: boolean }> {
    const response = await this.client.post<null>(
      VulogPaths.vvgLockVehicle(config.vulogConfig.fleetId, carId),
      null,
    );

    validateStatusCode([HttpStatus.OK], response.statusCode, () => {
      throw new VulogApiError(VULOG_LOCK_INVALID_STATUS_CODE);
    });

    return { success: true };
  }

  async unlockCar(carId: VulogCarId): Promise<{ success: boolean }> {
    const response = await this.client.post<null>(
      VulogPaths.vvgUnlockVehicle(config.vulogConfig.fleetId, carId),
      null,
    );

    validateStatusCode([HttpStatus.OK], response.statusCode, () => {
      throw new VulogApiError(VULOG_UNLOCK_INVALID_STATUS_CODE);
    });

    return { success: true };
  }

  async immobilizeCar(carId: VulogCarId): Promise<{ success: boolean }> {
    const response = await this.client.post<null>(
      VulogPaths.vvgImmobilizeVehicle(config.vulogConfig.fleetId, carId),
      null,
    );

    validateStatusCode([HttpStatus.OK], response.statusCode, () => {
      throw new VulogApiError(VULOG_IMMOBILIZE_INVALID_STATUS_CODE);
    });

    return { success: true };
  }

  async mobilizeCar(carId: VulogCarId): Promise<{ success: boolean }> {
    const response = await this.client.post<null>(
      VulogPaths.vvgMobilizeVehicle(config.vulogConfig.fleetId, carId),
      null,
    );

    validateStatusCode([HttpStatus.OK], response.statusCode, () => {
      throw new VulogApiError(VULOG_MOBILIZE_INVALID_STATUS_CODE);
    });

    return { success: true };
  }

  async standbyCar(carId: VulogCarId): Promise<{ isAwake: boolean }> {
    let response: HttpEmptyDataResponse;

    try {
      response = await this.client.post<null>(
        VulogPaths.vvgStandbyVehicle(config.vulogConfig.fleetId, carId),
        null,
      );
    } catch (err) {
      if (err instanceof VulogApiError && err.statusCode === HttpStatus.GONE) {
        return { isAwake: false };
      } else {
        throw err;
      }
    }

    if (response.statusCode === HttpStatus.OK) {
      return { isAwake: true };
    }
    throw new VulogApiError(
      `Unexpected status code received: ${response.statusCode}`,
    );
  }

  async unlockAndMobilizeCar(vulogCarId: VulogCarId): Promise<void> {
    try {
      await this.client.post(
        VulogPaths.vvgUnlockAndMobilizeVehicle(
          config.vulogConfig.fleetId,
          vulogCarId,
        ),
        null,
      );
    } catch (err) {
      Logger.warn(
        'Encountered issues when trying to unlock and mobilize the vehicle with Vulog Vehicle Gateway',
        {
          carId: vulogCarId.value,
          errDetails: err,
        },
      );

      throw err;
    }
  }

  async lockAndImmobilizeCar(vulogCarId: VulogCarId): Promise<void> {
    try {
      await this.client.post(
        VulogPaths.vvgLockAndImmobilizeVehicle(
          config.vulogConfig.fleetId,
          vulogCarId,
        ),
        null,
      );
    } catch (err) {
      Logger.warn(
        'Encountered issues when trying to lock and immobilize the vehicle with Vulog Vehicle Gateway',
        {
          carId: vulogCarId.value,
          errDetails: err,
        },
      );

      throw err;
    }
  }

  /**
   * Returns the latest vehicle's information
   *
   * Send a command to fetch all the vehicle's information directly from the box. This is a slow and costly request
   * @param vulogCarId
   * @returns
   */
  async getCarStatusDirectCommand(
    vulogCarId: VulogCarId,
  ): Promise<TelemetryCarStatus> {
    const carStatusDto = await this.client.post(
      VulogPaths.vvgVehicleStatus(config.vulogConfig.fleetId, vulogCarId),
      VvgVehicleStatusDto,
    );

    const customerUsableFleetService =
      await this.vulogFleetService.getCustomerUsableFleetService();

    const carsMapByVulogId = await this.carService.getMapByVulogId();

    const car: Car = carsMapByVulogId.get(carStatusDto.id);

    if (!car) {
      Logger.warn(
        'The vehicle that CRS tried to get status information is not existed in database',
        {
          details: {
            carStatusDto: LoggerUtils.removeLongAttributes(carStatusDto, [
              'zones',
            ]),
          },
        },
      );

      return null;
    }

    const filterOutNonCustomerZones =
      await this.featureFlagService.filterOutNonCustomerZones(
        CarInfo.fromCar(car),
      );

    const tlmtCarStatus = carStatusDto.toCarStatus(
      car,
      filterOutNonCustomerZones
        ? customerUsableFleetService.getZoneIdsAsMap()
        : null,
    );

    return tlmtCarStatus;
  }

  /**
   * Returns the latest vehicle's information
   *
   * Returns the latest vehicle's information stored in the server. If the box is connected, those information are automatically updated.
   * This is prefered way to fetch the latest information available in a optimized way.
   * @param vulogCarId
   * @returns
   */
  async getCarStatusSessionCommand(
    vulogCarId: VulogCarId,
    useCache = false,
  ): Promise<TelemetryCarInfo> {
    const cacheConfig = config.redisConfig.singleTelemetryCarInfo;

    const cacheKey = `${cacheConfig.cacheKey}/${vulogCarId.value}`;

    const cacheContent = useCache
      ? await this.cacheService.get(cacheKey)
      : null;

    const customerUsableFleetService =
      await this.vulogFleetService.getCustomerUsableFleetService();

    let carStatusDto: VvgVehicleStatusDto;

    if (cacheContent) {
      carStatusDto = plainToInstance(VvgVehicleStatusDto, cacheContent);
    } else {
      carStatusDto = await this.client.get(
        VulogPaths.vvgVehicle(config.vulogConfig.fleetId, vulogCarId),
        VvgVehicleStatusDto,
      );

      await this.cacheService.set(cacheKey, carStatusDto, {
        ttl: cacheConfig.ttl,
      });
    }

    const carsMapByVulogId = await this.carService.getMapByVulogId();

    const staticCarInfo = carsMapByVulogId.get(carStatusDto.id);

    if (!staticCarInfo) {
      Logger.warn(
        'The vehicle that CRS tried to get status information is not existed in database',
        {
          details: {
            carStatusDto: LoggerUtils.removeLongAttributes(carStatusDto, [
              'zones',
            ]),
          },
        },
      );

      return null;
    }

    const zoneStationMap: { [zoneId: string]: StationDto } =
      await this.stationService.getZoneStationMap();

    const filterOutNonCustomerZones =
      await this.featureFlagService.filterOutNonCustomerZones(
        CarInfo.fromCar(staticCarInfo),
      );

    const tlmtCarInfo = carStatusDto.toTelemetryCarInfo(
      staticCarInfo,
      filterOutNonCustomerZones
        ? customerUsableFleetService.getZoneIdsAsMap()
        : null,
      zoneStationMap,
    );

    return tlmtCarInfo;
  }

  async getListOfCarsStatusSessionCommand(
    useCache = true,
  ): Promise<TelemetryCarInfo[]> {
    const cacheConfig = config.redisConfig.vvgCarStatusInfo;

    let carStatusInfoDtos: VvgVehicleStatusDto[];

    const cacheContent: VvgVehicleStatusDto[] = useCache
      ? await this.cacheService.get(cacheConfig.cacheKey)
      : null;

    if (!cacheContent) {
      carStatusInfoDtos = await this.client.getAsArray(
        VulogPaths.vvgVehicles(config.vulogConfig.fleetId),
        VvgVehicleStatusDto,
      );

      await this.cacheService.set(cacheConfig.cacheKey, carStatusInfoDtos, {
        ttl: cacheConfig.ttl,
      });
    }

    const carsMapByVulogId = await this.carService.getMapByVulogId();

    const customerUsableFleetService =
      await this.vulogFleetService.getCustomerUsableFleetService();

    const zoneStationMap: { [zoneId: string]: StationDto } =
      await this.stationService.getZoneStationMap();

    const mapFunc = async (
      vvgStatusDto: VvgVehicleStatusDto,
    ): Promise<TelemetryCarInfo> => {
      const car = carsMapByVulogId.get(vvgStatusDto.id);

      if (!car) {
        Logger.warn(
          'The vehicle that CRS tried to get status information is not existed in database',
          {
            details: {
              carStatusDto: LoggerUtils.removeLongAttributes(vvgStatusDto, [
                'zones',
              ]),
            },
          },
        );

        return null;
      }

      const filterOutNonCustomerZones =
        await this.featureFlagService.filterOutNonCustomerZones(
          CarInfo.fromCar(car),
        );

      return vvgStatusDto.toTelemetryCarInfo(
        car,
        filterOutNonCustomerZones
          ? customerUsableFleetService.getZoneIdsAsMap()
          : null,
        zoneStationMap,
      );
    };

    const plainToInstances: VvgVehicleStatusDto[] = !!cacheContent
      ? plainToInstance(VvgVehicleStatusDto, cacheContent)
      : [];

    return !!cacheContent
      ? (await Promise.all(plainToInstances.map(mapFunc))).filter(Boolean)
      : (await Promise.all(carStatusInfoDtos.map(mapFunc))).filter(Boolean);
  }
}
