import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { DateTime } from 'luxon';
import { CarError } from 'src/common/errors/car.error';
import { StationId, VulogCarId } from 'src/common/tiny-types';
import { AvailableCarsAtStation } from 'src/model/available-cars-at-station';
import { CarEvent } from 'src/model/car-event';
import { VulogCarEventType } from 'src/model/dtos/vulog-car-event/vulog-car-event.dto';
import { FleetService } from 'src/model/fleet-service';
import { RealtimeCar } from 'src/model/realtime.car';
import { Station } from 'src/model/station';
import { TelemetryCarInfo } from 'src/model/telemetry-car-info';
import { AvailabilityService } from './availability.service';
import { VulogCarService } from './car-repository/vulog/vulog-car.service';
import { CarService } from './car/car.service';
import { Car } from './car/domain/car';
import { StationService } from './station/station.service';
import { VulogTelemetryService } from './telemetry-repository/vulog/vulog-telemetry.service';
import { VulogFleetService } from './vulog/vulog-fleet-service.service';

export type InternalCarNotChargingMetadata = {
  realtimeCar: RealtimeCar;
  event: CarEvent;
  timeDifference: number;
};

@Injectable()
export class InternalCarService {
  constructor(
    private readonly carService: CarService,
    private readonly vulogCarService: VulogCarService,
    private readonly vulogFleetService: VulogFleetService,
    private readonly stationService: StationService,
    private readonly availabilityService: AvailabilityService,
    private readonly vulogTelemetryService: VulogTelemetryService,
  ) {}

  async getCarMetadata(rltCar: RealtimeCar): Promise<{
    stickyStation: Station;
    fleetService: FleetService;
    bsgCar: Car;
  }> {
    let bsgCar: Car;

    try {
      bsgCar = await this.carService.getOneByPlateNumber(rltCar.plate);
    } catch (err) {
      throw new CarError(
        `Car [Plate: ${rltCar.plate?.value}, Vulog ID: ${rltCar.id}] is not found`,
        HttpStatus.BAD_REQUEST,
      );
    }

    const zoneIdStationMap = await this.stationService.getZoneStationMap();

    const stickyStation: Station =
      zoneIdStationMap[rltCar.allowedStickyZone?.zoneId]?.toStation();

    const staticCar = await this.vulogCarService.getStaticCar(rltCar.id);

    const fleetServices = await this.vulogFleetService.getFleetServices(true);

    const fleetService = fleetServices.find((fleetSvc) =>
      fleetSvc.id.equals(staticCar.serviceId),
    );

    return { stickyStation, fleetService, bsgCar };
  }

  async getCarNotInChargingMode(
    stationId: StationId,
    lastUpdatedOfOcpiEvse: DateTime,
  ): Promise<InternalCarNotChargingMetadata> {
    const availableCarsAtStations =
      await this.availabilityService.getAvailableCarsAtStations();

    const specificStationAvailability: AvailableCarsAtStation =
      AvailableCarsAtStation.toInstance(
        availableCarsAtStations[stationId.value],
      );

    if (!specificStationAvailability?.availableCars?.length) {
      Logger.warn('There is no car availability at station', {
        stationId: stationId.value,
      });

      return null;
    }

    const listOfVulogCarId: VulogCarId[] =
      specificStationAvailability.availableCars.map((rltCar) => rltCar.id);

    const listOfLatestTelemetryCars: TelemetryCarInfo[] = await Promise.all(
      listOfVulogCarId.map(async (vulogCarId) => {
        const telemetryCar =
          await this.vulogTelemetryService.getCarStatusSessionCommand(
            vulogCarId,
          );

        return telemetryCar;
      }),
    ).catch((err) => {
      Logger.error('Cannot get list of real-time car', err);

      throw new CarError(
        'Cannot get list of real-time car',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    });

    const rltCarsPluggedAndNotCharging: RealtimeCar[] =
      listOfLatestTelemetryCars.filter(
        (rltCar) => rltCar.isPluggedIn && !rltCar.isCharging,
      );

    if (!rltCarsPluggedAndNotCharging.length) {
      Logger.warn(
        'There is no available match with plugged and not charging cars at station',
        { stationId: stationId.value },
      );

      return null;
    }

    const chargingPluggedAtByVulogCarId: {
      realtimeCar: RealtimeCar;
      event: CarEvent;
      timeDifference: number;
    }[] = [];

    await Promise.all(
      rltCarsPluggedAndNotCharging.map(async (rltCar) => {
        const chargingPluggedCarEvent: CarEvent =
          await this.vulogCarService.getLatestEventByType(
            rltCar.id,
            VulogCarEventType.VEHICLE_CHARGING_PLUGGED,
          );

        if (chargingPluggedCarEvent)
          chargingPluggedAtByVulogCarId.push({
            realtimeCar: rltCar,
            event: chargingPluggedCarEvent,
            timeDifference: lastUpdatedOfOcpiEvse // `lastUpdated` of EVSE should happen after charging plugged event
              .diff(chargingPluggedCarEvent.date)
              .toMillis(),
          });
      }),
    ).catch((err) => {
      Logger.error('Cannot get VEHICLE_CHARGING_PLUGGED event for car(s)', err);

      throw new CarError(
        'Cannot get VEHICLE_CHARGING_PLUGGED event for car(s)',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    });

    // Choose the car that has the max time difference as observed, vulog plugged event happens before OCPI EVSE `CHARGING` event, often 1-minute difference
    const [suitablePluggedMetadata] = chargingPluggedAtByVulogCarId.sort(
      (a, b) => b.timeDifference - a.timeDifference,
    );

    if (!suitablePluggedMetadata) {
      Logger.warn(
        'There is no suitable VEHICLE_CHARGING_PLUGGED event for car(s)',
      );
    }

    return suitablePluggedMetadata;
  }
}
