import { Injectable, Logger } from '@nestjs/common';
import { InvalidVulogEventError } from 'src/common/errors/invalid-vulog-event-error';
import { CarPlateNumber, VulogCarId, VulogUserId } from 'src/common/tiny-types';
import {
  FindOneVulogEventConditions,
  VulogEventRepository,
} from 'src/model/repositories/vulog-event.repository';
import { VulogEvent } from 'src/model/vulog-event';
import { CarService } from './car/car.service';
import { VulogUserService } from './vulog/vulog-user.service';
import { BaseVulogEvent, VulogAimaEvent } from './vulog/vulog.event';

@Injectable()
export class VulogEventService {
  constructor(
    private readonly vulogEventRepository: VulogEventRepository,
    private readonly carService: CarService,
    private readonly vulogUserService: VulogUserService,
  ) {}

  private async validateVehicle(aimaEvent: VulogAimaEvent): Promise<void> {
    if (!aimaEvent.eVehicleId) return;

    let vehicle;
    try {
      vehicle = await this.carService.getOneByVulogId(
        new VulogCarId(aimaEvent.eVehicleId),
      );
    } catch {
      throw new InvalidVulogEventError(
        `Vehicle with Vulog ID [${aimaEvent.eVehicleId}] is not found`,
      );
    }

    if (
      aimaEvent.eVehiclePlate &&
      vehicle?.plate &&
      !vehicle?.plate.equals(new CarPlateNumber(aimaEvent.eVehiclePlate))
    ) {
      Logger.warn(
        `Vehicle is found but the vehicle plate does not match with incoming data (current: ${vehicle.plate.value}, incoming: ${aimaEvent.eVehiclePlate})`,
      );
    }

    if (
      aimaEvent.eVehicleVin &&
      vehicle?.vin &&
      vehicle?.vin !== aimaEvent.eVehicleVin
    ) {
      Logger.warn(
        `Vehicle is found but the vehicle VIN does not match with incoming data (current: ${vehicle.vin}, incoming: ${aimaEvent.eVehicleVin})`,
      );
    }
  }

  private async validateUser(aimaEvent: VulogAimaEvent): Promise<void> {
    if (!aimaEvent.eUserId) return;

    const vulogUser = await this.vulogUserService.findOneByVulogUserId(
      new VulogUserId(aimaEvent.eUserId),
    );

    if (!vulogUser) {
      throw new InvalidVulogEventError(
        `User with Vulog ID [${aimaEvent.eUserId}] is not found`,
      );
    }
  }

  private async validateEventUniqueness(eventId: string): Promise<void> {
    const existingVulogEvent = await this.vulogEventRepository.findOne({
      where: { eventId },
    });

    if (existingVulogEvent) {
      throw new InvalidVulogEventError('Vulog Event already existed');
    }
  }

  async createVulogEvent<T extends BaseVulogEvent>(
    vulogEvent: T,
  ): Promise<VulogEvent> {
    await this.validateVehicle(vulogEvent);
    await this.validateUser(vulogEvent);
    await this.validateEventUniqueness(vulogEvent.eId);

    // Save Vulog Event into database
    const bsgVulogEvent: VulogEvent =
      await this.vulogEventRepository.createVulogEvent(
        vulogEvent.toVulogEvent(),
      );

    // If the event is an error event, its business value is useless to CRS. Vulog should not relay the event again
    if (vulogEvent.isErrorEvent()) {
      throw new InvalidVulogEventError(
        `Vulog event received, but with error information: ${vulogEvent.getErrorMessage()}. No further action is performed.`,
      );
    }

    return bsgVulogEvent;
  }

  async getOneByConditions(
    conditions: FindOneVulogEventConditions,
  ): Promise<VulogEvent> {
    return this.vulogEventRepository.findOneByConditions(conditions);
  }
}
