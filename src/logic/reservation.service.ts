import { CorrelationIdService } from '@bluesg-2/monitoring';
import {
  GracePeriodInfo,
  PopInformation,
  PreReservation,
  QPopBsgUserId,
  QueuePopCarModel,
  ReservationInfo as QPopReservationInfo,
  ReservationService as IReservationService,
  StationAvailableInformation,
} from '@bluesg-2/queue-pop';

import * as asyncRetry from 'async-retry';

import { UserInfo } from '@bluesg-2/customer-authentication';
import {
  RentalEndedEvent,
  RentalReservationCancelledEvent,
  RentalReservationCreatedEvent,
  RentalReservationExpiredEvent,
  RentalReservationSwitchedEvent,
} from '@bluesg-2/event-library';
import { HttpService } from '@nestjs/axios';
import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { DateTime } from 'luxon';
import { ClsService } from 'nestjs-cls';
import { SERVICE_NAME } from 'src/common/constants/environments';
import { PaginationSortingOrder } from 'src/common/constants/pagination';
import { RentalTechnicalError } from 'src/common/errors/rental-technical-error';
import { GracePeriodError } from 'src/common/errors/reservation/grace-period.error';
import { MaxConcurrentTripReachedError } from 'src/common/errors/reservation/max-concurrent-trip-reached.error';
import { ReservationNotFoundError } from 'src/common/errors/reservation/reservation-not-found.error';
import { VulogApiError } from 'src/common/errors/vulog-api-error';
import { UUID } from 'src/common/tiny-types/uuid.type';
import { generateUUID } from 'src/common/utils/uuid.util';
import { AdminGetListReservationReqDtoV1 } from 'src/controllers/admin/v1/reservation/dto/request/admin.get-list-reservations.v1.req.dto';
import { AttachRentalPackageRequestDtoV1 } from 'src/controllers/dtos/rental/request/attach-rental-package.v1.request.dto';
import { RentalBilledItem } from 'src/controllers/dtos/rental/request/rental-billed.dto.v1';
import { CarReservationCreatedEventDto } from 'src/event/notification/car-reservation-created.event';
import { CarReservationExpiryEventDto } from 'src/event/notification/car-reservation-expiry.event';
import { ReservationCancelationAfterSwitchingCarEventDto } from 'src/event/reservation/reservation-cancelation-after-switching-car.event';
import { InternalUserDetailResponseDtoV1 } from 'src/external/user-subscription/dtos/response/internal.user-detail.v1.response.dto';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { RentalPackage } from 'src/model/rental-package';
import { Station } from 'src/model/station';
import {
  FindManyOptions,
  In,
  IsNull,
  MoreThan,
  MoreThanOrEqual,
  Not,
} from 'typeorm';
import { v4 } from 'uuid';
import { ConfigurationService } from '~shared/configuration/configuration.service';
import { ConfigurationKey } from '~shared/configuration/constants/configuration';
import { EventBusService } from '~shared/event-bus/event-bus.service';
import { HttpLogger } from '~shared/http-logger/http-logger';
import { NewRelicReservationEventService } from '~shared/newrelic/event/event-type/reservation.event.newrelic.service';
import {
  NewRelicMetricCarModel,
  NewRelicMetricStepInFlow,
  NewRelicMetricTrigger,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { PageResult } from '../common/api/page-result';
import { QueryPagination } from '../common/api/pagination';
import { CarModelEnum } from '../common/enum/car-model.enum';
import { ReservationStatus } from '../common/enum/reservation.enum';
import { BlueSGUserNotFound } from '../common/errors/external/blue-sg-user-not-found.error';
import { PackageNotAvailableError } from '../common/errors/external/package-not-available.error';
import { ReservationError } from '../common/errors/reservation-error';
import {
  BsgUserId,
  ReservationId,
  StationId,
  VulogCarId,
  VulogJourneyOrTripId,
  VulogProfileId,
} from '../common/tiny-types';
import { VulogDate } from '../common/tiny-types/vulog-date.type';
import { PaginationUtil } from '../common/utils/pagination.util';
import { config } from '../config';
import { GetReservationsByPlateNoAndTimeDtoV1 } from '../controllers/internal/v1/reservation/dto/request/get-reservations-by-plate-no.v1.dto';
import { BillingExternalService } from '../external/billing/billing.external.service';
import { UserSubscriptionExternalService } from '../external/user-subscription/user-subscription.external.service';
import { ReservationEntity } from '../model/repositories/entities/reservation.entity';
import { ReservationRepository } from '../model/repositories/reservation.repository';
import { VulogUserRepository } from '../model/repositories/vulog-user.repository';
import { ReservationInfo } from '../model/reservation-info';
import { Reservation } from '../model/reservation.domain';
import { AvailabilityService } from './availability.service';
import { CacheService } from './cache/cache.service';
import { VulogCarService } from './car-repository/vulog/vulog-car.service';
import { CarService } from './car/car.service';
import { Car } from './car/domain/car';
import { HttpAdapterService } from './http-adapter.service';
import { ReservationCacheService } from './reservation/reservation.cache.service';
import { StationService } from './station/station.service';
import { VulogAuthService } from './vulog/vulog-auth.service';

export const reservationServiceDepsExcludedFromRebuilding = [
  HttpService,
  CorrelationIdService,
  ClsService,
  CacheService,
  HttpAdapterService,
  HttpLogger,
];

@Injectable()
export class ReservationService implements IReservationService {
  constructor(
    private vulogCarClient: VulogCarService,
    private availabilityService: AvailabilityService,
    private vulogUserRepository: VulogUserRepository,
    private vulogAuthService: VulogAuthService,
    private reservationRepository: ReservationRepository,
    private billingService: BillingExternalService,
    private readonly eventBusService: EventBusService,
    private readonly corIdService: CorrelationIdService,
    private stationService: StationService,
    private configurationService: ConfigurationService,
    private userSubscriptionApiService: UserSubscriptionExternalService,
    private readonly reservationEventService: NewRelicReservationEventService,
    private readonly reservationCacheService: ReservationCacheService,
    private readonly carService: CarService,
  ) {}

  /**
      local = false, firstly mobile calls API /allocate, then later still calls API /allocate to switch
      local = true, firstly mobile calls API /allocate, then later calls API PUT /stations/:stationId/cars/:carPlate
      local = true results in no grace period
  */
  private async preStartRentalOnMobileApp(
    bsgUserId: BsgUserId,
    local: boolean,
  ) {
    const existingOnGoingReservation =
      await this.reservationRepository.getCurrentOnGoingReservation(bsgUserId);

    if (existingOnGoingReservation) {
      if (local && !existingOnGoingReservation.local) {
        existingOnGoingReservation.local = true;

        // `local = true` to indicate that customer is at Starting Rental page in mobile app,
        // when customer exists the app and open the app again, he should be at Starting Rental page.
        // Note that only at Starting Rental page, customer is able to switch car
        return await this.saveOne(existingOnGoingReservation);
      } else if (existingOnGoingReservation.local) {
        throw new ReservationError(
          'A car reservation already exists for this station',
          HttpStatus.BAD_REQUEST,
        );
      } else {
        // Do nothing
        return existingOnGoingReservation;
      }
    }
  }

  async getOwnOnGoingRental(bsgUserId: BsgUserId): Promise<Reservation> {
    const rentals = (
      await this.getAllReservations(
        new QueryPagination({
          page: 1,
          pageSize: 10,
          sortKey: 'createdAt',
          sortType: PaginationSortingOrder.DESCENDING,
        }),
        bsgUserId,
        [
          ReservationStatus.Converted,
          ReservationStatus.EndedWaitingForInvoicing,
        ],
      )
    ).result.filter((r) => r.isOnGoingRental());

    if (!rentals.length) return null;

    return rentals[0];
  }

  async getBsgOnGoingRentals(
    carModel?: CarModelEnum,
    rentalType?: 'subscription' | 'package',
  ): Promise<Reservation[]> {
    const rentals = await this.reservationRepository.find({
      where: {
        status: In([
          ReservationStatus.Converted,
          ReservationStatus.EndedWaitingForInvoicing,
        ]),
        carModel,
        rentalPackageData: rentalType
          ? rentalType === 'package'
            ? Not(IsNull())
            : IsNull()
          : undefined,
      },
    });

    return rentals?.length
      ? rentals
          .map((rt) => Reservation.from(rt))
          .filter((rt) => rt.isOnGoingRental())
      : [];
  }

  async countBsgOnGoingRentals(
    carModel?: CarModelEnum,
    rentalType?: 'subscription' | 'package',
  ): Promise<number> {
    const count = await this.reservationRepository.count({
      where: {
        status: In([
          ReservationStatus.Converted,
          ReservationStatus.EndedWaitingForInvoicing,
        ]),
        carModel,
        rentalPackageData: rentalType === 'package' ? Not(IsNull()) : IsNull(),
      },
    });

    return count;
  }

  async allocateCar(
    startStationId: string,
    userInfo: UserInfo,
    carModel?: CarModelEnum,
    local = false,
    backOfficeUserId?: string,
  ): Promise<Reservation> {
    const bsgUserId = new BsgUserId(userInfo.id);
    try {
      const ussUserInfo: InternalUserDetailResponseDtoV1 =
        await this.userSubscriptionApiService.validateUssUser(bsgUserId);

      if (!!(await this.getOwnOnGoingRental(bsgUserId))) {
        throw new ReservationError(
          `The user (BSG User ID: ${bsgUserId.value}) has an on-going rental. Cannot make new reservation.`,
          HttpStatus.BAD_REQUEST,
        );
      }

      // Legacy behavior (Polyconceil)
      // Local `true` is to go to "Pre-start rental" screen directly, at the moment the reservation still has status `RESERVED, cancel the reservation should not result in grace period
      const localReservation = await this.preStartRentalOnMobileApp(
        bsgUserId,
        local,
      );

      if (localReservation?.local) {
        return localReservation;
      }

      // After the first check in `shouldStartRentalImmediately`,
      // we could fetch the on-going reservation with more loose conditions
      const onGoingReservation =
        await this.reservationRepository.getCurrentOnGoingReservation(
          bsgUserId,
        );

      const latestFinishedReservation =
        await this.reservationRepository.getLatestFinishedReservation(
          bsgUserId,
        );

      if (latestFinishedReservation?.isInGracePeriod()) {
        throw new GracePeriodError(
          `You can't reserve now. After you cancel reservation or your reservation is expired, you must wait ${latestFinishedReservation.gracePeriodDuration} before making a new one.`,
        );
      }

      // Verify that a car is available at the target station
      const availableCars =
        await this.availabilityService.getAvailableCarsForSpecificStation(
          new StationId(startStationId),
          userInfo,
        );

      if (availableCars.length === 0) {
        throw new ReservationError(
          'There is no available car for allocation',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Retrieve Vulog user profileId
      const vulogUser = await this.vulogAuthService.findOrCreateUser(bsgUserId);

      let carToAllocate: Car;

      // Pick the first car in the list
      // Available cars from CS car sorted by availabilityUpdatedAt
      // See CarInternalApiService
      carToAllocate = availableCars[0];

      // If there is a specific carModel, it will allocate a random car matched that carModel
      if (carModel) {
        carToAllocate = availableCars.find((car) => car.model === carModel);

        if (!carToAllocate) {
          throw new ReservationError(
            `There is no available car matched the provided car model`,
            HttpStatus.BAD_REQUEST,
          );
        }
      }

      // Some customer was allocated a car, after 1 second, he tried to switch to a new car. At this moment, the cache was still stale (TTL: 30 seconds)
      const sameAllocatedCarDueToStaleCache: boolean =
        carToAllocate &&
        carToAllocate.vulogId.value === onGoingReservation?.carId?.value;

      if (sameAllocatedCarDueToStaleCache) {
        throw new ReservationError(
          'There is no available car for allocation (stale car availability)',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Saving creating reservation
      const newReservation = new ReservationEntity({
        bsgUserId: bsgUserId.value,
        userFullName: `${ussUserInfo.firstName} ${ussUserInfo.lastName}`,
        userEmail: ussUserInfo.email,
        startStationId,
        status: ReservationStatus.Creating,
        local,
        createdBy: backOfficeUserId,
        modifiedBy: backOfficeUserId,
      });

      let finalReservation: Reservation;

      const toBeSwitched = !!onGoingReservation;

      // Switching reservation inside this function due to behavior of legacy (Polyconceil).
      // Please note that only reservation with `local = true` could perform switching as customer is at Starting Rental page
      if (toBeSwitched) {
        finalReservation = onGoingReservation;

        // This flag is set to `true` in case the webhook receives `cancelTrip` event from Vulog, CRS should ignore it
        finalReservation.allocateAgain = true;
        await this.reservationRepository.updateReservation(finalReservation);

        await this.vulogCarClient.cancelCarReservationOrAllocation(
          finalReservation.vulogTripId,
          bsgUserId,
        );

        // Retry until `allocateAgain` is set to `false` by CancelTripReport event handler
        try {
          await asyncRetry<void>(
            async () => {
              finalReservation = await this.getOneByVulogId(
                finalReservation.vulogTripId,
              );

              if (finalReservation.allocateAgain) {
                throw new Error('Retries until zero reached');
              }
            },
            {
              factor: 2,
              retries: 5,
            },
          );
        } catch (err) {}
      }
      // A normal reservation is allocated
      else {
        finalReservation = await this.reservationRepository.createReservation(
          Reservation.from(newReservation),
        );

        await this.reservationEventService.emitAllocated(
          carToAllocate.model as unknown as NewRelicMetricCarModel,
        );
      }

      let reservationResult: ReservationInfo;

      let shouldBookOldCar = false;

      try {
        reservationResult = await this.vulogCarClient.bookCar(
          carToAllocate.vulogId,
          bsgUserId,
          config.vulogConfig.serviceId,
          vulogUser.profileId,
        );
      } catch (err) {
        // Switching car is failed because the availability cache is stale (refreshed every 30 seconds)
        if (toBeSwitched) {
          shouldBookOldCar = true;

          try {
            reservationResult = await this.vulogCarClient.bookCar(
              finalReservation.carId, // Re-book the old one
              bsgUserId,
              config.vulogConfig.serviceId,
              vulogUser.profileId,
            );
          } catch (err) {
            Logger.error('Failed to book old car when switching is failed', {
              err,
            });

            await this.reservationRepository.updateStatus(
              finalReservation,
              ReservationStatus.Cancelled,
              [ReservationStatus.Reserved],
              {
                canceledAt: VulogDate.now(),
                allocateAgain: false,
                abuseReleasesAt: null,
              },
            );

            await this.reservationEventService.emitTerminatedByTechnicalError(
              finalReservation.getMetricNewRelicCarModel(),
              NewRelicMetricStepInFlow.SwitchCar,
            );

            throw new RentalTechnicalError(
              'Your reservation was canceled due to technical problems. Please try to book a new car again.',
            );
          }
        } else {
          Logger.error('Normal booking is failed', { err });

          // Booking new car is failed because the availability cache is stale (refreshed every 30 seconds)
          throw new ReservationError(
            `Sorry that your booking request is failed. Please try to book again.`,
            HttpStatus.SERVICE_UNAVAILABLE,
          );
        }
      }

      const carFromReservationResult: Car =
        await this.carService.getOneByVulogId(reservationResult.carId);

      const finalStartStationId: string = !shouldBookOldCar
        ? startStationId
        : finalReservation.startStationId.value;

      const updatedReservationEntity = new ReservationEntity({
        id: finalReservation.getId,
        bsgUserId: bsgUserId.value,
        carId: carFromReservationResult.vulogId.value,
        carModel: carFromReservationResult.model,
        startStationId: finalStartStationId,
        vulogTripId: reservationResult.sourceId.value,
        status: ReservationStatus.Reserved,
        local,
        allocateAgain: false,
        reservedAt:
          finalReservation?.reservedAt?.toDateTime()?.toJSDate() ||
          DateTime.fromISO(reservationResult.reservedAt.value).toJSDate(),
        expiresAt:
          finalReservation?.expiresAt?.toDateTime()?.toJSDate() ||
          DateTime.fromISO(reservationResult.expiresAt.value).toJSDate(),
        plateNumber: carFromReservationResult.plate.value,
        pricingPolicy: null,
        rentalPackageData: null,
        canceledAt: null,
        createdBy: backOfficeUserId,
        modifiedBy: backOfficeUserId,
      });

      const updatedReservation =
        await this.reservationRepository.updateReservation(
          Reservation.from(updatedReservationEntity),
        );

      if (toBeSwitched) {
        // Save state of car switching for BlueSG user and current on-going BlueSG reservation
        await this.reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.update(
          finalReservation.bsgUserId,
          reservationResult,
        );

        await this.reservationEventService.emitSwitched();
      }

      // Because switching attempt is failed, the old car is still allocated to the customer
      if (shouldBookOldCar) {
        await this.reservationEventService.emitSwitchedFailed();

        throw new ReservationError(
          `Sorry that your attempt to switch from car ${onGoingReservation.plateNumber.value} to car ${carToAllocate.plate.value} is failed. Please choose another car to switch.`,
          HttpStatus.SERVICE_UNAVAILABLE,
        );
      }

      if (toBeSwitched) {
        this.eventBusService.nonBlockingEmit(
          new RentalReservationSwitchedEvent({
            correlationId: this.corIdService.getCorrelationId()?.value || v4(),
            sender: SERVICE_NAME,
            sentAt: DateTime.now().toMillis(),
            payload: {
              id: updatedReservation.getId,
              carId: updatedReservation.carId.value,
              previousCarId: onGoingReservation.carId.value,
              createdAt: updatedReservation.getCreatedAt.toISO(),
              reservedAt: updatedReservation.reservedAt.value,
              switchedAt: DateTime.now().toISO(),
              status: updatedReservation.status,
              userId: updatedReservation.bsgUserId.value,
            },
          }),
        );
      } else {
        this.eventBusService.nonBlockingEmit(
          new RentalReservationCreatedEvent({
            correlationId: this.corIdService.getCorrelationId()?.value || v4(),
            sender: SERVICE_NAME,
            sentAt: DateTime.now().toMillis(),
            payload: {
              id: updatedReservation.getId,
              carId: updatedReservation.carId.value,
              createdAt: updatedReservation.getCreatedAt.toISO(),
              reservedAt: updatedReservation.reservedAt.value,
              status: updatedReservation.status,
              userId: updatedReservation.bsgUserId.value,
            },
          }),
        );
      }

      return updatedReservation;
    } catch (error) {
      if (error instanceof VulogApiError) {
        const vulogApiError = error as VulogApiError;

        if (vulogApiError.data?.code === 'userAlreadyBooked') {
          throw new MaxConcurrentTripReachedError(
            'Give us a moment to update your status and try again in a few minutes.',
          );
        } else {
          throw vulogApiError;
        }
      } else {
        throw error;
      }
    }
  }

  async switchAllocationCar(
    bsgUserId: BsgUserId,
    existingAllocation: Reservation,
    targetCarId: VulogCarId,
    doNotRebookPreviousCar = false,
  ): Promise<Reservation> {
    const vulogUser = await this.vulogUserRepository.findVulogUserByBsgUserId(
      bsgUserId,
    );

    const availableTargetCar =
      await this.vulogCarClient.getSpecificCarInRealtime(targetCarId, true);

    if (!availableTargetCar) {
      throw new ReservationError(
        `The target car (Car ID = [${targetCarId.value}]) is not available right now`,
        HttpStatus.BAD_REQUEST,
      );
    }

    if (
      existingAllocation.local &&
      existingAllocation.carModel !== availableTargetCar.model.value
    ) {
      throw new ReservationError(
        `Your reservation is local. Hence, you could not switch to other cars that have different car model than current selected one (Model: ${existingAllocation.carModel}`,
        HttpStatus.BAD_REQUEST,
      );
    }

    const currentAttachedRentalPackage: RentalPackage =
      existingAllocation.getRentalPackageData();

    if (currentAttachedRentalPackage) {
      const checkPackageRes =
        await this.billingService.internalApi.checkPackageAvailable({
          hrid: currentAttachedRentalPackage.hrid,
          duration: currentAttachedRentalPackage.duration,
          minAllowedBatteryPercentage:
            currentAttachedRentalPackage.minAllowedBatteryPercentage,
          packageId: currentAttachedRentalPackage.packageId.value,
          price: currentAttachedRentalPackage.price,
          time: DateTime.now()
            .plus({
              minutes: 5,
            })
            .toISO(),
          carModel: availableTargetCar.model.value as CarModelEnum,
        });

      if (!checkPackageRes.isAvailable) {
        throw new ReservationError(
          'Your current attached rental package is not available for switching car. Please detach, then switch car, later attach a new suitable rental package',
          HttpStatus.BAD_REQUEST,
        );
      }

      if (
        !availableTargetCar.isAllowedToAttachPackage(
          currentAttachedRentalPackage.minAllowedBatteryPercentage,
        )
      ) {
        throw new ReservationError(
          `The vehicle (Vehicle ID: ${availableTargetCar.id.value}) is under the minimum allowed battery level ${currentAttachedRentalPackage.minAllowedBatteryPercentage} of the package (Package ID: ${currentAttachedRentalPackage.packageId.value}). Hence, cannot switch to the target car (Reservation ID: ${existingAllocation.getId})`,
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    const vulogProfileId = new VulogProfileId(vulogUser.profileId);

    // Ignore canceling reservation by Vulog Event Handler
    existingAllocation.allocateAgain = true;
    let updatedReservation = await this.reservationRepository.updateReservation(
      existingAllocation,
    );

    // Deallocate existing allocation car
    const previousCarId = existingAllocation.carId;
    await this.vulogCarClient.cancelCarReservationOrAllocationAsAdmin(
      previousCarId,
    );

    // Retry until `allocateAgain` is set to `false` by CancelTripReport event handler
    try {
      await asyncRetry<void>(
        async () => {
          updatedReservation = await this.getOneByVulogId(
            updatedReservation.vulogTripId,
          );

          if (updatedReservation.allocateAgain) {
            throw new Error('Retries until zero reached');
          }
        },
        {
          factor: 2,
          retries: 5,
        },
      );
    } catch (err) {}

    let bookingResult: ReservationInfo;

    let shouldBookOldCar = false;

    try {
      bookingResult = await this.vulogCarClient.bookCar(
        targetCarId,
        bsgUserId,
        config.vulogConfig.serviceId,
        vulogProfileId,
      );
    } catch (err) {
      // Switching car is failed because the availability cache is stale (refreshed every 30 seconds)
      try {
        if (doNotRebookPreviousCar) {
          throw new Error('Do not re-book previous car');
        }

        bookingResult = await this.vulogCarClient.bookCar(
          existingAllocation.carId, // Re-book the old one
          bsgUserId,
          config.vulogConfig.serviceId,
          vulogProfileId,
        );

        shouldBookOldCar = true;

        Logger.error(
          `Failed to switch from ${existingAllocation.plateNumber.value} to car ${availableTargetCar.plate.value}`,
          { err },
        );
      } catch (err) {
        Logger.error('Technical problem encountered after best efforts', {
          err,
        });

        await this.reservationRepository.updateStatus(
          existingAllocation,
          ReservationStatus.Cancelled,
          [ReservationStatus.Reserved],
          {
            canceledAt: VulogDate.now(),
            allocateAgain: false,
            abuseReleasesAt: null,
          },
        );

        await this.reservationEventService.emitTerminatedByTechnicalError(
          existingAllocation.getMetricNewRelicCarModel(),
          NewRelicMetricStepInFlow.SwitchCar,
        );

        throw new RentalTechnicalError(
          'Your reservation was canceled due to technical problems. Please try to book a new car again.',
        );
      }
    }

    const startStationId: StationId = !shouldBookOldCar
      ? (
          await this.stationService.getStationByZones(
            availableTargetCar.zoneIds.map(
              (zoneId) => new VulogZone(zoneId.value, null, null, null),
            ),
          )
        )?.id
      : existingAllocation.startStationId;

    const newCarId = bookingResult.carId;
    const carFromReservationResult: Car = await this.carService.getOneByVulogId(
      newCarId,
    );

    updatedReservation = await this.saveOne(
      new Reservation(
        {
          ...updatedReservation.getPropsCopy(),
          reservedAt: updatedReservation.reservedAt,
          expiresAt: updatedReservation.expiresAt,
          startStationId: startStationId,
          vulogTripId: bookingResult.sourceId,
          carId: carFromReservationResult.vulogId,
          carModel: carFromReservationResult.model,
          plateNumber: carFromReservationResult.plate,
          allocateAgain: false,
        },
        new UUID(updatedReservation.getId),
      ),
    );

    if (newCarId.value !== previousCarId.value) {
      this.eventBusService.nonBlockingEmit(
        new RentalReservationSwitchedEvent({
          correlationId: this.corIdService.getCorrelationId()?.value || v4(),
          sender: SERVICE_NAME,
          sentAt: DateTime.now().toMillis(),
          payload: {
            id: updatedReservation.getId,
            carId: newCarId.value,
            previousCarId: previousCarId.value,
            createdAt: updatedReservation.getCreatedAt.toISO(),
            reservedAt: updatedReservation.reservedAt.value,
            switchedAt: DateTime.now().toISO(),
            status: updatedReservation.status,
            userId: updatedReservation.bsgUserId.value,
          },
        }),
      );
    }

    // Because switching attempt is failed, the old car is still allocated to the customer
    if (shouldBookOldCar) {
      await this.reservationEventService.emitSwitchedFailed();

      throw new ReservationError(
        `Sorry that your attempt to switch from car ${existingAllocation.plateNumber.value} to car ${availableTargetCar.plate.value} is failed. Please choose another car to switch.`,
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }

    await this.reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.update(
      updatedReservation.bsgUserId,
      bookingResult,
    );

    await this.reservationEventService.emitSwitched();

    return updatedReservation;
  }

  // Unrealistic scenario? Let's not use this block of code
  /** 
  private async swapAllocationWithOtherUser(
    vulogUserId: VulogUserId,
    targetCar: RealtimeCar,
    existingAllocation: ReservationInfo,
    vulogProfileId: VulogProfileId,
  ): Promise<ReservationInfo> {
    // Find user who is currently allocated to target car
    // there should only be one reservation for the target car
    const targetCarReservations =
      await this.vulogCarClient.getAllFinishedReservationsAndAllocations(
        targetCar.plate,
      );
    if (targetCarReservations.length !== 1) {
      throw new ReservationError(
        `Invalid number of reservations found for car: ${targetCarReservations.length}`,
      );
    }
    const vulogUserIdToSwapWith = targetCarReservations[0].vulogUserId;

    // Deallocate/unreserve target car from other user
    await this.vulogCarClient.cancelCarReservationOrAllocationAsAdmin(
      targetCarReservations[0].sourceCarId,
    );

    // Deallocate existing car from current user
    await this.vulogCarClient.cancelCarReservationOrAllocationAsAdmin(
      existingAllocation.sourceCarId,
    );

    // Allocate target car to current user
    const finalAllocation = await this.vulogCarClient.bookCarAsAdmin(
      targetCar.sourceId,
      vulogUserId,
      vulogProfileId,
    );

    // Allocate/reserve existing car to other user
    await this.vulogCarClient.bookCarAsAdmin(
      existingAllocation.sourceCarId,
      vulogUserIdToSwapWith,
      vulogProfileId,
    );

    return finalAllocation;
  }
    */

  async attachRentalPackage(
    reservation: Reservation,
    checkPackageParams: AttachRentalPackageRequestDtoV1,
  ): Promise<Reservation> {
    const time = DateTime.now()
      .plus({
        minutes: 5,
      })
      .toISO();

    const checkPackageRes =
      await this.billingService.internalApi.checkPackageAvailable({
        ...checkPackageParams,
        time,
        carModel: reservation.carModel,
      });

    if (!checkPackageRes.isAvailable) {
      throw new PackageNotAvailableError();
    }

    const currentAllocatedRealtimeCar =
      await this.vulogCarClient.getSpecificCarInRealtime(reservation.carId);

    if (
      !currentAllocatedRealtimeCar.isAllowedToAttachPackage(
        checkPackageParams.minAllowedBatteryPercentage,
      )
    ) {
      throw new ReservationError(
        `The vehicle (Vehicle ID: ${currentAllocatedRealtimeCar.id.value}) is under the minimum allowed battery level ${checkPackageParams.minAllowedBatteryPercentage} of the package (Package ID: ${checkPackageParams.packageId}). Hence, cannot switch to the target car (Reservation ID: ${reservation.getId})`,
        HttpStatus.BAD_REQUEST,
      );
    }

    return await this.reservationRepository.updatePackage(reservation, {
      ...checkPackageParams,
      isDynamic: checkPackageRes.isDynamic,
    });
  }

  async detachRentalPackage(reservation: Reservation): Promise<Reservation> {
    const updatedReservation = await this.reservationRepository.updatePackage(
      reservation,
      null,
    );
    return updatedReservation;
  }

  async getOneByCarIdAndTripId(
    carId: VulogCarId,
    vulogTripId: VulogJourneyOrTripId,
  ): Promise<Reservation> {
    const reservation =
      await this.reservationRepository.findOneByCarIdAndTripId(
        carId,
        vulogTripId,
      );

    if (!reservation) {
      throw new ReservationError(
        'No reservation is found',
        HttpStatus.BAD_REQUEST,
      );
    }

    return reservation;
  }

  async getOne(id: ReservationId): Promise<Reservation> {
    const reservation = await this.reservationRepository.findOneById(id);

    if (!reservation) {
      throw new ReservationError(
        'No reservation is found',
        HttpStatus.BAD_REQUEST,
      );
    }

    return reservation;
  }

  async saveOne(reservation: Reservation): Promise<Reservation> {
    return await this.reservationRepository.saveOne(reservation);
  }

  async cancelCurrentReservation(
    bsgUserId: BsgUserId,
    skipAbuseCheck = false,
  ): Promise<Reservation[]> {
    const gracePeriodInMinutes = (
      await this.configurationService.findOne<number>(
        ConfigurationKey.GRACE_PERIOD_IN_MINUTES,
      )
    ).value;

    let skipAbuseFlag = skipAbuseCheck;

    // Although Vulog allows 1 reservation at at time, it returns an array format
    const currentReservations =
      await this.vulogCarClient.getUserCurrentReservations(bsgUserId);

    return await Promise.all(
      currentReservations.map(async (rsrv) => {
        let ownReservation = await this.findOneByTripId(rsrv.sourceId);

        // Set this to true for avoiding executing unnecessary CancelTripReport event handler from Vulog
        ownReservation.allocateAgain = true;

        Logger.log(
          'Set `allocateAgain` to false to avoid unnecessary CancelTripReport handler',
        );

        await this.saveOne(ownReservation);

        try {
          await this.vulogCarClient.cancelCarReservationOrAllocation(
            rsrv.sourceId,
            bsgUserId,
          );
        } catch (err) {
          ownReservation.allocateAgain = false;

          await this.saveOne(ownReservation);

          throw err;
        }

        // Retry until `allocateAgain` is set to `false` by CancelTripReport event handler
        try {
          await asyncRetry<void>(
            async () => {
              ownReservation = await this.getOneByVulogId(
                ownReservation.vulogTripId,
              );

              if (ownReservation.allocateAgain) {
                throw new Error('Retries until zero reached');
              }
            },
            {
              factor: 2,
              retries: 5,
            },
          );
        } catch (err) {}

        ownReservation.canceledAt = VulogDate.now();

        if (ownReservation.local) {
          skipAbuseFlag = true;
        }

        if (!skipAbuseFlag) {
          ownReservation.setAbuseReleasesAt(gracePeriodInMinutes);
        }

        ownReservation = await this.reservationRepository.updateStatus(
          ownReservation,
          ReservationStatus.Cancelled,
          [ReservationStatus.Reserved],
          {
            canceledAt: ownReservation.canceledAt, // Canceling reservation is trivial action, we don't need to wait for Vulog to return this event information
            abuseReleasesAt: !skipAbuseFlag
              ? ownReservation.abuseReleasesAt
              : undefined,
          },
        );

        skipAbuseFlag = skipAbuseCheck; // revert to the original one

        if (ownReservation.isInGracePeriod()) {
          Logger.log('Reservation is in grace period/cooldown period', {
            reservation: ownReservation,
          });
        }

        await this.reservationEventService.emitCanceled(
          NewRelicMetricTrigger.Mobile,
        );

        this.eventBusService.nonBlockingEmit(
          new RentalReservationCancelledEvent({
            correlationId: this.corIdService.getCorrelationId()?.value || v4(),
            sender: SERVICE_NAME,
            sentAt: DateTime.now().toMillis(),
            payload: {
              id: ownReservation.getId,
              carId: ownReservation.carId.value,
              createdAt: ownReservation.getCreatedAt.toISO(),
              reservedAt: ownReservation.reservedAt.value,
              cancelledAt: ownReservation.canceledAt.value,
              status: ownReservation.status,
              userId: ownReservation.bsgUserId.value,
            },
          }),
        );

        return ownReservation;
      }),
    );
  }

  /** @deprecated from legacy @bluesg-2/queue-pop interface */
  isAvailable: (stationId: string) => Promise<boolean> = async (
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    stationId: string,
  ) => {
    throw new Error('Deprecated method');
  };

  /** @deprecated from legacy @bluesg-2/queue-pop interface */
  getAvailable: (stationId: string) => Promise<StationAvailableInformation> =
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async (stationId: string) => {
      throw new Error('Deprecated method');
    };

  /** @deprecated from legacy @bluesg-2/queue-pop interface */
  getAvailableByPopInformation: (
    popInfo: PopInformation,
  ) => Promise<StationAvailableInformation> = async (
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    popInfo: PopInformation,
  ) => {
    throw new Error('Deprecated method');
  };

  hasOnGoingRental: (userId: string) => Promise<boolean> = async (
    userId: string,
  ) => {
    const onGoingRental = await this.getAllReservations(
      {
        page: 1,
        pageSize: 1,
        sortKey: 'reservedAt',
        sortType: PaginationSortingOrder.DESCENDING,
      },
      new BsgUserId(userId),
      [ReservationStatus.Converted],
    );

    return !!onGoingRental.result?.length;
  };

  isInGracePeriod: (userId: QPopBsgUserId) => Promise<GracePeriodInfo> = async (
    userId: QPopBsgUserId,
  ) => {
    const latestFinishedReservation =
      await this.reservationRepository.getLatestFinishedReservation(
        new BsgUserId(userId.value),
      );

    const isInGracePeriod = !!(
      latestFinishedReservation && latestFinishedReservation.isInGracePeriod()
    );

    return {
      isInGracePeriod,
      gracePeriodDuration: isInGracePeriod
        ? latestFinishedReservation.gracePeriodDuration
        : null,
    };
  };

  async getLatestFinishedReservation(bsgUser: BsgUserId): Promise<Reservation> {
    return await this.reservationRepository.getLatestFinishedReservation(
      bsgUser,
    );
  }

  expiresOnGoingReservation: (userId: string) => Promise<void> = async (
    userId: string,
  ) => {
    const bsgUserId = new BsgUserId(userId);

    const reservation = await this.getLatestFinishedReservation(
      new BsgUserId(userId),
    );

    if (!reservation) {
      return;
    }

    await this.cancelCurrentReservation(bsgUserId, true);
  };

  preReservationToReservation: (
    preReservation: PreReservation,
    pickedCarModel: QueuePopCarModel,
  ) => Promise<QPopReservationInfo> = async (
    preReservation,
    pickedCarModel,
  ) => {
    Logger.log('[Q-Pop] Running logic to pop pre-reservation...', {
      preReservation,
      pickedCarModel,
    });

    if (!preReservation.popStationId) {
      Logger.debug(
        `Q-Pop (ID: ${preReservation.id} does not have station to POP`,
      );

      return;
    }

    const reservation = await this.allocateCar(
      preReservation.popStationId,
      new UserInfo(preReservation.userId, null, null),
      pickedCarModel as unknown as CarModelEnum,
    );

    this.eventBusService.nonBlockingEmit(
      new CarReservationCreatedEventDto(
        reservation,
        this.corIdService.getCorrelationId()?.value || generateUUID(),
      ),
    );

    const qPopReservationInfo = new QPopReservationInfo();

    qPopReservationInfo.id = reservation.getId;
    qPopReservationInfo.reservedAt = reservation.reservedAt.toDateTime();
    qPopReservationInfo.expiresAt = reservation.expiresAt.toDateTime();
    qPopReservationInfo.metadata = {
      crs: {
        carId: reservation.carId.value,
        carModel: reservation.carModel as unknown as QueuePopCarModel,
      },
    };

    return qPopReservationInfo;
  };

  async getReservationsByPlateNumberAndTime(
    dto: GetReservationsByPlateNoAndTimeDtoV1,
  ) {
    return this.reservationRepository.getReservationsByPlateNumberAndTime(dto);
  }

  async findOneById(id: ReservationId): Promise<Reservation> {
    return await this.reservationRepository.findOneById(id);
  }

  async updateReservation(domain: Reservation): Promise<Reservation> {
    return await this.reservationRepository.updateReservation(domain);
  }

  async findOneByTripId(tripId: VulogJourneyOrTripId): Promise<Reservation> {
    return await this.reservationRepository.findOneByTripId(tripId);
  }

  async getReservationsEndedFrom(endedFrom: DateTime): Promise<Reservation[]> {
    const reservationEntities = await this.reservationRepository.find({
      where: {
        carId: Not(IsNull()),
        endStationId: Not(IsNull()),
        status: ReservationStatus.Ended,
        endedAt: MoreThanOrEqual(endedFrom.toJSDate()),
      },
      order: {
        endedAt: PaginationSortingOrder.ASCENDING,
      },
    });

    return reservationEntities?.length
      ? reservationEntities.map((entity) => Reservation.from(entity))
      : [];
  }

  async getRecentlyUpdatedReservations(
    updatedFrom: DateTime,
  ): Promise<Reservation[]> {
    return this.reservationRepository.getRecentlyUpdatedReservations(
      updatedFrom,
    );
  }

  async getAllReservations(
    queryPagination: QueryPagination,
    bsgUserId?: BsgUserId,
    status?: ReservationStatus[],
    vulogCarId?: VulogCarId,
    vulogTripId?: VulogJourneyOrTripId,
  ): Promise<PageResult<Reservation[]>> {
    const { page, pageSize, sortKey, sortType } = queryPagination;

    const findOptions: FindManyOptions<ReservationEntity> = {
      order: {
        [sortKey]: sortType.toLowerCase(),
      },
    };

    if (page || pageSize) {
      const paginationUtil = new PaginationUtil(page, pageSize);

      const skip = paginationUtil.getSkip();
      const take = paginationUtil.getTake();

      findOptions.skip = skip;
      findOptions.take = take;

      queryPagination = new QueryPagination({
        page: paginationUtil.page,
        pageSize: paginationUtil.pageSize,
      });
    }

    if (status && status.length) {
      findOptions.where = {
        ...findOptions.where,
        status: In(status),
      };
    }

    if (bsgUserId) {
      findOptions.where = {
        ...findOptions.where,
        bsgUserId: bsgUserId.value,
      };
    }

    if (vulogCarId) {
      findOptions.where = {
        ...findOptions.where,
        carId: vulogCarId.value,
      };
    }

    if (vulogTripId) {
      findOptions.where = {
        ...findOptions.where,
        vulogTripId: vulogTripId.value,
      };
    }

    const total = await this.reservationRepository.count(findOptions);

    const reservationEntities = await this.reservationRepository.find(
      findOptions,
    );

    return PageResult.from(
      reservationEntities?.length
        ? reservationEntities.map((entity) => Reservation.from(entity))
        : [],
      queryPagination,
      total,
      sortKey,
      sortType,
    );
  }

  async handleVulogEndTripReportEvent(
    reservation: Reservation,
    endStation: Station,
    tripEndDate: string,
    correlationId?: string,
  ): Promise<Reservation> {
    const car = await this.vulogCarClient.getStaticCar(reservation.carId);

    if (!car) {
      throw new Error('Car not found!');
    }

    const userInfo =
      await this.userSubscriptionApiService.internalApi.getUserDetail(
        reservation.bsgUserId.value,
      );

    if (!userInfo) {
      throw new BlueSGUserNotFound();
    }

    const startStation = await this.stationService.getStation(
      reservation.startStationId,
    );

    if (!startStation) {
      throw new Error('Start station not found!');
    }

    reservation = await this.prepareForInvoicing({
      reservation,
      user: userInfo,
      startStation,
      endStation,
      endedAt: new VulogDate(tripEndDate),
      correlationId: correlationId || v4(),
    });

    return reservation;
  }

  async getOneByVulogId(
    vulogTripId: VulogJourneyOrTripId,
    skipValidate = false,
  ): Promise<Reservation> {
    const entity = await this.reservationRepository.findOne({
      where: {
        vulogTripId: vulogTripId.value,
      },
    });

    if (!entity && !skipValidate) {
      throw new ReservationNotFoundError();
    }

    return entity ? Reservation.from(entity) : null;
  }

  async cancel(reservation: Reservation, cancelledAt: Date): Promise<void> {
    await this.reservationRepository.updateStatus(
      reservation,
      ReservationStatus.Cancelled,
      [ReservationStatus.Reserved],
      { canceledAt: VulogDate.fromJsDate(cancelledAt) },
    );
  }

  async expireReservation(): Promise<void> {
    const reservations =
      await this.reservationRepository.getToBeExpiredReservations();

    const gracePeriodInMinutes = (
      await this.configurationService.findOne<number>(
        ConfigurationKey.GRACE_PERIOD_IN_MINUTES,
      )
    ).value;

    await Promise.allSettled(
      reservations.map(async (reservation) => {
        const carSwitchingPerformed: boolean =
          await this.reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.retrieve(
            reservation.bsgUserId,
          );

        const correlationId =
          this.corIdService.getCorrelationId()?.value || v4();

        if (carSwitchingPerformed) {
          Logger.log(
            'After expiration of car-switching reservation, emit message to self reservation cancelation in Vulog AiMa',
            { corId: correlationId },
          );

          this.eventBusService.nonBlockingEmit(
            new ReservationCancelationAfterSwitchingCarEventDto(
              {
                reservationId: reservation.getId,
                bsgUserId: reservation.bsgUserId.value,
                correlationId,
              },
              correlationId,
            ),
          );
        }

        reservation.setAbuseReleasesAt(gracePeriodInMinutes);

        const updatedReservation: Reservation =
          await this.reservationRepository.updateStatus(
            reservation,
            ReservationStatus.Expired,
            [ReservationStatus.Reserved],
            {
              abuseReleasesAt: reservation.abuseReleasesAt,
            },
          );

        await this.reservationEventService.emitExpired(
          updatedReservation.getMetricNewRelicCarModel(),
        );

        this.eventBusService.nonBlockingEmit(
          new CarReservationExpiryEventDto(
            updatedReservation,
            this.corIdService.getCorrelationId()?.value || v4(),
          ),
        );

        this.eventBusService.nonBlockingEmit(
          new RentalReservationExpiredEvent({
            correlationId: this.corIdService.getCorrelationId()?.value || v4(),
            sender: SERVICE_NAME,
            sentAt: DateTime.now().toMillis(),
            payload: {
              id: updatedReservation.getId,
              carId: updatedReservation.carId.value,
              createdAt: updatedReservation.getCreatedAt.toISO(),
              reservedAt: updatedReservation.reservedAt.value,
              expiredAt: updatedReservation.expiresAt.value,
              status: updatedReservation.status,
              userId: updatedReservation.bsgUserId.value,
            },
          }),
        );
      }),
    ).then((values) => {
      const errorResults = values.filter(
        (v) => v.status === 'rejected',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ) as any;

      if (errorResults.length) {
        Logger.error('Settled Error: ', errorResults);
      }
    });
  }

  async updateWithBill(
    reservationId: ReservationId,
    rentalBilledItems: Array<RentalBilledItem>,
  ): Promise<Reservation> {
    const totalNetAmountTaxIncluded =
      rentalBilledItems.reduce((total, cur) => {
        return total + (cur.netAmount || 0);
      }, 0) || 0; // refactor why null is 0 later

    const reservation: Reservation =
      await this.reservationRepository.findOneById(reservationId);

    const updatedReservation = await this.reservationRepository.updateStatus(
      reservation,
      ReservationStatus.Ended,
      [ReservationStatus.Ended],
      {
        amount: totalNetAmountTaxIncluded,
      },
    );

    return updatedReservation;
  }

  async getListReservationForAdmin(
    dto: AdminGetListReservationReqDtoV1,
  ): Promise<[Reservation[], number]> {
    const { startStationName, endStationName } = dto;
    const [startStations, endStations] = await Promise.all([
      this.stationService.getStationByName(startStationName),
      this.stationService.getStationByName(endStationName),
    ]);

    const startStationIds = startStations?.map((station) => station.id.value);
    const endStationIds = endStations?.map((station) => station.id.value);

    return this.reservationRepository.getListReservations(
      Object.assign(dto, {
        startStationIds,
        endStationIds,
      }),
    );
  }

  async prepareForInvoicing({
    reservation,
    user,
    startStation,
    endStation,
    endedAt,
    correlationId,
  }: {
    reservation: Reservation;
    user: InternalUserDetailResponseDtoV1;
    startStation: Station;
    endStation?: Station;
    endedAt: VulogDate;
    correlationId?: string;
  }): Promise<Reservation> {
    const updatedReservation = await this.reservationRepository.updateStatus(
      reservation,
      ReservationStatus.Ended,
      [ReservationStatus.Converted],
      {
        endedAt,
        endStationId: endStation ? endStation.id : null,
        duration: reservation.getBilledDuration(),
        isEndingRentalDataAdded: endStation ? true : undefined,
      },
    );

    Logger.log('Prepare for invoicing: ', updatedReservation);

    this.sendRentalEndedEvent(
      updatedReservation,
      user,
      startStation,
      endStation,
      correlationId,
    );

    return updatedReservation;
  }

  private sendRentalEndedEvent(
    reservation: Reservation,
    user: InternalUserDetailResponseDtoV1,
    startStation: Station,
    endStation?: Station,
    correlationId?: string,
  ): void {
    const rentalPackageData = reservation.getRentalPackageData();

    this.eventBusService.nonBlockingEmit(
      new RentalEndedEvent({
        correlationId: correlationId || v4(),
        sender: SERVICE_NAME,
        sentAt: DateTime.now().toMillis(),
        payload: {
          id: reservation.getId,
          carId: reservation.carId.value,
          createdAt: reservation.getCreatedAt.toISO(),
          reservedAt: reservation.reservedAt.value,
          startedAt: reservation.startedAt.value,
          startStationId: reservation.startStationId.value,
          endedAt: reservation.endedAt.value,
          endStationId: reservation.endStationId.value,
          status: reservation.status,
          userId: reservation.bsgUserId.value,
          // legacy fields for backward compatibility
          tripInfo: {
            id: reservation.getId,
            startedAt: reservation.startedAt.value,
            endedAt: reservation.endedAt.value,
            duration: reservation.getBilledDuration(),
          },
          carInfo: {
            id: reservation.carId.value,
            model: reservation.carModel,
          },
          userInfo: {
            id: reservation.bsgUserId.value,
          },
          subscriptionInfo: {
            id: user.currentSubscription.id,
            subscriptionPlanId: user.currentSubscription.subscriptionPlanId,
          },
          startStationInfo: {
            id: reservation.startStationId.value,
            sourceId: startStation.sourceId,
            name: startStation.displayName,
          },
          endStationInfo: endStation
            ? {
                id: reservation.endStationId.value,
                sourceId: endStation.sourceId,
                name: endStation.displayName,
              }
            : null,
          rentalPackageInfo: rentalPackageData
            ? {
                id: rentalPackageData.packageId.value,
                title: rentalPackageData.name,
                hrid: rentalPackageData.hrid,
                name: rentalPackageData.name,
                duration: rentalPackageData.duration,
                price: rentalPackageData.price,
              }
            : null,
        },
      }),
    );
  }

  async getBsgOnGoingReservationByBsgUserId(
    bsgUserId: BsgUserId,
  ): Promise<Reservation> {
    const entity = await this.reservationRepository.findOne({
      where: {
        bsgUserId: bsgUserId.value,
        status: ReservationStatus.Reserved,
        expiresAt: MoreThan(DateTime.now().toJSDate()),
      },
    });

    return entity ? Reservation.from(entity) : null;
  }

  async getBsgOnGoingReservationByVulogCarId(
    vulogCarId: VulogCarId,
  ): Promise<Reservation> {
    const entity = await this.reservationRepository.findOne({
      where: {
        carId: vulogCarId.value,
        status: ReservationStatus.Reserved,
        expiresAt: MoreThan(DateTime.now().toJSDate()),
      },
    });

    return entity ? Reservation.from(entity) : null;
  }

  async getBsgOnGoingReservationByVulogTripId(
    vulogTripId: VulogJourneyOrTripId,
  ): Promise<Reservation> {
    const entity = await this.reservationRepository.findOne({
      where: {
        vulogTripId: vulogTripId.value,
        status: ReservationStatus.Reserved,
        expiresAt: MoreThan(DateTime.now().toJSDate()),
      },
    });

    return entity ? Reservation.from(entity) : null;
  }

  async getBsgOnGoingRentalByVulogCarId(
    vulogCarId: VulogCarId,
  ): Promise<Reservation> {
    const rentals = (
      await this.getAllReservations(
        new QueryPagination({
          page: 1,
          pageSize: 10,
          sortKey: 'createdAt',
          sortType: PaginationSortingOrder.DESCENDING,
        }),
        null,
        [
          ReservationStatus.Converted,
          ReservationStatus.EndedWaitingForInvoicing,
        ],
        vulogCarId,
      )
    ).result.filter((r) => r.isOnGoingRental());

    if (!rentals.length) return null;

    return rentals[0];
  }

  async getBsgOnGoingRentalByVulogTripId(
    vulogTripId: VulogJourneyOrTripId,
  ): Promise<Reservation> {
    const rentals = (
      await this.getAllReservations(
        new QueryPagination({
          page: 1,
          pageSize: 10,
          sortKey: 'createdAt',
          sortType: PaginationSortingOrder.DESCENDING,
        }),
        null,
        [
          ReservationStatus.Converted,
          ReservationStatus.EndedWaitingForInvoicing,
        ],
        null,
        vulogTripId,
      )
    ).result.filter((r) => r.isOnGoingRental());

    if (!rentals.length) return null;

    return rentals[0];
  }

  async getBsgOnGoingReservations(
    carModel?: CarModelEnum,
  ): Promise<Reservation[]> {
    const entities = await this.reservationRepository.find({
      where: {
        status: ReservationStatus.Reserved,
        expiresAt: MoreThan(DateTime.now().toJSDate()),
        carModel,
      },
    });

    return entities?.length
      ? entities.map((entity) => Reservation.from(entity))
      : [];
  }

  async countBsgOnGoingReservations(carModel?: CarModelEnum): Promise<number> {
    const count = await this.reservationRepository.count({
      where: {
        status: ReservationStatus.Reserved,
        expiresAt: MoreThan(DateTime.now().toJSDate()),
        carModel,
      },
    });

    return count;
  }

  async getBsgCooldownReservations(
    carModel?: CarModelEnum,
  ): Promise<Reservation[]> {
    const entities = await this.reservationRepository.find({
      where: {
        status: In([ReservationStatus.Cancelled, ReservationStatus.Expired]),
        abuseReleasesAt: MoreThan(DateTime.now().toJSDate()),
        carModel,
      },
    });

    return entities?.length
      ? entities.map((entity) => Reservation.from(entity))
      : [];
  }

  async countBsgCooldownReservations(carModel?: CarModelEnum): Promise<number> {
    const count = await this.reservationRepository.count({
      where: {
        status: In([ReservationStatus.Cancelled, ReservationStatus.Expired]),
        abuseReleasesAt: MoreThan(DateTime.now().toJSDate()),
        carModel,
      },
    });

    return count;
  }

  async getBsgEndedWaitingForInvoicing(
    carModel?: CarModelEnum,
    rentalType?: 'package' | 'subscription',
  ): Promise<Reservation[]> {
    const rentals = await this.reservationRepository.find({
      where: {
        status: ReservationStatus.EndedWaitingForInvoicing,
        carModel,
        rentalPackageData: rentalType === 'package' ? Not(IsNull()) : IsNull(),
      },
    });

    return rentals?.length ? rentals.map((rt) => Reservation.from(rt)) : [];
  }

  async countBsgEndedWaitingForInvoicing(
    carModel?: CarModelEnum,
    rentalType?: 'package' | 'subscription',
  ): Promise<number> {
    const count = await this.reservationRepository.count({
      where: {
        status: ReservationStatus.EndedWaitingForInvoicing,
        carModel,
        rentalPackageData: rentalType === 'package' ? Not(IsNull()) : IsNull(),
      },
    });

    return count;
  }
}
