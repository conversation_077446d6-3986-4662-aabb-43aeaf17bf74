import {
  BsgUserId,
  CarPlateNumber,
  VulogCarId,
  VulogJourneyOrTripId,
  VulogProfileId,
  VulogServiceId,
  VulogUserId,
} from 'src/common/tiny-types';
import { RentalInfo } from 'src/model/rental-info';
import { ReservationAdminInfo } from 'src/model/reservation-admin-info';
import { ReservationInfo } from 'src/model/reservation-info';
import { RealtimeCar } from '../../model/realtime.car';

export class CarRepositoryInterface {
  getAllAvailableCars: () => Promise<RealtimeCar[]>;
  // getCarInformation: (carId: CarId) => Promise<Car>;
  bookCar: (
    carSourceId: VulogCarId,
    bsgUserId: BsgUserId,
    vulogServiceId: VulogServiceId,
    vulogUserProfileId: VulogProfileId,
    vulogPricingId: string,
    userEmail: string,
  ) => Promise<ReservationInfo>;
  bookCarAsAdmin: (
    carSourceId: VulogCarId,
    vulogUserId: VulogUserId,
    profileId: VulogProfileId,
  ) => Promise<ReservationInfo>;
  getUserCurrentReservations: (
    bsgUserId: BsgUserId,
    userEmail: string,
  ) => Promise<ReservationInfo[]>;
  cancelCarReservationOrAllocation: (
    vulogJourneyId: VulogJourneyOrTripId,
    bsgUserId: BsgUserId,
    userEmail: string,
  ) => Promise<void>;
  cancelCarReservationOrAllocationAsAdmin: (
    vulogVehicleId: VulogCarId,
  ) => Promise<void>;
  getAllFinishedRentals: (carPlate?: string) => Promise<RentalInfo[]>;
  getAllFinishedReservationsAndAllocations: (
    carPlate?: CarPlateNumber,
  ) => Promise<ReservationAdminInfo[]>;
  startRental: (
    bsgUserId: BsgUserId,
    vulogJourneyId: VulogJourneyOrTripId,
    userEmail: string,
  ) => Promise<RentalInfo>;
  getUserCurrentRentals: (
    bsgUserId: BsgUserId,
    userEmail: string,
  ) => Promise<RentalInfo[]>;
  endRental: (
    vulogJourneyId: VulogJourneyOrTripId,
    bsgUserId: BsgUserId,
    userEmail: string,
  ) => Promise<void>;
}
