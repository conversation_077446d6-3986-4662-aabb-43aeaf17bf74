import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import * as asyncRetry from 'async-retry';
import { plainToInstance } from 'class-transformer';
import { isDefined } from 'class-validator';
import { PaginationSortingOrder } from 'src/common/constants/pagination';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { VulogApiError } from 'src/common/errors/vulog-api-error';
import { Page } from 'src/common/query/page';
import {
  BsgUserId,
  CarModel,
  CarPlateNumber,
  VulogCarId,
  VulogJourneyOrTripId,
  VulogProfileId,
  VulogServiceId,
  VulogUserId,
  VulogZoneId,
} from 'src/common/tiny-types';
import { VulogFilterDate } from 'src/common/tiny-types/vulog-filter-date.type';
import { LoggerUtils } from 'src/common/utils/logger.util';
import { MapUtils } from 'src/common/utils/map.util';
import { config } from 'src/config';
import { CacheService } from 'src/logic/cache/cache.service';
import { CarService } from 'src/logic/car/car.service';
import { Car } from 'src/logic/car/domain/car';
import { HttpEmptyDataResponse } from 'src/logic/http-adapter.service';
import { VulogCarConnectionProblemService } from 'src/logic/vulog/vulog-car-connection-problem.service';
import { VulogClientService } from 'src/logic/vulog/vulog-client.service';
import { VulogFleetService } from 'src/logic/vulog/vulog-fleet-service.service';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { CarEvent } from 'src/model/car-event';
import { Configuration } from 'src/model/configuration';
import {
  VulogCarEventDto,
  VulogCarEventType,
} from 'src/model/dtos/vulog-car-event/vulog-car-event.dto';
import { VulogCarRealTimeDto } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogCarStaticInfoDto } from 'src/model/dtos/vulog-car-static-info/vulog-car-static-info.dto';
import {
  VulogJourneyDto,
  VulogJourneyWrapperDto,
  VulogRelActions,
} from 'src/model/dtos/vulog-journey/vulog-journey.dto';
import { VulogPagination } from 'src/model/dtos/vulog-pagination/vulog-pagination';
import {
  VulogZone,
  VulogZoneType,
} from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { VulogTripDto } from 'src/model/dtos/vulog-trip/vulog-trip.dto';
import { FleetService } from 'src/model/fleet-service';
import { RentalInfo } from 'src/model/rental-info';
import { ReservationAdminInfo } from 'src/model/reservation-admin-info';
import { ReservationInfo } from 'src/model/reservation-info';
import { StaticCar } from 'src/model/static.car';
import { ConfigurationService } from 'src/shared/configuration/configuration.service';
import { ConfigurationKey } from 'src/shared/configuration/constants/configuration';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { CarInfo } from '~shared/feature-flag/models/car-info';
import { NewRelicMetricStepInFlow } from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { RealtimeCar } from '../../../model/realtime.car';
import { CarRepositoryInterface } from '../car-repository.interface';
import { DoorOpenEndRentalError } from '../errors/door-open.end-rental.error';
import { EndRentalError } from '../errors/end-rental.error';
import { TerminateTripError } from '../errors/terminate-trip.error';
import { EndRentalVulogErrorCode } from './end-rental.vulog-error-code';

const availableCarModels = Object.keys(CarModelEnum).filter((item) => {
  return isNaN(Number(item));
});

@Injectable()
export class VulogCarService implements CarRepositoryInterface {
  constructor(
    private client: VulogClientService,
    private readonly cacheService: CacheService,
    private readonly configurationService: ConfigurationService,
    private readonly carService: CarService,
    private readonly vulogFleetService: VulogFleetService,
    private readonly vulogCarConnectionProblemService: VulogCarConnectionProblemService,
    private readonly featureFlagService: FeatureFlagService,
  ) {}

  async getAllStaticCars(): Promise<StaticCar[]> {
    const cacheResult = await this.cacheService.get<Record<string, unknown>[]>(
      config.redisConfig.staticCars.cacheKey,
    );

    let allCars = cacheResult?.map((item) =>
      plainToInstance(VulogCarStaticInfoDto, item),
    );

    if (!allCars) {
      allCars = await this.client.getAllPagesAsArray(
        VulogPaths.backOfficeListingAllStaticVehicles(
          config.vulogConfig.fleetId,
        ),
        VulogCarStaticInfoDto,
      );

      await this.cacheService.set(
        config.redisConfig.staticCars.cacheKey,
        allCars,
        { ttl: config.redisConfig.staticCars.ttl },
      );
    }

    return allCars ? allCars.map((carDto) => carDto.toStaticCar()) : [];
  }

  async getStaticCar(carId: VulogCarId): Promise<StaticCar> {
    const staticCars = await this.getAllStaticCars();

    return staticCars.find((car) => car.id.value === carId.value);
  }

  async fetchRealtimeVehicles(useCache = true): Promise<VulogCarRealTimeDto[]> {
    const cacheConfig = config.redisConfig.realTimeCars;
    let realTimeCarDtos: VulogCarRealTimeDto[];

    const cacheContent: VulogCarRealTimeDto[] = useCache
      ? await this.cacheService.get(cacheConfig.cacheKey)
      : null;

    if (!cacheContent) {
      realTimeCarDtos = await this.client.getAsArray(
        VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId),
        VulogCarRealTimeDto,
      );

      await this.cacheService.set(cacheConfig.cacheKey, realTimeCarDtos, {
        ttl: cacheConfig.ttl,
      });
    }

    const finalData = !!cacheContent
      ? plainToInstance(VulogCarRealTimeDto, cacheContent)
      : realTimeCarDtos;

    const realTimeCarPlateMap = MapUtils.build(
      finalData,
      (rltCarDto) => rltCarDto.plate,
    );

    await this.cacheService.set(
      config.redisConfig.realtimeCarPlateMap.cacheKey,
      MapUtils.toObject(realTimeCarPlateMap),
      {
        ttl: config.redisConfig.realtimeCarPlateMap.ttl,
      },
    );

    return finalData;
  }

  async getRealtimeCarsPlateMap(): Promise<{
    [plate: string]: VulogCarRealTimeDto;
  }> {
    let cacheResult = await this.cacheService.get<{
      [plate: string]: VulogCarRealTimeDto;
    }>(config.redisConfig.realtimeCarPlateMap.cacheKey);

    if (!cacheResult || !Object.keys(cacheResult).length) {
      await this.fetchRealtimeVehicles(false);
    }

    cacheResult = await this.cacheService.get<{
      [plate: string]: VulogCarRealTimeDto;
    }>(config.redisConfig.realtimeCarPlateMap.cacheKey);

    const data: { [plate: string]: VulogCarRealTimeDto } = {};

    Object.entries(cacheResult).forEach(([key, realtimeCarDto]) => {
      data[key] = plainToInstance(VulogCarRealTimeDto, realtimeCarDto);
    });

    return data;
  }

  async getWarningLowBatteryLevel(): Promise<Configuration> {
    return (
      await this.configurationService.findOne<Configuration>(
        ConfigurationKey.WARNING_LOW_BATTERY_LEVEL_PER_CAR_MODEL,
      )
    ).value;
  }

  async filterAvailableCars(
    realTimeCarDtos: VulogCarRealTimeDto[],
    carsMapByVulogId: Map<string, Car>,
    customerUsableFleetService: FleetService,
    warningLowBatterLevel: object,
  ): Promise<VulogCarRealTimeDto[]> {
    return (
      await Promise.all(
        realTimeCarDtos.map(async (rltCar) => {
          const result = await this.checkCarAvailableCriteria(rltCar, {
            carsMapByVulogId,
            customerUsableFleetService,
            warningLowBatterLevel,
          });

          return result;
        }),
      )
    ).filter((car) => !!car);
  }

  private async checkCarAvailableCriteria(
    vgRealTimeCarDto: VulogCarRealTimeDto,
    comparisonInfo: {
      carsMapByVulogId: Map<string, Car>;
      warningLowBatterLevel: object;
      customerUsableFleetService: FleetService;
    },
  ): Promise<VulogCarRealTimeDto> {
    const {
      carsMapByVulogId,
      customerUsableFleetService,
      warningLowBatterLevel,
    } = comparisonInfo;

    const model = carsMapByVulogId.get(vgRealTimeCarDto.id)?.model;

    const warningLowBatteryLevelByModel = warningLowBatterLevel[model];

    const filterOutNonCustomerZones =
      await this.featureFlagService.filterOutNonCustomerZones(
        CarInfo.fromRealtimeCar(vgRealTimeCarDto.toRealtimeCar()),
      );

    if (!warningLowBatteryLevelByModel) {
      Logger.debug(
        `The car (Car ID: [${vgRealTimeCarDto.id}]) has introduced new model (Model: [${model}]) that is not known by our service (Available model enum keys (not values): ${availableCarModels})`,
      );
    }

    return vgRealTimeCarDto.isAvailable(
      warningLowBatteryLevelByModel,
      customerUsableFleetService?.vehicleIds,
      filterOutNonCustomerZones
        ? customerUsableFleetService.getZoneIdsAsMap()
        : null,
    )
      ? vgRealTimeCarDto
      : null;
  }
  async getCustomerUsableFleetService(): Promise<FleetService> {
    return await this.vulogFleetService.getCustomerUsableFleetService();
  }

  /**
   * Use this method to get available cars based on acceptance criteria
   * @param availableOnly
   * @returns
   */
  async getCarsInRealtime(
    availableOnly = false,
    warnMultipleStickyZones = false,
  ): Promise<RealtimeCar[]> {
    const realTimeCarDtos: VulogCarRealTimeDto[] =
      await this.fetchRealtimeVehicles();
    const carsMapByVulogId: Map<string, Car> =
      await this.carService.getMapByVulogId();
    const customerUsableFleetService: FleetService =
      await this.vulogFleetService.getCustomerUsableFleetService();

    const warningLowBatterLevel = await this.getWarningLowBatteryLevel();

    const filteredRealTimeCarDtos = availableOnly
      ? await this.filterAvailableCars(
          realTimeCarDtos,
          carsMapByVulogId,
          customerUsableFleetService,
          warningLowBatterLevel,
        )
      : realTimeCarDtos;

    const result: RealtimeCar[] = await Promise.all(
      filteredRealTimeCarDtos.map(async (rltCarDto) => {
        const carModel = carsMapByVulogId.has(rltCarDto.id)
          ? carsMapByVulogId.get(rltCarDto.id)?.model
            ? new CarModel(carsMapByVulogId.get(rltCarDto.id)?.model)
            : null
          : null;

        const filterOutNonCustomerZones =
          await this.featureFlagService.filterOutNonCustomerZones(
            CarInfo.fromRealtimeCar(rltCarDto.toRealtimeCar()),
          );

        return rltCarDto.toRealtimeCar(
          carModel,
          filterOutNonCustomerZones
            ? customerUsableFleetService.getZoneIdsAsMap()
            : null,
          warnMultipleStickyZones,
        );
      }),
    );

    return result ? result : [];
  }

  async getSpecificCarInRealtime(
    carId: VulogCarId,
    availableOnly = false,
  ): Promise<RealtimeCar> {
    const carsInRealtime = await this.getCarsInRealtime(availableOnly);

    return carsInRealtime.find((rltCar) => rltCar.id.equals(carId));
  }

  /**
   * Get available car directly from Vulog, not through cache
   * @param vulogCarId
   * @param availableOnly
   * @returns
   */
  async getSpecificCarInRealTimeDirectly(
    vulogCarId: VulogCarId,
    availableOnly?: boolean,
  ): Promise<RealtimeCar> {
    const rltCarDto = await this.client.get(
      VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId, vulogCarId),
      VulogCarRealTimeDto,
    );

    const carsMapByVulogId = await this.carService.getMapByVulogId();

    const warningLowBatterLevel = await this.getWarningLowBatteryLevel();

    const car: Car = carsMapByVulogId.get(rltCarDto.id);

    const model = car?.model;

    const warningLowBatteryLevelByModel = warningLowBatterLevel[model];

    const customerUsableFleetService =
      await this.vulogFleetService.getCustomerUsableFleetService();

    if (!warningLowBatteryLevelByModel) {
      Logger.warn(
        `The car (Car ID: [${rltCarDto.id}]) has introduced new model (Model: [${model}]) that is not known by our service (Available model enum keys (not values): ${availableCarModels})`,
      );
    }

    const filterOutNonCustomerZones =
      await this.featureFlagService.filterOutNonCustomerZones(
        CarInfo.fromCar(car),
      );

    let rltCar: RealtimeCar = null;

    if (rltCarDto) {
      const carModel = carsMapByVulogId.has(rltCarDto.id)
        ? carsMapByVulogId.get(rltCarDto.id)?.model
          ? new CarModel(carsMapByVulogId.get(rltCarDto.id)?.model)
          : null
        : null;

      rltCar = availableOnly
        ? (
            await this.checkCarAvailableCriteria(rltCarDto, {
              carsMapByVulogId,
              customerUsableFleetService,
              warningLowBatterLevel,
            })
          )?.toRealtimeCar()
        : rltCarDto.toRealtimeCar(
            carModel,
            filterOutNonCustomerZones
              ? customerUsableFleetService.getZoneIdsAsMap()
              : null,
          );
    }

    return rltCar;
  }

  async getSpecificOnGoingRental(
    car: Car,
    useCache = true,
  ): Promise<RentalInfo> {
    const rentalInfos = useCache
      ? await this.getOnGoingRentals({ vin: car.vin }, true)
      : null;

    let rentalInfo = rentalInfos?.length ? rentalInfos[0] : null;

    if (!rentalInfo) {
      const rltCarDto = await this.client.get(
        VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId, car.vulogId),
        VulogCarRealTimeDto,
      );

      const customerUsableFleetService =
        await this.vulogFleetService.getCustomerUsableFleetService();

      const filterOutNonCustomerZones =
        await this.featureFlagService.filterOutNonCustomerZones(
          CarInfo.fromCar(car),
        );

      rentalInfo = rltCarDto?.isRental()
        ? rltCarDto.toRentalInfo(
            filterOutNonCustomerZones
              ? customerUsableFleetService.getZoneIdsAsMap()
              : null,
          )
        : null;
    }

    return rentalInfo;
  }

  async getAllAvailableCars(): Promise<RealtimeCar[]> {
    return await this.getCarsInRealtime(true);
  }

  async getStaticInformationOfAllCars(): Promise<StaticCar[]> {
    const allStaticInfo = await this.client.getAllPagesAsArray(
      VulogPaths.backOfficeListAllVehicles(config.vulogConfig.fleetId),
      VulogCarStaticInfoDto,
    );

    return allStaticInfo
      ? allStaticInfo.map((staticInfo) => staticInfo.toStaticCar())
      : [];
  }

  async bookCar(
    carSourceId: VulogCarId,
    bsgUserId: BsgUserId,
    vulogServiceId: VulogServiceId,
    vulogUserProfileId: VulogProfileId,
  ): Promise<ReservationInfo> {
    // No need to be handled by VulogCarConnectionProblemService
    // as most carConnectionProblem happens on actions except `book`
    // NewRelic pattern to look for `FROM carConnectionProblem SELECT count(*) WHERE env = 'production' FACET origin, stepInFlow SINCE 60 days ago`
    const response = await this.client.post(
      VulogPaths.bookJourney(carSourceId),
      VulogJourneyDto,
      null,
      {
        serviceId: vulogServiceId.value,
        profileId: vulogUserProfileId.value,
      },
      bsgUserId,
    );

    return response.toReservationInfo();
  }

  /**
   * Do not use this function as Vulog requires `pricingId`, which we don't have it
   */
  async bookCarAsAdmin(
    carSourceId: VulogCarId,
    vulogUserId: VulogUserId,
    profileId: VulogProfileId,
  ): Promise<ReservationInfo> {
    const response = await this.client.post(
      VulogPaths.vehicleBookingBackOffice(
        config.vulogConfig.fleetId,
        carSourceId,
      ),
      VulogJourneyDto,
      null,
      {
        userId: vulogUserId.value,
        serviceId: config.vulogConfig.serviceId.value,
        profileId: profileId.value,
        pricingId: '',
      },
    );

    return response.toReservationInfo();
  }

  private isJourneyReservation(vulogJourneyDto: VulogJourneyDto): boolean {
    return (
      vulogJourneyDto.rel.includes(VulogRelActions.TripCreate) &&
      vulogJourneyDto.rel.includes(VulogRelActions.BookingDelete)
    );
  }

  private isJourneyRental(vulogJourneyDto: VulogJourneyDto): boolean {
    return (
      !vulogJourneyDto.rel.includes(VulogRelActions.TripCreate) &&
      (!vulogJourneyDto.rel.includes(VulogRelActions.BookingDelete) ||
        vulogJourneyDto.rel.includes(VulogRelActions.TripPause))
    );
  }

  async getUserCurrentReservations(
    bsgUserId: BsgUserId,
  ): Promise<ReservationInfo[]> {
    const response = await this.client.get(
      VulogPaths.currentJourneys(),
      VulogJourneyWrapperDto,
      null,
      null,
      bsgUserId,
    );

    return response
      ? response.journeys
          .filter((dto) => this.isJourneyReservation(dto))
          .map((dto) => dto.toReservationInfo())
      : [];
  }

  async cancelCarReservationOrAllocation(
    vulogJourneyId: VulogJourneyOrTripId,
    bsgUserId: BsgUserId,
  ): Promise<void> {
    await this.vulogCarConnectionProblemService.retryWhenEncounterCarConnectionProblem<void>(
      {
        vulogCarId: null,
        vulogJourneyId: vulogJourneyId,
        callback: async () => {
          await this.client.delete<null>(
            VulogPaths.cancelBooking(vulogJourneyId),
            null,
            null,
            null,
            bsgUserId,
          );
        },
        step: NewRelicMetricStepInFlow.CancelReservation,
      },
    );
  }

  async cancelCarReservationOrAllocationAsAdmin(
    vulogVehicleId: VulogCarId,
  ): Promise<void> {
    await this.vulogCarConnectionProblemService.retryWhenEncounterCarConnectionProblem<void>(
      {
        vulogCarId: vulogVehicleId,
        callback: async () => {
          await this.client.delete<null>(
            VulogPaths.vehicleBookingBackOffice(
              config.vulogConfig.fleetId,
              vulogVehicleId,
            ),
            null,
          );
        },
        step: NewRelicMetricStepInFlow.CancelReservation,
      },
    );
  }

  /**
   * This API only returns finished trips (including ones having status such as fulfilled, canceled)
   */
  async getAllFinishedVulogTrips({
    serviceId,
    startDate,
    endDate,
    carPlate,
    getAll = true,
  }: {
    serviceId?: VulogServiceId;
    startDate?: VulogFilterDate;
    endDate?: VulogFilterDate;
    carPlate?: string;
    /**
     * Use this flag if you wants to return full results
     */
    getAll?: boolean;
  }) {
    const url = VulogPaths.finishedTrips(
      config.vulogConfig.fleetId,
    ).addQueryParams({
      serviceId: serviceId?.value,
      startDate: startDate?.value,
      endDate: endDate?.value,
      plate: carPlate,
    });

    const response = (await this.client.getAllPagesAsArray(
      url,
      VulogTripDto,
      null,
      null,
      undefined,
      getAll
        ? undefined
        : { sortBy: 'endDate', sortOrder: 'desc', startPage: 0, size: 200 },
    )) as unknown;

    return getAll
      ? ((response || []) as VulogTripDto[])
      : (response as Page<VulogTripDto[]>);
  }

  /**
   * This API only returns finished trips (including ones having status such as fulfilled, canceled)
   */
  async getSpecificFinishedTrip(
    tripId: VulogJourneyOrTripId,
  ): Promise<RentalInfo> {
    const response = await this.client.get(
      VulogPaths.specificFinishedTrip(config.vulogConfig.fleetId, tripId),
      VulogTripDto,
      null,
      null,
    );

    return response ? response.toRentalInfo() : null;
  }

  /**
   * Get ongoing rentals. Do not use `getAllFinishedRentals`.
   * Should use cache as much as possible.
   */
  async getOnGoingRentals(
    filters?: {
      vin?: string;
      profileId?: VulogProfileId;
    },
    useCache = true,
  ): Promise<RentalInfo[]> {
    let onGoingTrips = (await this.fetchRealtimeVehicles(useCache)).filter(
      (rc) => !!rc.orderId,
    );

    if (isDefined(filters) && Object.keys(filters).length) {
      onGoingTrips = onGoingTrips.filter((trip) => {
        return Object.keys(filters).every(
          (filterName) =>
            trip[filterName] ===
            (filters[filterName]?.value || filters[filterName]),
        );
      });
    }

    const customerUsableFleetService: FleetService =
      await this.vulogFleetService.getCustomerUsableFleetService();

    return onGoingTrips
      ? await Promise.all(
          onGoingTrips
            .filter((vgRltCarDto) => vgRltCarDto.isRental())
            .map(async (vgRltCarDto) => {
              const filterOutNonCustomerZones =
                await this.featureFlagService.filterOutNonCustomerZones(
                  CarInfo.fromRealtimeCar(vgRltCarDto.toRealtimeCar()),
                );

              return vgRltCarDto.toRentalInfo(
                filterOutNonCustomerZones
                  ? customerUsableFleetService.getZoneIdsAsMap()
                  : null,
              );
            }),
        )
      : [];
  }

  /**
   * This API only returns finished rentals (including ones having status such as fulfilled, canceled)
   */
  async getAllFinishedRentals(carPlate?: string): Promise<RentalInfo[]> {
    const trips = (await this.getAllFinishedVulogTrips({
      carPlate,
    })) as VulogTripDto[];

    return trips
      ? trips
          .filter((trip) => trip.isRental())
          .map((trip) => trip.toRentalInfo())
      : [];
  }

  /**
   * This API only returns finished reservations (including ones having status such as fulfilled, canceled)
   */
  async getAllFinishedReservationsAndAllocations(
    carPlate?: CarPlateNumber,
  ): Promise<ReservationAdminInfo[]> {
    const trips = (await this.getAllFinishedVulogTrips({
      carPlate: carPlate.value,
    })) as VulogTripDto[];

    return trips
      ? trips
          .filter((trip) => trip.isReservationOrAllocation())
          .map((trip) => trip.toReservationAdminInfo())
      : [];
  }

  async startRental(
    bsgUserId: BsgUserId,
    vulogJourneyId: VulogJourneyOrTripId,
  ): Promise<RentalInfo> {
    Logger.debug('Request Vulog to start the rental', {
      vulogJourneyId,
    });

    const response =
      await this.vulogCarConnectionProblemService.retryWhenEncounterCarConnectionProblem<VulogJourneyDto>(
        {
          vulogCarId: null,
          vulogJourneyId: vulogJourneyId,
          callback: async () => {
            return await this.client.post(
              VulogPaths.startOrEndTrip(vulogJourneyId),
              VulogJourneyDto,
              null,
              null,
              bsgUserId,
            );
          },
          shouldCancelReservationAfterBestEffort: true,
          step: NewRelicMetricStepInFlow.StartRental,
        },
      );

    Logger.debug('Response from Vulog', { response });

    return response.toRentalInfo();
  }

  async getUserCurrentRentals(bsgUserId: BsgUserId): Promise<RentalInfo[]> {
    const response = await this.client.get(
      VulogPaths.currentJourneys(),
      VulogJourneyWrapperDto,
      null,
      null,
      bsgUserId,
    );

    return response
      ? response.journeys
          .filter((dto) => this.isJourneyRental(dto))
          .map((dto) => dto.toRentalInfo())
      : [];
  }

  async endRental(
    vulogJourneyId: VulogJourneyOrTripId,
    bsgUserId: BsgUserId,
  ): Promise<void> {
    try {
      await this.vulogCarConnectionProblemService.retryWhenEncounterCarConnectionProblem<void>(
        {
          vulogCarId: null,
          vulogJourneyId,
          callback: async () => {
            await this.client.delete<null>(
              VulogPaths.startOrEndTrip(vulogJourneyId),
              null,
              null,
              null,
              bsgUserId,
            );
          },
          step: NewRelicMetricStepInFlow.EndRental,
        },
      );
    } catch (error) {
      if (
        error instanceof VulogApiError &&
        error.statusCode === HttpStatus.FORBIDDEN &&
        error.data &&
        error.data.code
      ) {
        switch (error.data.code) {
          case EndRentalVulogErrorCode.DOOR_OR_WINDOW_OPEN:
            throw new DoorOpenEndRentalError(error);
          default:
            throw new EndRentalError(
              error.data.code,
              'an error happened when trying to end the rental',
              error,
            );
        }
      }
      throw error;
    }
  }

  async terminateTrip(vehicleId: VulogCarId): Promise<void> {
    let response: HttpEmptyDataResponse;

    try {
      response =
        await this.vulogCarConnectionProblemService.retryWhenEncounterCarConnectionProblem<HttpEmptyDataResponse>(
          {
            vulogCarId: vehicleId,
            callback: async () => {
              return await this.client.post(
                VulogPaths.backOfficeExpert(
                  config.vulogConfig.fleetId,
                  vehicleId,
                ),
                null,
                null,
                {
                  cmd: 'Trip Termination',
                },
              );
            },
            step: NewRelicMetricStepInFlow.TerminateTrip,
          },
        );
    } catch (error) {
      Logger.error(
        'Encountered issues when trying to terminate the trip with Vulog Vehicle Gateway',
        {
          carId: vehicleId.value,
          errDetails: error,
        },
      );

      throw new TerminateTripError(
        error.data.code,
        `an error happened when trying to terminate the trip with Vulog Vehicle Gateway`,
        error,
      );
    }

    Logger.log('Terminate trip response status', {
      response: response.statusMessage,
      responseCode: response.statusCode,
    });

    if (response.statusCode !== HttpStatus.OK) {
      throw new VulogApiError(
        `Unexpected status code received: ${response.statusCode}`,
      );
    }
  }

  async getSpecificATrip(tripId: VulogJourneyOrTripId): Promise<RentalInfo> {
    const response = await this.client.get(
      VulogPaths.getDetailRentalByRentalId(config.vulogConfig.fleetId, tripId),
      VulogTripDto,
      null,
      null,
    );

    return response ? response.toRentalInfo() : null;
  }

  async unlock(tripId: VulogJourneyOrTripId, userId: BsgUserId): Promise<void> {
    await this.client.delete(
      VulogPaths.pauseJourney(tripId),
      null,
      null,
      null,
      userId,
    );
  }

  async isVehicleAvailableWithRetry(car: Car, retries = 4) {
    let rltCar: RealtimeCar;

    // Detect if the vehicle is available for reservation
    try {
      rltCar = await asyncRetry<RealtimeCar>(
        async () => {
          const rltCar = await this.getSpecificCarInRealTimeDirectly(
            car.vulogId,
            true,
          );

          if (!rltCar) {
            throw new Error(
              'The vehicle is not available to reserve. Polling again...',
            );
          }

          return rltCar;
        },
        {
          factor: 2,
          retries,
        },
      );
    } catch (err) {
      Logger.warn(
        'The car is still not available to reserve after many retries...',
        {
          errorDetails: err,
        },
      );
    }

    rltCar &&
      Logger.log('Here is the information of available vehicle', {
        carDetails: LoggerUtils.removeLongAttributes<RealtimeCar>(rltCar, [
          'zoneIds',
          'zoneMaps',
        ]),
      });

    return !!rltCar;
  }

  async disableCar(vulogCarId: VulogCarId, reason: string): Promise<boolean> {
    const response: HttpEmptyDataResponse =
      await this.vulogCarConnectionProblemService.retryWhenEncounterCarConnectionProblem<HttpEmptyDataResponse>(
        {
          vulogCarId,
          callback: async () => {
            return await this.client.post<null>(
              VulogPaths.backOfficeDisableVehicle(
                config.vulogConfig.fleetId,
                vulogCarId,
              ),
              null,
              null,
              {
                subStatus: reason,
              },
            );
          },
          step: NewRelicMetricStepInFlow.DisableCar,
        },
      );

    return response.statusCode === HttpStatus.OK;
  }

  async toggleStickyStationWithCar(
    vulogCarId: VulogCarId,
    vulogZoneId: VulogZoneId,
    sticky: boolean,
  ): Promise<boolean> {
    const response: HttpEmptyDataResponse =
      await this.vulogCarConnectionProblemService.retryWhenEncounterCarConnectionProblem<HttpEmptyDataResponse>(
        {
          vulogCarId,
          callback: async () => {
            return await this.client.put<null>(
              VulogPaths.backOfficeStickStationWithCar(
                config.vulogConfig.fleetId,
                vulogCarId,
                vulogZoneId,
              ),
              null,
              null,
              {
                type: VulogZoneType.Allowed,
                sticky,
              } as Pick<VulogZone, 'type' | 'sticky'>,
            );
          },
          step: sticky
            ? NewRelicMetricStepInFlow.StationAttachment
            : NewRelicMetricStepInFlow.StationDetachment,
        },
      );

    return response.statusCode === HttpStatus.OK;
  }

  async getLatestCarInChargingModeByZoneId(
    zoneId: string,
  ): Promise<RealtimeCar> {
    const carsInRealtime = await this.getCarsInRealtime();

    // Filter only car that is in charging mode, having correlated OCPI session in charging mode also
    const matchedCar = carsInRealtime
      .filter((rltCar) => rltCar.zoneMaps.has(zoneId) && rltCar.isCharging)
      .sort((a, b) => b.lastActiveDate.diff(a.lastActiveDate).toMillis())[0];

    if (!matchedCar)
      Logger.log(
        `No latest car at station (zone Id: ${zoneId}) is in charging mode`,
      );

    return matchedCar;
  }

  async getEvents(
    vulogCarId: VulogCarId,
    pagination: VulogPagination,
  ): Promise<CarEvent[]> {
    const response = (await this.client.get(
      VulogPaths.backOfficeCarEvents(
        config.vulogConfig.fleetId,
        vulogCarId,
      ).addQueryParams(pagination.toDto()),
      VulogCarEventDto,
    )) as unknown as VulogCarEventDto[];

    const carEvents = response?.length
      ? response.map((item) => {
          return item.toCarEvent();
        })
      : [];

    return carEvents;
  }

  async getLatestEventByType(
    vulogCarId: VulogCarId,
    carEventType: VulogCarEventType,
  ) {
    const [carEvent] = await this.getEvents(
      vulogCarId,
      new VulogPagination({
        page: 1,
        size: 1,
        sort: ['date', PaginationSortingOrder.DESCENDING],
        type: carEventType,
      }),
    );

    return carEvent;
  }
}
