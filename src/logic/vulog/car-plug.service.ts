import { Injectable } from '@nestjs/common';
import { VulogCarId } from 'src/common/tiny-types';
import { CarService } from '../car/car.service';

@Injectable()
export class CarPlugService {
  constructor(private readonly carService: CarService) {}

  async plugCable(vulogCarId: VulogCarId): Promise<void> {
    await this.carService.plugChargingCable(vulogCarId, true);
  }

  async unplugCable(vulogCarId: VulogCarId): Promise<void> {
    await this.carService.plugChargingCable(vulogCarId, false);
  }
}
