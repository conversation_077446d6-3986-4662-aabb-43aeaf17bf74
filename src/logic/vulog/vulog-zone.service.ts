import { Injectable } from '@nestjs/common';
import { config } from 'src/config';
import { VulogFeatureCollectionDto } from 'src/model/dtos/vulog-feature/vulog-feature-collection.dto';
import { VulogGeoZone } from 'src/model/vulog-geo-zone';
import { VulogClientService } from './vulog-client.service';
import { VulogPaths } from './vulog-paths';

@Injectable()
export class VulogZoneService {
  constructor(private readonly vulogClientService: VulogClientService) {}

  async getVulogGeoZones(): Promise<VulogGeoZone[]> {
    const vulogFeatureCollection = await this.vulogClientService.get(
      VulogPaths.backOfficeZones(config.vulogConfig.fleetId),
      VulogFeatureCollectionDto,
    );

    return vulogFeatureCollection?.features?.length
      ? vulogFeatureCollection.features.map((vgFeat) => vgFeat.toVulogGeoZone())
      : [];
  }
}
