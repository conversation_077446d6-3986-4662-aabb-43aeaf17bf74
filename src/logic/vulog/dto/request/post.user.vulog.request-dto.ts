import { BsgUserId } from '../../../../common/tiny-types';
import { config } from '../../../../config';
import { VulogUser } from '../../../../model/vulog-user';

const VULOG_CONFIG = config.vulogConfig;

export class PostUserVulogRequestDto {
  readonly emailConsent: boolean = false;
  readonly dataPrivacyConsent: boolean = true;
  readonly birthDate: string = new Date(
    VULOG_CONFIG.birthdatePlaceholder,
  ).toISOString();
  readonly firstName: string = VULOG_CONFIG.firstNamePlaceholder;
  readonly lastName: string = VULOG_CONFIG.lastNamePlaceholder;
  readonly phoneNumber: string = VULOG_CONFIG.phoneNumberPlaceholder;
  readonly locale: string = VULOG_CONFIG.locale;
  readonly membershipNumber: string = VULOG_CONFIG.membershipNumberPlaceholder;

  constructor(
    readonly userName: string,
    readonly email: string,
    readonly password: string,
  ) {}

  static from(vulogUser: VulogUser): PostUserVulogRequestDto {
    return new PostUserVulogRequestDto(
      vulogUser.getUsername(),
      vulogUser.email,
      vulogUser.getPassword(),
    );
  }

  static fromUserInfo(
    id: BsgUserId,
    email: string,
    password: string,
  ): PostUserVulogRequestDto {
    return new PostUserVulogRequestDto(
      id.value,
      `${VULOG_CONFIG.emailPlaceholderPrefix}${email}${VULOG_CONFIG.emailPlaceholderSuffix}`,
      password,
    );
  }
}
