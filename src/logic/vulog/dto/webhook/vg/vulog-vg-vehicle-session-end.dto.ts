import {
  VulogVehicleGatewayEventNotificationDto,
  VulogVehicleGatewayEventType,
  VulogVehicleGatewayOrigin,
} from './vulog-vehicle-gateway-event-notification.dto';

export type VulogVgVehicleSessionEndOrigin =
  | VulogVehicleGatewayOrigin.API // Always consider origin of API first as it is synchronized with Vulog server. BLE and BOX could be outdated (not reliable)
  | VulogVehicleGatewayOrigin.BLE
  | VulogVehicleGatewayOrigin.BOX;

export class VulogVgVehicleSessionEndDto extends VulogVehicleGatewayEventNotificationDto {
  type: VulogVehicleGatewayEventType =
    VulogVehicleGatewayEventType.VEHICLE_SESSION_END;

  origin: VulogVgVehicleSessionEndOrigin;
}
