export enum VulogVehicleGatewayEventType {
  VEHICLE_FATAL = 'VEHICLE_FATAL',
  VEHICLE_ALERT = 'VEHICLE_ALERT',
  VEHICLE_TOWED = 'VEHICLE_TOWED',
  VEHICLE_WARNING = 'VEHICLE_WARNING',
  VEHICLE_DOORS_CLOSED = 'VEHICLE_DOORS_CLOSED',
  VEHICLE_DOORS_OPEN = 'VEHICLE_DOORS_OPEN',
  VEHICLE_BATTERY_CRITICAL = 'VEHICLE_BATTERY_CRITICAL',
  VEHICLE_BATTERY_OK = 'VEHICLE_BATTERY_OK',
  VEHICLE_CHARGING_UNPLUGGED = 'VEHICLE_CHARGING_UNPLUGGED',
  VEHICLE_CHARGING_PLUGGED = 'VEHICLE_CHARGING_PLUGGED',
  VEHICLE_CHARGING_ACTIVE = 'VEHICLE_CHARGING_ACTIVE',
  VEHICLE_CRUISING_RANGE_LOW = 'VEHICLE_CRUISING_RANGE_LOW',
  VEHICLE_CRUISING_RANGE_OK = 'VEHICLE_CRUISING_RANGE_OK',
  VEHICLE_OUT_OF_COM = 'VEHICLE_OUT_OF_COM',
  VEHICLE_SESSION_INIT = 'VEHICLE_SESSION_INIT',
  VEHICLE_PING = 'VEHICLE_PING',
  VEHICLE_CMD = 'VEHICLE_CMD',
  VEHICLE_SESSION_PAUSE = 'VEHICLE_SESSION_PAUSE',
  VEHICLE_SESSION_END = 'VEHICLE_SESSION_END',
  VEHICLE_SESSION_CANCEL = 'VEHICLE_SESSION_CANCEL',
  VEHICLE_SESSION_RESUME = 'VEHICLE_SESSION_RESUME',
  VEHICLE_SESSION_START = 'VEHICLE_SESSION_START',
  VEHICLE_SESSION_REPORT = 'VEHICLE_SESSION_REPORT',
  VEHICLE_REBOOT = 'VEHICLE_REBOOT',
  VEHICLE_STANDBY = 'VEHICLE_STANDBY',
  VEHICLE_GET_STATUS = 'VEHICLE_GET_STATUS',
  VEHICLE_CKH_UPDATE = 'VEHICLE_CKH_UPDATE',
  VEHICLE_SPEED_ALERT = 'VEHICLE_SPEED_ALERT',
  VEHICLE_ACCEL_ALERT = 'VEHICLE_ACCEL_ALERT',
  VEHICLE_UPDATED = 'VEHICLE_UPDATED',
  VEHICLE_CREATED = 'VEHICLE_CREATED',
  VEHICLE_DELETED = 'VEHICLE_DELETED',
  VEHICLE_ADMIN_CARD = 'VEHICLE_ADMIN_CARD',
  VEHICLE_ADMIN_BLE = 'VEHICLE_ADMIN_BLE',
  VEHICLE_AUTOCLOSE_ON = 'VEHICLE_AUTOCLOSE_ON',
  VEHICLE_IGNITION_ON = 'VEHICLE_IGNITION_ON',
  VEHICLE_IGNITION_OFF = 'VEHICLE_IGNITION_OFF',
  VEHICLE_LOCK = 'VEHICLE_LOCK',
  VEHICLE_UNLOCK = 'VEHICLE_UNLOCK',
  VEHICLE_MOBILIZE = 'VEHICLE_MOBILIZE',
  VEHICLE_IMMOBILIZE = 'VEHICLE_IMMOBILIZE',
  VEHICLE_WAKE_UP = 'VEHICLE_WAKE_UP',
  VEHICLE_CONNECTIVITY_ONLINE = 'VEHICLE_CONNECTIVITY_ONLINE', // ⚠️ Identical to AiMA
  VEHICLE_CONNECTIVITY_OFFLINE = 'VEHICLE_CONNECTIVITY_OFFLINE', // ⚠️ Identical to AiMA
  VEHICLE_CONNECTIVITY_INTERRUPTED = 'VEHICLE_CONNECTIVITY_INTERRUPTED', // ⚠️ Identical to AiMA,
  // Starting from this lines: undocumented in Vehicle Gateway documentation, but existing in AiMA documentation
  VEHICLE_END_TRIP = 'VEHICLE_END_TRIP',
  VEHICLE_CANCEL_TRIP = 'VEHICLE_CANCEL_TRIP',
  VEHICLE_START_TRIP = 'VEHICLE_START_TRIP',
}

export enum VulogVehicleGatewayOrigin {
  BOX = 'BOX',
  API = 'API',
  BLE = 'BLE',
  INTERNAL = 'INTERNAL',
  FABRICATION = 'FABRICATION',
}

export class VulogVehicleGatewayEventNotificationDto {
  id: string;

  type: VulogVehicleGatewayEventType;

  origin: VulogVehicleGatewayOrigin;

  vehicleId: string;

  fleetId: string;

  boxId: string;

  hasAlerts?: boolean;

  date?: string;

  insertionDate: string;

  failed: boolean;

  // Present if failed=true. Contains the short description of the error.
  errorMessage?: string;

  errorCode: string;

  sessionId?: string;

  latitude: number;

  longitude?: number;

  speed?: number;

  altitude?: number;

  nbSat?: number;

  gpsFix?: string;

  hdop?: number;

  head?: number;

  primaryFuelTank?: object;

  secondaryFuelTank?: object;

  tractionBattery?: object;

  secondaryTractionBattery?: object;

  mileage?: number;

  auxBatteryVoltage?: number;

  locked?: boolean;

  doorsAndWindowsClosed?: boolean;

  engineOn?: boolean;

  card1Present?: boolean;

  card2Present?: boolean;

  keyPresent?: boolean;

  ckhOk?: boolean;

  charging?: boolean;

  immobilized?: boolean;

  boxStatus?: string;

  // UUID which tags the request. It's used by our support team to easilly extract the context of a request.
  transactionId?: string;

  duration: number;
}
