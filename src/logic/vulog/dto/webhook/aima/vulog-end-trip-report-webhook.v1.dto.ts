import { VulogEventNotifierType } from 'src/common/constants/vulog-event-type';
import { VulogEventNotifierWebhookDtoV1 } from './vulog-event-notifier-webhook.v1.dto';

export class VulogEndTripReportBodyV1 {
  bookingDuration: number;
  tripDuration: number;
  duration: number;
  pauseDuration: number;
  startDate: string;
  endDate: string;
  theorStartDate: string;
  theorEndDate: string;
  mileage: number;
  serviceId: string;
  serviceType: string;
  pricingId: string;
  profileId: string;
  firstDriveDelayMinutes: unknown;
  endZones: string[];
  startZones: string[];
  isSuspicious: boolean;
  suspiciousReason: string;
  startLocation: string;
  endLocation: string;
  error?: string; // Undocumented, but sent by Vulog
  qrCode?: string;
}

export class VulogEndTripReportWebhookDtoV1 extends VulogEventNotifierWebhookDtoV1<VulogEndTripReportBodyV1> {
  constructor() {
    super();

    this.type = VulogEventNotifierType.EndTripReport;
  }
}
