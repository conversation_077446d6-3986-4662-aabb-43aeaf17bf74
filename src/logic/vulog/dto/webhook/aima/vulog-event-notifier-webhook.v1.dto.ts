import { IsOptional } from 'class-validator';
import { VulogEventNotifierType } from 'src/common/constants/vulog-event-type';

export class VulogVehicleStatusV1 {
  energyLevel: number;
  energyLevel2: number;
  auxBatLevel: number;
  doorsClose: boolean;
  doorsLock: boolean;
  speed: number;
  odometer: number;
  engineOn: boolean;
  immobilizerOn: boolean;
  isCharging: boolean;
  boxStatus: number;
}

export class VulogEventNotifierWebhookDtoV1<
  // eslint-disable-next-line @typescript-eslint/ban-types
  T extends object = any,
> {
  @IsOptional()
  id: string;

  @IsOptional()
  fleetId: string;

  @IsOptional()
  vehicleId: string;

  @IsOptional()
  boxId: string;

  @IsOptional()
  date: string;

  @IsOptional()
  vehicleName: string;

  @IsOptional()
  vehicleVin: string;

  @IsOptional()
  vehiclePlate: string;

  @IsOptional()
  userId: string;

  @IsOptional()
  tripId: string;

  @IsOptional()
  isInTrip: boolean;

  @IsOptional()
  isInZone: boolean;

  @IsOptional()
  trigger?: string;

  @IsOptional()
  origin: string;

  @IsOptional()
  vehicleStatus: VulogVehicleStatusV1;

  @IsOptional()
  position: {
    lat: number;
    lon: number;
  };

  @IsOptional()
  type: VulogEventNotifierType;

  @IsOptional()
  body: T;

  @IsOptional()
  qrCode: string;
}
