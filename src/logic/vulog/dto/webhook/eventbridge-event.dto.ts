import { IsOptional } from 'class-validator';

/* Event Bridge payload in body
" {
  version": "0",
  "id": "1ff1d2c1-6eeb-79bd-8210-5c3144255099",
  "detail-type": "Test",
  "source": "vulog.apigateway",
  "account": "************",
  "time": "2023-12-12T08:16:47Z",
  "region": "ap-southeast-1",
  "resources": [],
  detail: { --- payload from vulog injected here --- }
  }
 */

export class EventbridgeEventDto<T = unknown> {
  @IsOptional()
  version: string;

  @IsOptional()
  id: string;

  @IsOptional()
  'detail-type': string;

  @IsOptional()
  source: string;

  @IsOptional()
  account: string;

  @IsOptional()
  time: string;

  @IsOptional()
  region: string;

  @IsOptional()
  resources: string[];

  @IsOptional()
  detail: T; // The type is unknown because it could be AiMA events or VG events
}
