import { Inject } from '@nestjs/common';
import { LOCAL_EVENT_EMITTER_TOKEN } from '~shared/local-event-emitter/constants';
import { LocalEventEmitterService } from '~shared/local-event-emitter/local-event-emitter.service';
import { VulogEventService } from '../vulog-event.service';
import { BaseVulogEvent } from './vulog.event';

export abstract class BaseVulogEventService {
  constructor(
    @Inject(LOCAL_EVENT_EMITTER_TOKEN)
    protected readonly localEventEmitterService: LocalEventEmitterService,
    protected readonly vulogEventService: VulogEventService,
  ) {}

  abstract processEvent(event: BaseVulogEvent): Promise<void>;

  protected abstract routeEvent(event: BaseVulogEvent);
}
