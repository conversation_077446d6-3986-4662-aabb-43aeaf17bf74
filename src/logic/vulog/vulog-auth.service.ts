/* eslint-disable prefer-spread */
import {
  VULOG_ACCESS_TOKEN_KEY,
  VULOG_REFRESH_TOKEN_KEY,
} from 'src/common/constants/cache-keys';
import { VulogAuthError } from 'src/common/errors/vulog-auth-error';
import { BsgUserId, VulogUserId } from 'src/common/tiny-types';
import { config } from 'src/config';
import { VulogUserDto } from 'src/model/dtos/vulog-user/vulog-user.dto';
import { VulogUserRepository } from 'src/model/repositories/vulog-user.repository';

import { Inject, Injectable, Logger } from '@nestjs/common';

import { CorrelationIdService } from '@bluesg-2/monitoring';
import { VULOG_CORRELATION_ID_HEADER } from 'src/common/constants/http-headers';
import { UssApiError } from 'src/common/errors/uss-api-error';
import { UserSubscriptionExternalService } from 'src/external/user-subscription/user-subscription.external.service';
import { NoRateLimitHttpAdapterService } from '~shared/no-rate-limit-http/no-rate-limit-http-adapter.service';
import { NO_RATE_LIMIT_SERVICE_TOKEN } from '~shared/no-rate-limit-http/no-rate-limit-http.module';
import { VulogUser, VulogUserStatus } from '../../model/vulog-user';
import { CacheService } from '../cache/cache.service';
import { AuthConfig, HttpAdapterService } from '../http-adapter.service';
import { RfidUsageService } from '../rfid-usage.service';
import { PostUserVulogRequestDto } from './dto/request/post.user.vulog.request-dto';
import { SystemAccessTokenNotFoundError } from './errors/SystemAccessTokenNotFoundError';
import { VulogAdapterService } from './vulog-adapter.service';
import { VulogPaths } from './vulog-paths';
import { VulogProfileService } from './vulog-profile.service';
import { VulogUserService } from './vulog-user.service';

export interface VulogAuthResponse {
  access_token: string;
  expires_in: number;
  refresh_expires_in: number;
  refresh_token: string;
  token_type: string;
}

export const PROFILE_TO_SERVICE_APPROVAL_STATUS = 'APPROVED';

@Injectable()
export class VulogAuthService {
  constructor(
    private readonly httpAdapterService: HttpAdapterService,
    @Inject(NO_RATE_LIMIT_SERVICE_TOKEN)
    private readonly noRateLimitedHttpAdapterService: NoRateLimitHttpAdapterService,
    private readonly cacheManager: CacheService,
    private vulogUserRepository: VulogUserRepository,
    private vulogAdapterService: VulogAdapterService,
    private userSubscriptionExternalService: UserSubscriptionExternalService,
    private vulogUserService: VulogUserService,
    private correlationIdService: CorrelationIdService,
    private readonly rfidUsageService: RfidUsageService,
    private readonly vulogProfileService: VulogProfileService,
  ) {}

  static readonly CLIENT_ID: string = config.vulogConfig.clientId;
  static readonly CLIENT_SECRET: string = config.vulogConfig.clientSecret;

  private async authReq(body: object, noRateLimit = false) {
    const arrayOfArgs = [
      `${VulogPaths.auth().value}`,
      body,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          [VULOG_CORRELATION_ID_HEADER]:
            this.correlationIdService.getCorrelationId()?.value,
        },
      },
    ];

    return !noRateLimit
      ? await this.httpAdapterService.axiosInstance.post.apply(
          this.httpAdapterService.axiosInstance,
          arrayOfArgs,
        )
      : await this.noRateLimitedHttpAdapterService.axiosInstance.post.apply(
          this.noRateLimitedHttpAdapterService.axiosInstance,
          arrayOfArgs,
        );
  }

  async authWithRefreshToken(
    refreshToken: string,
    noRateLimit = false,
  ): Promise<VulogAuthResponse> {
    return (
      await this.authReq(
        {
          client_id: VulogAuthService.CLIENT_ID,
          client_secret: VulogAuthService.CLIENT_SECRET,
          securityOptions: 'SSL_OP_NO_SSLv3',
          grant_type: 'refresh_token',
          refresh_token: refreshToken,
        },
        noRateLimit,
      )
    ).data;
  }

  async authWithPassword(
    username: string,
    password: string,
    noRateLimit = false,
  ): Promise<VulogAuthResponse> {
    return (
      await this.authReq(
        {
          client_id: VulogAuthService.CLIENT_ID,
          client_secret: VulogAuthService.CLIENT_SECRET,
          grant_type: 'password',
          username: username,
          password: password,
          securityOptions: 'SSL_OP_NO_SSLv3',
        },
        noRateLimit,
      )
    ).data;
  }

  generateTokenKey(
    tokenKey: string,
    bsgUserId: string,
    defaultUser: { username: string },
  ): string {
    return `${bsgUserId ? bsgUserId : defaultUser.username}:${tokenKey}`;
  }

  private getRandomInt(max: number): number {
    return Math.floor(Math.random() * max);
  }

  getSystemUser(): {
    username: string;
    password: string;
  } {
    return config.vulogConfig.defaultUsers[
      this.getRandomInt(config.vulogConfig.defaultUsers.length)
    ];
  }

  getSystemUsers(): {
    username: string;
    password: string;
  }[] {
    return config.vulogConfig.defaultUsers;
  }

  async getCachedAccessToken(
    vulogAccessTokenKey: string,
    isSystemUser: boolean,
  ): Promise<string> {
    Logger.debug('Keys ready. Looking at the cache...', {
      token: vulogAccessTokenKey,
    });

    // Check for cached access token
    let cachedAccessToken = await this.cacheManager.get<string>(
      vulogAccessTokenKey,
    );

    if (cachedAccessToken) return cachedAccessToken;

    if (!isSystemUser) return null;

    // System users do not refresh token. / Wait for a re-login from a seperated service
    Logger.log(
      'Token not found in cache for System User. Wait and try again in 1000 ms.',
      {
        token: vulogAccessTokenKey,
      },
    );
    await new Promise((r) => setTimeout(r, 1000));
    cachedAccessToken = await this.cacheManager.get<string>(
      vulogAccessTokenKey,
    );
    if (cachedAccessToken) {
      return cachedAccessToken;
    } else {
      Logger.log(
        'Token not found in cache during second attempt for system user. throwing error and abort.',
        {
          token: vulogAccessTokenKey,
        },
      );
      throw new SystemAccessTokenNotFoundError();
    }
  }

  private async login(
    bsgUserId: BsgUserId,
    systemUser: { username: string; password: string },
  ): Promise<VulogAuthResponse> {
    let username: string = systemUser.username;
    let password: string = systemUser.password;

    // BlueSG customer
    if (bsgUserId.value !== '') {
      const vulogUser = await this.findOrCreateUser(bsgUserId);

      username = vulogUser.getUsername();
      password = vulogUser.getPassword();
    }
    // Get new access token and refresh token with login credentials
    try {
      const response = await this.authWithPassword(username, password);
      return response;
    } catch (error) {
      throw new VulogAuthError(error);
    }
  }

  async getAccessToken(
    bsgUserId: BsgUserId = new BsgUserId(''),
  ): Promise<string> {
    const systemUser = this.getSystemUser();

    const vulogAccessTokenKey = this.generateTokenKey(
      VULOG_ACCESS_TOKEN_KEY,
      bsgUserId.value,
      systemUser,
    );
    const vulogRefreshTokenKey = this.generateTokenKey(
      VULOG_REFRESH_TOKEN_KEY,
      bsgUserId.value,
      systemUser,
    );

    const cachedAccessToken = await this.getCachedAccessToken(
      vulogAccessTokenKey,
      !bsgUserId.value, // System user
    );

    if (cachedAccessToken) return cachedAccessToken;

    // code below is never called for system users but still called for normal users

    Logger.log('Token not found in cache. Prepare for logging into Vulog', {
      token: vulogAccessTokenKey,
    });

    const cachedRefreshToken = await this.cacheManager.get<string>(
      vulogRefreshTokenKey,
    );

    let accessInfo: VulogAuthResponse;

    if (cachedRefreshToken) {
      try {
        accessInfo = await this.authWithRefreshToken(cachedRefreshToken);
      } catch (error) {
        // Refresh token invalid, catch error and proceed with getting new tokens
        Logger.log('Refresh token invalid or expired, reobtaining...', error);
      }
    }

    if (!accessInfo) {
      accessInfo = await this.login(bsgUserId, systemUser);
    }

    const { access_token, expires_in, refresh_token, refresh_expires_in } =
      accessInfo;
    await this.cacheManager.set(vulogAccessTokenKey, access_token, {
      ttl: expires_in,
    });
    await this.cacheManager.set(vulogRefreshTokenKey, refresh_token, {
      ttl: refresh_expires_in,
    });
    return access_token;
  }

  private prepareAuthorizationHeader(authToken: string) {
    return {
      headers: {
        Authorization: `Bearer ${authToken}`,
        'x-api-key': config.vulogConfig.apiKey,
      },
    };
  }

  async setupAuthConfig(bsgUserId?: BsgUserId): Promise<AuthConfig> {
    const authToken = await this.getAccessToken(bsgUserId);

    // Scope for customers only, no admin calls
    if (bsgUserId) {
      const systemUserAuthToken = await this.getAccessToken();

      const systemUserAuthConfig: AuthConfig =
        this.prepareAuthorizationHeader(systemUserAuthToken);

      // Toggle whether customer can use his RFID card to interact with the car or not
      await this.rfidUsageService.toggleRfidUsage(
        bsgUserId,
        systemUserAuthConfig,
      );
    }

    return this.prepareAuthorizationHeader(authToken);
  }

  async findOrCreateUser(bsgUserId: BsgUserId): Promise<VulogUser> {
    const vulogUserEntity =
      await this.vulogUserRepository.findVulogUserByBsgUserId(bsgUserId);

    // if the user does not exist we start a new one
    let vulogUser =
      vulogUserEntity?.toVulogUser() || (await this.newUser(bsgUserId));

    const systemAuthConfig = await this.setupAuthConfig();

    // Synchronize the user with Vulog
    if (vulogUser.status === VulogUserStatus.CREATING)
      vulogUser = await this.createVulogUser(vulogUser);
    if (vulogUser.status === VulogUserStatus.WAITING_FOR_PROFILE)
      vulogUser = await this.vulogProfileService.retrieveProfileId(
        vulogUser,
        systemAuthConfig,
      );
    if (vulogUser.status === VulogUserStatus.UPDATING)
      vulogUser = await this.syncUserWithVulog(vulogUser, systemAuthConfig);
    if (!vulogUser.isRegistered)
      await this.vulogProfileService.registerUserToService(
        vulogUser,
        config.vulogConfig.serviceId,
        systemAuthConfig,
      );

    return vulogUser;
  }

  private async createVulogUser(
    createdVulogUser: VulogUser,
  ): Promise<VulogUser> {
    const vulogUserDto = await this.vulogAdapterService.post(
      VulogPaths.registerUser(config.vulogConfig.fleetId),
      VulogUserDto,
      await this.setupAuthConfig(),
      null,
      PostUserVulogRequestDto.from(createdVulogUser),
    );

    return await this.vulogUserService.save(
      vulogUserDto.toVulogUser(
        createdVulogUser.bsgUserId,
        null,
        createdVulogUser.email,
        VulogUserStatus.WAITING_FOR_PROFILE,
        new VulogUserId(vulogUserDto.id),
        createdVulogUser.entityId,
        createdVulogUser.getPassword(),
      ),
    );
  }

  private async syncUserWithVulog(
    vulogUser: VulogUser,
    authConfig: AuthConfig,
  ): Promise<VulogUser> {
    await this.vulogProfileService.updateProfile(vulogUser, authConfig);

    vulogUser.status = VulogUserStatus.SYNCHRONIZED;

    return await this.vulogUserService.save(vulogUser);
  }

  async newUser(bsgUserId: BsgUserId): Promise<VulogUser> {
    Logger.log(
      'User not found in existing Vulog Users. Generating a new Vulog User...',
    );

    // If the user does not exist we create it
    const userInfo =
      await this.userSubscriptionExternalService.internalApi.getUserDetail(
        bsgUserId.value,
      );

    if (!userInfo) {
      throw new UssApiError(`User not found with ${bsgUserId.value}`);
    }

    return VulogUser.generateFrom(userInfo.toUser());
  }
}
