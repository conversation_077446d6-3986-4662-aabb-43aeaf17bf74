import { Injectable } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { TransactionFor } from 'nest-transact';
import { PaginationSortingOrder } from 'src/common/constants/pagination';
import { BsgUserId, VulogUserId } from 'src/common/tiny-types';
import { VulogUserEntity } from '../../model/repositories/entities/vulog-user.entity';
import { VulogUserRepository } from '../../model/repositories/vulog-user.repository';
import { VulogUser } from '../../model/vulog-user';

@Injectable()
export class VulogUserService extends TransactionFor<VulogUserService> {
  constructor(
    private readonly repository: VulogUserRepository,
    moduleRef: ModuleRef,
  ) {
    super(moduleRef);
  }

  async save(vulogUser: VulogUser): Promise<VulogUser> {
    return (
      await this.repository.save([VulogUserEntity.from(vulogUser)])
    )[0].toVulogUser();
  }

  async findOneByBsgUserId(bsgUserId: BsgUserId): Promise<VulogUser> {
    const entity = await this.repository.findOne({
      where: {
        bsgUserId: bsgUserId.value,
      },
    });

    return entity ? entity.toVulogUser() : null;
  }

  async findOneByVulogUserId(vulogUserId: VulogUserId): Promise<VulogUser> {
    const entity = await this.repository.findOne({
      where: {
        vulogUserId: vulogUserId.value,
      },
    });

    return entity ? entity.toVulogUser() : null;
  }

  async findManyHavingRfidRemoved(
    page: number,
    pageSize: number,
    excludedVulogUserIds: string[],
  ): Promise<VulogUser[]> {
    const vulogUsers: VulogUser[] =
      await this.repository.getPaginatedVulogUsersHavingRfidRemoved({
        page,
        pageSize,
        sortKey: 'rfid',
        sortType: PaginationSortingOrder.ASCENDING,
        excludedVulogUserIds,
      });

    return vulogUsers;
  }

  async countManyHavingRfidRemoved(): Promise<number> {
    return this.repository.countVulogUsersHavingRfidRemoved();
  }
}
