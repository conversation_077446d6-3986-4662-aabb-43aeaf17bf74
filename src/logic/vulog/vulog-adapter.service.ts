import { AxiosRequestConfig } from 'axios';
import { VulogApiError } from 'src/common/errors/vulog-api-error';

import { Injectable } from '@nestjs/common';

import { Page } from '../../common/query/page';
import { PaginationQuery } from '../../common/query/pagination-query';
import {
  AuthConfig,
  HttpAdapterService,
  ObjectOrVoid,
} from '../http-adapter.service';
import { Url } from '../url';

@Injectable()
export class VulogAdapterService {
  constructor(private httpAdapter: HttpAdapterService) {}

  async get<T extends object>(
    url: Url,
    expectedDto: new () => T,
    authConfig: AuthConfig,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
  ): Promise<ObjectOrVoid<T>> {
    return this.httpAdapter.get<T>(
      url,
      expectedDto,
      VulogApiError,
      authConfig,
      customConfig,
      data,
    );
  }

  async getOnePage<T extends object>(
    url: Url,
    pagination: PaginationQuery,
    expectedDto: new () => T,
    authConfig: AuthConfig,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
  ): Promise<Page<T[]>> {
    const result = await this.getAsArrayMobilePaging(
      url,
      expectedDto,
      authConfig,
      customConfig,
      data,
      pagination,
    );

    return result as Page<T[]>;
  }

  async getAllPagesAsArray<T extends object>(
    url: Url,
    expectedDto: new () => T,
    authConfig: AuthConfig,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
    pagination?: PaginationQuery,
  ): Promise<T[]> {
    const result = await this.getAsArrayMobilePaging(
      url,
      expectedDto,
      authConfig,
      customConfig,
      data,
      pagination,
    );

    return result as T[];
  }

  private async getAsArrayMobilePaging<T extends object>(
    url: Url,
    expectedDto: new () => T,
    authConfig: AuthConfig,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
    pagination?: PaginationQuery,
  ): Promise<T[] | Page<T[]>> {
    const headersCapture = { headers: { last: null } };
    const accumulatedResult: T[] = [];
    let currentPage = pagination ? pagination.startPage : 0;

    while (
      (!pagination &&
        (!headersCapture.headers.last ||
          headersCapture.headers.last === 'false')) ||
      (pagination && currentPage === pagination.startPage)
    ) {
      const currentPageUrl = url.addQueryParams({
        page: `${currentPage}`,
        sort:
          pagination && pagination.sortBy
            ? `${pagination.sortBy},${pagination.sortOrder}`
            : undefined,
        size: pagination && pagination.size ? `${pagination.size}` : '200',
      });

      const response = await this.getAsArray(
        currentPageUrl,
        expectedDto,
        authConfig,
        customConfig,
        data,
        headersCapture,
      );

      if (!response?.length) {
        break;
      }

      accumulatedResult.push(...response);

      currentPage += 1;
    }

    if (pagination) {
      return new Page(
        accumulatedResult,
        accumulatedResult.length,
        headersCapture.headers.last || headersCapture.headers.last === 'true',
      );
    }

    return accumulatedResult;
  }

  async getAsArray<T extends object>(
    url: Url,
    expectedDto: new () => T,
    authConfig: AuthConfig,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
    resHeadersCapture?: { headers: object },
  ): Promise<T[]> {
    return this.httpAdapter.getAsArray<T>(
      url,
      expectedDto,
      VulogApiError,
      authConfig,
      customConfig,
      data,
      resHeadersCapture,
    );
  }

  async post<T extends object>(
    url: Url,
    expectedDto: new () => T,
    authConfig: AuthConfig,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
  ): Promise<ObjectOrVoid<T>> {
    return this.httpAdapter.post<T>(
      url,
      expectedDto,
      VulogApiError,
      authConfig,
      customConfig,
      data,
    );
  }

  async put<T extends object>(
    url: Url,
    expectedDto: new () => T,
    authConfig: AuthConfig,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
  ): Promise<ObjectOrVoid<T>> {
    return this.httpAdapter.put<T>(
      url,
      expectedDto,
      VulogApiError,
      authConfig,
      customConfig,
      data,
    );
  }

  async delete<T extends object>(
    url: Url,
    expectedDto: new () => T,
    authConfig: AuthConfig,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
  ): Promise<ObjectOrVoid<T>> {
    return this.httpAdapter.delete<T>(
      url,
      expectedDto,
      VulogApiError,
      authConfig,
      customConfig,
      data,
    );
  }
}
