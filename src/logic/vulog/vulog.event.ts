import { CorrelationId } from '@bluesg-2/monitoring/dist/correlation-id/correlation-id.service';
import { isEmpty, isNotEmptyObject } from 'class-validator';

import { VulogEventNotifierType } from 'src/common/constants/vulog-event-type';
import { InvalidVulogEventError } from 'src/common/errors/invalid-vulog-event-error';
import { VulogEventSource } from 'src/model/repositories/entities/vulog-event.entity';
import { VulogEvent } from 'src/model/vulog-event';
import { VulogEventDto } from './dto/vulog.dto';
import { VulogEventNotifierWebhookDtoV1 } from './dto/webhook/aima/vulog-event-notifier-webhook.v1.dto';
import {
  VulogVehicleGatewayEventNotificationDto,
  VulogVehicleGatewayEventType,
} from './dto/webhook/vg/vulog-vehicle-gateway-event-notification.dto';

import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { VulogVehicleStatusV1 } from './dto/webhook/aima/vulog-event-notifier-webhook.v1.dto';

const AIMA_EVENT_TYPES_IDENTICAL_TO_VG = [
  VulogVehicleGatewayEventType.VEHICLE_CONNECTIVITY_ONLINE,
  VulogVehicleGatewayEventType.VEHICLE_CONNECTIVITY_OFFLINE,
  VulogVehicleGatewayEventType.VEHICLE_CONNECTIVITY_INTERRUPTED,
];

export class BaseVulogEvent {
  constructor(
    props?: Omit<
      BaseVulogEvent,
      'isErrorEvent' | 'getErrorMessage' | 'getEventSource' | 'toVulogEvent'
    >,
  ) {
    props && Object.assign(this, props);
  }

  eId?: string;

  eFleetId?: string;

  eVehicleId?: string;

  eBoxId?: string;

  eDate?: string;

  eOrigin?: string;

  eType?: VulogEventNotifierType | VulogVehicleGatewayEventType;

  rawDto?: VulogEventDto; // For quickly save into the DB with full schema

  correlationId?: CorrelationId;

  isErrorEvent(): boolean {
    throw new Error('You need to implement the function');
  }

  getErrorMessage(): string {
    throw new Error('You need to implement the function');
  }

  getEventSource(): VulogEventSource {
    const isSentByVehicleGateway: boolean = Object.values(
      VulogVehicleGatewayEventType,
    )
      .filter((value) => !AIMA_EVENT_TYPES_IDENTICAL_TO_VG.includes(value))
      .includes(this.eType as VulogVehicleGatewayEventType);

    const eventSource: VulogEventSource = isSentByVehicleGateway
      ? VulogEventSource.VEHICLE_GATEWAY
      : VulogEventSource.AiMA;

    return eventSource;
  }

  toVulogEvent(): VulogEvent {
    throw new Error('You need to implement the function');
  }

  static fromDto(
    dto: VulogEventDto,
    correlationId: CorrelationId,
  ): BaseVulogEvent {
    if (isEmpty(dto) || !isNotEmptyObject(dto)) {
      throw new InvalidVulogEventError(
        'Vulog Event Response should be defined',
      );
    }

    if (isEmpty(dto.id)) {
      throw new InvalidVulogEventError('Vulog Event ID should be defined');
    }

    const baseEvent: BaseVulogEvent = new BaseVulogEvent();
    baseEvent.eId = dto.id;
    baseEvent.eBoxId = dto.boxId;
    baseEvent.eDate = dto.date;
    baseEvent.eFleetId = dto.fleetId;
    baseEvent.eOrigin = dto.origin;
    baseEvent.eType = dto.type;
    baseEvent.eVehicleId = dto.vehicleId;
    baseEvent.rawDto = dto;
    baseEvent.correlationId = correlationId;

    const eventSource: VulogEventSource = baseEvent.getEventSource();

    let event: BaseVulogEvent = baseEvent;

    switch (eventSource) {
      case VulogEventSource.AiMA:
        const {
          body,
          isInTrip,
          isInZone,
          position,
          qrCode,
          tripId,
          userId,
          vehicleName,
          vehiclePlate,
          vehicleStatus,
          vehicleVin,
          trigger,
        } = dto as VulogEventNotifierWebhookDtoV1;

        event = new VulogAimaEvent({
          ...baseEvent,
          eBody: body,
          eIsInTrip: isInTrip,
          eIsInZone: isInZone,
          position,
          qrCode,
          eTrigger: trigger,
          eTripId: tripId,
          eUserId: userId,
          eVehicleName: vehicleName,
          eVehiclePlate: vehiclePlate,
          eVehicleStatus: vehicleStatus,
          eVehicleVin: vehicleVin,
        });

        break;

      case VulogEventSource.VEHICLE_GATEWAY:
        const {
          duration,
          errorCode,
          failed,
          insertionDate,
          latitude,
          altitude,
          auxBatteryVoltage,
          boxStatus,
          card1Present,
          card2Present,
          charging,
          ckhOk,
          doorsAndWindowsClosed,
          engineOn,
          errorMessage,
          gpsFix,
          hasAlerts,
          hdop,
          head,
          immobilized,
          keyPresent,
          locked,
          longitude,
          mileage,
          nbSat,
          primaryFuelTank,
          secondaryFuelTank,
          secondaryTractionBattery,
          sessionId,
          speed,
          tractionBattery,
          transactionId,
        } = dto as VulogVehicleGatewayEventNotificationDto;

        event = new VulogVgEvent({
          ...baseEvent,
          eDuration: duration,
          eErrorCode: errorCode,
          eFailed: failed,
          eInsertionDate: insertionDate,
          eLatitude: latitude,
          eAltitude: altitude,
          eAuxBatteryVoltage: auxBatteryVoltage,
          eBoxStatus: boxStatus,
          eCard1Present: card1Present,
          eCard2Present: card2Present,
          eCharging: charging,
          eChkOk: ckhOk,
          eDoorsAndWindowsClosed: doorsAndWindowsClosed,
          eEngineOn: engineOn,
          eErrorMessage: errorMessage,
          eGpsFix: gpsFix,
          eHasAlerts: hasAlerts,
          eHdop: hdop,
          eHead: head,
          eImmobilized: immobilized,
          eKeyPresent: keyPresent,
          eLocked: locked,
          eLongitude: longitude,
          eMileage: mileage,
          eNbSat: nbSat,
          ePrimaryFuelTank: primaryFuelTank,
          eSecondaryFuelTank: secondaryFuelTank,
          eSecondaryTractionBattery: secondaryTractionBattery,
          eSessionId: sessionId,
          eSpeed: speed,
          eTractionBattery: tractionBattery,
          eTransactionId: transactionId,
        });

        break;
    }

    return event;
  }
}

export class VulogAimaEvent extends BaseVulogEvent {
  constructor(
    props: Omit<
      VulogAimaEvent,
      | 'getEventSource'
      | 'toVulogEvent'
      | 'isErrorEvent'
      | 'getErrorMessage'
      | 'toVulogEvent'
    >,
  ) {
    super(props);

    Object.assign(this, props);
  }

  eTrigger?: string;

  eUserId?: string;

  eTripId?: string;

  eVehicleName?: string;

  eVehicleVin?: string;

  eVehiclePlate?: string;

  eBody?: any;

  qrCode?: string; // This field is our own logic (relic thing). Will consider to remove soon

  eIsInTrip?: boolean;

  eIsInZone?: boolean;

  eVehicleStatus?: VulogVehicleStatusV1;

  position?: {
    lat?: number;
    lon?: number;
  };

  isErrorEvent() {
    return !!this?.eBody?.error;
  }

  getErrorMessage() {
    return this?.eBody?.errorMessage || (this?.eBody.error as string);
  }

  toVulogEvent() {
    return new VulogEvent({
      eventId: this.eId,
      source: VulogEventSource.AiMA,
      type: this.eType,
      origin: this.eOrigin,
      trigger: this.eTrigger,
      payload: this.rawDto,
      receivedAt: VulogDate.now(),
      correlationId: this.correlationId,
    });
  }
}

export class VulogVgEvent extends BaseVulogEvent {
  constructor(
    props: Omit<
      VulogVgEvent,
      'getEventSource' | 'isErrorEvent' | 'getErrorMessage' | 'toVulogEvent'
    >,
  ) {
    super(props);

    Object.assign(this, props);
  }

  eHasAlerts?: boolean;

  eInsertionDate: string;

  eFailed: boolean;

  // Present if failed=true. Contains the short description of the error.
  eErrorMessage?: string;

  eErrorCode: string;

  eSessionId?: string;

  eLatitude: number;

  eLongitude?: number;

  eSpeed?: number;

  eAltitude?: number;

  eNbSat?: number;

  eGpsFix?: string;

  eHdop?: number;

  eHead?: number;

  ePrimaryFuelTank?: object;

  eSecondaryFuelTank?: object;

  eTractionBattery?: object;

  eSecondaryTractionBattery?: object;

  eMileage?: number;

  eAuxBatteryVoltage?: number;

  eLocked?: boolean;

  eDoorsAndWindowsClosed?: boolean;

  eEngineOn?: boolean;

  eCard1Present?: boolean;

  eCard2Present?: boolean;

  eKeyPresent?: boolean;

  eChkOk?: boolean;

  eCharging?: boolean;

  eImmobilized?: boolean;

  eBoxStatus?: string;

  // UUID which tags the request. It's used by our support team to easilly extract the context of a request.
  eTransactionId?: string;

  eDuration: number; // Request processing time, not trip duration

  isErrorEvent() {
    return !!this.eFailed;
  }

  getErrorMessage() {
    return this.eErrorMessage;
  }

  toVulogEvent() {
    return new VulogEvent({
      eventId: this.eId,
      source: VulogEventSource.VEHICLE_GATEWAY,
      type: this.eType,
      origin: this.eOrigin,
      trigger: null,
      payload: this.rawDto,
      receivedAt: VulogDate.now(),
      correlationId: this.correlationId,
    });
  }
}
