import { RentalReservationCancelledEvent } from '@bluesg-2/event-library';
import { CorrelationIdService } from '@bluesg-2/monitoring';
import {
  LocalPostCancelReservationEventV1,
  QueuePopCarModel,
} from '@bluesg-2/queue-pop';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { DateTime } from 'luxon';
import { SERVICE_NAME } from 'src/common/constants/environments';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { CarService } from 'src/logic/car/car.service';
import { ReservationService } from 'src/logic/reservation.service';
import { ReservationCacheService } from 'src/logic/reservation/reservation.cache.service';
import { ReservationRepository } from 'src/model/repositories/reservation.repository';
import { Reservation } from 'src/model/reservation.domain';
import { v4 } from 'uuid';
import { EventBusService } from '~shared/event-bus/event-bus.service';
import { LOCAL_EVENT_EMITTER_TOKEN } from '~shared/local-event-emitter/constants';
import { LocalEventEmitterService } from '~shared/local-event-emitter/local-event-emitter.service';
import { NewRelicReservationEventService } from '~shared/newrelic/event/event-type/reservation.event.newrelic.service';
import { NewRelicMetricTrigger } from '~shared/newrelic/metric/enums/newrelic-metric.enum';

@Injectable()
export class CancelReservationService {
  constructor(
    private readonly reservationService: ReservationService,
    @Inject(LOCAL_EVENT_EMITTER_TOKEN)
    private readonly localEventEmitterService: LocalEventEmitterService,
    private carService: CarService,
    private vulogCarService: VulogCarService,
    private readonly reservationEventService: NewRelicReservationEventService,
    private readonly reservationRepository: ReservationRepository,
    private readonly eventBusService: EventBusService,
    private readonly corIdService: CorrelationIdService,

    private readonly reservationCacheService: ReservationCacheService,
  ) {}

  async makeReservationCancelled(
    reservation: Reservation,
    correlationId?: string,
  ): Promise<void> {
    // If it is to allocated again with the same reservation, make allocateAgain false
    if (reservation.allocateAgain) {
      reservation.allocateAgain = false;

      const resultReservation = await this.reservationService.saveOne(
        reservation,
      );

      Logger.log(
        'Canceling the trip on purpose (Such as switching vehicle). No need to update the status of current reservation to `CANCELED`',
        {
          reservation: resultReservation,
        },
      );

      return;
    }

    await this.reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.delete(
      reservation.bsgUserId,
    );

    // If customer cancel the reservation himself or it is expired
    if (
      [ReservationStatus.Cancelled, ReservationStatus.Expired].includes(
        reservation.status,
      )
    ) {
      Logger.debug(
        'Reservation is already cancelled or expired. No further action needed.',
        { reservation },
      );

      return;
    }

    const car = await this.carService.getOneByVulogId(reservation.carId);

    if (!(await this.vulogCarService.isVehicleAvailableWithRetry(car))) {
      return;
    }

    reservation = await this.reservationRepository.updateStatus(
      reservation,
      ReservationStatus.Cancelled, // Update status as Cancelled, this is for CRC actions
      [ReservationStatus.Reserved],
      {
        canceledAt: VulogDate.now(),
      },
    );

    Logger.log('CRC canceled the reservation on behalf of customer', {
      reservation,
    });

    await this.reservationEventService.emitCanceled(
      NewRelicMetricTrigger.Admin,
    );

    this.eventBusService.nonBlockingEmit(
      new RentalReservationCancelledEvent({
        correlationId: this.corIdService.getCorrelationId()?.value || v4(),
        sender: SERVICE_NAME,
        sentAt: DateTime.now().toMillis(),
        payload: {
          id: reservation.getId,
          carId: reservation.carId.value,
          createdAt: reservation.getCreatedAt.toISO(),
          reservedAt: reservation.reservedAt.value,
          cancelledAt: reservation.canceledAt.value,
          status: reservation.status,
          userId: reservation.bsgUserId.value,
        },
      }),
    );

    // Although the vehicle is available to book by fetching API realtime,
    // but the booking status of vehicle is still `CANCELING` on AiMA, so we will wait for 8 seconds to pop
    await new Promise((resolve) => {
      setTimeout(() => {
        // Q-Pop service will consume this event and popping pre-reservation
        this.localEventEmitterService.dispatchEvent(
          new LocalPostCancelReservationEventV1({
            bsgUserId: reservation.bsgUserId.value,
            correlationId,
            carMetadata: {
              id: car.id.value,
              model: reservation.carModel as unknown as QueuePopCarModel,
              stationId: reservation.startStationId.value,
              vulogId: car.vulogId.value,
            },
          }),
        );

        resolve(true);
      }, 8000);
    });
  }
}
