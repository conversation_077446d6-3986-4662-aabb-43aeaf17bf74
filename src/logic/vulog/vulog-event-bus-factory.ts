import {
  AimaCarEnabledEvent,
  AimaCarPutOutOfServiceEvent,
} from '@bluesg-2/event-library';
import { DateTime } from 'luxon';
import { SERVICE_NAME } from 'src/common/constants/environments';
import { BaseVulogEvent } from './vulog.event';

export class VulogEventBusFactory {
  static buildAimaCarEnabledEvent(
    event: BaseVulogEvent,
    correlationId: string,
  ) {
    return new AimaCarEnabledEvent({
      correlationId: correlationId,
      sender: SERVICE_NAME,
      sentAt: DateTime.now().toMillis(),
      payload: {
        vulogCarId: event.eVehicleId,
        timestamp: event.eDate,
      },
    });
  }

  static buildAimaCarPutOutOfServiceEvent(
    event: BaseVulogEvent,
    correlationId: string,
  ) {
    return new AimaCarPutOutOfServiceEvent({
      correlationId: correlationId,
      sender: SERVICE_NAME,
      sentAt: DateTime.now().toMill<PERSON>(),
      payload: {
        vulogCarId: event.eVehicleId,
        timestamp: event.eDate,
      },
    });
  }
}
