import { Injectable, Logger } from '@nestjs/common';
import * as nr from 'newrelic';
import { VulogAimaEventService } from './vulog-aima-event.service';

import { VulogVgEventService } from './vulog-vg-event.service';
import { BaseVulogEvent, VulogAimaEvent, VulogVgEvent } from './vulog.event';

@Injectable()
export class VulogEventNotifierService {
  constructor(
    private readonly vulogAimaEventService: VulogAimaEventService,
    private readonly vulogVgEventService: VulogVgEventService,
  ) {}

  async handleWebhookCall(event: BaseVulogEvent) {
    nr.incrementMetric('CRS/vulog_event_received');

    Logger.log(
      `[Vulog Event Handler] Receiving event from webhook ${
        event.eType
      }, event source: ${event.getEventSource()}`,
      { event, eventType: event.eType },
    );

    if (event instanceof VulogAimaEvent) {
      await this.vulogAimaEventService.processEvent(event);
    } else if (event instanceof VulogVgEvent) {
      await this.vulogVgEventService.processEvent(event);
    } else {
      Logger.warn(
        'Unknown type of Vulog Event, it should be AiMA event or VG event',
      );
    }
  }
}
