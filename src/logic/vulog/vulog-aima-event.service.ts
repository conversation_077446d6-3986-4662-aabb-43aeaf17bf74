import { EventProps } from '@bluesg-2/sqs-event';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { MISSING_VULOG_EVENT_HANDLER } from 'src/common/constants/messages';
import { VulogEventNotifierType } from 'src/common/constants/vulog-event-type';
import { VulogEventSource } from 'src/model/repositories/entities/vulog-event.entity';
import { LOCAL_EVENT_EMITTER_TOKEN } from '~shared/local-event-emitter/constants';
import { LocalEventEmitterService } from '~shared/local-event-emitter/local-event-emitter.service';
import { VulogEventService } from '../vulog-event.service';
import { BaseVulogEventService } from './base-vulog-event.service';
import { LocalAiMAEventFactory } from './local-aima-event.factory';
import { VulogAimaEvent } from './vulog.event';

@Injectable()
export class VulogAimaEventService extends BaseVulogEventService {
  constructor(
    @Inject(LOCAL_EVENT_EMITTER_TOKEN)
    protected readonly localEventEmitterService: LocalEventEmitterService,
    protected readonly vulogEventService: VulogEventService,
  ) {
    super(localEventEmitterService, vulogEventService);
  }

  protected routeEvent(event: VulogAimaEvent): void {
    const localAiMAEventFactory: LocalAiMAEventFactory =
      new LocalAiMAEventFactory(event, event.correlationId);

    let localEvent: EventProps<any>;

    switch (event.eType) {
      case VulogEventNotifierType.EndTripReport:
        localEvent = localAiMAEventFactory.createLocalEndTripReportEvent();
        break;

      case VulogEventNotifierType.CancelTripReport:
        localEvent = localAiMAEventFactory.createLocalCancelTripReportEvent();
        break;

      case VulogEventNotifierType.ChargingCablePlugged:
        localEvent = localAiMAEventFactory.createLocalCablePluggedEvent();
        break;

      // adding quick fix as the ChargingActive as the exact same body/payload as a ChargingCablePlugged
      case VulogEventNotifierType.ChargingActive:
        localEvent = localAiMAEventFactory.createLocalCableActiveEvent();
        break;

      case VulogEventNotifierType.ChargingCableUnplugged:
        localEvent = localAiMAEventFactory.createLocalCableUnpluggedEvent();
        break;

      case VulogEventNotifierType.StartTrip:
        localEvent = localAiMAEventFactory.createLocalStartTripEvent();
        break;

      case VulogEventNotifierType.TripStart:
        localEvent = localAiMAEventFactory.createLocalTripStartEvent();
        break;

      case VulogEventNotifierType.EndTrip:
        localEvent = localAiMAEventFactory.createLocalEndTripEvent();
        break;

      case VulogEventNotifierType.TripEnd:
        localEvent = localAiMAEventFactory.createLocalTripEndRfidEvent();
        break;

      default:
        Logger.debug(MISSING_VULOG_EVENT_HANDLER, {
          details: event,
          eventSource: VulogEventSource.AiMA,
        });
    }

    localEvent && this.localEventEmitterService.dispatchEvent(localEvent);
  }

  async processEvent(event: VulogAimaEvent) {
    await this.vulogEventService.createVulogEvent(event);

    this.routeEvent(event);
  }
}
