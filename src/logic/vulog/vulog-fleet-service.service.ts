import { Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { config } from 'src/config';
import { VulogFleetServiceDto } from 'src/model/dtos/vulog-fleet-service/vulog-fleet-service.dto';
import { FleetService } from 'src/model/fleet-service';
import { CacheService } from '../cache/cache.service';
import { VulogClientService } from './vulog-client.service';
import { VulogPaths } from './vulog-paths';

const customerUsableFleetServiceID = config.vulogConfig.serviceId;

@Injectable()
export class VulogFleetService {
  constructor(
    private readonly cacheService: CacheService,
    private readonly vulogClientService: VulogClientService,
  ) {}

  async getFleetServices(useCache = true): Promise<FleetService[]> {
    const cacheConfig = config.redisConfig.fleetServices;

    let fleetServiceDtos: VulogFleetServiceDto[];
    let fleetServices: FleetService[] = [];

    const cacheContent: VulogFleetServiceDto[] = useCache
      ? await this.cacheService.get(cacheConfig.cacheKey)
      : null;

    if (cacheContent && cacheContent?.length) {
      fleetServices = plainToInstance(VulogFleetServiceDto, cacheContent).map(
        (flSvcDto) => flSvcDto.toFleetService(),
      );
    } else {
      fleetServiceDtos = await this.vulogClientService.getAsArray(
        VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId),
        VulogFleetServiceDto,
      );

      fleetServices = fleetServiceDtos?.length
        ? fleetServiceDtos.map((flSvcDto) => flSvcDto.toFleetService())
        : [];

      await this.cacheService.set(cacheConfig.cacheKey, fleetServiceDtos, {
        ttl: cacheConfig.ttl,
      });
    }

    return fleetServices;
  }

  async getCustomerUsableFleetService(): Promise<FleetService> {
    const fleetServices = await this.getFleetServices(true);

    return fleetServices.find((flSvc) =>
      flSvc.id.equals(customerUsableFleetServiceID),
    );
  }
}
