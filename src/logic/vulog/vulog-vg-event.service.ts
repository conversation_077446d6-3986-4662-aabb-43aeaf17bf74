import { EventProps } from '@bluesg-2/sqs-event';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { MISSING_VULOG_EVENT_HANDLER } from 'src/common/constants/messages';
import { VulogEventSource } from 'src/model/repositories/entities/vulog-event.entity';
import { LOCAL_EVENT_EMITTER_TOKEN } from '~shared/local-event-emitter/constants';
import { LocalEventEmitterService } from '~shared/local-event-emitter/local-event-emitter.service';
import { VulogEventService } from '../vulog-event.service';
import { BaseVulogEventService } from './base-vulog-event.service';
import { VulogVehicleGatewayEventType } from './dto/webhook/vg/vulog-vehicle-gateway-event-notification.dto';
import { LocalVgEventFactory } from './local-vg-event.factory';
import { VulogVgEvent } from './vulog.event';

@Injectable()
export class VulogVgEventService extends BaseVulogEventService {
  constructor(
    @Inject(LOCAL_EVENT_EMITTER_TOKEN)
    protected readonly localEventEmitterService: LocalEventEmitterService,
    protected readonly vulogEventService: VulogEventService,
  ) {
    super(localEventEmitterService, vulogEventService);
  }

  protected routeEvent(event: VulogVgEvent): void {
    const localVgEventFactory: LocalVgEventFactory = new LocalVgEventFactory(
      event,
      event.correlationId,
    );

    let localEvent: EventProps<any>;

    switch (event.eType) {
      case VulogVehicleGatewayEventType.VEHICLE_SESSION_START:
      case VulogVehicleGatewayEventType.VEHICLE_START_TRIP:
        localEvent = localVgEventFactory.createLocalVehicleSessionStartByAdminEvent();
        break;
      case VulogVehicleGatewayEventType.VEHICLE_SESSION_END:
      case VulogVehicleGatewayEventType.VEHICLE_END_TRIP:
        localEvent = localVgEventFactory.createLocalVehicleSessionEndEvent();
        break;

      case VulogVehicleGatewayEventType.VEHICLE_SESSION_CANCEL:
      case VulogVehicleGatewayEventType.VEHICLE_CANCEL_TRIP:
        localEvent = localVgEventFactory.createLocalVehicleSessionCancelEvent();
        break;

      case VulogVehicleGatewayEventType.VEHICLE_CHARGING_PLUGGED:
      case VulogVehicleGatewayEventType.VEHICLE_CHARGING_ACTIVE:
        localEvent = localVgEventFactory.createLocalChargingCablePluggedEvent();
        break;

      case VulogVehicleGatewayEventType.VEHICLE_CHARGING_UNPLUGGED:
        localEvent =
          localVgEventFactory.createLocalChargingCableUnpluggedEvent();
        break;

      default:
        Logger.debug(MISSING_VULOG_EVENT_HANDLER, {
          details: event,
          eventSource: VulogEventSource.VEHICLE_GATEWAY,
        });
    }

    localEvent && this.localEventEmitterService.dispatchEvent(localEvent);
  }

  async processEvent(event: VulogVgEvent) {
    await this.vulogEventService.createVulogEvent(event);

    this.routeEvent(event);
  }
}
