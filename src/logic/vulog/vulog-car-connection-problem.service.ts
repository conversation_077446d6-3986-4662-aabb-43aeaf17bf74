import { Injectable, Logger } from '@nestjs/common';
import * as asyncRetry from 'async-retry';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { CarConnectionProblemError } from 'src/common/errors/car-connection-problem-error';
import { RentalTechnicalError } from 'src/common/errors/rental-technical-error';
import { VulogApiError } from 'src/common/errors/vulog-api-error';
import { VulogCarId, VulogJourneyOrTripId } from 'src/common/tiny-types';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { config } from 'src/config';
import { ReservationRepository } from 'src/model/repositories/reservation.repository';
import { Reservation } from 'src/model/reservation.domain';
import { ConfigurationService } from '~shared/configuration/configuration.service';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { CarInfo } from '~shared/feature-flag/models/car-info';
import { NewRelicCarConnectionProblemEventService } from '~shared/newrelic/event/event-type/car-connection-problem.event.newrelic.service';
import { NewRelicMetricStepInFlow } from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { CarService } from '../car/car.service';
import { Car } from '../car/domain/car';
import { VulogTelemetryService } from '../telemetry-repository/vulog/vulog-telemetry.service';
import { VulogClientService } from './vulog-client.service';
import { VulogPaths } from './vulog-paths';

export interface RetryWhenEncounterCarConnectionProblemFuncProps<R> {
  vulogCarId: VulogCarId;
  vulogJourneyId?: VulogJourneyOrTripId;
  callback: () => Promise<R>;
  shouldCancelReservationAfterBestEffort?: boolean;
  step: NewRelicMetricStepInFlow;
}

@Injectable()
export class VulogCarConnectionProblemService {
  constructor(
    private readonly vulogTelemetryService: VulogTelemetryService,
    private readonly reservationRepository: ReservationRepository,
    private readonly vgClientService: VulogClientService,
    private readonly featureFlagService: FeatureFlagService,
    private readonly carService: CarService,
    private readonly carConnectionProblemEventService: NewRelicCarConnectionProblemEventService,
    private readonly configurationService: ConfigurationService,
  ) {}

  private async executeTaskInBestEffort<R>(
    callback: () => Promise<R>,
    vulogCarId: VulogCarId,
    vulogJourneyId: VulogJourneyOrTripId,
    bsgReservation: Reservation,
    step: NewRelicMetricStepInFlow,
  ) {
    const maximumRetryForPing: number =
      await this.configurationService.carConnectionProblemMaximumRetryAttemptsForPing();

    Logger.log(
      `VulogCarConnectionProblemService - Maximum retries for ping is ${maximumRetryForPing}`,
    );

    try {
      return await callback();
    } catch (err) {
      if (err instanceof VulogApiError) {
        const vulogApiError = err as VulogApiError;

        if (
          [
            vulogApiError.data?.code, // Vulog AiMA, HTTP status code: 403
            vulogApiError.data?.codeStr, // Vulog Vehicle Gateway, HTTP status code: 410
            vulogApiError.data, // Vulog AiMA, HTTP status code: 410
          ].includes('carConnectionProblem')
        ) {
          Logger.error(
            'VulogCarConnectionProblemService, VulogApiError - carConnectionProblem: ',
            {
              err,
              stack: err?.stack,
            },
          );

          let vgCarId: VulogCarId = vulogCarId;

          if (!vulogCarId) {
            if (!vulogJourneyId) {
              throw new Error(
                'Insufficient information to retry when encountering car connection problem',
              );
            }

            bsgReservation = await this.reservationRepository.findOneByTripId(
              vulogJourneyId,
            );

            if (!bsgReservation) {
              throw new Error(
                'Insufficient information to retry when encountering car connection problem',
              );
            }

            vgCarId = bsgReservation.carId;
          }

          const car: Car = await this.carService.getOneByVulogId(vgCarId);

          await this.carConnectionProblemEventService.emitCarConnectionProblem(
            car.getMetricNewRelicCarModel(),
            step,
            +vulogApiError.statusCode,
          );

          const wakeUpResp = await this.vulogTelemetryService.wakeUpCar(
            vgCarId,
          );

          let pingResp: {
            isOnline: boolean;
            message?: string;
          };

          // "Wake up" command is sent to Vubox but not yet acknowledged
          if (!wakeUpResp.isAwake) {
            try {
              pingResp = await asyncRetry<{
                isOnline: boolean;
                message?: string;
              }>(
                async () => {
                  const tempPingResp = await this.vulogTelemetryService.pingCar(
                    vgCarId,
                  );

                  if (!tempPingResp.isOnline) {
                    Logger.warn(
                      'The vehicle is not connected (Vubox is in standby mode), need to be awaken',
                      {
                        vulogCarId: vgCarId,
                        vulogErrorMessage: tempPingResp?.message,
                      },
                    );

                    throw new Error('Retries until zero reached');
                  }

                  return tempPingResp;
                },
                {
                  factor: 2,
                  retries: maximumRetryForPing, // After the first failed one, will retry
                },
              );
            } catch (err) {
              Logger.error(
                'Failed to ping vehicle after trying to wake it up',
                {
                  vulogCarId: vgCarId,
                  err,
                },
              );

              throw new CarConnectionProblemError(
                'Failed to ping vehicle after trying to wake it up',
              );
            }

            // This is our last best effort to do failed task
            if (pingResp.isOnline) {
              return await callback();
            }
          } else {
            return await callback();
          }
        } else {
          Logger.error(
            'VulogCarConnectionProblemService, VulogApiError - Not carConnectionProblem: ',
            {
              err,
              stack: err?.stack,
            },
          );

          throw vulogApiError;
        }
      } else {
        Logger.error('VulogCarConnectionProblemService, not VulogApiError: ', {
          err,
          stack: err?.stack,
        });

        throw err;
      }
    }
  }

  async retryWhenEncounterCarConnectionProblem<R = unknown>({
    vulogCarId,
    vulogJourneyId,
    callback,
    shouldCancelReservationAfterBestEffort,
    step,
  }: RetryWhenEncounterCarConnectionProblemFuncProps<R>): Promise<R> {
    let bsgReservation: Reservation;

    try {
      return await this.executeTaskInBestEffort<R>(
        callback,
        vulogCarId,
        vulogJourneyId,
        bsgReservation,
        step,
      );
    } catch (err) {
      let car: Car;

      if (vulogCarId) {
        car = await this.carService.getOneByVulogId(vulogCarId);
      } else if (vulogJourneyId) {
        bsgReservation = await this.reservationRepository.findOneByTripId(
          vulogJourneyId,
        );

        if (bsgReservation) {
          car = await this.carService.getOneByVulogId(bsgReservation.carId);
        }
      }

      // Depends on A/B flag
      if (
        car &&
        (await this.featureFlagService.shouldCancelReservationAfterBestEffort(
          CarInfo.fromCar(car),
        ))
      ) {
        if (shouldCancelReservationAfterBestEffort && bsgReservation) {
          Logger.warn(
            `Trip termination for step "${step}" when encountering car connection problem`,
            { bsgReservation },
          );

          // Need to make this reservation cancelled first to avoid it being handled by CancelTripReport from Vulog AiMA
          bsgReservation = await this.reservationRepository.updateStatus(
            bsgReservation,
            ReservationStatus.Cancelled,
            [ReservationStatus.Converted, ReservationStatus.Reserved],
            {
              canceledAt: VulogDate.now(),
              deletedBy:
                'The rental was canceled by CRS due to technical problems with Vulog',
            },
          );

          try {
            await this.retryWhenEncounterCarConnectionProblem<void>({
              vulogCarId: car.vulogId,
              callback: async () => {
                await this.vgClientService.post(
                  VulogPaths.backOfficeExpert(
                    config.vulogConfig.fleetId,
                    car.vulogId,
                  ),
                  null,
                  null,
                  {
                    cmd: 'Trip Termination',
                  },
                );
              },
              step: NewRelicMetricStepInFlow.TerminateTrip,
            });
          } catch (err) {
            Logger.error(
              'Cannot terminate the reservation/rental after best effort, details: ',
              {
                err,
              },
            );

            Logger.error(
              'Cannot terminate the reservation/rental after best effort, error stack: ',
              {
                errorStack: err.stack,
              },
            );
          }

          Logger.error(
            'The rental was canceled due to technical problems with Vulog',
            {
              reservation: bsgReservation,
            },
          );

          throw new RentalTechnicalError(
            'Due to technical issues, your rental has been cancelled. Please note that your rental is not eligible for billing. Sorry for the inconvenience, you should try to book car again.',
          );
        }
      }

      throw err;
    }
  }
}
