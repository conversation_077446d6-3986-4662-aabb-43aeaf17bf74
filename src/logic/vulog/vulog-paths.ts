import { VulogApiError } from 'src/common/errors/vulog-api-error';
import {
  VulogCarId,
  VulogFleetId,
  VulogJourneyOrTripId,
  VulogProfileId,
  VulogServiceId,
  VulogUserId,
  VulogZoneId,
} from 'src/common/tiny-types';
import { config } from 'src/config';

import { Url } from '../url';

export class VulogPaths {
  static urlPrefix(): Url {
    return new Url(config.vulogConfig.apiUrl);
  }

  static mobileApiPrefix(): Url {
    return new Url('apiv5');
  }

  static backOfficeApiPrefix(): Url {
    return new Url('boapi/proxy');
  }

  static vvgApiVersionPrefix(): Url {
    return new Url('v2');
  }

  static vvgUrlPrefix(): Url {
    return new Url(config.vulogConfig.secondApiUrl);
  }

  static postfixPathOnly(fullUrl: string): Url {
    const result = fullUrl
      .replace(`${this.urlPrefix()}`, '')
      .replace(`${this.vvgUrlPrefix()}`, '');

    if (result === fullUrl) {
      throw new VulogApiError('Invalid URL, cannot be parsed');
    }

    return new Url(result);
  }

  static auth(): Url {
    return new Url(
      `${this.urlPrefix()}/auth/realms/${
        config.vulogConfig.fleetId.value
      }/protocol/openid-connect/token`,
    );
  }

  static availableVehicles(cityId: string): Url {
    return new Url(`${this.urlPrefix()}/availableVehicles/${cityId}`);
  }

  // APIs from Vulog Vehicle Gateway API (VVG)
  static vvgVehicles(fleetId: VulogFleetId, vehicleId?: VulogCarId): Url {
    return new Url(
      `${this.vvgUrlPrefix()}/${this.vvgApiVersionPrefix()}/fleets/${
        fleetId.value
      }/vehicles${vehicleId ? `/${vehicleId.value}` : ''}`,
    );
  }

  static vvgWakeUpVehicle(fleetId: VulogFleetId, vehicleId: VulogCarId): Url {
    return new Url(this.vvgVehicles(fleetId, vehicleId) + '/wakeup');
  }

  static vvgPingVehicle(fleetId: VulogFleetId, vehicleId: VulogCarId): Url {
    return new Url(this.vvgVehicles(fleetId, vehicleId) + '/ping');
  }

  static vvgLockVehicle(fleetId: VulogFleetId, vehicleId: VulogCarId): Url {
    return new Url(this.vvgVehicles(fleetId, vehicleId) + '/lock');
  }

  static vvgUnlockVehicle(fleetId: VulogFleetId, vehicleId: VulogCarId): Url {
    return new Url(this.vvgVehicles(fleetId, vehicleId) + '/unlock');
  }

  static vvgImmobilizeVehicle(
    fleetId: VulogFleetId,
    vehicleId: VulogCarId,
  ): Url {
    return new Url(this.vvgVehicles(fleetId, vehicleId) + '/immobilize');
  }

  static backOfficeExpert(
    fleetId: VulogFleetId,
    vulogVehicleId: VulogCarId,
  ): Url {
    return new Url(
      `${this.backOfficeVehicles(fleetId, vulogVehicleId)}/expert`,
    );
  }

  static vvgMobilizeVehicle(fleetId: VulogFleetId, vehicleId: VulogCarId): Url {
    return new Url(this.vvgVehicles(fleetId, vehicleId) + '/mobilize');
  }

  static vvgStandbyVehicle(fleetId: VulogFleetId, vehicleId: VulogCarId): Url {
    return new Url(this.vvgVehicles(fleetId, vehicleId) + '/standby');
  }

  static vvgLockAndImmobilizeVehicle(
    fleetId: VulogFleetId,
    vehicleId: VulogCarId,
  ): Url {
    return new Url(this.vvgVehicles(fleetId, vehicleId) + '/lockImmobilize');
  }

  static vvgUnlockAndMobilizeVehicle(
    fleetId: VulogFleetId,
    vehicleId: VulogCarId,
  ): Url {
    return new Url(this.vvgVehicles(fleetId, vehicleId) + '/unlockMobilize');
  }

  static bookJourney(vehicleId: VulogCarId): Url {
    return new Url(
      `${this.urlPrefix()}/${this.mobileApiPrefix()}/vehicles/${
        vehicleId.value
      }/journey`,
    );
  }

  static registerUser(fleetId: VulogFleetId): Url {
    return new Url(
      `${this.urlPrefix()}/${this.backOfficeApiPrefix()}/user/fleets/${
        fleetId.value
      }/userDefault`,
    );
  }

  static userServicesAndProfiles(
    fleetId: VulogFleetId,
    vulogUserId: VulogUserId,
  ): Url {
    return new Url(
      `${this.urlPrefix()}/${this.backOfficeApiPrefix()}/user/fleets/${
        fleetId.value
      }/users/${vulogUserId.value}/services`,
    );
  }

  static userProfile(fleetId: VulogFleetId, profileId: VulogProfileId): Url {
    return new Url(
      `${this.urlPrefix()}/${this.backOfficeApiPrefix()}/user/fleets/${
        fleetId.value
      }/profiles/${profileId.value}`,
    );
  }

  static registerUserProfileForService(
    fleetId: VulogFleetId,
    vulogUserProfileId: VulogProfileId,
  ): Url {
    return this.userProfile(fleetId, vulogUserProfileId).add(
      `/serviceRegistrations`,
    );
  }

  static currentJourneys(): Url {
    return new Url(`${this.urlPrefix()}/apiv5/user/currentJourneys`);
  }

  static cancelBooking(vulogJourneyId: VulogJourneyOrTripId): Url {
    return new Url(
      `${this.urlPrefix()}/${this.mobileApiPrefix()}/journeys/${
        vulogJourneyId.value
      }/booking`,
    );
  }

  static backOfficeVehicles(
    fleetId: VulogFleetId,
    vulogVehicleId?: VulogCarId,
  ): Url {
    let url = new Url(
      `${this.urlPrefix()}/${this.backOfficeApiPrefix()}/fleetmanager/public/fleets/${
        fleetId.value
      }/vehicles`,
    );

    if (vulogVehicleId) {
      url = new Url(`${url.value}/${vulogVehicleId.value}`);
    }

    return url;
  }

  static backOfficeListingAllStaticVehicles(fleetId: VulogFleetId): Url {
    return new Url(
      `${this.urlPrefix()}/${this.backOfficeApiPrefix()}/vehicle/fleets/${
        fleetId.value
      }/vehicles`,
    );
  }

  static vvgVehicleStatus(
    fleetId: VulogFleetId,
    vulogVehicleId: VulogCarId,
  ): Url {
    let url = this.vvgVehicles(fleetId, vulogVehicleId);

    url = url.add('/status');

    return url;
  }

  static vvgVehicle(fleetId: VulogFleetId, vulogVehicleId: VulogCarId): Url {
    return this.vvgVehicles(fleetId, vulogVehicleId);
  }

  // Undocumented API. Not found in Vulog documentation April 11, 2023
  static vehicleBookingBackOffice(
    fleetId: VulogFleetId,
    vulogVehicleId: VulogCarId,
  ): Url {
    return new Url(`${this.backOfficeVehicles(fleetId, vulogVehicleId)}/book`);
  }

  static backOfficeListAllVehicles(fleetId: VulogFleetId): Url {
    return new Url(
      `${this.urlPrefix()}/${this.backOfficeApiPrefix()}/user/vehicle/fleets/${
        fleetId.value
      }/vehicles`,
    );
  }

  static finishedTrips(vulogFleetId: VulogFleetId) {
    return new Url(
      `${this.urlPrefix()}/${this.backOfficeApiPrefix()}/trip/fleets/${
        vulogFleetId.value
      }/trips`,
    );
  }

  static specificFinishedTrip(
    vulogFleetId: VulogFleetId,
    tripId: VulogJourneyOrTripId,
  ) {
    return new Url(`${this.finishedTrips(vulogFleetId)}/${tripId.value}`);
  }

  static startOrEndTrip(vulogJourneyId: VulogJourneyOrTripId): Url {
    return new Url(
      `${this.urlPrefix()}/${this.mobileApiPrefix()}/journeys/${
        vulogJourneyId.value
      }/trip`,
    );
  }

  static getDetailRentalByRentalId(
    fleetId: VulogFleetId,
    rentalId: VulogJourneyOrTripId,
  ): Url {
    return new Url(
      `${this.urlPrefix()}/${this.backOfficeApiPrefix()}/trip/fleets/${
        fleetId.value
      }/trips/${rentalId.value}`,
    );
  }

  static pauseJourney(rentalId: VulogJourneyOrTripId): Url {
    return new Url(
      `${this.urlPrefix()}/${this.mobileApiPrefix()}/journeys/${
        rentalId.value
      }/pause`,
    );
  }

  static backOfficeFleetServices(fleetId: VulogFleetId): Url {
    return new Url(
      `${this.urlPrefix()}/${this.backOfficeApiPrefix()}/user/fleets/${
        fleetId.value
      }/services`,
    );
  }

  static backOfficeUnassignVehicleFromService(
    fleetId: VulogFleetId,
    serviceId: VulogServiceId,
    vehicleId: VulogCarId,
  ): Url {
    return new Url(
      `${this.backOfficeFleetServices(fleetId)}/${serviceId.value}/vehicles/${
        vehicleId.value
      }`,
    );
  }

  static backOfficeDisableVehicle(
    fleetId: VulogFleetId,
    vehicleId: VulogCarId,
  ): Url {
    return new Url(
      `${this.urlPrefix()}/${this.backOfficeApiPrefix()}/fleetmanager/public/fleets/${
        fleetId.value
      }/vehicles/${vehicleId.value}/disable`,
    );
  }

  static backOfficeZones(fleetId: VulogFleetId): Url {
    return new Url(
      `${this.urlPrefix()}/${this.backOfficeApiPrefix()}/geoloc/fleets/${
        fleetId.value
      }/zones`,
    );
  }

  static backOfficeStickStationWithCar(
    fleetId: VulogFleetId,
    vehicleId: VulogCarId,
    zoneId: VulogZoneId,
  ): Url {
    return new Url(
      `${this.backOfficeVehicles(fleetId, vehicleId)}/zones/${zoneId.value}`,
    );
  }

  static backOfficeCarEvents(
    fleetId: VulogFleetId,
    vehicleId: VulogCarId,
  ): Url {
    return new Url(
      `${this.urlPrefix()}/${this.backOfficeApiPrefix()}/user/fleets/${
        fleetId.value
      }/events/vehicles/${vehicleId.value}`,
    );
  }
}
