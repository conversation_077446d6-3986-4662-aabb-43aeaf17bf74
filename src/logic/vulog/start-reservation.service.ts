import { RentalStartedEvent } from '@bluesg-2/event-library';
import { CorrelationIdService } from '@bluesg-2/monitoring';
import {
  LocalPostStartRentalEventV1,
  QueuePopCarModel,
} from '@bluesg-2/queue-pop';
import { Inject, Injectable, Logger } from '@nestjs/common';
import * as asyncRetry from 'async-retry';
import { VulogEventNotifierType } from 'src/common/constants/vulog-event-type';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ReservationError } from 'src/common/errors/reservation-error';
import { VulogCarId, VulogJourneyOrTripId } from 'src/common/tiny-types';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { CarService } from 'src/logic/car/car.service';
import { ReservationService } from 'src/logic/reservation.service';
import { PricingPolicy } from 'src/model/pricing-policy';
import { ReservationRepository } from 'src/model/repositories/reservation.repository';
import { Reservation } from 'src/model/reservation.domain';
import { v4 } from 'uuid';
import { EventBusService } from '~shared/event-bus/event-bus.service';
import { LOCAL_EVENT_EMITTER_TOKEN } from '~shared/local-event-emitter/constants';
import { LocalEventEmitterService } from '~shared/local-event-emitter/local-event-emitter.service';
import { NewRelicRentalEventService } from '~shared/newrelic/event/event-type/rental.event.newrelic.service';
import { NewRelicMetricTrigger } from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { Car } from '../car/domain/car';
import { RentalService } from '../rental.service';
import { VulogEventService } from '../vulog-event.service';

@Injectable()
export class StartReservationService {
  constructor(
    private readonly reservationService: ReservationService,
    @Inject(LOCAL_EVENT_EMITTER_TOKEN)
    private readonly localEventEmitterService: LocalEventEmitterService,
    private readonly rentalService: RentalService,

    private carService: CarService,
    private readonly eventBusService: EventBusService,
    private readonly vulogEventService: VulogEventService,

    private readonly rentalEventService: NewRelicRentalEventService,
    private readonly reservationRepository: ReservationRepository,

    private readonly correlationIdService: CorrelationIdService,
  ) {}

  async startReservationByAdmin(
    tripId: string,
    correlationId: string,
    vehicleId: string,
    date: string,
  ): Promise<void> {
    try {
      const existingReservation = await this.getExistingReservation(tripId);

      const car = await this.getCar(vehicleId);
      const { startedAt, pricingPolicy } =
        await this.convertReservationToRental(existingReservation, date);

      const convertedRental = await this.updateReservationStatus(
        existingReservation,
        startedAt,
        pricingPolicy,
      );

      await this.handleRentalEvent(convertedRental, correlationId, car, tripId);
    } catch (e) {
      this.localEventEmitterService.throwError(e, correlationId);
    }
  }

  private async getExistingReservation(tripId: string): Promise<Reservation> {
    const vulogTripId = new VulogJourneyOrTripId(tripId);
    const existingReservation = await this.reservationService.getOneByVulogId(
      vulogTripId,
      true,
    );
    if (!existingReservation?.isReserved) {
      throw new ReservationError(
        `There is no ongoing BlueSG reservation with Vulog Trip ID ${tripId}`,
      );
    }
    return existingReservation;
  }

  private async getCar(vehicleId: string): Promise<Car> {
    return await this.carService.getOneByVulogId(new VulogCarId(vehicleId));
  }

  private async convertReservationToRental(
    existingReservation: Reservation,
    date: string | null,
  ): Promise<{ startedAt: VulogDate; pricingPolicy: PricingPolicy }> {
    return await this.rentalService.convertReservationToRental(
      existingReservation,
      date ? new VulogDate(date) : null,
    );
  }

  private async updateReservationStatus(
    existingReservation: Reservation,
    startedAt: VulogDate,
    pricingPolicy: PricingPolicy,
  ): Promise<Reservation> {
    return await this.reservationRepository.updateStatus(
      existingReservation,
      ReservationStatus.Converted,
      [ReservationStatus.Reserved],
      {
        startedAt: startedAt,
        pricingPolicy,
      },
    );
  }

  private async handleRentalEvent(
    convertedRental: Reservation,
    correlationId: string,
    car: Car,
    tripId: string,
  ): Promise<void> {
    let latestRentalInfo: Reservation;

    try {
      latestRentalInfo = await this.retryGetLatestRentalInfo(tripId);
    } catch (err) {
      // Handle retry failure if necessary
    }

    if (
      latestRentalInfo.startedAt
        .toDateTime()
        .equals(convertedRental.startedAt.toDateTime())
    ) {
      this.dispatchRentalEvents(convertedRental, correlationId, car);
      await this.checkAndLogTripStartEvent(tripId, convertedRental);
    }
  }

  private async retryGetLatestRentalInfo(tripId: string): Promise<Reservation> {
    return await asyncRetry<Reservation>(
      async () => {
        const vulogTripId = new VulogJourneyOrTripId(tripId);
        return await this.reservationService.getOneByVulogId(vulogTripId);
      },
      {
        factor: 2,
        retries: 2,
      },
    );
  }

  private dispatchRentalEvents(
    convertedRental: Reservation,
    correlationId: string,
    car: Car,
  ): void {
    this.localEventEmitterService.dispatchEvent(
      new LocalPostStartRentalEventV1({
        bsgUserId: convertedRental.bsgUserId.value,
        correlationId: correlationId,
        carMetadata: {
          id: car.id.value,
          model: car.model as unknown as QueuePopCarModel,
          stationId: convertedRental.startStationId.value,
          vulogId: car.vulogId.value,
        },
      }),
    );

    this.eventBusService.nonBlockingEmit(
      new RentalStartedEvent({
        correlationId:
          this.correlationIdService.getCorrelationId()?.value || v4(),
        payload: {
          id: convertedRental.getId,
          carId: convertedRental.carId.value,
          createdAt: convertedRental.getCreatedAt.toISO(),
          reservedAt: convertedRental.reservedAt.value,
          startedAt: convertedRental.startedAt.value,
          startStationId: convertedRental.startStationId.value,
          status: convertedRental.status,
          userId: convertedRental.bsgUserId.value,
          // legacy fields
          stationId: convertedRental.startStationId.value,
          carMetadata: {
            id: car.id.value, // CRS CarId
            model: convertedRental.carModel,
            vulogId: car.vulogId.value,
          },
        },
      }),
    );
  }

  private async checkAndLogTripStartEvent(
    tripId: string,
    convertedRental: Reservation,
  ): Promise<void> {
    const vulogTripId = new VulogJourneyOrTripId(tripId);
    const tripStartEvent = await this.vulogEventService.getOneByConditions({
      tripId: vulogTripId,
      type: VulogEventNotifierType.TripStart,
    });

    if (tripStartEvent) {
      Logger.warn(
        'CRC started a rental on behalf of customer (These 2 events should be persisted into database `TripStart` and `StartTrip`)',
        {
          details: convertedRental,
        },
      );

      await this.rentalEventService.emitStartedAtVulog(
        NewRelicMetricTrigger.Admin,
        convertedRental.getMetricNewRelicRentalType(),
        convertedRental.getMetricNewRelicCarModel(),
      );
    }
  }
}
