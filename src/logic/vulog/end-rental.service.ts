import {
  LocalPostEndRentalEventV1,
  QueuePopCarModel,
} from '@bluesg-2/queue-pop';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { ReservationNotFoundError } from 'src/common/errors/reservation/reservation-not-found.error';
import { VulogJourneyOrTripId, VulogZoneId } from 'src/common/tiny-types';
import { StationExternalService } from 'src/external/station/station.external.service';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { CarService } from 'src/logic/car/car.service';
import { ReservationService } from 'src/logic/reservation.service';
import { VulogTelemetryService } from 'src/logic/telemetry-repository/vulog/vulog-telemetry.service';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { Reservation } from 'src/model/reservation.domain';
import { Station } from 'src/model/station';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { CarInfo } from '~shared/feature-flag/models/car-info';
import { LOCAL_EVENT_EMITTER_TOKEN } from '~shared/local-event-emitter/constants';
import { LocalEventEmitterService } from '~shared/local-event-emitter/local-event-emitter.service';
import { NewRelicRentalEventService } from '~shared/newrelic/event/event-type/rental.event.newrelic.service';
import { NewRelicMetricTrigger } from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { StationService } from '../station/station.service';

@Injectable()
export class EndRentalService {
  private readonly POST_RENTAL_DELAY = 8000;

  constructor(
    private readonly reservationService: ReservationService,
    @Inject(LOCAL_EVENT_EMITTER_TOKEN)
    private readonly localEventEmitterService: LocalEventEmitterService,

    private stationService: StationService,
    private carService: CarService,
    private vulogCarService: VulogCarService,

    private readonly vulogTelemetryService: VulogTelemetryService,

    private readonly featureFlagService: FeatureFlagService,
    private readonly rentalEventService: NewRelicRentalEventService,

    private readonly stationExternalService: StationExternalService,
  ) {}

  async makeRentalEnded(
    sessionId: string,
    endZoneIds: string[],
    tripEndDate: string,
    fabricatedMetadata?: {
      nrTriggerDimension?: NewRelicMetricTrigger;
      qrCode?: string;
    },
    correlationId?: string,
  ): Promise<void> {
    const { nrTriggerDimension, qrCode } = fabricatedMetadata || {};

    const reservation = await this.getReservation(sessionId);

    // Sometimes we receive the event twice
    if (reservation.isRentalEnded()) {
      Logger.warn('Reservation is already ended. No further action needed.', {
        reservation,
        correlationId,
      });

      return;
    }

    const carInfo = await this.vulogTelemetryService.getCarStatusSessionCommand(
      reservation.carId,
    );

    Logger.log('[Rental End] onRentalEnded', {
      reservationId: reservation.getId,
      carInfo,
      correlationId,
    });

    const endStation = await this.getEndStation(
      qrCode,
      endZoneIds,
      carInfo,
      reservation,
    );

    Logger.log('End station for Ending Rental', { endStation, correlationId });

    await this.handleStickyStation(endStation, carInfo);

    await this.reservationService.handleVulogEndTripReportEvent(
      reservation,
      endStation,
      tripEndDate,
      correlationId,
    );

    // Pushing NewRelic metric, not publishing events to Message Queue
    if (nrTriggerDimension === NewRelicMetricTrigger.Mobile) {
      await this.rentalEventService.emitTriggeredWaitingForInvoicing(
        NewRelicMetricTrigger.Mobile,
        reservation.getMetricNewRelicRentalType(),
        reservation.getMetricNewRelicCarModel(),
      );
    }

    // Comment this one as the Q-POP package is removed forever
    // await this.emitLocalPostEndRentalEvent(reservation, correlationId);
  }

  private async getReservation(sessionId: string): Promise<Reservation> {
    const reservation = await this.reservationService.findOneByTripId(
      new VulogJourneyOrTripId(sessionId),
    );
    if (!reservation) {
      throw new ReservationNotFoundError();
    }
    return reservation;
  }

  private async getEndStation(
    qrCode: string | undefined,
    endZoneIds: string[],
    carInfo: any,
    reservation: any,
  ): Promise<Station> {
    if (qrCode) {
      return await this.getEndStationByQRCode(qrCode, reservation);
    } else {
      return await this.getEndStationByZones(endZoneIds, carInfo, reservation);
    }
  }

  private async getEndStationByQRCode(
    qrCode: string,
    reservation: any,
  ): Promise<Station> {
    try {
      const parkingLotResponse =
        await this.stationExternalService.internalApi.getParkingLotByQR(qrCode);
      return parkingLotResponse.toParkingLot().station;
    } catch (error) {
      Logger.error('Failed to end trip with QR', {
        reservationId: reservation.getId,
        qrCode,
        error,
      });
      throw error;
    }
  }

  private async getEndStationByZones(
    endZoneIds: string[],
    carInfo: any,
    reservation: any,
  ): Promise<Station> {
    const endZones = (endZoneIds || []).map(
      (item) => new VulogZone(item, null, null, null),
    );
    let endStation = await this.stationService.getStationByZones(endZones);

    if (!endStation) {
      endStation = await this.stationService.getStationByZones(
        carInfo?.currentZones || [],
      );
      Logger.log('Zones from Vulog AiMA response are empty, VVG response:', {
        onGoingRental: reservation,
        zones: carInfo?.currentZones,
        station: endStation,
      });
    }
    return endStation;
  }

  private async handleStickyStation(
    endStation: any,
    carInfo: any,
  ): Promise<void> {
    if (!endStation || !carInfo) {
      return;
    }

    const shouldStickStationWithCar =
      await this.featureFlagService.stickStationWithCarInRentalFlow(
        CarInfo.fromRealtimeCar(carInfo),
      );

    if (!shouldStickStationWithCar) {
      return;
    }

    Logger.log('Allowed Zone ID that should be sticky with car', {
      carInfo,
      endStation,
    });

    await this.vulogCarService.toggleStickyStationWithCar(
      carInfo.id,
      new VulogZoneId(endStation.zoneId),
      true,
    );

    Logger.log('Car is sticky with station after ending rental in BlueSG', {
      carInfo,
      endStation,
    });
  }

  private async emitLocalPostEndRentalEvent(
    reservation: any,
    correlationId: string | undefined,
  ): Promise<void> {
    const car = await this.carService.getOneByVulogId(reservation.carId);

    await new Promise<void>((resolve) => {
      setTimeout(() => {
        this.localEventEmitterService.dispatchEvent(
          new LocalPostEndRentalEventV1({
            bsgUserId: reservation.bsgUserId.value,
            correlationId,
            carMetadata: {
              id: car.id.value,
              model: reservation.carModel as unknown as QueuePopCarModel,
              startStationId: reservation.startStationId.value,
              endStationId: reservation.endStationId
                ? reservation.endStationId?.value
                : null,
              vulogId: car.vulogId.value,
            },
          }),
        );

        resolve();
      }, this.POST_RENTAL_DELAY);
    });
  }
}
