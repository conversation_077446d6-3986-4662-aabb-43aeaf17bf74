import { CorrelationId } from '@bluesg-2/monitoring/dist/correlation-id/correlation-id.service';
import { LocalVulogCancelTripReportEventV1 } from 'src/event/local/vulog/aima/local.v1.vulog-cancel-trip-report.event';
import { LocalVulogChargingCablePluggedEventV1 } from 'src/event/local/vulog/aima/local.v1.vulog-charging-cable-plugged.event';
import { LocalVulogChargingCableUnpluggedEventV1 } from 'src/event/local/vulog/aima/local.v1.vulog-charging-cable-unplugged.event';
import { LocalVulogEndTripReportEventV1 } from 'src/event/local/vulog/aima/local.v1.vulog-end-trip-report.event';
import { LocalVulogEndTripEventV1 } from 'src/event/local/vulog/aima/local.v1.vulog-end-trip.event';
import { LocalVulogAdminStartTripEventV1 } from 'src/event/local/vulog/aima/local.v1.vulog-start-trip.event';
import { LocalVulogTripEndRfidEventV1 } from 'src/event/local/vulog/aima/local.v1.vulog-trip-end-rfid.event';
import { LocalVulogTripStartRfidEventV1 } from 'src/event/local/vulog/aima/local.v1.vulog-trip-start-rfid.event';
import { NewRelicMetricTrigger } from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { VulogAimaEvent } from './vulog.event';

export class LocalAiMAEventFactory {
  constructor(
    private event: VulogAimaEvent,
    private correlationId: CorrelationId,
  ) {}

  createLocalEndTripReportEvent(): LocalVulogEndTripReportEventV1 | null {
    // Only handle event with `origin` of `BOX`, `FABRICATION`
    if (!['BOX', 'FABRICATION'].includes(this.event.eOrigin)) {
      return null;
    }

    return LocalVulogEndTripReportEventV1.from(
      this.event,
      this.correlationId.value,
      this.event.eOrigin === 'FABRICATION'
        ? NewRelicMetricTrigger.Mobile
        : null,
    );
  }

  createLocalCancelTripReportEvent(): LocalVulogCancelTripReportEventV1 | null {
    // Only handle event with `origin` of `INTERNAL`, `FABRICATION`
    if (!['INTERNAL', 'FABRICATION'].includes(this.event.eOrigin)) {
      return null;
    }

    return LocalVulogCancelTripReportEventV1.from(
      this.event,
      this.correlationId.value,
      this.event.eOrigin === 'FABRICATION'
        ? NewRelicMetricTrigger.Mobile
        : null,
    );
  }

  createLocalCablePluggedEvent(): LocalVulogChargingCablePluggedEventV1 | null {
    return LocalVulogChargingCablePluggedEventV1.from(
      this.event,
      this.correlationId.value,
    );
  }

  // @Eric: // adding quick fix as the ChargingActive as the exact same body/payload as a ChargingCablePlugged
  createLocalCableActiveEvent(): LocalVulogChargingCablePluggedEventV1 | null {
    return LocalVulogChargingCablePluggedEventV1.from(
      this.event,
      this.correlationId.value,
    );
  }

  createLocalCableUnpluggedEvent(): LocalVulogChargingCableUnpluggedEventV1 | null {
    return LocalVulogChargingCableUnpluggedEventV1.from(
      this.event,
      this.correlationId.value,
    );
  }

  createLocalStartTripEvent(): LocalVulogAdminStartTripEventV1 | null {
    return LocalVulogAdminStartTripEventV1.from(
      this.event,
      this.correlationId.value,
    );
  }

  createLocalTripStartEvent(): LocalVulogTripStartRfidEventV1 | null {
    const isBoxOrigin = ['BOX'].includes(this.event.eOrigin);
    const isRealtimeTrigger = ['realtime'].includes(this.event.eTrigger);

    if (!isBoxOrigin || !isRealtimeTrigger) {
      return null;
    }

    return LocalVulogTripStartRfidEventV1.from(
      this.event,
      this.correlationId.value,
    );
  }

  createLocalEndTripEvent(): LocalVulogEndTripEventV1 | null {
    return LocalVulogEndTripEventV1.from(this.event, this.correlationId.value);
  }

  createLocalTripEndRfidEvent(): LocalVulogTripEndRfidEventV1 | null {
    return LocalVulogTripEndRfidEventV1.from(
      this.event,
      this.correlationId.value,
    );
  }
}
