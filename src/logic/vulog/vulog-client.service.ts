import { AxiosRequestConfig } from 'axios';
import { BsgUserId } from 'src/common/tiny-types';

import { Injectable } from '@nestjs/common';

import { Page } from '../../common/query/page';
import { PaginationQuery } from '../../common/query/pagination-query';
import { ObjectOrVoid } from '../http-adapter.service';
import { Url } from '../url';
import { VulogAdapterService } from './vulog-adapter.service';
import { VulogAuthService } from './vulog-auth.service';

@Injectable()
export class VulogClientService {
  constructor(
    private vulogAdapterService: VulogAdapterService,
    private vulogAuthService: VulogAuthService,
  ) {}

  async get<T extends object>(
    url: Url,
    expectedDto: new () => T,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
    bsgUserId?: BsgUserId,
  ): Promise<ObjectOrVoid<T>> {
    return this.vulogAdapterService.get<T>(
      url,
      expectedDto,
      await this.vulogAuthService.setupAuthConfig(bsgUserId),
      customConfig,
      data,
    );
  }

  async getOnePage<T extends object>(
    url: Url,
    pagination: PaginationQuery,
    expectedDto: new () => T,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
    bsgUserId?: BsgUserId,
  ): Promise<Page<T[]>> {
    return this.vulogAdapterService.getOnePage(
      url,
      pagination,
      expectedDto,
      await this.vulogAuthService.setupAuthConfig(bsgUserId),
      customConfig,
      data,
    );
  }

  async getAllPagesAsArray<T extends object>(
    url: Url,
    expectedDto: new () => T,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
    bsgUserId?: BsgUserId,
    pagination?: PaginationQuery,
  ): Promise<T[]> {
    return this.vulogAdapterService.getAllPagesAsArray(
      url,
      expectedDto,
      await this.vulogAuthService.setupAuthConfig(bsgUserId),
      customConfig,
      data,
      pagination,
    );
  }

  async getAsArray<T extends object>(
    url: Url,
    expectedDto: new () => T,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
    bsgUserId?: BsgUserId,
    resHeadersCapture?: { headers: object },
  ): Promise<T[]> {
    return this.vulogAdapterService.getAsArray<T>(
      url,
      expectedDto,
      await this.vulogAuthService.setupAuthConfig(bsgUserId),
      customConfig,
      data,
      resHeadersCapture,
    );
  }

  async post<T extends object>(
    url: Url,
    expectedDto: new () => T,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
    bsgUserId?: BsgUserId,
  ): Promise<ObjectOrVoid<T>> {
    return this.vulogAdapterService.post<T>(
      url,
      expectedDto,
      await this.vulogAuthService.setupAuthConfig(bsgUserId),
      customConfig,
      data,
    );
  }

  async put<T extends object>(
    url: Url,
    expectedDto: new () => T,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
    bsgUserId?: BsgUserId,
  ): Promise<ObjectOrVoid<T>> {
    return this.vulogAdapterService.put<T>(
      url,
      expectedDto,
      await this.vulogAuthService.setupAuthConfig(bsgUserId),
      customConfig,
      data,
    );
  }

  async delete<T extends object>(
    url: Url,
    expectedDto: new () => T,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
    bsgUserId?: BsgUserId,
  ): Promise<ObjectOrVoid<T>> {
    return this.vulogAdapterService.delete<T>(
      url,
      expectedDto,
      await this.vulogAuthService.setupAuthConfig(bsgUserId),
      customConfig,
      data,
    );
  }
}
