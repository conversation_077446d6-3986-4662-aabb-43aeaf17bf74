import { CorrelationId } from '@bluesg-2/monitoring/dist/correlation-id/correlation-id.service';
import { LocalVgChargingCablePluggedEvent } from 'src/event/local/vulog/vg/local.vg-charging-cable-plugged';
import { LocalVgChargingCableUnpluggedEvent } from 'src/event/local/vulog/vg/local.vg-charging-cable-unplugged';
import { LocalVgVehicleSessionCancelEvent } from 'src/event/local/vulog/vg/local.vg-vehicle-session-cancel.event';
import { LocalVgVehicleSessionEndEvent } from 'src/event/local/vulog/vg/local.vg-vehicle-session-end.event';
import { LocalVgVehicleSessionStartByAdminEvent } from 'src/event/local/vulog/vg/local.vg-vehicle-session-start-by-admin.event';
import { NewRelicMetricTrigger } from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { VulogVehicleGatewayOrigin } from './dto/webhook/vg/vulog-vehicle-gateway-event-notification.dto';
import { VulogVgEvent } from './vulog.event';
export class LocalVgEventFactory {
  constructor(
    private event: VulogVgEvent,
    private correlationId: CorrelationId,
  ) {}

  createLocalVehicleSessionEndEvent(): LocalVgVehicleSessionEndEvent {
    return LocalVgVehicleSessionEndEvent.from(
      this.event,
      this.correlationId.value,
    );
  }

  createLocalVehicleSessionCancelEvent(): LocalVgVehicleSessionCancelEvent {
    return LocalVgVehicleSessionCancelEvent.from(
      this.event,
      this.correlationId.value,
      this.event.eOrigin === VulogVehicleGatewayOrigin.FABRICATION
        ? NewRelicMetricTrigger.Mobile
        : null,
    );
  }

  createLocalVehicleSessionStartByAdminEvent(): LocalVgVehicleSessionStartByAdminEvent {
    return LocalVgVehicleSessionStartByAdminEvent.from(
      this.event,
      this.correlationId.value,
      this.event.eOrigin === VulogVehicleGatewayOrigin.FABRICATION
        ? NewRelicMetricTrigger.Mobile
        : null,
    );
  }
  createLocalChargingCablePluggedEvent(): LocalVgChargingCablePluggedEvent {
    return LocalVgChargingCablePluggedEvent.from(
      this.event,
      this.correlationId.value,
    );
  }

  createLocalChargingCableUnpluggedEvent(): LocalVgChargingCableUnpluggedEvent {
    return LocalVgChargingCableUnpluggedEvent.from(
      this.event,
      this.correlationId.value,
    );
  }
}
