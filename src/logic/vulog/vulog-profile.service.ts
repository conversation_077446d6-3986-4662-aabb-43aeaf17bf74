import { Injectable } from '@nestjs/common';
import { VulogProfileId, VulogServiceId } from 'src/common/tiny-types';
import { config } from 'src/config';
import { VulogUserServicesDto } from 'src/model/dtos/vulog-user/vulog-user-services.dto';
import { VulogUser, VulogUserStatus } from 'src/model/vulog-user';
import { AuthConfig } from '../http-adapter.service';
import { VulogAdapterService } from './vulog-adapter.service';
import { PROFILE_TO_SERVICE_APPROVAL_STATUS } from './vulog-auth.service';
import { VulogPaths } from './vulog-paths';
import { VulogUserService } from './vulog-user.service';

@Injectable()
export class VulogProfileService {
  constructor(
    private vulogAdapterService: VulogAdapterService,
    private vulogUserService: VulogUserService,
  ) {}

  async retrieveProfileId(
    vulogUser: VulogUser,
    authConfig: AuthConfig,
  ): Promise<VulogUser> {
    // Retrieve user profileId and serviceId
    const vulogUserServicesAndProfiles = await this.vulogAdapterService.get(
      VulogPaths.userServicesAndProfiles(
        config.vulogConfig.fleetId,
        vulogUser.id,
      ),
      VulogUserServicesDto,
      authConfig,
    );

    vulogUser.profileId = new VulogProfileId(
      vulogUserServicesAndProfiles.getUserProfileId(),
    );
    vulogUser.status = VulogUserStatus.UPDATING;

    return await this.vulogUserService.save(vulogUser);
  }

  async updateProfile(
    vulogUser: VulogUser,
    authConfig: AuthConfig,
  ): Promise<void> {
    await this.vulogAdapterService.post(
      VulogPaths.userProfile(config.vulogConfig.fleetId, vulogUser.profileId),
      null,
      authConfig,
      null,
      {
        email: vulogUser.email,
        rfid: vulogUser.formattedRfid(),
      },
    );
  }

  async registerUserToService(
    vulogUser: VulogUser,
    serviceId: VulogServiceId,
    authConfig: AuthConfig,
  ): Promise<VulogUser> {
    await this.vulogAdapterService.post<null>(
      VulogPaths.registerUserProfileForService(
        config.vulogConfig.fleetId,
        vulogUser.profileId,
      ),
      null,
      authConfig,
      null,
      {
        actions: [
          {
            serviceId,
            status: PROFILE_TO_SERVICE_APPROVAL_STATUS,
          },
        ],
      },
    );

    vulogUser.isRegistered = true;

    return await this.vulogUserService.save(vulogUser);
  }
}
