import { UserInfo } from '@bluesg-2/customer-authentication';
import { Injectable } from '@nestjs/common';
import { BsgUserId } from 'src/common/tiny-types';
import { VulogUser, VulogUserStatus } from 'src/model/vulog-user';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { AuthConfig } from './http-adapter.service';
import { VulogProfileService } from './vulog/vulog-profile.service';
import { VulogUserService } from './vulog/vulog-user.service';

@Injectable()
export class RfidUsageService {
  constructor(
    private readonly featureFlagService: FeatureFlagService,
    private readonly vulogProfileService: VulogProfileService,
    private readonly vulogUserService: VulogUserService,
  ) {}

  private enforceConstraint(vulogUser: VulogUser): boolean {
    // Vulog user must be synchronized to toggle RFID usage
    if (
      ![VulogUserStatus.SYNCHRONIZED, VulogUserStatus.UPDATING].includes(
        vulogUser.status,
      )
    ) {
      return false;
    }

    return true;
  }

  async toggleRfidUsage(
    bsgUserId: BsgUserId,
    authConfig: AuthConfig,
  ): Promise<void> {
    const vulogUser = await this.vulogUserService.findOneByBsgUserId(bsgUserId);

    if (!this.enforceConstraint(vulogUser)) {
      return;
    }

    const userInfo = new UserInfo(vulogUser.bsgUserId.value, null, null);
    const shouldDisableRfidFlag =
      await this.featureFlagService.disableRfidUsageOfCustomer(userInfo);

    let ack: boolean;

    if (shouldDisableRfidFlag && !vulogUser.isRfidDisabled()) {
      ack = vulogUser.disableRfidUsage();
    } else if (!shouldDisableRfidFlag && vulogUser.isRfidDisabled()) {
      ack = vulogUser.enableRfidUsage();
    }

    if (ack) {
      await this.vulogProfileService.updateProfile(vulogUser, authConfig);

      await this.vulogUserService.save(vulogUser);
    }
  }

  async enableRfidUsage(bsgUserId: BsgUserId, authConfig: AuthConfig) {
    const vulogUser = await this.vulogUserService.findOneByBsgUserId(bsgUserId);

    if (!this.enforceConstraint(vulogUser)) {
      return;
    }

    let ack: boolean;

    if (vulogUser.isRfidDisabled()) {
      ack = vulogUser.enableRfidUsage();
    }

    if (ack) {
      await this.vulogProfileService.updateProfile(vulogUser, authConfig);

      await this.vulogUserService.save(vulogUser);
    }
  }
}
