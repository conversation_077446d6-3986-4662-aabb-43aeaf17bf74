import { isDefined } from 'class-validator';
import * as qs from 'qs';

export class Url {
  constructor(public value: string) {}

  public addQueryParams(queryParams: {
    [queryParam: string]: string | number;
  }) {
    if (
      !queryParams ||
      Object.values(queryParams).every((item) => !isDefined(item))
    ) {
      return this;
    }

    let newUrlValue = this.value.includes('?') ? this.value : this.value + '?';

    const isAlreadyHavingQueryParams = !!(
      new URLSearchParams(new URL(this.value).search) as URLSearchParams & {
        size: number;
      }
    ).size;

    newUrlValue = `${newUrlValue}${
      isAlreadyHavingQueryParams ? '&' : ''
    }${qs.stringify(queryParams)}`;

    return new Url(newUrlValue);
  }

  add(path: string): Url {
    return new Url(this.value + path);
  }

  public toString() {
    return this.value;
  }
}
