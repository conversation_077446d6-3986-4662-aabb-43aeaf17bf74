import {
  BaseEntity,
  Column,
  Entity,
  Index,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { CarModelEnum } from '../../../common/enum/car-model.enum';
import { Car } from '../domain/car';

@Entity({ name: 'car' })
export class CarEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  @Index()
  vulogId?: string;

  @Column({ type: 'text', nullable: true })
  model: CarModelEnum;

  @Column({ unique: false })
  @Index()
  plate: string;

  @Column()
  vin: string;

  @Column({ type: 'boolean', nullable: true })
  isPlugged?: boolean;

  @Column({ type: 'boolean', nullable: true })
  isActive?: boolean;

  toCar(): Car {
    return new Car({
      id: this.id,
      vulogId: this.vulogId,
      model: this.model,
      plate: this.plate,
      vin: this.vin,
      realtimeMetadata: {
        isPlugged: this.isPlugged,
      },
      isActive: this.isActive,
    });
  }

  static from(car?: Car): CarEntity {
    if (!car) return null;

    const e = new CarEntity();

    e.id = car.id?.value;
    e.vulogId = car.vulogId.value;
    e.model = car.model;
    e.plate = car.plate.value;
    e.vin = car.vin;
    e.isPlugged = car.realtimeMetadata?.isPlugged;

    e.isActive = car.isActive;

    return e;
  }
}
