import { HttpStatusCode } from 'axios';
import { CAR_NOT_MATCHING_EXPECTED_MODEL_ERROR } from 'src/common/constants/error-codes';
import { InternalError } from 'src/common/errors/internal-error';

export class CarNotMatchingExpectedModelError extends InternalError {
  constructor(cause) {
    super(
      CAR_NOT_MATCHING_EXPECTED_MODEL_ERROR,
      'An error occurred',
      cause,
      HttpStatusCode.BadRequest,
    );
  }
}
