import { HttpStatusCode } from 'axios';
import { CAR_NOT_FOUND_ERROR } from 'src/common/constants/error-codes';
import { RESERVATION_ERROR_MESSAGE } from 'src/common/constants/messages';
import { InternalError } from 'src/common/errors/internal-error';

export class CarNotFoundError extends InternalError {
  constructor(cause) {
    super(
      CAR_NOT_FOUND_ERROR,
      RESERVATION_ERROR_MESSAGE,
      cause,
      HttpStatusCode.BadRequest,
    );
  }
}
