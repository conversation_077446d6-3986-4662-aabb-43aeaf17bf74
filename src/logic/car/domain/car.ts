import { Type } from 'class-transformer';
import { DateTime } from 'luxon';
import { NewRelicMetricCarModel } from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { CarModelEnum } from '../../../common/enum/car-model.enum';
import { CarId, CarPlateNumber, VulogCarId } from '../../../common/tiny-types';

export interface CarRealtimeMetadata {
  isPlugged?: boolean;
  disabled?: boolean;
  batteryPercentage?: number;
  pluggedAt?: DateTime;
}

export interface CarDescription {
  id: string;
  vuboxId?: string;
  vulogId?: string;
  model: CarModelEnum;
  plate: string;
  vin: string;
  realtimeMetadata?: CarRealtimeMetadata;
  isActive?: boolean;
}
export class Car {
  constructor(props?: CarDescription) {
    this.id = props.id ? new CarId(props.id) : null;
    this.model = props.model;
    this.plate = new CarPlateNumber(props.plate);
    this.vin = props.vin;
    this.vulogId = props.vulogId ? new VulogCarId(props.vulogId) : null;
    this.realtimeMetadata = props.realtimeMetadata;
    this.isActive = props.isActive;
  }

  @Type(() => CarId)
  id: CarId;

  @Type(() => VulogCarId)
  vulogId?: VulogCarId;

  model: CarModelEnum;

  @Type(() => CarPlateNumber)
  plate: CarPlateNumber;

  vin: string;

  vuboxId?: string;

  /**
   * Realtime information of car
   */
  realtimeMetadata?: CarRealtimeMetadata;

  isActive?: boolean;

  plugChargingCable(plugged: boolean) {
    this.realtimeMetadata.isPlugged = plugged;
  }

  getMetricNewRelicCarModel(): NewRelicMetricCarModel {
    switch (this.model) {
      case CarModelEnum.BlueCar:
        return NewRelicMetricCarModel.Bluecar;

      case CarModelEnum.OpelCorsaE:
        return NewRelicMetricCarModel.CorsaE;
    }
  }

  public getPropsCopy(): CarDescription {
    return {
      id: this.id.value,
      vulogId: this.vulogId.value,
      model: this.model,
      plate: this.plate.value,
      vin: this.vin,
      vuboxId: this.vuboxId,
      isActive: this.isActive,
      realtimeMetadata: this.realtimeMetadata,
    };
  }
}
