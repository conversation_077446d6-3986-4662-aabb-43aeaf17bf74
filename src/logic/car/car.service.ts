import { Injectable } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { TransactionFor } from 'nest-transact';
import { PageResult } from 'src/common/api/page-result';
import { QueryPagination } from 'src/common/api/pagination';
import { EscapeWildCardsUtil } from 'src/common/utils/escape-wildcards.util';
import { PaginationUtil } from 'src/common/utils/pagination.util';
import { AdminCarQueryPaginationV1 } from 'src/controllers/admin/v1/car/dto/request/admin.car.query.v1.dto';
import { CarRepository } from 'src/model/repositories/car.repository';
import { FindManyOptions, FindOptionsOrderValue, ILike, In } from 'typeorm';
import { CarId, CarPlateNumber, VulogCarId } from '../../common/tiny-types';
import { config } from '../../config';
import { InMemoryCacheService } from '../cache/in-memory.cache.service';
import { Car } from './domain/car';
import { CarEntity } from './entity/car.entity';
import { CarNotFoundError } from './errors/car-not-found.error';
import { PlateNumberNotFoundError } from './errors/plate-number-not-found.error';

export const carServiceDepsExcludedFromRebuilding = [InMemoryCacheService];

@Injectable()
export class CarService extends TransactionFor<CarService> {
  constructor(
    private readonly carRepository: CarRepository,
    private readonly inMemoryCacheService: InMemoryCacheService,

    moduleRef: ModuleRef,
  ) {
    super(moduleRef);
  }

  async plugChargingCable(id: VulogCarId, plugged: boolean): Promise<Car> {
    const existingCar = await this.getOneByVulogId(id);

    existingCar.plugChargingCable(plugged);

    return await this.save(existingCar);
  }

  async save(car: Car): Promise<Car> {
    return (await this.carRepository.save([CarEntity.from(car)]))[0].toCar();
  }

  async saveAll(cars: Car[]): Promise<Car[]> {
    return (
      await this.carRepository.save(cars.map((c) => CarEntity.from(c)))
    ).map((e) => e.toCar());
  }

  async getOneById(id: CarId): Promise<Car> {
    const carEntity = await this.carRepository.findOne({
      where: {
        id: id.value,
      },
    });

    if (!carEntity)
      throw new CarNotFoundError(`The car [${id.value}] is not found`);

    return carEntity.toCar();
  }

  async getOneByVulogId(vulogId: VulogCarId): Promise<Car> {
    const carEntity = await this.carRepository.findOne({
      where: {
        vulogId: vulogId.value,
      },
    });

    if (!carEntity)
      throw new CarNotFoundError(`The car [${vulogId.value}] is not found`);

    return carEntity.toCar();
  }

  async getOneByPlateNumber(plate: CarPlateNumber): Promise<Car> {
    const carEntity = await this.carRepository.findOne({
      where: {
        plate: plate.value,
      },
    });

    if (!carEntity) throw new PlateNumberNotFoundError(plate);

    return carEntity.toCar();
  }

  async getCarsByPlateNumbers(plateNumbers: CarPlateNumber[]): Promise<Car[]> {
    const carEntities = await this.carRepository.find({
      where: {
        plate: In(plateNumbers.map((plate) => plate.value)),
      },
    });

    return carEntities.map((entity) => entity.toCar());
  }

  async getAll(): Promise<Car[]> {
    let cars = this.inMemoryCacheService.get<Car[]>(
      config.redisConfig.localAllCars.cacheKey,
    );

    if (!cars) {
      cars = (await this.carRepository.find()).map((e) => e.toCar());

      this.inMemoryCacheService.set(
        config.redisConfig.localAllCars.cacheKey,
        cars,
        {
          ttl: config.redisConfig.localAllCars.ttl,
        },
      );
    }

    return cars;
  }

  async getMapByVulogId(): Promise<Map<string, Car>> {
    let cachedMap = this.inMemoryCacheService.get<Map<string, Car>>(
      config.redisConfig.localVulogIdCarMap.cacheKey,
    );

    if (!cachedMap) {
      const allCars = await this.getAll();
      cachedMap = new Map<string, Car>(
        allCars.map((c) => [c.vulogId.value, c]),
      );

      this.inMemoryCacheService.set(
        config.redisConfig.localVulogIdCarMap.cacheKey,
        cachedMap,
        {
          ttl: config.redisConfig.localVulogIdCarMap.ttl,
        },
      );
    }

    return cachedMap;
  }

  async getMapByVin(): Promise<Map<string, Car>> {
    let cachedMap = this.inMemoryCacheService.get<Map<string, Car>>(
      config.redisConfig.localVinCarMap.cacheKey,
    );

    if (!cachedMap) {
      const allCars = await this.getAll();
      cachedMap = new Map<string, Car>(allCars.map((c) => [c.vin, c]));

      this.inMemoryCacheService.set(
        config.redisConfig.localVinCarMap.cacheKey,
        cachedMap,
        {
          ttl: config.redisConfig.localVinCarMap.ttl,
        },
      );
    }

    return cachedMap;
  }

  async getMapByPlate(): Promise<Map<string, Car>> {
    let cachedMap = this.inMemoryCacheService.get<Map<string, Car>>(
      config.redisConfig.localPlateCarMap.cacheKey,
    );

    if (!cachedMap) {
      const allCars = await this.getAll();
      cachedMap = new Map<string, Car>(allCars.map((c) => [c.plate.value, c]));

      this.inMemoryCacheService.set(
        config.redisConfig.localPlateCarMap.cacheKey,
        cachedMap,
        {
          ttl: config.redisConfig.localPlateCarMap.ttl,
        },
      );
    }

    return cachedMap;
  }

  async getPaginatedCars(
    queryPagination: AdminCarQueryPaginationV1,
    filters?: {
      search?: string;
    },
  ): Promise<PageResult<Car[]>> {
    const { page, pageSize, sortKey, sortType } = queryPagination;

    let calculatedQueryPagination: QueryPagination;

    let findOptions: FindManyOptions<CarEntity> = {};

    if (page || pageSize) {
      const paginationUtil = new PaginationUtil(page, pageSize);

      const skip = paginationUtil.getSkip();
      const take = paginationUtil.getTake();

      findOptions.skip = skip;
      findOptions.take = take;

      calculatedQueryPagination = new QueryPagination({
        page: paginationUtil.page,
        pageSize: paginationUtil.pageSize,
        sortKey,
        sortType,
      });
    }

    if (sortKey) {
      switch (sortKey) {
        case 'id':
          findOptions = {
            ...findOptions,
            order: {
              id: sortType.toLowerCase() as FindOptionsOrderValue,
            },
          };

          break;

        case 'category':
          findOptions = {
            ...findOptions,
            order: {
              model: sortType.toLowerCase() as FindOptionsOrderValue,
            },
          };

          break;

        case 'plateNumber':
          findOptions = {
            ...findOptions,
            order: {
              plate: sortType.toLowerCase() as FindOptionsOrderValue,
            },
          };

          break;

        case 'vin':
          findOptions = {
            ...findOptions,
            order: {
              vin: sortType.toLowerCase() as FindOptionsOrderValue,
            },
          };

          break;

        default:
          findOptions = {
            ...findOptions,
            order: {
              plate: sortType.toLowerCase() as FindOptionsOrderValue,
            },
          };

          break;
      }
    }

    if (filters?.search) {
      const searchInput = EscapeWildCardsUtil.removeWildcards(
        filters?.search,
      ).trim();

      const likeQuery = `%${searchInput}%`;

      findOptions = {
        ...findOptions,
        where: [
          {
            vin: ILike(likeQuery),
          },
          {
            plate: ILike(likeQuery),
          },
        ],
      };
    }

    const total = await this.carRepository.count(findOptions);

    const carEntities: CarEntity[] = await this.carRepository.find(findOptions);

    return PageResult.from(
      carEntities?.length ? carEntities.map((entity) => entity.toCar()) : [],
      calculatedQueryPagination,
      total,
      sortKey,
      sortType,
    );
  }

  async upsertOne(cars: Car): Promise<void> {
    await this.upsertMany([cars]);
  }

  async upsertMany(cars: Car[]): Promise<void> {
    await this.carRepository.upsert(
      cars.map((car) => CarEntity.from(car)),
      {
        conflictPaths: ['vulogId'],
      },
    );
  }
}
