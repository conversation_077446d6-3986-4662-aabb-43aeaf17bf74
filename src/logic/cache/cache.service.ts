import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable } from '@nestjs/common';
import {
  HealthCheckError,
  HealthIndicatorResult,
  HealthIndicatorStatus,
} from '@nestjs/terminus';
import { Cache } from 'cache-manager';

const HEALTH_CHECK_KEY = 'redis-health-check';
@Injectable()
export class CacheService {
  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  private cacheKey(key: string): string {
    return `rental::${key}`;
  }

  async get<T>(key: string): Promise<T> {
    return this.cacheManager.get<T>(this.cacheKey(key));
  }

  async set<T>(key: string, value: T, options?: any): Promise<T> {
    return this.cacheManager.set(this.cacheKey(key), value, options);
  }

  async del(key: string): Promise<any> {
    return this.cacheManager.del(this.cacheKey(key));
  }

  async clearAll(): Promise<void> {
    await this.cacheManager.reset();
  }

  async checkIfHealthy(): Promise<HealthIndicatorResult> {
    const healthCheckVal = new Date().getTime();
    await this.set(HEALTH_CHECK_KEY, healthCheckVal, { ttl: 2 });
    const retrievedVal = await this.get(HEALTH_CHECK_KEY);

    const isHealthy = `${healthCheckVal}` === `${retrievedVal}`;
    const result: { redis: { status: HealthIndicatorStatus } } = {
      redis: { status: isHealthy ? 'up' : 'down' },
    };

    if (isHealthy) return result;
    throw new HealthCheckError('Redis health check failed', result);
  }
}
