import { Injectable } from '@nestjs/common';

class CacheStorage<T> {
  constructor(readonly value: T, readonly expireAt: number) {}
}

@Injectable()
export class InMemoryCacheService {
  private store = new Map<string, CacheStorage<unknown>>();

  /**
   * Store in memory the value at the key store.
   * @param key
   * @param value
   * @param options ttl is in seconds
   */
  set<T>(key: string, value: T, options: { ttl: number }): void {
    this[key] = new CacheStorage(value, Date.now() + options.ttl * 1000);
  }

  get<T>(key: string): T {
    const storage = this.store.get(key);

    if (!storage) return null;

    if (storage.expireAt < new Date().getTime()) {
      this.store.delete(key);
      return null;
    }

    return storage.value as T;
  }
}
