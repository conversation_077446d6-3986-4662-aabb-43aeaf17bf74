import { Injectable, Logger } from '@nestjs/common';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { StationId } from 'src/common/tiny-types';
import { config } from 'src/config';
import {
  CarAvailabilityUpdatedEventDto,
  CarAvailabilityUpdatedEventPayload,
} from 'src/event/station/station.car.availability.event';
import { AvailableCarsAtStation } from 'src/model/available-cars-at-station';
import { AvailableCarsAtStationMap } from 'src/model/available-cars-at-station-map';
import { Station } from 'src/model/station';
import { v4 } from 'uuid';
import { EventBusService } from '~shared/event-bus/event-bus.service';
import { RealtimeCar } from '../model/realtime.car';
import { CacheService } from './cache/cache.service';
import { CarAvailability, CarsCounter } from './car-availability';
import { VulogCarService } from './car-repository/vulog/vulog-car.service';
import { CarService } from './car/car.service';
import { StationService } from './station/station.service';

@Injectable()
export class AvailabilityService {
  constructor(
    private vulogCarClient: VulogCarService,
    private stationService: StationService,
    private carService: CarService,
    private readonly cacheService: CacheService,
    private readonly eventBusService: EventBusService,
  ) {}

  private async mapCarsToStations(
    rltCars: RealtimeCar[],
    stations: Station[],
  ): Promise<{
    [stationId: string]: CarsCounter;
  }> {
    const carMap = await this.carService.getMapByVulogId();

    const zoneStationMap = new Map<string, Station>();
    stations.forEach((station) => {
      if (!station.zoneId) {
        Logger.debug(`Station ${station.id.value} has no zone configured`, {
          stationId: station.id.value,
        });
        return;
      }
      zoneStationMap.set(station.zoneId, station);
    });

    const availabilityMap = new Map<string, CarsCounter>(
      stations.map((station) => [
        station.id.value,
        { station, carAvailability: {} },
      ]),
    );

    const availabilityDetailMap: AvailableCarsAtStationMap = {};

    stations.forEach((station) => {
      availabilityDetailMap[station.id.value] = { station, availableCars: [] };
    });

    rltCars.forEach((rltCar) => {
      if (!rltCar.id) {
        Logger.warn('Found car without ID in realtime data');
      }

      const carStation = this.determineCarStation(rltCar, zoneStationMap);

      if (!carStation) {
        Logger.debug(
          `Car ${rltCar.id?.value} could not be assigned to a station`,
          {
            carId: rltCar.id?.value,
            zones: rltCar.zoneIds?.map((z) => z.value),
          },
        );
        return;
      }

      const car = carMap.get(rltCar.sourceId.value);

      const carModel = car?.model;

      if (!carModel) {
        Logger.warn(`Car ${rltCar.id?.value} has no model information`, {
          carId: rltCar.id?.value,
          sourceId: rltCar.sourceId.value,
        });
        return;
      }

      this.updateAvailabilityDetails(
        availabilityDetailMap[carStation.id.value],
        rltCar,
      );

      this.updateAvailabilityCounter(
        availabilityMap.get(carStation.id.value),
        carModel,
      );
    });

    // For quick retrieval of available cars at a station
    await this.cacheService.set(
      config.redisConfig.carAvailabilityDetails.cacheKey,
      availabilityDetailMap,
      {
        ttl: config.redisConfig.carAvailabilityDetails.ttl,
      },
    );

    return Object.fromEntries(availabilityMap);
  }

  private determineCarStation(
    car: RealtimeCar,
    zoneStationMap: Map<string, Station>,
  ): Station | null {
    Logger.debug(
      `Car ${car.id?.value} has a sticky zone ${car.allowedStickyZone?.vulogZoneId.value}`,
    );
    if (car.allowedStickyZone?.vulogZoneId) {
      const stickyStation = zoneStationMap.get(
        car.allowedStickyZone.vulogZoneId.value,
      );
      if (stickyStation) {
        return stickyStation;
      }
      Logger.warn(
        `Car ${car.id?.value} has invalid sticky zone ${car.allowedStickyZone.vulogZoneId.value}`,
      );
    }

    Logger.debug(
      `Car ${car.id?.value} has no sticky zone configured - falling back to regular zones`,
      {
        carId: car.id?.value,
        availableZones: car.zoneIds?.map((z) => z.value) || [],
      },
    );

    if (!car.zoneIds?.length) {
      Logger.debug(`Car ${car.id?.value} has no regular vulog zones`);
      return null;
    }

    // Find first valid zone that maps to a station
    const validZone = car.zoneIds.find((zoneId) =>
      zoneStationMap.has(zoneId.value),
    );

    const station = validZone ? zoneStationMap.get(validZone.value) : null;
    Logger.debug(`Car ${car.id?.value} has a valid non-sticky zone`, {
      carId: car.id?.value,
      validZone: validZone?.value,
      zones: car.zoneIds.map((z) => z.value),
      stationId: station?.id.value,
    });
    return station;
  }

  private updateAvailabilityDetails(
    availabilityAtStation: AvailableCarsAtStation,
    rltCar: RealtimeCar,
  ): void {
    availabilityAtStation.availableCars.push(rltCar);
  }

  private updateAvailabilityCounter(
    counter: CarsCounter,
    carModel: string,
  ): void {
    if (!counter) {
      return;
    }
    counter.carAvailability[carModel] =
      (counter.carAvailability[carModel] || 0) + 1;
  }

  async getCarAvailabilityForAllStations(): Promise<CarAvailability> {
    // Retrieve cached availability
    const cachedAvailability = await this.cacheService.get<CarAvailability>(
      config.redisConfig.carAvailabilities.cacheKey,
    );

    if (cachedAvailability) {
      return cachedAvailability;
    }

    // No cached availability, retrieve from sources
    const vulogCars = await this.vulogCarClient.getCarsInRealtime(false, true);
    const carsMapByVulogId = await this.carService.getMapByVulogId();
    const customerUsableFleetService =
      await this.vulogCarClient.getCustomerUsableFleetService();
    const warningLowBatterLevel =
      await this.vulogCarClient.getWarningLowBatteryLevel();

    const vulogCarRealTimeDtos = vulogCars.map((car) => {
      return car.toVulogCarRealTimeDto();
    });
    const filteredCars = await this.vulogCarClient.filterAvailableCars(
      vulogCarRealTimeDtos,
      carsMapByVulogId,
      customerUsableFleetService,
      warningLowBatterLevel,
    );

    const stations = await this.stationService.getAllStations();
    if (stations.length === 0) {
      return {};
    }
    const availability = await this.mapCarsToStations(
      filteredCars.map((car) => car.toRealtimeCar()),
      stations,
    );
    Logger.log('Car availability mapped:', {
      availability: JSON.stringify(availability, null, 2),
    });
    // Generate event payload
    const eventPayload: CarAvailabilityUpdatedEventPayload = {
      stations: Object.entries(availability).map(([stationId, counter]) => ({
        stationId,
        carAvailability: {
          byModel: counter.carAvailability,
          total: Object.values(counter.carAvailability).reduce(
            (sum, count) => sum + count,
            0,
          ),
        },
      })),
      timestamp: new Date().toISOString(),
    };
    Logger.log('Car availability updated event payload:', {
      eventPayload: JSON.stringify(eventPayload, null, 2),
    });
    // Emit event
    this.eventBusService.nonBlockingEmit(
      new CarAvailabilityUpdatedEventDto(eventPayload, v4()),
    );
    // Cache availability
    await this.cacheService.set(
      config.redisConfig.carAvailabilities.cacheKey,
      availability,
      {
        ttl: config.redisConfig.carAvailabilities.ttl,
      },
    );

    return availability;
  }

  async getCarsCounterForSpecificStation(
    stationId: StationId,
  ): Promise<CarsCounter> {
    const carAvailability = await this.getCarAvailabilityForAllStations();

    return carAvailability[stationId.value];
  }

  async getAvailableCarsForSpecificStation(
    stationId: StationId,
    expectedCarModel?: CarModelEnum,
  ): Promise<RealtimeCar[]> {
    const vulogAvailableCars: RealtimeCar[] =
      await this.vulogCarClient.getAllAvailableCars();

    const expectedCars: RealtimeCar[] = expectedCarModel
      ? vulogAvailableCars.filter(
          (availCar) =>
            availCar.model && availCar.model.value === expectedCarModel, // Some cars on Vulog AiMA does not have "Model"
        )
      : vulogAvailableCars;

    const station = await this.stationService.getStation(stationId);

    return expectedCars.filter((car) => car?.zoneMaps?.get(station.zoneId));
  }

  async getAvailableCarsAtStations(): Promise<AvailableCarsAtStationMap> {
    let availableCarsAtStationMap =
      await this.cacheService.get<AvailableCarsAtStationMap>(
        config.redisConfig.carAvailabilityDetails.cacheKey,
      );

    // There is a cron job that computes this information, just need to return it if it does exist
    if (availableCarsAtStationMap) {
      return availableCarsAtStationMap;
    }

    availableCarsAtStationMap = {};

    const allStations = await this.stationService.getAllStations();

    const availableCars = await this.vulogCarClient.getAllAvailableCars();

    allStations.forEach((station) => {
      const element = {
        station,
        availableCars: [],
      };

      element.availableCars = availableCars.filter((availCar) =>
        availCar.zoneMaps?.has(station.zoneId),
      );

      availableCarsAtStationMap[station.id.value] = element;
    });

    return availableCarsAtStationMap;
  }
}
