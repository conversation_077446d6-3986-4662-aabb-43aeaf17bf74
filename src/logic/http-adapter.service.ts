import { CorrelationIdService } from '@bluesg-2/monitoring';
import { HttpService } from '@nestjs/axios';
import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { AxiosInstance, AxiosRequestConfig } from 'axios';
import * as rateLimit from 'axios-rate-limit';
import { isDefined } from 'class-validator';
import { InternalError } from 'src/common/errors/internal-error';
import { Page } from 'src/common/query/page';
import { PaginationQuery } from 'src/common/query/pagination-query';
import { parseAndValidate } from 'src/common/utils/parse-and-validate-dto';
import { HttpLogger } from 'src/shared/http-logger/http-logger';
import {
  CORRELATION_ID_HEADER,
  VULOG_CORRELATION_ID_HEADER,
} from '../common/constants/http-headers';
import { config } from '../config';
import { Url } from './url';

const USER_AGENT = config.httpServiceConfig.userAgent;

enum CallMethod {
  Get = 'GET',
  Post = 'POST',
  Put = 'PUT',
  Delete = 'DELETE',
}

export interface HttpEmptyDataResponse {
  statusCode: HttpStatus;
  statusMessage: string;
}

export type ObjectOrVoid<T> = T extends null ? HttpEmptyDataResponse : T;

export interface AuthConfig {
  headers: {
    Authorization: string;
    'x-api-key': string;
  };
}

@Injectable()
export class HttpAdapterService {
  private axiosRateLimited: AxiosInstance;

  constructor(
    public readonly httpService: HttpService,
    private readonly httpLogger: HttpLogger,
    private readonly correlationIdService: CorrelationIdService,
  ) {
    const tempRateLimit = rateLimit as any;

    this.axiosRateLimited = tempRateLimit(this.httpService.axiosRef, {
      maxRPS: 3,
    });

    this.httpLogger.setupAxiosInterceptorsWithAxiosInstance(
      this.axiosRateLimited,
    );
  }

  get axiosInstance(): AxiosInstance {
    return this.axiosRateLimited;
  }

  async get<T extends object>(
    url: Url,
    expectedDto: new () => T,
    errorType: new (cause: Error) => InternalError,
    authConfig: { headers: { Authorization: string } } = null,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
    resHeadersCapture?: { headers: object },
  ): Promise<ObjectOrVoid<T>> {
    return (await this.call(
      CallMethod.Get,
      url,
      expectedDto,
      errorType,
      authConfig,
      data,
      customConfig,
      resHeadersCapture,
    )) as ObjectOrVoid<T>;
  }

  async getAsArray<T extends object>(
    url: Url,
    expectedDto: new () => T,
    errorType: new (cause: Error) => InternalError,
    authConfig: { headers: { Authorization: string } } = null,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
    resHeadersCapture?: { headers: object },
  ): Promise<T[]> {
    const result = await this.get(
      url,
      expectedDto,
      errorType,
      authConfig,
      customConfig,
      data,
      resHeadersCapture,
    );

    return !result ? [] : (result as T[]);
  }

  async getAllPagesAsArray<T extends object>(
    url: Url,
    expectedDto: new () => T,
    errorType: new (cause: Error) => InternalError,
    authConfig: { headers: { Authorization: string } } = null,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
    pagination?: PaginationQuery,
  ): Promise<T[]> {
    const result = await this.makeSequentialApiCalls(
      url,
      expectedDto,
      errorType,
      authConfig,
      customConfig,
      data,
      pagination,
    );

    if (pagination) return (result as Page<T[]>).result;

    return result as T[];
  }

  private async makeSequentialApiCalls<T extends object>(
    url: Url,
    expectedDto: new () => T,
    errorType: new (cause: Error) => InternalError,
    authConfig: { headers: { Authorization: string } } = null,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
    pagination?: PaginationQuery,
  ): Promise<T[] | Page<T[]>> {
    const headersCapture = { headers: { last: null } };
    const accumulatedResult: T[] = [];
    let currentPage = pagination ? pagination.startPage : 1;

    while (
      (!pagination &&
        (!headersCapture.headers.last ||
          headersCapture.headers.last === 'false')) ||
      pagination
    ) {
      const isInternalCall = pagination && pagination.internal;

      const pageSizeExternal = !isInternalCall
        ? pagination.size || 200
        : undefined;

      const pageSizeInternal = isInternalCall
        ? pagination.size || 200
        : undefined;

      const currentPageUrl = url.addQueryParams({
        page: `${currentPage}`,
        // Vulog field. Internal API calls should be `sortBy` and `sortType`
        sort:
          pagination && pagination.sortBy
            ? `${pagination.sortBy},${pagination.sortOrder}`
            : undefined,
        size: pageSizeExternal, // Vulog field
        pageSize: pageSizeInternal, // Internal API field
      });

      const response = await this.getAsArray(
        currentPageUrl,
        expectedDto,
        errorType,
        authConfig,
        customConfig,
        data,
        headersCapture,
      );

      accumulatedResult.push(...response);

      if (
        !response?.length ||
        (pagination && response?.length < pagination.size)
      ) {
        break;
      }

      currentPage += 1;
    }

    if (pagination) {
      return new Page(
        accumulatedResult,
        accumulatedResult.length,
        headersCapture.headers.last || headersCapture.headers.last === 'true',
      );
    }

    return accumulatedResult;
  }

  async post<T extends object>(
    url: Url,
    expectedDto: new () => T,
    errorType: new (cause: Error) => InternalError,
    authConfig: { headers: { Authorization: string } } = null,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
  ): Promise<ObjectOrVoid<T>> {
    return (await this.call(
      CallMethod.Post,
      url,
      expectedDto,
      errorType,
      authConfig,
      data,
      customConfig,
    )) as ObjectOrVoid<T>;
  }

  async put<T extends object>(
    url: Url,
    expectedDto: new () => T,
    errorType: new (cause: Error) => InternalError,
    authConfig: { headers: { Authorization: string } } = null,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
  ): Promise<ObjectOrVoid<T>> {
    return (await this.call(
      CallMethod.Put,
      url,
      expectedDto,
      errorType,
      authConfig,
      data,
      customConfig,
    )) as ObjectOrVoid<T>;
  }

  async delete<T extends object>(
    url: Url,
    expectedDto: new () => T,
    errorType: new (cause: Error) => InternalError,
    authConfig: { headers: { Authorization: string } } = null,
    customConfig: AxiosRequestConfig = {},
    data?: unknown,
  ): Promise<ObjectOrVoid<T>> {
    return (await this.call(
      CallMethod.Delete,
      url,
      expectedDto,
      errorType,
      authConfig,
      data,
      customConfig,
    )) as ObjectOrVoid<T>;
  }

  async call<T extends object>(
    method: CallMethod,
    url: Url,
    toClass: new () => T,
    errorType: new (cause: Error) => InternalError,
    reqConfig: { headers: { Authorization: string } } = null,
    data?: unknown,
    customConfig: AxiosRequestConfig = {},
    resHeadersCapture?: { headers: object },
  ): Promise<T | T[] | HttpEmptyDataResponse> {
    try {
      const headers = reqConfig ? reqConfig.headers : {};

      const correlationId = this.correlationIdService.getCorrelationId()?.value;

      headers[CORRELATION_ID_HEADER] = correlationId;
      headers[VULOG_CORRELATION_ID_HEADER] = correlationId;

      headers['User-Agent'] = USER_AGENT;

      const response = await this.axiosRateLimited({
        method,
        url: url.value,
        data,
        headers: reqConfig && reqConfig.headers,
        ...customConfig,
      });

      if (resHeadersCapture) {
        resHeadersCapture.headers = response.headers;
      }

      if (!toClass) {
        return {
          statusCode: response.status,
          statusMessage: response.statusText,
        };
      }

      // Priority order: top-to-bottom
      const expectedDataProp = [
        response?.data?.result, // "response.data.result" is the structure for internal HTTP calls (e.g. stations service)
        response?.data?.content, // "response.data.content" is the structure for Vulog Vehicle Gateway
        response?.data, // "response.data" is the structure for Vulog AiMA
      ];

      const finalResponse = expectedDataProp.find((prop) => isDefined(prop));

      return parseAndValidate(finalResponse, toClass);
    } catch (error) {
      Logger.error(
        `HttpAdapterService Error, raw error message: ${error.message}`,
        {
          request: {
            url,
            method,
          },
          response: {
            status: error.response?.status,
            payload: error.response?.data,
          },
        },
      );

      throw new errorType(error);
    }
  }

  async callExpectingEmptyResponse(
    method: CallMethod,
    url: string,
    errorType: new (cause: Error) => InternalError,
    reqConfig: { headers: { Authorization: string } } = null,
    data?: unknown,
    customConfig: AxiosRequestConfig = {},
  ): Promise<HttpEmptyDataResponse> {
    try {
      const response = await this.axiosRateLimited({
        method,
        url,
        data,
        headers: reqConfig && reqConfig.headers,
        ...customConfig,
      });

      return {
        statusCode: response.status,
        statusMessage: response.statusText,
      };
    } catch (error) {
      throw new errorType(error);
    }
  }
}
