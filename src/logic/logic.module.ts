import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BillingExternalModule } from 'src/external/billing/billing.external.module';
import { StationExternalModule } from 'src/external/station/station.external.module';
import { UserSubscriptionExternalModule } from 'src/external/user-subscription/user-subscription.external.module';
import { ModelModule } from 'src/model/model.module';
import { ConfigurationModule } from 'src/shared/configuration/configuration.module';
import { EventBusModule } from 'src/shared/event-bus/event-bus.module';
import { LocalEventEmitterService } from '~shared/local-event-emitter/local-event-emitter.service';
import {
  NoRateLimitHttpModule,
  NO_RATE_LIMIT_SERVICE_TOKEN,
} from '~shared/no-rate-limit-http/no-rate-limit-http.module';
import { AvailabilityService } from './availability.service';
import { CacheService } from './cache/cache.service';
import { InMemoryCacheService } from './cache/in-memory.cache.service';
import { VulogCarService } from './car-repository/vulog/vulog-car.service';
import { CarService } from './car/car.service';
import { CommentService } from './comment/comment.service';
import { CommentEntity } from './comment/entity/comment.entity';
import { HttpAdapterService } from './http-adapter.service';
import { InternalCarService } from './internal.car.service';
import { RentalService } from './rental.service';
import { ReservationService } from './reservation.service';
import { ReservationCacheService } from './reservation/reservation.cache.service';
import { RfidUsageService } from './rfid-usage.service';
import { StationAdapterService } from './station/station-adapter.service';
import { StationService } from './station/station.service';
import { VulogTelemetryService } from './telemetry-repository/vulog/vulog-telemetry.service';
import { VulogEventService } from './vulog-event.service';
import { CancelReservationService } from './vulog/cancel-reservation.service';
import { EndRentalService } from './vulog/end-rental.service';
import { VulogAdapterService } from './vulog/vulog-adapter.service';
import { VulogAimaEventService } from './vulog/vulog-aima-event.service';
import { VulogAuthService } from './vulog/vulog-auth.service';
import { VulogCarConnectionProblemService } from './vulog/vulog-car-connection-problem.service';
import { VulogClientService } from './vulog/vulog-client.service';
import { VulogEventNotifierService } from './vulog/vulog-event-notifier.service';
import { VulogFleetService } from './vulog/vulog-fleet-service.service';
import { VulogProfileService } from './vulog/vulog-profile.service';
import { VulogUserService } from './vulog/vulog-user.service';
import { VulogVgEventService } from './vulog/vulog-vg-event.service';
import { VulogZoneService } from './vulog/vulog-zone.service';

const MAXIMUM_VULOG_TIMEOUT = 120000; // Maximum Vulog timeout

@Module({
  imports: [
    TypeOrmModule.forFeature([CommentEntity]),
    TerminusModule,
    HttpModule.register({
      timeout: MAXIMUM_VULOG_TIMEOUT,
    }),
    ModelModule,
    BillingExternalModule,
    UserSubscriptionExternalModule,
    StationExternalModule,
    EventBusModule,
    ConfigurationModule,
    NoRateLimitHttpModule.register({
      timeout: MAXIMUM_VULOG_TIMEOUT,
      serviceName: NO_RATE_LIMIT_SERVICE_TOKEN,
      baseURL: '',
    }),
  ],
  providers: [
    VulogClientService,
    VulogAdapterService,
    VulogUserService,
    VulogAuthService,
    StationAdapterService,
    StationService,
    HttpAdapterService,
    AvailabilityService,
    ReservationService,
    RentalService,
    CacheService,
    VulogCarService,
    VulogTelemetryService,
    VulogEventNotifierService,
    CarService,
    CommentService,
    VulogEventService,
    InMemoryCacheService,
    LocalEventEmitterService,
    VulogFleetService,
    VulogZoneService,
    VulogCarConnectionProblemService,
    RfidUsageService,
    VulogProfileService,
    ReservationCacheService,
    InternalCarService,
    VulogAimaEventService,
    VulogVgEventService,
    EndRentalService,
    CancelReservationService,
  ],
  exports: [
    TerminusModule,
    AvailabilityService,
    VulogTelemetryService,
    VulogCarService,
    ReservationService,
    RentalService,
    CacheService,
    VulogAuthService,
    ModelModule,
    BillingExternalModule,
    UserSubscriptionExternalModule,
    StationService,
    VulogEventNotifierService,
    ConfigurationModule,
    CarService,
    CommentService,
    VulogEventService,
    VulogUserService,
    StationExternalModule,
    VulogFleetService,
    VulogZoneService,
    VulogCarConnectionProblemService,
    LocalEventEmitterService,
    HttpAdapterService,
    RfidUsageService,
    VulogProfileService,
    ReservationCacheService,
    InternalCarService,
  ],
})
export class LogicModule {}
