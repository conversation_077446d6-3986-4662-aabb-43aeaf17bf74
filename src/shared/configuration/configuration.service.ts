import { ConfigurationService as IConfigurationService } from '@bluesg-2/queue-pop';
import { Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { ConfigurationError } from 'src/common/errors/configuration.error';
import { config } from 'src/config';
import { Configuration } from 'src/model/configuration';
import { ConfigurationRepository } from 'src/shared/configuration/configuration.repository';
import { ConfigurationKey } from 'src/shared/configuration/constants/configuration';
import { CacheService } from '../../logic/cache/cache.service';

const CONFIGURATION_CACHE_KEY = 'CONFIGURATION';

@Injectable()
export class ConfigurationService implements IConfigurationService {
  constructor(
    private configurationRepository: ConfigurationRepository,
    private cacheService: CacheService,
  ) {}

  private keyGenerator(key): string {
    return `${CONFIGURATION_CACHE_KEY}/${key}`;
  }

  async saveConfiguration<T>(
    configuration: Configuration<T>,
  ): Promise<Configuration<T>> {
    const cacheKey = this.keyGenerator(configuration.key);

    const cachedConfiguration = await this.cacheService.get(cacheKey);

    if (cachedConfiguration) {
      await this.cacheService.set(cacheKey, undefined);
    }

    return this.configurationRepository.saveOne(configuration);
  }

  async findOne<T>(key: ConfigurationKey): Promise<Configuration<T>> {
    const cachedConfiguration = await this.cacheService.get(
      this.keyGenerator(key),
    );

    let existingConfiguration: Configuration<T> = cachedConfiguration
      ? plainToInstance(Configuration<T>, cachedConfiguration)
      : null;

    if (!existingConfiguration) {
      existingConfiguration = await this.configurationRepository.getOneByKey<T>(
        key,
      );

      if (!existingConfiguration) {
        throw new ConfigurationError(
          `The configuration value (key: ${key}) could not be found`,
        );
      }

      this.cacheService.set(
        this.keyGenerator(key),
        JSON.parse(JSON.stringify(existingConfiguration)),
        {
          ttl: config.redisConfig.configurations.ttl,
        },
      );
    }

    return existingConfiguration;
  }

  maximumItemsPerQueue: () => Promise<number> = async () => {
    const config = await this.findOne(ConfigurationKey.MAXIMUM_ITEMS_PER_QUEUE);

    return +config.value as number;
  };

  queueTimeout: () => Promise<number> = async () => {
    const config = await this.findOne(
      ConfigurationKey.QUEUE_POP_TIMEOUT_IN_MINUTES,
    );

    return +config.value as number;
  };

  carConnectionProblemMaximumRetryAttemptsForPing: () => Promise<number> =
    async () => {
      const config = await this.findOne(
        ConfigurationKey.CAR_CONNECTION_PROBLEM__MAXIMUM_RETRY_ATTEMPTS_FOR_PING,
      );

      return +config.value as number;
    };
}
