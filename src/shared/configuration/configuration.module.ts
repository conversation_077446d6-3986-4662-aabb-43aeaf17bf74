import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheService } from 'src/logic/cache/cache.service';
import { ConfigurationRepository } from './configuration.repository';
import { ConfigurationService } from './configuration.service';
import { ConfigurationEntity } from './entities/configuration.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ConfigurationEntity])],
  providers: [ConfigurationService, ConfigurationRepository, CacheService],
  exports: [ConfigurationService],
})
export class ConfigurationModule {}
