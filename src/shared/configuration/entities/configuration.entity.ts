import { Column, Entity } from 'typeorm';

import { BaseEntity } from 'src/model/repositories/entities/base.entity';
import { EntityProps } from 'src/model/repositories/entities/entity-props';
import {
  ConfigurationKey,
  ConfigurationType,
} from '../constants/configuration';

@Entity('configuration')
export class ConfigurationEntity extends BaseEntity {
  constructor(props: EntityProps<ConfigurationEntity>) {
    super();

    Object.assign(this, props);
  }

  @Column({
    type: 'enum',
    enum: ConfigurationKey,
    nullable: false,
  })
  key: ConfigurationKey;

  @Column({
    type: 'enum',
    enum: ConfigurationType,
    nullable: false,
  })
  type: ConfigurationType;

  @Column({
    type: 'varchar',
    nullable: false,
  })
  value: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  description?: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  note?: string;
}
