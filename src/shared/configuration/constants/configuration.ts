export enum ConfigurationKey {
  WARNING_LOW_BATTERY_LEVEL_PER_CAR_MODEL = 'WARNING_LOW_BATTERY_LEVEL_PER_CAR_MODEL',
  GRACE_PERIOD_IN_MINUTES = 'GRACE_PERIOD_IN_MINUTES',
  MAXIMUM_ITEMS_PER_QUEUE = 'MAXIMUM_ITEMS_PER_QUEUE',
  QUEUE_POP_TIMEOUT_IN_MINUTES = 'QUEUE_POP_TIMEOUT_IN_MINUTES',
  CAR_CONNECTION_PROBLEM__MAXIMUM_RETRY_ATTEMPTS_FOR_PING = 'CAR_CONNECTION_PROBLEM__MAXIMUM_RETRY_ATTEMPTS_FOR_PING',
}

export enum ConfigurationType {
  JSON = 'JSON',
  PLAIN = 'PLAIN',
  NUMBER = 'NUMBER',
}
