import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigurationId } from 'src/common/tiny-types';
import { Repository } from 'typeorm';
import { Configuration } from '../../model/configuration';
import { TypeOrmBaseRepository } from '../../model/repositories/typeorm-base-repository';
import { ConfigurationKey } from './constants/configuration';
import { ConfigurationEntity } from './entities/configuration.entity';

@Injectable()
export class ConfigurationRepository extends TypeOrmBaseRepository<ConfigurationEntity> {
  constructor(
    @InjectRepository(ConfigurationEntity)
    private configurationRepository: Repository<ConfigurationEntity>,
  ) {
    super(configurationRepository);
  }

  async saveOne<T>(configuration: Configuration): Promise<Configuration<T>> {
    const [entity] = await this.save([configuration.toEntity()]);

    return this.getOne(new ConfigurationId(entity.id));
  }

  async getOne<T>(id: ConfigurationId): Promise<Configuration<T>> {
    const result = await this.findOne({
      where: {
        id: id.value,
      },
    });

    return result
      ? (Configuration.fromEntity(result) as Configuration<T>)
      : null;
  }

  async getOneByKey<T>(key: ConfigurationKey): Promise<Configuration<T>> {
    const result = await this.findOne({
      where: {
        key,
      },
    });

    return result
      ? (Configuration.fromEntity(result) as Configuration<T>)
      : null;
  }
}
