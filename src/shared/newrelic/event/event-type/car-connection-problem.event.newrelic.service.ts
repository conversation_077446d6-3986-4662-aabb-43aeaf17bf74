import { HttpStatus, Injectable } from '@nestjs/common';
import { NewRelicEventTypeService } from '../event-type.newrelic.service';

import {
  NewRelicMetricCarConnectionProblemOrigin,
  NewRelicMetricCarModel,
  NewRelicMetricStepInFlow,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';

@Injectable()
export class NewRelicCarConnectionProblemEventService extends NewRelicEventTypeService {
  constructor() {
    super('carConnectionProblem');
  }

  emitCarConnectionProblem(
    carModel: NewRelicMetricCarModel,
    stepInFlow: NewRelicMetricStepInFlow,
    httpStatus: HttpStatus,
  ) {
    let origin: NewRelicMetricCarConnectionProblemOrigin;

    switch (httpStatus) {
      case HttpStatus.FORBIDDEN:
        origin = NewRelicMetricCarConnectionProblemOrigin.VulogAiMA;
        break;

      case HttpStatus.GONE:
        origin = NewRelicMetricCarConnectionProblemOrigin.VulogVehicleGateway;
        break;

      default:
        origin = NewRelicMetricCarConnectionProblemOrigin.Unknown; // If it is unknown, it means that Vulog has modified HTTP status code for case `carConnectionProblem`. This dimension will let us know.
        break;
    }

    this.emitEvent('', {
      carModel: carModel || NewRelicMetricCarModel.NoCarModel,
      stepInFlow,
      origin,
    });
  }
}
