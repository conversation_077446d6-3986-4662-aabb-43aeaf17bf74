import { Injectable } from '@nestjs/common';
import { NewRelicEventTypeService } from '../event-type.newrelic.service';

import {
  NewRelicMetricCarModel,
  NewRelicMetricStepInFlow,
  NewRelicMetricTrigger,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';

@Injectable()
export class NewRelicReservationEventService extends NewRelicEventTypeService {
  constructor() {
    super('reservation');
  }

  emitAllocated(carModel: NewRelicMetricCarModel) {
    this.emitEvent('allocated', {
      carModel: carModel || NewRelicMetricCarModel.NoCarModel,
    });
  }

  emitSwitched() {
    this.emitEvent('switched');
  }

  emitSwitchedFailed() {
    this.emitEvent('switchedFailed');
  }

  emitCanceled(trigger: NewRelicMetricTrigger) {
    this.emitEvent('canceled', { trigger });
  }

  emitExpired(carModel: NewRelicMetricCarModel) {
    this.emitEvent('expired', {
      carModel: carModel || NewRelicMetricCarModel.NoCarModel,
    });
  }

  emitTerminatedByTechnicalError(
    carModel: NewRelicMetricCarModel,
    stepInFlow: NewRelicMetricStepInFlow,
  ) {
    this.emitEvent('terminatedByTechnicalError', {
      carModel: carModel || NewRelicMetricCarModel.NoCarModel,
      stepInFlow,
    });
  }
}
