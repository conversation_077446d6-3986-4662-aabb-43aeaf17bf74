import { Injectable } from '@nestjs/common';
import { NewRelicEventTypeService } from '../event-type.newrelic.service';

import * as _ from 'lodash';
import { TestingEndRentalConditionResult } from 'src/model/testing-end-rental.report';
import {
  NewRelicMetricCarModel,
  NewRelicMetricRentalType,
  NewRelicMetricTrigger,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';

@Injectable()
export class NewRelicRentalEventService extends NewRelicEventTypeService {
  constructor() {
    super('rental');
  }

  emitStartedAtBlueSG(
    trigger: NewRelicMetricTrigger,
    rentalType: NewRelicMetricRentalType,
    carModel: NewRelicMetricCarModel,
  ) {
    this.emitEvent('startedAtBlueSG', {
      trigger,
      rentalType,
      carModel: carModel || NewRelicMetricCarModel.NoCarModel,
    });
  }

  emitStartedAtVulog(
    trigger: NewRelicMetricTrigger,
    rentalType: NewRelicMetricRentalType,
    carModel: NewRelicMetricCarModel,
  ) {
    this.emitEvent('startedAtVulog', {
      trigger,
      rentalType,
      carModel: carModel || NewRelicMetricCarModel.NoCarModel,
    });
  }

  emitFailedToSatisfyEndingConditions(
    trigger: NewRelicMetricTrigger,
    rentalType: NewRelicMetricRentalType,
    carModel: NewRelicMetricCarModel,
    endingRentalConditionResults: TestingEndRentalConditionResult[],
    stationName: string,
    stationId: string,
  ) {
    const endingRentalConditionsMap: { [conditionId: string]: boolean } = {};

    endingRentalConditionResults.forEach((value) => {
      endingRentalConditionsMap[_.camelCase(value.conditionId)] =
        value.fulfilled;
    });

    this.emitEvent('failedToSatisfyEndingConditions', {
      trigger,
      rentalType,
      carModel: carModel || NewRelicMetricCarModel.NoCarModel,
      stationName,
      stationId,
      ...endingRentalConditionsMap,
    });
  }

  emitTriggeredWaitingForInvoicing(
    trigger: NewRelicMetricTrigger,
    rentalType: NewRelicMetricRentalType,
    carModel: NewRelicMetricCarModel,
  ) {
    this.emitEvent('triggeredWaitingForInvoicing', {
      trigger,
      rentalType,
      carModel: carModel || NewRelicMetricCarModel.NoCarModel,
    });
  }

  emitEndedByBlueSG(
    rentalType: NewRelicMetricRentalType,
    carModel: NewRelicMetricCarModel,
    stationName: string,
    stationId: string,
  ) {
    this.emitEvent('endedByBlueSG', {
      rentalType,
      carModel: carModel || NewRelicMetricCarModel.NoCarModel,
      stationName,
      stationId,
    });
  }

  emitEndedByAiMA(
    rentalType: NewRelicMetricRentalType,
    carModel: NewRelicMetricCarModel,
  ) {
    this.emitEvent('endedByAiMA', {
      rentalType,
      carModel: carModel || NewRelicMetricCarModel.NoCarModel,
    });
  }

  emitRated(
    rentalType: NewRelicMetricRentalType,
    carModel: NewRelicMetricCarModel,
  ) {
    this.emitEvent('rated', {
      rentalType,
      carModel: carModel || NewRelicMetricCarModel.NoCarModel,
    });
  }

  // Helper method for QR-related events
  private emitQREvent(
    eventName: string,
    plateNo: string | undefined,
    userId: string,
    qrCode: string,
    additionalData: Record<string, any> = {},
  ) {
    this.emitEvent(eventName, {
      plateNo,
      userId,
      qrCode,
      ...additionalData,
    });
  }

  private emitConditionValidationEvent(
    eventName: string,
    plateNo: string | undefined,
    userId: string,
    qrCode: string | undefined,
    isValid: boolean,
    additionalData: Record<string, any> = {},
  ) {
    this.emitEvent(eventName, {
      plateNo,
      userId,
      qrCode,
      isValid,
      ...additionalData,
    });
  }

  emitCarInServiceZoneValidation(
    plateNo: string,
    userId: string,
    qrCode: string | undefined,
    isValid: boolean,
  ) {
    this.emitConditionValidationEvent(
      'carInServiceZoneValidation',
      plateNo,
      userId,
      qrCode,
      isValid,
    );
  }

  emitDoorsAndWindowsClosedValidation(
    plateNo: string,
    userId: string,
    qrCode: string | undefined,
    isValid: boolean,
  ) {
    this.emitConditionValidationEvent(
      'doorsAndWindowsClosedValidation',
      plateNo,
      userId,
      qrCode,
      isValid,
    );
  }

  emitDoorsLockedValidation(
    plateNo: string,
    userId: string,
    qrCode: string | undefined,
    isValid: boolean,
  ) {
    this.emitConditionValidationEvent(
      'doorsLockedValidation',
      plateNo,
      userId,
      qrCode,
      isValid,
    );
  }

  emitEngineOffValidation(
    plateNo: string,
    userId: string,
    qrCode: string | undefined,
    isValid: boolean,
  ) {
    this.emitConditionValidationEvent(
      'engineOffValidation',
      plateNo,
      userId,
      qrCode,
      isValid,
    );
  }

  emitCarPluggedValidation(
    plateNo: string,
    userId: string,
    qrCode: string | undefined,
    isValid: boolean,
  ) {
    this.emitConditionValidationEvent(
      'carPluggedValidation',
      plateNo,
      userId,
      qrCode,
      isValid,
    );
  }

  emitParkingBrakeValidation(
    plateNo: string,
    userId: string,
    qrCode: string | undefined,
    isValid: boolean,
  ) {
    this.emitConditionValidationEvent(
      'parkingBrakeValidation',
      plateNo,
      userId,
      qrCode,
      isValid,
    );
  }

  emitQREndRentalAttempt(plateNo: string, userId: string, qrCode: string) {
    this.emitQREvent('qrEndRentalAttempt', plateNo, userId, qrCode);
  }

  emitQRFormatValidation(
    plateNo: string,
    userId: string,
    qrCode: string,
    isValid: boolean,
  ) {
    this.emitQREvent('qrFormatValidation', plateNo, userId, qrCode, {
      isValid,
    });
  }

  emitQRStationValidation(
    plateNo: string,
    userId: string,
    qrCode: string,
    isValid: boolean,
  ) {
    this.emitQREvent('qrStationValidation', plateNo, userId, qrCode, {
      isValid,
    });
  }

  emitQRParkingSpaceValidation(
    plateNo: string,
    userId: string,
    qrCode: string,
    isValid: boolean,
  ) {
    this.emitQREvent('qrParkingSpaceValidation', plateNo, userId, qrCode, {
      isValid,
    });
  }

  emitQREndRentalSuccess(plateNo: string, userId: string, qrCode: string) {
    this.emitQREvent('qrEndRentalSuccess', plateNo, userId, qrCode);
  }

  emitQREndRentalError(plateNo: string, userId: string, qrCode: string) {
    this.emitQREvent('qrEndRentalError', plateNo, userId, qrCode);
  }
}
