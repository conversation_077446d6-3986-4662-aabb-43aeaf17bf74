import { Module } from '@nestjs/common';
import { NewRelicCarConnectionProblemEventService } from './event-type/car-connection-problem.event.newrelic.service';
import { NewRelicRentalEventService } from './event-type/rental.event.newrelic.service';
import { NewRelicReservationEventService } from './event-type/reservation.event.newrelic.service';

@Module({
  imports: [],
  providers: [
    NewRelicCarConnectionProblemEventService,
    NewRelicRentalEventService,
    NewRelicReservationEventService,
  ],
  exports: [
    NewRelicCarConnectionProblemEventService,
    NewRelicRentalEventService,
    NewRelicReservationEventService,
  ],
})
export class NewRelicEventModule {}
