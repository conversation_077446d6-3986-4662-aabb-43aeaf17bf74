import { Logger } from '@nestjs/common';
import * as newRelicApmAgent from 'newrelic';
import {
  NewRelicCommonEventAttributes,
  NewRelicEventAttributes,
} from './types';

export class NewRelicEventTypeService {
  constructor(private eventType: string) {}

  private getFullyQualifiedEventName(eventName: string): string {
    return `${this.eventType}.${eventName}`;
  }

  protected emitEvent(eventName: string, attributes?: NewRelicEventAttributes) {
    const commonAttributes: NewRelicCommonEventAttributes = {
      event: eventName.length
        ? this.getFullyQualifiedEventName(eventName)
        : undefined,
      env: process.env.NODE_ENV,
    };

    let aggregatedAttributes: { [keys: string]: boolean | number | string } =
      commonAttributes as unknown as {
        [keys: string]: boolean | number | string;
      };

    if (attributes) {
      aggregatedAttributes = {
        ...attributes,
        ...aggregatedAttributes,
      };
    }

    try {
      newRelicApmAgent.recordCustomEvent(this.eventType, aggregatedAttributes);
    } catch (err) {
      Logger.error('NewRelic Event: Failed to send events', {
        eventType: this.eventType,
        event: aggregatedAttributes.event,
        env: aggregatedAttributes.env,
        errorDetails: err,
      });
    }
  }
}
