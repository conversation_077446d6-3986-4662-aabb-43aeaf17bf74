import { AxiosRequestConfig } from 'axios';
import { config } from 'src/config';
import { Url } from 'src/logic/url';
import {
  NoRateLimitHttpAdapterService,
  ObjectOrVoid,
} from '~shared/no-rate-limit-http/no-rate-limit-http-adapter.service';
import { NewRelicApiError } from './newrelic.error';

export class NewRelicBaseService {
  constructor(
    protected readonly httpAdapterService: NoRateLimitHttpAdapterService,
  ) {}

  private readonly apiKey = config.newRelicConfig.licenseKey;

  private targetApiEndpoint: Url = null;

  protected getTargetApiEndpoint(): Url {
    if (!this.targetApiEndpoint) {
      throw new NewRelicApiError(
        'Missing target API endpoint. Please set it up in concrete class',
      );
    }

    return this.targetApiEndpoint;
  }

  protected setTargetApiEndpoint(value: Url): void {
    this.targetApiEndpoint = value;
  }

  protected async post<T extends object>({
    expectedDtoClass,
    customRequestConfig,
    body,
  }: {
    expectedDtoClass: new () => T;
    customRequestConfig?: AxiosRequestConfig;
    body?: unknown;
  }): Promise<ObjectOrVoid<T>> {
    return this.httpAdapterService.post<T>(
      this.getTargetApiEndpoint(),
      expectedDtoClass,
      NewRelicApiError,
      null,
      {
        ...customRequestConfig,
        headers: {
          'Api-Key': this.apiKey,
        },
      },
      body,
    );
  }

  protected async get<T extends object>({
    expectedDtoClass,
    customRequestConfig,
    body,
  }: {
    expectedDtoClass: new () => T;
    customRequestConfig?: AxiosRequestConfig;
    body?: unknown;
  }): Promise<ObjectOrVoid<T>> {
    return this.httpAdapterService.get<T>(
      this.getTargetApiEndpoint(),
      expectedDtoClass,
      NewRelicApiError,
      null,
      {
        ...customRequestConfig,
        headers: {
          'Api-Key': this.apiKey,
        },
      },
      body,
    );
  }
}
