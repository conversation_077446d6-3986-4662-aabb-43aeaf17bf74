import { Injectable } from '@nestjs/common';
import { DateTime } from 'luxon';
import { bigNumberize } from 'src/common/utils/big-decimal.util';
import { CacheService } from 'src/logic/cache/cache.service';
import { NewRelicMetricCacheService } from './metric-cache.newrelic.service';
import { NewRelicMetricCachePayload } from './types/metric-cache-payload.newrelic.type';

type NewRelicCountPayload = Pick<
  NewRelicMetricCachePayload,
  'key' | 'data' | 'startTimestamp' | 'intervalInSeconds'
>;

type BaseCountPayload = Omit<NewRelicCountPayload, 'data'>;

/**
 * NewRelic does not provide us way to get raw metric data points that are outside of application (APM-integrated), and we need to store information ourself
 */
@Injectable()
export class NewRelicCountMetricCacheService extends NewRelicMetricCacheService {
  constructor(protected readonly cacheService: CacheService) {
    super(cacheService);
  }

  private sameTimeWindow(
    stale: NewRelicCountPayload,
    latest: NewRelicCountPayload,
  ) {
    const {
      startTimestamp: startTimestampStale,
      intervalInSeconds: intervalInSecondsStale,
      data: dataStale,
    } = stale;

    const endTimestampBaseStale = DateTime.fromMillis(startTimestampStale)
      .plus({
        seconds: intervalInSecondsStale,
      })
      .toMillis();

    if (
      latest.startTimestamp >= startTimestampStale &&
      latest.startTimestamp <= endTimestampBaseStale
    ) {
      return {
        ...latest,
        startTimestamp: startTimestampStale,
        intervalInSeconds: intervalInSecondsStale,
        data: bigNumberize(latest.data)
          .plus(bigNumberize(dataStale))
          .toNumber(),
      };
    }

    return latest;
  }

  private async isSameTimeWindowForCountMetric(
    stale: NewRelicCountPayload,
    latest: NewRelicCountPayload,
  ): Promise<NewRelicCountPayload> {
    if (!stale) {
      const baseCountKeyStale = await this.getMetric<BaseCountPayload>(
        latest.key,
      );

      if (!baseCountKeyStale) {
        return latest;
      }

      return this.sameTimeWindow(
        { ...baseCountKeyStale, data: 0 } as NewRelicCountPayload,
        latest,
      );
    }

    return this.sameTimeWindow(stale, latest);
  }

  async setCount(
    cacheData: NewRelicCountPayload,
    dimensions?: object,
  ): Promise<NewRelicCountPayload> {
    const ttlInSecs = 2592000; // All raw metric data points will be retained for 30 days. See: https://docs.newrelic.com/docs/data-apis/ingest-apis/metric-api/introduction-metric-api/#retention

    let latestData: NewRelicCountPayload = cacheData;

    const staleData = await this.getMetric<NewRelicCountPayload>(
      this.formCacheKey(latestData.key, dimensions),
    );

    latestData = await this.isSameTimeWindowForCountMetric(
      staleData,
      latestData,
    );

    await this.cacheService.set(
      this.formCacheKey(latestData.key, dimensions),
      latestData,
      {
        ttl: ttlInSecs,
      },
    );

    await this.cacheService.set(
      latestData.key,
      {
        key: latestData.key,
        intervalInSeconds: latestData.intervalInSeconds,
        startTimestamp: latestData.startTimestamp,
      } as BaseCountPayload,
      {
        ttl: ttlInSecs,
      },
    );

    return latestData;
  }
}
