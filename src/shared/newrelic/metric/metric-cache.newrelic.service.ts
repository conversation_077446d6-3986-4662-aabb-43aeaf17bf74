import { Injectable } from '@nestjs/common';
import * as stringify from 'json-stable-stringify';
import { CacheService } from 'src/logic/cache/cache.service';

/**
 * NewRelic does not provide us way to get raw metric data points that are outside of application (APM-integrated), and we need to store information ourself
 */
@Injectable()
export abstract class NewRelicMetricCacheService {
  constructor(protected readonly cacheService: CacheService) {}

  async getMetric<T>(key: string): Promise<T> {
    const data = await this.cacheService.get(key);

    return data as T;
  }

  protected formCacheKey(flqnMetricName: string, dimensions?: object) {
    return `metricName:${flqnMetricName}${
      dimensions
        ? `/dimensions:${stringify(dimensions, function (a, b) {
            return a.key.localeCompare(b.key);
          })}`
        : ''
    }`;
  }

  abstract setCount(cacheData: unknown, dimensions?: object);
}
