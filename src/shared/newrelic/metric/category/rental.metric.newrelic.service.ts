import { Injectable } from '@nestjs/common';
import {
  NewRelicMetricCarModel,
  NewRelicMetricRentalType,
} from '../enums/newrelic-metric.enum';
import { NewRelicMetricCategoryService } from '../metric-category.newrelic.service';
import { NewRelicMetricSenderService } from '../metric-sender.newrelic.service';

@Injectable()
export class NewRelicRentalMetricService extends NewRelicMetricCategoryService {
  constructor(
    private readonly metricSenderService: NewRelicMetricSenderService,
  ) {
    super('rental');
  }

  async setNumberOfBlueSGStartedButVulogBooking(
    value: number,
    rentalType: NewRelicMetricRentalType,
    carModel: NewRelicMetricCarModel,
  ) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfBlueSGStartedButVulogBooking'),
      value,
      { rentalType, carModel: carModel || NewRelicMetricCarModel.NoCarModel },
    );
  }

  bulkSetNumberOfBlueSGStartedButVulogBooking(
    dataPoints: {
      count: number;
      rentalType: NewRelicMetricRentalType;
      carModel: NewRelicMetricCarModel;
    }[],
  ): Promise<boolean> {
    return this.metricSenderService.bulkSendGauge(
      this.getFullyQualifiedMetricName('numberOfBlueSGStartedButVulogBooking'),
      dataPoints,
    );
  }

  async setNumberOfOnGoing(
    value: number,
    rentalType: NewRelicMetricRentalType,
    carModel: NewRelicMetricCarModel,
  ) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfOnGoing'),
      value,
      { rentalType, carModel: carModel || NewRelicMetricCarModel.NoCarModel },
    );
  }

  bulkSetNumberOfOnGoing(
    dataPoints: {
      count: number;
      rentalType: NewRelicMetricRentalType;
      carModel: NewRelicMetricCarModel;
    }[],
  ): Promise<boolean> {
    return this.metricSenderService.bulkSendGauge(
      this.getFullyQualifiedMetricName('numberOfOnGoing'),
      dataPoints,
    );
  }

  async setNumberOfEndingRentalConditionsSatisfied(
    value: number,
    rentalType: NewRelicMetricRentalType,
    carModel: NewRelicMetricCarModel,
  ) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName(
        'numberOfEndingRentalConditionsSatisfied',
      ),
      value,
      { rentalType, carModel: carModel || NewRelicMetricCarModel.NoCarModel },
    );
  }

  bulkSetNumberOfEndingRentalConditionsSatisfied(
    dataPoints: {
      count: number;
      rentalType: NewRelicMetricRentalType;
      carModel: NewRelicMetricCarModel;
    }[],
  ): Promise<boolean> {
    return this.metricSenderService.bulkSendGauge(
      this.getFullyQualifiedMetricName(
        'numberOfEndingRentalConditionsSatisfied',
      ),
      dataPoints,
    );
  }

  async setNumberOfWaitingForInvoicing(
    value: number,
    rentalType: NewRelicMetricRentalType,
    carModel: NewRelicMetricCarModel,
  ) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfWaitingForInvoicing'),
      value,
      { rentalType, carModel: carModel || NewRelicMetricCarModel.NoCarModel },
    );
  }

  bulkSetNumberOfWaitingForInvoicing(
    dataPoints: {
      count: number;
      rentalType: NewRelicMetricRentalType;
      carModel: NewRelicMetricCarModel;
    }[],
  ): Promise<boolean> {
    return this.metricSenderService.bulkSendGauge(
      this.getFullyQualifiedMetricName('numberOfWaitingForInvoicing'),
      dataPoints,
    );
  }
}
