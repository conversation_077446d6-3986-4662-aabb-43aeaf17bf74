import { Injectable } from '@nestjs/common';
import { CarMetricDataPoint } from 'src/cron/jobs/report-metric/car/base/car-metric.payload';
import {
  NewRelicMetricCarModel,
  NewRelicMetricService,
} from '../enums/newrelic-metric.enum';
import { NewRelicMetricCategoryService } from '../metric-category.newrelic.service';
import { NewRelicMetricSenderService } from '../metric-sender.newrelic.service';

@Injectable()
export class NewRelicCarMetricService extends NewRelicMetricCategoryService {
  constructor(
    private readonly metricSenderService: NewRelicMetricSenderService,
  ) {
    super('car');
  }

  async setNumberOfPlugged(
    value: number,
    service: NewRelicMetricService,
    carModel: NewRelicMetricCarModel,
    station: string,
  ) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfPlugged'),
      value,
      {
        service,
        carModel: carModel || NewRelicMetricCarModel.NoCarModel,
        station,
      },
    );
  }

  bulkSetNumberOfPlugged(dataPoints: CarMetricDataPoint[]): Promise<boolean> {
    return this.metricSenderService.bulkSendGauge<CarMetricDataPoint>(
      this.getFullyQualifiedMetricName('numberOfPlugged'),
      dataPoints,
    );
  }

  async setNumberOfUnplugged(
    value: number,
    service: NewRelicMetricService,
    carModel: NewRelicMetricCarModel,
    station: string,
  ) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfUnplugged'),
      value,
      {
        service,
        carModel: carModel || NewRelicMetricCarModel.NoCarModel,
        station,
      },
    );
  }

  bulkSetNumberOfUnplugged(dataPoints: CarMetricDataPoint[]): Promise<boolean> {
    return this.metricSenderService.bulkSendGauge<CarMetricDataPoint>(
      this.getFullyQualifiedMetricName('numberOfUnplugged'),
      dataPoints,
    );
  }

  async setNumberOfCharging(
    value: number,
    service: NewRelicMetricService,
    carModel: NewRelicMetricCarModel,
    station: string,
  ) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfCharging'),
      value,
      {
        service,
        carModel: carModel || NewRelicMetricCarModel.NoCarModel,
        station,
      },
    );
  }

  bulkSetNumberOfCharging(dataPoints: CarMetricDataPoint[]): Promise<boolean> {
    return this.metricSenderService.bulkSendGauge<CarMetricDataPoint>(
      this.getFullyQualifiedMetricName('numberOfCharging'),
      dataPoints,
    );
  }

  async setNumberOfLockedAndImmobilized(
    value: number,
    service: NewRelicMetricService,
    carModel: NewRelicMetricCarModel,
    station: string,
  ) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfLockedAndImmobilized'),
      value,
      {
        service,
        carModel: carModel || NewRelicMetricCarModel.NoCarModel,
        station,
      },
    );
  }

  bulkSetNumberOfLockedAndImmobilized(
    dataPoints: CarMetricDataPoint[],
  ): Promise<boolean> {
    return this.metricSenderService.bulkSendGauge<CarMetricDataPoint>(
      this.getFullyQualifiedMetricName('numberOfLockedAndImmobilized'),
      dataPoints,
    );
  }

  async setNumberOfUnlockedAndMobilized(
    value: number,
    service: NewRelicMetricService,
    carModel: NewRelicMetricCarModel,
    station: string,
  ) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfUnlockedAndMobilized'),
      value,
      {
        service,
        carModel: carModel || NewRelicMetricCarModel.NoCarModel,
        station,
      },
    );
  }

  bulkSetNumberOfUnlockedAndMobilized(dataPoints: CarMetricDataPoint[]) {
    return this.metricSenderService.bulkSendGauge<CarMetricDataPoint>(
      this.getFullyQualifiedMetricName('numberOfUnlockedAndMobilized'),
      dataPoints,
    );
  }

  async setNumberOfOutOfZone(
    value: number,
    service: NewRelicMetricService,
    carModel: NewRelicMetricCarModel,
  ) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfOutOfZone'),
      value,
      { service, carModel },
    );
  }

  bulkSetNumberOfOutOfZone(dataPoints: CarMetricDataPoint[]): Promise<boolean> {
    return this.metricSenderService.bulkSendGauge<CarMetricDataPoint>(
      this.getFullyQualifiedMetricName('numberOfOutOfZone'),
      dataPoints,
    );
  }

  async setNumberOfInZone(
    value: number,
    service: NewRelicMetricService,
    carModel: NewRelicMetricCarModel,
  ) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfInZone'),
      value,
      { service, carModel },
    );
  }

  bulkSetNumberOfInZone(dataPoints: CarMetricDataPoint[]): Promise<boolean> {
    return this.metricSenderService.bulkSendGauge<CarMetricDataPoint>(
      this.getFullyQualifiedMetricName('numberOfInZone'),
      dataPoints,
    );
  }

  async setNumberOfOutOfCustomerFleetService(
    value: number,
    carModel: NewRelicMetricCarModel,
  ) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfOutOfCustomerFleetService'),
      value,
      { carModel },
    );
  }

  bulkSetNumberOfOutOfCustomerFleetService(
    dataPoints: CarMetricDataPoint[],
  ): Promise<boolean> {
    return this.metricSenderService.bulkSendGauge<CarMetricDataPoint>(
      this.getFullyQualifiedMetricName('numberOfOutOfCustomerFleetService'),
      dataPoints,
    );
  }

  async setNumberOfOutOfService(
    value: number,
    carModel: NewRelicMetricCarModel,
  ) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfOutOfService'),
      value,
      { carModel },
    );
  }

  bulkSetNumberOfOutOfService(
    dataPoints: CarMetricDataPoint[],
  ): Promise<boolean> {
    return this.metricSenderService.bulkSendGauge<CarMetricDataPoint>(
      this.getFullyQualifiedMetricName('numberOfOutOfService'),
      dataPoints,
    );
  }

  async setNumberOfAvailable(
    value: number,
    service: NewRelicMetricService,
    carModel: NewRelicMetricCarModel,
    station: string,
  ) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfAvailable'),
      value,
      {
        service,
        carModel: carModel || NewRelicMetricCarModel.NoCarModel,
        station,
      },
    );
  }

  bulkSetNumberOfAvailable(dataPoints: CarMetricDataPoint[]) {
    return this.metricSenderService.bulkSendGauge<CarMetricDataPoint>(
      this.getFullyQualifiedMetricName('numberOfAvailable'),
      dataPoints,
    );
  }

  async setNumberOfSticky(
    value: number,
    service: NewRelicMetricService,
    carModel: NewRelicMetricCarModel,
    station: string,
  ) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfSticky'),
      value,
      {
        service,
        carModel: carModel || NewRelicMetricCarModel.NoCarModel,
        station,
      },
    );
  }

  bulkSetNumberOfSticky(dataPoints: CarMetricDataPoint[]): Promise<boolean> {
    return this.metricSenderService.bulkSendGauge<CarMetricDataPoint>(
      this.getFullyQualifiedMetricName('numberOfSticky'),
      dataPoints,
    );
  }

  async setNumberOfUnsticky(
    value: number,
    service: NewRelicMetricService,
    carModel: NewRelicMetricCarModel,
    station: string,
  ) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfUnsticky'),
      value,
      {
        service,
        carModel: carModel || NewRelicMetricCarModel.NoCarModel,
        station,
      },
    );
  }

  bulkSetNumberOfUnsticky(dataPoints: CarMetricDataPoint[]): Promise<boolean> {
    return this.metricSenderService.bulkSendGauge<CarMetricDataPoint>(
      this.getFullyQualifiedMetricName('numberOfUnsticky'),
      dataPoints,
    );
  }
}
