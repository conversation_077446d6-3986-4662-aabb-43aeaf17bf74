import { Injectable } from '@nestjs/common';
import { NewRelicMetricCarModel } from '../enums/newrelic-metric.enum';
import { NewRelicMetricCategoryService } from '../metric-category.newrelic.service';
import { NewRelicMetricSenderService } from '../metric-sender.newrelic.service';

@Injectable()
export class NewRelicReservationMetricService extends NewRelicMetricCategoryService {
  constructor(
    private readonly metricSenderService: NewRelicMetricSenderService,
  ) {
    super('reservation');
  }

  async setNumberOfOnGoing(value: number, carModel: NewRelicMetricCarModel) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfOnGoing'),
      value,
      { carModel },
    );
  }

  bulkSetNumberOfOnGoing(
    dataPoints: {
      count: number;
      carModel: NewRelicMetricCarModel;
    }[],
  ): Promise<boolean> {
    return this.metricSenderService.bulkSendGauge(
      this.getFullyQualifiedMetricName('numberOfOnGoing'),
      dataPoints,
    );
  }

  async setNumberOfCooldown(value: number, carModel: NewRelicMetricCarModel) {
    await this.metricSenderService.sendGauge(
      this.getFullyQualifiedMetricName('numberOfCooldown'),
      value,
      { carModel: carModel || NewRelicMetricCarModel.NoCarModel },
    );
  }

  bulkSetNumberOfCooldown(
    dataPoints: {
      count: number;
      carModel: NewRelicMetricCarModel;
    }[],
  ): Promise<boolean> {
    return this.metricSenderService.bulkSendGauge(
      this.getFullyQualifiedMetricName('numberOfCooldown'),
      dataPoints,
    );
  }
}
