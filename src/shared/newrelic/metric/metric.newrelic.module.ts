import { Module } from '@nestjs/common';
import { LogicModule } from 'src/logic/logic.module';
import { NewRelicCarMetricService } from './category/car.metric.newrelic.service';
import { NewRelicRentalMetricService } from './category/rental.metric.newrelic.service';
import { NewRelicReservationMetricService } from './category/reservation.metric.newrelic.service';
import { NewRelicCountMetricCacheService } from './count.metric-cache.newrelic.service';
import { NewRelicMetricSenderService } from './metric-sender.newrelic.service';

@Module({
  imports: [LogicModule],
  providers: [
    NewRelicReservationMetricService,
    NewRelicCarMetricService,
    NewRelicRentalMetricService,
    NewRelicMetricSenderService,
    NewRelicCountMetricCacheService,
  ],
  exports: [
    NewRelicReservationMetricService,
    NewRelicCarMetricService,
    NewRelicRentalMetricService,
    NewRelicMetricSenderService,
    NewRelicCountMetricCacheService,
  ],
})
export class NewRelicMetricModule {}
