import { NewRelicMetricType } from './metric-type.newrelic.type';

export type NewRelicMetricDataPoint<
  T extends NewRelicMetricType = NewRelicMetricType,
  A extends object = object,
> = {
  name: string;
  type: T;
  value: T extends NewRelicMetricType.Summary
    ? { count: number; sum: number; min: number; max: number }
    : number;
  timestamp?: number;
  'interval.ms'?: number;
  attributes: A;
};
