import { Inject, Injectable, Logger } from '@nestjs/common';
import { DateTime } from 'luxon';
import { bigNumberize } from 'src/common/utils/big-decimal.util';
import { config } from 'src/config';
import { Url } from 'src/logic/url';
import { NoRateLimitHttpAdapterService } from '~shared/no-rate-limit-http/no-rate-limit-http-adapter.service';
import { NO_RATE_LIMIT_SERVICE_TOKEN } from '~shared/no-rate-limit-http/no-rate-limit-http.module';
import { NewRelicBaseService } from '../base/base.newrelic.service';
import { NewRelicCountMetricCacheService } from './count.metric-cache.newrelic.service';
import { NewRelicCommonAttributes } from './types/metric-common-attributes.newrelic.type';
import { NewRelicMetricCreationRequest } from './types/metric-creation-request.newrelic.type';
import { NewRelicMetricDataPoint } from './types/metric-data-point.newrelic.type';
import { NewRelicMetricType } from './types/metric-type.newrelic.type';

const COMMON_ATTRIBUTES: NewRelicCommonAttributes = {
  attributes: {
    env: process.env.NODE_ENV,
  },
};

@Injectable()
export class NewRelicMetricSenderService extends NewRelicBaseService {
  constructor(
    @Inject(NO_RATE_LIMIT_SERVICE_TOKEN)
    private readonly noRateLimitedHttpAdapterService: NoRateLimitHttpAdapterService,
    protected readonly countMetricCacheService: NewRelicCountMetricCacheService,
  ) {
    super(noRateLimitedHttpAdapterService);

    this.setTargetApiEndpoint(
      new Url(
        `${config.newRelicConfig.metric.url}/${config.newRelicConfig.metric.version}`,
      ),
    );
  }

  async sendMetrics(
    metrics: NewRelicMetricDataPoint[],
    commonAttributes?: NewRelicCommonAttributes,
  ): Promise<boolean> {
    try {
      await this.post<{
        requestId: string;
      }>({
        body: [
          {
            common: commonAttributes || COMMON_ATTRIBUTES,
            metrics,
          } as NewRelicMetricCreationRequest,
        ],
        expectedDtoClass: null,
      });

      return true;
    } catch (err) {
      Logger.error('NewRelic Metric: Failed to send metrics', {
        errorDetails: err,
      });

      return false;
    }
  }

  async sendCount(
    metricName: string,
    value: number,
    intervalInMs: number,
    dimensions?: Record<string, unknown>,
  ): Promise<void> {
    const returnedPayload = await this.countMetricCacheService.setCount(
      {
        key: metricName,
        data: value,
        intervalInSeconds: bigNumberize(intervalInMs)
          .dividedBy(1000)
          .toNumber(),
        startTimestamp: DateTime.now().toMillis(),
      },
      dimensions,
    );

    await this.sendMetrics(
      [
        {
          name: metricName,
          type: NewRelicMetricType.Count,
          value: returnedPayload.data,
          timestamp: returnedPayload.startTimestamp,
          'interval.ms': bigNumberize(returnedPayload.intervalInSeconds)
            .multipliedBy(1000)
            .toNumber(),
          attributes: dimensions,
        },
      ],
      COMMON_ATTRIBUTES,
    );
  }

  async sendGauge(
    metricName: string,
    value: number,
    dimensions?: Record<string, unknown>,
  ): Promise<void> {
    await this.sendMetrics(
      [
        {
          name: metricName,
          type: NewRelicMetricType.Gauge,
          value,
          timestamp: DateTime.now().toMillis(),
          attributes: dimensions,
        },
      ],
      COMMON_ATTRIBUTES,
    );
  }

  private prepareGaugeMetric(
    metricName: string,
    value: number,
    dimensions?: Record<string, unknown>,
    timestamp?: number,
  ): NewRelicMetricDataPoint {
    return {
      name: metricName,
      type: NewRelicMetricType.Gauge,
      value,
      timestamp: timestamp || DateTime.now().toMillis(),
      attributes: dimensions,
    };
  }

  bulkSendGauge<T extends { count: number }>(
    metricName: string,
    dataPoints: T[],
  ): Promise<boolean> {
    const timestamp = DateTime.now().toMillis();

    const summaryCount: number = dataPoints.reduce((prev, curr) => {
      return prev + curr.count;
    }, 0);

    const gaugeDataPoints: NewRelicMetricDataPoint[] = dataPoints.map(
      (dtp: T) => {
        const count = dtp.count;

        delete dtp.count;

        return this.prepareGaugeMetric(
          metricName,
          count,
          { ...dtp },
          timestamp,
        );
      },
    );

    gaugeDataPoints.push(
      this.prepareGaugeMetric(
        metricName,
        summaryCount,
        { summary: true },
        timestamp,
      ),
    );

    return this.sendMetrics(gaugeDataPoints);
  }
}
