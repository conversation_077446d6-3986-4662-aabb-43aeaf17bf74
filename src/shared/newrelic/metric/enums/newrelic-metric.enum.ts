export enum NewRelicMetricRentalType {
  Subscription = 'subscription',
  RentalPackage = 'rental_package',
}

export enum NewRelicMetricCarModel {
  Bluecar = 'bluecar',
  CorsaE = 'corsa_e',
  NoCarModel = 'NO_CAR_MODEL',
}

export enum NewRelicMetricTrigger {
  Mobile = 'mobile',
  Admin = 'admin',
  RFID = 'rfid',
}

export enum NewRelicMetricStepInFlow {
  LockAndImmobilize = 'lock_and_immobilize',
  UnlockAndMobilize = 'unlock_and_mobilize',
  AllocateReservation = 'allocate_reservation',
  CancelReservation = 'cancel_reservation',
  SwitchCar = 'switch_car',
  StartRental = 'start_rental',
  EndRental = 'end_rental',
  TerminateTrip = 'terminate_trip',
  DisableCar = 'disable_car',
  StationAttachment = 'station_attachment',
  StationDetachment = 'station_detachment',
}

export enum NewRelicMetricService {
  CustomerTrip = 'customer_trip',
  ServiceTrip = 'service_trip',
  OutOfService = 'OUT_OF_SERVICE',
}

export enum NewRelicMetricStation {
  OutOfStation = 'OUT_OF_STATION',
}

export enum NewRelicMetricCarConnectionProblemOrigin {
  VulogAiMA = 'vulog_aima',
  VulogVehicleGateway = 'vulog_vehicle_gateway',
  Unknown = 'UNKNOWN',
}
