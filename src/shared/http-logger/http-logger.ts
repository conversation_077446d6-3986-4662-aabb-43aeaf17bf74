import { HttpService } from '@nestjs/axios';
import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import {
  AxiosInstance,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';

const muteUrls = [
  'https://java-eu01.vulog.com/boapi/proxy/vehicle/fleets/BLUESG-SINGA/vehicles',
  'https://java-eu01.vulog.com/boapi/proxy/vehicle/fleets/BLUESG-SINGA/vehicles',
  '/fleetmanager/public/fleets/BLUESG-SINGA/vehicles',
  '/api/v1/stations',
  '/api/v1/availability',
  'v2/fleets/BLUESG-SINGA/vehicles',
];

const excludeUrls = [];

export interface LogHttpData {
  url: string;

  method: string;

  statusCode: HttpStatus;

  body?: unknown;
}

@Injectable()
export class HttpLogger {
  private beautifyAndTransformData(
    data: Partial<LogHttpData>,
    apiBelonging: string,
  ): unknown {
    return {
      ...data,
      method: data.method?.toUpperCase(),
      origin: apiBelonging,
    };
  }

  public setupAxiosInterceptors(
    httpService: HttpService,
    errorLogging = false,
  ) {
    httpService.axiosRef.interceptors.request.use(
      function (config: InternalAxiosRequestConfig) {
        this.logRequest(
          {
            url: config.url,
            method: config.method,
            body: config.data,
          },
          'THIRD-PARTY',
        );

        return config;
      }.bind(this),
      function (error) {
        errorLogging &&
          Logger.error(
            `Axios Request Interceptor - Error: ${JSON.stringify(error)}`,
          );

        // Do something with request error
        return Promise.reject(error);
      }.bind(this),
    );

    httpService.axiosRef.interceptors.response.use(
      function (response: AxiosResponse) {
        const excludedFromMutingUrl = excludeUrls.find((url) =>
          response.config.url.includes(url),
        );

        this.logResponse(
          {
            url: response.config.url,
            method: response.config.method,
            body:
              !excludedFromMutingUrl &&
              muteUrls.find((url) => response.config.url.includes(url))
                ? '<TOO LONG TO DISPLAY>'
                : response.data,
            statusCode: response.status,
          },
          'THIRD-PARTY',
        );

        return response;
      }.bind(this),
      function (error) {
        errorLogging &&
          Logger.error(
            `Axios Response Interceptor - Error: ${JSON.stringify(error)}`,
          );

        // Any status codes that falls outside the range of 2xx cause this function to trigger
        // Do something with response error
        return Promise.reject(error);
      }.bind(this),
    );
  }

  public setupAxiosInterceptorsWithAxiosInstance(
    axiosRef: AxiosInstance,
    errorLogging = false,
    noRateLimit = false,
  ) {
    axiosRef.interceptors.request.use(
      function (config: InternalAxiosRequestConfig) {
        noRateLimit && Logger.log(`No Rate limit for ${config.url}!`);

        this.logRequest(
          {
            url: config.url,
            method: config.method,
            body: config.data,
          },
          'THIRD-PARTY',
        );

        return config;
      }.bind(this),
      function (error) {
        errorLogging &&
          Logger.error(
            `Axios Request Interceptor - Error: ${JSON.stringify(error)}`,
          );

        // Do something with request error
        return Promise.reject(error);
      }.bind(this),
    );

    axiosRef.interceptors.response.use(
      function (response: AxiosResponse) {
        const excludedFromMutingUrl = excludeUrls.find((url) =>
          response.config.url.includes(url),
        );

        this.logResponse(
          {
            url: response.config.url,
            method: response.config.method,
            body:
              !excludedFromMutingUrl &&
              muteUrls.find((url) => response.config.url.includes(url))
                ? '<TOO LONG TO DISPLAY>'
                : response.data,
            statusCode: response.status,
          },
          'THIRD-PARTY',
        );

        return response;
      }.bind(this),
      function (error) {
        errorLogging &&
          Logger.error(
            `Axios Response Interceptor - Error: ${JSON.stringify(error)}`,
          );

        // Any status codes that falls outside the range of 2xx cause this function to trigger
        // Do something with response error
        return Promise.reject(error);
      }.bind(this),
    );
  }

  logRequest(data: Omit<LogHttpData, 'statusCode'>, apiBelonging = 'US') {
    Logger.log(
      `Incoming Request ${data.method.toUpperCase()} ${data.url}`,
      this.beautifyAndTransformData(data, apiBelonging),
    );
  }

  logResponse(data: LogHttpData, apiBelonging = 'US') {
    Logger.log(
      `Outgoing Response ${data.method.toUpperCase()} ${data.url}`,
      this.beautifyAndTransformData(data, apiBelonging),
    );
  }
}
