import { HttpModule as BuiltInHttpModule } from '@nestjs/axios';
import { DynamicModule, Global, Module } from '@nestjs/common';
import { NoRateLimitHttpAdapterService } from '~shared/no-rate-limit-http/no-rate-limit-http-adapter.service';

import { InternalHttpModuleOptions } from '~shared/http/interface/http-module.interface';

export const NO_RATE_LIMIT_SERVICE_TOKEN = 'NO_RATE_LIMIT_SERVICE';

@Global()
@Module({})
export class NoRateLimitHttpModule {
  public static register(
    httpModuleOptions: InternalHttpModuleOptions,
  ): DynamicModule {
    return {
      module: NoRateLimitHttpModule,
      imports: [
        BuiltInHttpModule.register({
          timeout: httpModuleOptions.timeout,
          baseURL: httpModuleOptions.baseURL,
        }),
      ],
      providers: [
        {
          provide: httpModuleOptions.serviceName,
          useClass: NoRateLimitHttpAdapterService,
        },
      ],
      exports: [httpModuleOptions.serviceName],
    };
  }
}
