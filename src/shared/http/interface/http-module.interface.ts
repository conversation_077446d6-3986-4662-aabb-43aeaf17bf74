import { HttpModuleAsyncOptions, HttpModuleOptions } from '@nestjs/axios';
import { DynamicModule, ForwardReference, Type } from '@nestjs/common';

export type InternalHttpModuleOptions = Pick<
  HttpModuleOptions,
  'baseURL' | 'timeout'
> & { serviceName?: string };

export type InternalHttpModuleAsyncOptions = Pick<
  HttpModuleAsyncOptions,
  'inject' | 'useFactory'
> & {
  serviceName: string;
  imports?: Array<
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    Type<any> | DynamicModule | Promise<DynamicModule> | ForwardReference
  >;
};
