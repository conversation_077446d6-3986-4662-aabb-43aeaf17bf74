import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { catchError, map, Observable, of } from 'rxjs';

import { AUTHORIZATION_HEADER } from '@bluesg-2/customer-authentication/dist/guards/customer-jwt.guard';
import { CorrelationIdService } from '@bluesg-2/monitoring';
import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { CORRELATION_ID_HEADER } from 'src/common/constants/http-headers';
import { InternalMicroserviceExceptionError } from 'src/common/errors/http-exception-error';
import { ApiServiceConfig } from 'src/shared/http/types';
import { HttpLogger } from '../http-logger/http-logger';

/**
 * Currently we don't have time to write a Node package to control error handling consistency.
 * So this is temporary way to get all fields regarding error
 */
export type AggregateErrorResponse<T> = BaseResponseDto<T> & {
  error?: string; // Returned from STS
  errorCode?: string; // Returned from STS
  details?: object; // Returned from STS
};

@Injectable()
export class InternalApiService {
  constructor(
    public readonly httpService: HttpService,
    private readonly httpLogger: HttpLogger,
    private readonly correlationIdService: CorrelationIdService,
    private readonly clsService: ClsService,
  ) {
    this.httpLogger.setupAxiosInterceptors(this.httpService);
  }

  get<T = unknown>(
    url: string,
    config: ApiServiceConfig = {},
  ): Promise<BaseResponseDto<T>> {
    const axiosConfig = this.setup(config);

    return this.transformHttpServiceResponse(
      this.httpService.get<BaseResponseDto<T>>(url, axiosConfig),
    );
  }

  post<T = unknown>(
    url: string,
    data?: unknown,
    config: ApiServiceConfig = {},
  ): Promise<BaseResponseDto<T>> {
    const axiosConfig = this.setup(config);

    return this.transformHttpServiceResponse(
      this.httpService.post<BaseResponseDto<T>>(url, data, axiosConfig),
    );
  }

  put<T = unknown>(
    url: string,
    data?: unknown,
    config: ApiServiceConfig = {},
  ): Promise<BaseResponseDto<T>> {
    const axiosConfig = this.setup(config);

    return this.transformHttpServiceResponse(
      this.httpService.put<BaseResponseDto<T>>(url, data, axiosConfig),
    );
  }

  patch<T = unknown>(
    url: string,
    data?: unknown,
    config: ApiServiceConfig = {},
  ): Promise<BaseResponseDto<T>> {
    const axiosConfig = this.setup(config);

    return this.transformHttpServiceResponse(
      this.httpService.patch<BaseResponseDto<T>>(url, data, axiosConfig),
    );
  }

  delete<T = unknown>(
    url: string,
    config: ApiServiceConfig = {},
  ): Promise<BaseResponseDto<T>> {
    const axiosConfig = this.setup(config);

    return this.transformHttpServiceResponse(
      this.httpService.delete<BaseResponseDto<T>>(url, axiosConfig),
    );
  }

  private transformHttpServiceResponse = <T>(
    observable: Observable<AxiosResponse<BaseResponseDto<T>>>,
  ): Promise<BaseResponseDto<T>> =>
    new Promise((resolve, reject) => {
      observable
        .pipe(
          map((response): BaseResponseDto<T> => {
            const { data, status } = response;

            return {
              ...data,
              responseCode: status,
            };
          }),
          catchError((err): Observable<AggregateErrorResponse<T>> => {
            Logger.error('[InternalApiService] Error Caught: ', err);

            const errorResponse = err.response?.data;
            const errorStatus = err.response?.status;

            return of({
              message: errorResponse?.message
                ? errorResponse?.message
                : 'Error from Axios or unknown error response format',
              responseCode: errorStatus ? errorStatus : '',
              success: false,
              error: errorResponse?.error ? errorResponse.error : err.code,
              errorCode: errorResponse?.errorCode
                ? errorResponse.errorCode
                : '',
              details: errorResponse?.details
                ? errorResponse.details
                : 'Error from Axios or unknown error response format',
            } as AggregateErrorResponse<T>);
          }),
        )
        .subscribe((response) => {
          if (response.success) {
            resolve(response);
          }

          const errorResponse: AggregateErrorResponse<T> = response;

          reject(
            new InternalMicroserviceExceptionError({
              statusCode: errorResponse.responseCode,
              error: errorResponse.error,
              errorCode: errorResponse.errorCode,
              message: errorResponse.message,
              details: errorResponse.details,
            }),
          );
        });
    });

  private setup(config: ApiServiceConfig = {}): AxiosRequestConfig {
    const authorizationHeaderValue = this.clsService.get(AUTHORIZATION_HEADER);

    const axiosConfig: AxiosRequestConfig = config?.axiosConfig || {};

    axiosConfig.headers = {
      ...axiosConfig.headers,
      [CORRELATION_ID_HEADER]:
        this.correlationIdService.getCorrelationId()?.value,
    };

    const token = authorizationHeaderValue;

    if (!config.omitAuthorization && token) {
      axiosConfig.headers.authorization = token;
    }

    return axiosConfig;
  }
}
