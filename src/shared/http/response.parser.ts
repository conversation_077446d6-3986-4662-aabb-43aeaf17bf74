import { AxiosError } from 'axios';

import {
  BadRequestException,
  ForbiddenException,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnauthorizedException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { ApiResponse } from 'src/api/dtos/api-response.dto';
import { ExternalError } from 'src/common/errors/external/external.error';

export class ResponseParser {
  constructor(private readonly serviceName: string) {}

  public parseError(err: AxiosError<ApiResponse>): Error {
    Logger.debug(`API Error: ${err.message}`, {
      error: err.message,
      type: err.name,
      cause: err.cause,
      response: {
        status: err.response?.status,
        data: err.response?.data,
      },
    });

    if (!err.response) return new InternalServerErrorException(err.message);

    const { message } = err.response.data;

    switch (err?.response?.status) {
      case 400:
        return new BadRequestException(message);
      case 401:
        return new UnauthorizedException(message);
      case 403:
        return new ForbiddenException(message);
      case 404:
        return new NotFoundException(message);
      case 422:
        return new UnprocessableEntityException(message);
      case 500:
        return new InternalServerErrorException(message);
      default:
        return new ExternalError(
          this.serviceName,
          (Array.isArray(message) ? message.join(',') : message) ||
            `External Error [${err?.response?.status}] while calling ${this.serviceName} [${err?.request?.path}]: ${err?.message}`,
          err.response.data.correlationId.value,
          err.status,
          err,
        );
    }
  }
}
