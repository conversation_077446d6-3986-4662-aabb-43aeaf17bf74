import { HttpModule as BuiltInHttpModule } from '@nestjs/axios';
import { DynamicModule, Global, Module } from '@nestjs/common';

import { InternalApiService } from 'src/shared/http/internal-api.service';
import {
  InternalHttpModuleAsyncOptions,
  InternalHttpModuleOptions,
} from './interface/http-module.interface';

@Global()
@Module({})
export class HttpModule {
  public static register(
    httpModuleOptions: InternalHttpModuleOptions,
  ): DynamicModule {
    return {
      module: HttpModule,
      imports: [
        BuiltInHttpModule.register({
          timeout: httpModuleOptions.timeout,
          baseURL: httpModuleOptions.baseURL,
        }),
      ],
      providers: [
        {
          provide: httpModuleOptions.serviceName,
          useClass: InternalApiService,
        },
      ],
      exports: [httpModuleOptions.serviceName],
    };
  }

  public static registerAsync(
    options: InternalHttpModuleAsyncOptions,
  ): DynamicModule {
    return {
      module: HttpModule,
      imports: [
        BuiltInHttpModule.registerAsync({
          useFactory: options.useFactory,
          inject: options.inject,
        }),
        ...options.imports,
      ],
      providers: [
        {
          provide: options.serviceName,
          useClass: InternalApiService,
        },
      ],
      exports: [options.serviceName],
    };
  }
}
