import { SqsEventClient } from '@bluesg-2/sqs-event';
import { DynamicModule, Global, Module } from '@nestjs/common';
import { QueueClientLogger } from '~shared/queue-client/queue-client.logger';
import { QueueClientService } from '~shared/queue-client/queue-client.service';
import { QueueClientConfigModuleOptions } from '~shared/queue-client/types';

@Global()
@Module({})
export class QueueClientModule {
  public static registerAsync(
    options: QueueClientConfigModuleOptions,
  ): DynamicModule {
    return {
      module: QueueClientModule,
      providers: [
        {
          provide: options.queueName,
          useClass: QueueClientService,
        },
        {
          provide: QueueClientLogger,
          useValue: new QueueClientLogger(options.queueName),
        },
        {
          provide: SqsEventClient,
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          useValue: new SqsEventClient({
            region: options.sqsConfig.region,
            queueUrl: options.sqsConfig.queueUrl,
          }),
        },
      ],
      exports: [options.queueName],
    };
  }
}
