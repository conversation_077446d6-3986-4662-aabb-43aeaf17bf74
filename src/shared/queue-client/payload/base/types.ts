import { Event } from '@bluesg-2/sqs-event';

export class BaseEvent<T = unknown> extends Event<T> {
  constructor(props) {
    super({
      ...props,
      event: null,
      payload: null,
    });

    Object.assign(this, props);
  }

  event = null; // Intentionally marked as `null` because when we send the event, we don't need it from client

  payload: T = null; // Intentionally marked as `null` because the way `sqs-event` package handles
}
