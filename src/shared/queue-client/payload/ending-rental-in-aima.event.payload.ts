import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { BaseEvent } from './base/types';

export class EndRentalInAiMAEventPayload extends BaseEvent {
  constructor(props?: Partial<EndRentalInAiMAEventPayload>) {
    super(props);

    Object.assign(this, props);
  }

  drivingStarted: boolean;

  rentalInfo: {
    reservationId: string;
    vulogTripId: string;
    startedAt: string;
    endedAt: string;
    billedDuration: number;
    billedAmount: number;
    local: boolean;
  };

  userInfo: {
    bsgUserId: string;
    vulogUserId: string;
  };

  carInfo: {
    carId: string;
    vulogCarId: string;
    model: CarModelEnum;
    plate: string;
  };

  startStationInfo: {
    stationId: string;
    zoneId: string;
    stationName: string;
  };

  endStationInfo?: {
    stationId: string;
    zoneId: string;
    stationName: string;
  };
}
