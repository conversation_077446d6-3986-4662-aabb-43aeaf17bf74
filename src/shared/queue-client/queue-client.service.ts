import { SqsEventClient } from '@bluesg-2/sqs-event';
import { Injectable } from '@nestjs/common';
import { ReadPacket } from '@nestjs/microservices';
import { QueueClientLogger } from '~shared/queue-client/queue-client.logger';

@Injectable()
export class QueueClientService {
  constructor(
    private readonly sqsClient: SqsEventClient,
    private readonly queueClientLogger: QueueClientLogger,
  ) {
    this.queueClientLogger.log('Starting `QueueClientService`...');

    try {
      this.sqsClient.connect();
    } catch (err) {
      this.queueClientLogger.error(
        `Failed to connect to message broker, detail: ${err}`,
      );
    }
  }

  /**
   * Publish a message to message broker
   * based on request-response message-based style
   */
  public send(packet: ReadPacket): void {
    try {
      this.queueClientLogger.log(packet);

      // `sqs-event.client.ts` of `@bluesg-2/sqs-event` should expose and accept also `sender`, `sentAt`, `correlationId`
      this.sqsClient.dispatchEvent(packet);
    } catch (err) {
      this.queueClientLogger.error(
        `Cannot send message to message broker, detail: ${err}`,
      );
    }
  }
}
