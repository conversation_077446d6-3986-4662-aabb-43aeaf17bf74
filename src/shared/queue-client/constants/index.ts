import { config } from 'src/config';

/**
 * Message Pattern for exchanging messages between application.
 *
 * <!> Please note that message pattern should be both uppercase and
 * snake-naming case such as `PAYMENT_METHOD_CREATED`.
 */
export const MessageQueuePattern = Object.freeze({
  EndRentalInAiMA: 'END_RENTAL_IN_AIMA',
});

/**
 * Injection token when registering `QueueClientModule`.
 *
 * The name should be the same on AWS SQS for debugging purpose.
 */
export const MessageQueueName = Object.freeze({
  EndRentalInAiMA: config.sqs.EndRentalInAiMA.name,
});
