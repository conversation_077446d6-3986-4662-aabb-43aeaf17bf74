import { Injectable, Logger } from '@nestjs/common';

/**
 * Please use this logger for queue logging purpose
 */
@Injectable()
export class QueueClientLogger {
  private readonly queueName: string;

  constructor(queueName: string) {
    this.queueName = queueName;
  }

  private stringify(message): string {
    return JSON.stringify(message);
  }

  // eslint-disable-next-line @typescript-eslint/explicit-function-return-type, @typescript-eslint/no-explicit-any
  log(message: any) {
    Logger.log(
      `[Message Queue] [LOG] queue name: ${this.queueName}, ${this.stringify(
        message,
      )}`,
    );
  }

  // eslint-disable-next-line @typescript-eslint/explicit-function-return-type, @typescript-eslint/no-explicit-any
  debug(message: any) {
    Logger.debug(
      `[Message Queue] [DEBUG] queue name: ${this.queueName}, ${this.stringify(
        message,
      )}`,
    );
  }

  // eslint-disable-next-line @typescript-eslint/explicit-function-return-type, @typescript-eslint/no-explicit-any
  error(message: any) {
    Logger.error(
      `[Message Queue] [ERROR] queue name: ${this.queueName}, ${this.stringify(
        message,
      )}`,
    );
  }
}
