import { UserInfo } from '@bluesg-2/customer-authentication';
import { Injectable, Logger } from '@nestjs/common';
import * as configcat from 'configcat-node';
import { FeatureFlagName } from '~shared/feature-flag/feature-flag-name';
import { config } from '../../config';
import { CarInfo } from './models/car-info';
import { StationInfo } from './models/station-info';

const featureFlagMainConfigKey =
  config.featureFlagConfig.configCatKey.mainConfig;

const featureFlagMigrationConfigKey =
  config.featureFlagConfig.configCatKey.migrationConfig;

const featureFlagReleaseConfigKey =
  config.featureFlagConfig.configCatKey.releaseConfig;

@Injectable()
export class FeatureFlagService {
  private mainConfigCatClient = configcat.getClient(
    featureFlagMainConfigKey,
    configcat.PollingMode.AutoPoll,
    {
      logger: Logger,
    },
  );

  private migrationConfigCatClient = configcat.getClient(
    featureFlagMigrationConfigKey,
    configcat.PollingMode.AutoPoll,
    {
      logger: Logger,
    },
  );

  private releaseConfigCatClient = configcat.getClient(
    featureFlagReleaseConfigKey,
    configcat.PollingMode.AutoPoll,
    {
      logger: Logger,
    },
  );

  async unlockDoorsWhenStartRental(user: UserInfo): Promise<boolean> {
    return this.isOn(
      this.migrationConfigCatClient,
      FeatureFlagName.UNLOCK_DOORS_WHEN_START_RENTAL,
      false,
      this.buildUser(user),
    );
  }

  async isFinishRentalEnabled(user: UserInfo): Promise<boolean> {
    return this.isOn(
      this.migrationConfigCatClient,
      FeatureFlagName.ENABLE_FINISH_RENTAL,
      false,
      this.buildUser(user),
    );
  }

  async isQrEndRentalDisabled(id: string): Promise<boolean> {
    // for charge-less, id example: BSG-<postal_code>-<lot>
    // for total energies, id example: BE*BEC*E041504007
    return this.isOn(
      this.migrationConfigCatClient,
      FeatureFlagName.DISABLE_QR_CODE_RENTAL_END_AT_EVSE,
      false,
      id ? this.buildUserFromEvseId(id) : null,
    );
  }

  async isChargelessEnabled(): Promise<boolean> {
    return this.isOn(
      this.migrationConfigCatClient,
      FeatureFlagName.ENABLE_CHARGELESS,
      false,
    );
  }

  async disableCarIfMissingEndStationWhenEndRental(
    car: CarInfo,
  ): Promise<boolean> {
    return this.isOn(
      this.mainConfigCatClient,
      FeatureFlagName.DISABLE_CAR_IF_MISSING_END_STATION_WHEN_END_RENTAL,
      false,
      this.buildCar(car),
    );
  }

  async stickStationWithCarInRentalFlow(car: CarInfo): Promise<boolean> {
    return this.isOn(
      this.mainConfigCatClient,
      FeatureFlagName.STICK_STATION_WITH_CAR_IN_RENTAL_FLOW,
      false,
      this.buildCar(car),
    );
  }

  async shouldCancelReservationAfterBestEffort(car: CarInfo): Promise<boolean> {
    return this.isOn(
      this.mainConfigCatClient,
      FeatureFlagName.SHOULD_CANCEL_RESERVATION_AFTER_BEST_EFFORT,
      false,
      this.buildCar(car),
    );
  }

  async disableRfidUsageOfCustomer(user: UserInfo): Promise<boolean> {
    return this.isOn(
      this.mainConfigCatClient,
      FeatureFlagName.DISABLE_RFID_USAGE_OF_CUSTOMER,
      false,
      this.buildUser(user),
    );
  }

  async filterOutNonCustomerZones(car?: CarInfo): Promise<boolean> {
    return this.isOn(
      this.mainConfigCatClient,
      FeatureFlagName.FILTER_OUT_NON_CUSTOMER_ZONES,
      false,
      car ? this.buildCar(car) : null,
    );
  }

  async startRentalAutoUnlocked(car?: CarInfo): Promise<boolean> {
    return this.isOn(
      this.mainConfigCatClient,
      FeatureFlagName.START_RENTAL_AUTO_UNLOCKED,
      false,
      car ? this.buildCar(car) : null,
    );
  }

  async enforceQRStationSameAsEndingStation(
    station?: StationInfo,
  ): Promise<boolean> {
    return this.isOn(
      this.mainConfigCatClient,
      FeatureFlagName.EndingRental_WithQrCode_Enable_QRStationSameAsEndingStation,
      false,
      station ? this.buildStation(station) : null,
    );
  }
  async isRequireLockCarWhenEndRental(car?: CarInfo): Promise<boolean> {
    return this.isOn(
      this.mainConfigCatClient,
      FeatureFlagName.REQUIRE_LOCK_CAR_WHEN_END_RENTAL,
      true,
      car ? this.buildCar(car) : null,
    );
  }

  async isEnableCsCarAvailability(user?: UserInfo): Promise<boolean> {
    return this.isOn(
      this.releaseConfigCatClient,
      FeatureFlagName.ENABLE_CS_CAR_AVAILABILITY,
      false,
      user ? this.buildUser(user) : null,
    );
  }

  async shouldUpdateAmountFromBS(): Promise<boolean> {
    return this.isOn(
      this.mainConfigCatClient,
      FeatureFlagName.SHOULD_UPDATE_AMOUNT_FROM_BS,
      true,
    );
  }

  private async isOn(
    configCatClient: configcat.IConfigCatClient,
    flag: FeatureFlagName,
    defaultValue: boolean,
    configCatUser?: configcat.User,
  ): Promise<boolean> {
    return await configCatClient.getValueAsync(
      flag,
      defaultValue,
      configCatUser || null,
    );
  }

  private buildStation(stationInfo: StationInfo): configcat.User {
    const configCatUserInfo: configcat.User = this.buildUser(
      new UserInfo(stationInfo.sourceId, null, null),
    );

    configCatUserInfo.custom = JSON.parse(JSON.stringify(stationInfo));

    return configCatUserInfo;
  }

  private buildCar(carInfo: CarInfo): configcat.User {
    const configCatUserInfo: configcat.User = this.buildUser(
      new UserInfo(carInfo.vulogId, null, null),
    );

    configCatUserInfo.custom = JSON.parse(JSON.stringify(carInfo));

    return configCatUserInfo;
  }

  private buildUser(user: UserInfo): configcat.User {
    return new configcat.User(user.id, user.email, 'Singapore', {});
  }

  private buildUserFromEvseId(evseId: string): configcat.User {
    const configCatUserInfo: configcat.User = this.buildUser(
      new UserInfo(evseId, null, null),
    );
    return configCatUserInfo;
  }
}
