import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { CarId } from 'src/common/tiny-types';
import { Car } from 'src/logic/car/domain/car';
import { VulogFleetServiceType } from 'src/model/dtos/vulog-fleet-service/vulog-fleet-service.dto';
import { RealtimeCar } from 'src/model/realtime.car';
import { StaticCar } from 'src/model/static.car';

export class CarInfo {
  constructor(props?: CarInfo) {
    Object.assign(this, props);
  }

  bsgId?: string;

  vulogId: string;

  plateNumber: string;

  vin: string;

  model: CarModelEnum;

  serviceId?: string;

  serviceType?: VulogFleetServiceType;

  static fromRealtimeCar(
    rltCar: Omit<RealtimeCar, 'isAllowedToAttachPackage' | 'toCar'> & {
      bsgCarId?: CarId;
    },
  ) {
    return new CarInfo({
      bsgId: rltCar.bsgCarId ? rltCar.bsgCarId.value : null,
      vulogId: rltCar.id.value,
      plateNumber: rltCar.plate?.value,
      model: rltCar.model?.value as CarModelEnum,
      vin: rltCar.vin,
      serviceId: rltCar.serviceId ? rltCar.serviceId.value : null,
      serviceType: rltCar.serviceType ? rltCar.serviceType : null,
    });
  }

  static fromCar(car: Car) {
    return new CarInfo({
      bsgId: car.id ? car.id.value : null,
      vulogId: car.vulogId?.value,
      plateNumber: car.plate?.value,
      model: car.model,
      vin: car.vin,
      serviceId: null,
      serviceType: null,
    });
  }

  static fromStaticCar(staticCar: StaticCar) {
    return new CarInfo({
      bsgId: null,
      vulogId: staticCar.id.value,
      plateNumber: staticCar.plate?.value,
      model: staticCar.model?.value as CarModelEnum,
      vin: staticCar.vin,
      serviceId: staticCar.serviceId?.value,
      serviceType: staticCar?.serviceType,
    });
  }
}
