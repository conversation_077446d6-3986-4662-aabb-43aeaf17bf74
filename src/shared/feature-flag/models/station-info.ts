import { ProviderName } from 'src/external/station/dtos/response/internal.station-response.v1.response.dto';
import { StationStatus } from 'src/external/station/dtos/station-status';
import { Station } from 'src/model/station';

export class StationInfo {
  constructor(props?: Omit<StationInfo, 'fromStation'>) {
    Object.assign(this, props);
  }

  bsgId: string;

  sourceId: string;

  source: ProviderName;

  displayName: string;

  street: string;

  postalCode: string;

  city: string;

  zoneId?: string;

  status: StationStatus;

  evseUids: string; // Strings joined together with token ","

  openForPublic: boolean;

  static fromStation(station: Station): StationInfo {
    return new StationInfo({
      bsgId: station.id.value,
      sourceId: station.sourceId,
      source: null,
      displayName: station.displayName,
      street: station.street,
      postalCode: station.postalCode,
      city: null,
      zoneId: station.zoneId,
      status: station.status,
      evseUids: null,
      openForPublic: null,
    });
  }
}
