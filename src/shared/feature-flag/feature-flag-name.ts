export enum FeatureFlagName {
  UNLOCK_DOORS_WHEN_START_RENTAL = 'unlockDoorsWhenStartRental',
  ENABLE_FINISH_RENTAL = 'enablefinishrental',
  DISABLE_QR_CODE_RENTAL_END_AT_EVSE = 'disableQrCodeRentalEndAtEvse',
  ENABLE_CHARGELESS = 'enableChargeless',
  DISABLE_CAR_IF_MISSING_END_STATION_WHEN_END_RENTAL = 'disableCarIfMissingEndStationWhenEndRental',
  STICK_STATION_WITH_CAR_IN_RENTAL_FLOW = 'stickStationWithCarInRentalFlow',
  SHOULD_CANCEL_RESERVATION_AFTER_BEST_EFFORT = 'shouldCancelReservationAfterBestEffort',
  DISABLE_RFID_USAGE_OF_CUSTOMER = 'disableRfidUsageOfCustomer',
  FILTER_OUT_NON_CUSTOMER_ZONES = 'filterOutNonCustomerZones',
  START_RENTAL_AUTO_UNLOCKED = 'startRentalAutoUnlocked',
  EndingRental_WithQrCode_Enable_QRStationSameAsEndingStation = 'EndingRental_WithQrCode_Enable_QRStationSameAsEndingStation',
  REQUIRE_LOCK_CAR_WHEN_END_RENTAL = 'requireLockCarWhenEndRental',
  SHOULD_UPDATE_AMOUNT_FROM_BS = 'shouldUpdateAmountFromBS',
  ENABLE_CS_CAR_AVAILABILITY = 'enableCsCarAvailability',
}
