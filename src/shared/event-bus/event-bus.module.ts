import { SnsEventModule } from '@bluesg-2/sqs-event';
import { Global, Module } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';

import { eventBusConfig } from 'src/config/event-bus.config';
import { EventBusService } from './event-bus.service';

@Global()
@Module({
  imports: [
    SnsEventModule.registerAsync({
      useFactory: (eventBusConfiguration: ConfigType<typeof eventBusConfig>) =>
        eventBusConfiguration,
      inject: [eventBusConfig.KEY],
    }),
  ],
  providers: [EventBusService],
  exports: [EventBusService],
})
export class EventBusModule {}
