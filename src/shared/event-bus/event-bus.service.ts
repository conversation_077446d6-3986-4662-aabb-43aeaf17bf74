import { EventProps, SnsEventPublisher } from '@bluesg-2/sqs-event';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class EventBusService {
  constructor(private readonly snsPublisher: SnsEventPublisher) {}

  async emit<T>(event: EventProps<T>): Promise<void> {
    await this.snsPublisher.emit(event);
  }

  nonBlockingEmit<T>(event: EventProps<T>): void {
    this.emit(event).catch((err) =>
      Logger.error(`Error while sending an event: ${err}`),
    );
  }
}
