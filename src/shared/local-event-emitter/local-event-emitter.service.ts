import { EventProps } from '@bluesg-2/sqs-event';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { DateTime } from 'luxon';
import { localEventPattern } from 'src/common/constants/event';
import { inspect } from 'util';
import { LocalEventHandlerExceptionEvent } from './events/local-event-handler-exception.event';

@Injectable()
export class LocalEventEmitterService {
  private isInit = true;

  constructor(private readonly eventEmitter: EventEmitter2) {}

  // NOTE: Still bug, comment for now
  // onModuleInit() {
  //   // NOTE: Register event handler for error event
  //   this.eventEmitter.on(
  //     localEventPattern.error.index,
  //     this.catchError.bind(this),
  //   );
  // }

  /**
   * Handle any error emitted by `EventEmitter2`
   * @param localEventErr
   */
  public catchError(localEventErr: LocalEventHandlerExceptionEvent): void {
    Logger.error(
      `[LOCAL] [EVENT] [ERROR] Correlation ID: ${
        localEventErr.correlationId
      }, error: ${inspect(localEventErr.payload)}
      )}`,
    );

    // NOTE: save error to database
  }

  throwError(error: Error, correlationId?: string): void {
    this.eventEmitter.emit(
      localEventPattern.error.index,
      new LocalEventHandlerExceptionEvent(error, correlationId),
    );
  }

  public getEventEmitter2(): EventEmitter2 {
    return this.eventEmitter;
  }

  public dispatchEvent(event: EventProps<unknown>): void {
    const {
      event: eventPattern,
      payload,
      correlationId,
      sentAt: sentAtNumber,
    } = event;

    // Not work with @OnModuleInit, try this
    if (this.isInit) {
      this.eventEmitter.on(localEventPattern.error.index, this.catchError);

      this.isInit = false;
    }

    const sentAt = DateTime.fromSeconds(sentAtNumber);

    try {
      this.eventEmitter.emit(eventPattern, payload);

      Logger.log(
        `[LOCAL] [EVENT] [SUCCESS], info: ${inspect({
          payload,
          sentAt: sentAt.toISO(),
          correlationId,
          eventPattern,
        })}`,
      );
    } catch (err) {
      Logger.error(
        `[LOCAL] [EVENT] [FAILED], info: ${inspect({
          payload,
          sentAt: sentAt.toISO(),
          correlationId,
          eventPattern,
        })}`,
      );
    }
  }
}
