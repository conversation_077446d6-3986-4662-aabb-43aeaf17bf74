import { Global, Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { LOCAL_EVENT_EMITTER_TOKEN } from '~shared/local-event-emitter/constants';
import { LocalEventEmitterService } from '~shared/local-event-emitter/local-event-emitter.service';

@Global()
@Module({
  imports: [
    EventEmitterModule.forRoot({
      global: true,
      wildcard: false,
    }),
  ],
  providers: [
    {
      provide: LOCAL_EVENT_EMITTER_TOKEN,
      useClass: LocalEventEmitterService,
    },
  ],
  exports: [LOCAL_EVENT_EMITTER_TOKEN],
})
export class LocalEventEmitterModule {}
