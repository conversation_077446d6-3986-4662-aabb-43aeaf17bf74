/* eslint-disable no-underscore-dangle */
import { cloneDeep } from 'lodash';
import { DateTime } from 'luxon';
import { DataNotAllowedError } from 'src/common/errors/data-not-allowed.error';
import { UUID } from 'src/common/tiny-types/uuid.type';
import { DomainShield } from '../helper/domain.shield';

export interface BaseDomainProps {
  id: UUID;

  createdAt: DateTime;

  modifiedAt: DateTime;

  deletedAt?: DateTime;

  createdBy?: string;

  modifiedBy?: string;
}

export interface CreateDomainProps<T> {
  id?: UUID;

  props: T;

  createdAt?: DateTime;

  modifiedAt?: DateTime;

  deletedAt?: DateTime;

  deletedBy?: string;

  createdBy?: string;

  modifiedBy?: string;
}

export abstract class BaseDomain<DomainProps, E> {
  constructor({
    createdBy,
    modifiedBy,
    deletedAt,
    deletedBy,
    props,
    id,
    modifiedAt,
    createdAt,
  }: CreateDomainProps<DomainProps>) {
    this.validateProps(props);
    const now = DateTime.now();

    this.id = id || new UUID();
    this.createdBy = createdBy;
    this.modifiedBy = modifiedBy;
    this.createdAt = createdAt || now;
    this.modifiedAt = modifiedAt || now;
    this.deletedAt = deletedAt;
    this.deletedBy = deletedBy;
    this.props = props;

    this.validate();
  }

  protected readonly props: DomainProps;

  private readonly createdAt: DateTime;

  private readonly id: UUID;

  private modifiedAt: DateTime;

  private createdBy: string;

  private modifiedBy: string;

  private deletedAt: DateTime;

  private deletedBy: string;

  get getId(): string {
    return this.id.value;
  }

  get getCreatedAt(): DateTime {
    return this.createdAt;
  }

  get getCreatedBy(): string {
    return this.createdBy;
  }

  get getModifiedAt(): DateTime {
    return this.modifiedAt;
  }

  get getModifiedBy(): string {
    return this.modifiedBy;
  }

  get getDeletedAt(): DateTime {
    return this.deletedAt;
  }

  setDeletedAt(value: DateTime) {
    this.deletedAt = value;
  }

  get getDeletedBy(): string {
    return this.deletedBy;
  }

  setDeletedBy(value: string) {
    this.deletedBy = value;
  }

  static isEntity(entity: unknown): boolean {
    return entity instanceof BaseDomain;
  }

  /**
   * Implement this method to use equals method
   * @param object Entity
   */
  protected abstract equalWith(obj: BaseDomain<DomainProps, E>): boolean;

  /**
   * Convert to reservation object
   * @param object Entity
   */
  public abstract toEntity(): Partial<E>;

  /**
   *  Check if two entities are the equal
   * @param object Entity
   */
  public equals(object: BaseDomain<DomainProps, E>): boolean {
    if (object === null || object === undefined) {
      return false;
    }

    if (this === object) {
      return true;
    }

    if (!BaseDomain.isEntity(object)) {
      return false;
    }

    return this.equalWith(object);
  }

  /**
   * Returns current **copy** of entity's props.
   * Modifying entity's state won't change previously created
   * copy returned by this method since it doesn't return a reference.
   * If a reference to a specific property is needed create a getter in parent class.
   *
   * @return {*}  {Props & EntityProps}
   * @memberof Entity
   */
  public getPropsCopy(): DomainProps & BaseDomainProps {
    const propsCopy = {
      id: new UUID(this.getId),
      createdAt: this.getCreatedAt,
      modifiedAt: this.getModifiedAt,
      createdBy: this.getCreatedBy,
      modifiedBy: this.getModifiedBy,
      ...cloneDeep(this.props),
    };

    return Object.freeze(propsCopy);
  }

  /**
   * Convert an Entity and all sub-entities/Value Objects it
   * contains to a plain object with primitive types. Can be
   * useful when logging an entity during testing/debugging
   */
  public toObject(): unknown {
    const propsCopy = {
      id: this.getId,
      createdAt: this.getCreatedAt,
      modifiedAt: this.getModifiedAt,
      createdBy: this.getCreatedBy,
      modifiedBy: this.getModifiedBy,
      ...cloneDeep(this.props),
    };

    return Object.freeze(propsCopy);
  }

  /**
   * Validate invariant (policies and conditions)
   */
  public abstract validate(): void;

  private validateProps(props: DomainProps): void {
    const maxProps = 200;

    if (DomainShield.isEmpty(props)) {
      throw new DataNotAllowedError();
    }

    if (typeof props !== 'object') {
      throw new DataNotAllowedError();
    }

    if (Object.keys(props).length > maxProps) {
      throw new DataNotAllowedError();
    }
  }
}
