import { ApiProperty } from '@nestjs/swagger';
import { BasePaginationRequestDto } from 'src/common/api/base.pagination.request.dto';
import { QueryPagination } from './pagination';

export class BasePaginationResponseDto extends BasePaginationRequestDto {
  @ApiProperty({
    type: 'number',
    required: false,
  })
  total?: number;

  @ApiProperty({
    type: 'number',
    required: false,
  })
  totalPages?: number;

  static from(
    pageResPagination: QueryPagination,
    total: number,
    totalPages: number,
  ): BasePaginationResponseDto {
    const pagination = new BasePaginationResponseDto();

    pagination.total = total;
    pagination.totalPages = totalPages;
    pagination.sortKey = pageResPagination.sortKey;
    pagination.sortType = pageResPagination.sortType;
    pagination.page = pageResPagination.page;
    pagination.pageSize = pageResPagination.pageSize;

    return pagination;
  }
}
