import { PaginationSortingOrder } from '../constants/pagination';
import { PaginationUtil } from '../utils/pagination.util';
import { QueryPagination } from './pagination';

export class PageResult<T> {
  public result: T;
  public pagination: QueryPagination;
  public total?: number;
  public totalPages?: number;
  public sortKey?: string;
  public sortType?: PaginationSortingOrder;

  constructor(props: PageResult<T>) {
    Object.assign(this, props);
  }

  static from<T>(
    result: T,
    pagination: QueryPagination,
    total: number,
    sortKey?: string,
    sortType?: PaginationSortingOrder,
  ): PageResult<T> {
    const totalPages = PaginationUtil.getTotalPages(total, pagination.pageSize);

    return new PageResult({
      result,
      pagination,
      total,
      totalPages,
      sortKey,
      sortType,
    });
  }

  static fromFullResult<T>(
    result: Array<T>,
    pagination?: QueryPagination,
  ): PageResult<T[]> {
    const { page, pageSize } = pagination;
    const total = result.length;

    if (page || pageSize) {
      const paginationUtil = new PaginationUtil(page, pageSize);
      const paginatedArr = paginationUtil.paginateArray(result);

      pagination = new QueryPagination({
        page: paginationUtil.page,
        pageSize: paginationUtil.pageSize,
      });

      result = paginatedArr;
    }

    return new PageResult({
      result,
      pagination,
      total,
      sortKey: pagination.sortKey,
      sortType: pagination.sortType,
    });
  }
}
