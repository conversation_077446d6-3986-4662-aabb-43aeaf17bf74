import { ApiProperty } from '@nestjs/swagger';
import { BasePaginationResponseDto } from 'src/common/api/base.pagination.response.dto';

export class BaseResponseDto<T = unknown> {
  @ApiProperty()
  success: boolean;

  @ApiProperty({
    type: 'string',
    format: 'uuid',
  })
  correlationId?: string;

  @ApiProperty()
  result?: T;

  @ApiProperty()
  message?: string;

  responseCode?: number;

  @ApiProperty({
    type: BasePaginationResponseDto,
    required: false,
  })
  pagination?: BasePaginationResponseDto;

  static success<T = unknown>(
    result: T = null,
    pagination: BasePaginationResponseDto = undefined,
  ): BaseResponseDto<T> {
    return {
      success: true,
      result,
      pagination,
    };
  }
}
