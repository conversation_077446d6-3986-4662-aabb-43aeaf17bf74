import { Type } from 'class-transformer';
import { IsEnum, IsInt, IsOptional, IsPositive, Max } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';
import { PaginationSortingOrder } from '../constants/pagination';
import { QueryPagination } from './pagination';

export class BasePaginationRequestDto {
  @ApiProperty({
    type: 'number',
    default: 1,
    required: false,
  })
  @IsPositive()
  @IsInt()
  @Type(() => Number)
  @IsOptional()
  page?: number = 1;

  @ApiProperty({
    type: 'number',
    default: 10,
    required: false,
  })
  @Max(200)
  @IsPositive()
  @IsInt()
  @Type(() => Number)
  @IsOptional()
  pageSize?: number = 10;

  @ApiProperty({
    type: 'string',
    required: false,
  })
  @IsOptional()
  sortKey?: string;

  @ApiProperty({
    type: 'enum',
    enum: PaginationSortingOrder,
    default: PaginationSortingOrder.ASCENDING,
    required: false,
  })
  @IsEnum(PaginationSortingOrder)
  @IsOptional()
  sortType?: PaginationSortingOrder = PaginationSortingOrder.ASCENDING;

  toPagination(): QueryPagination {
    return new QueryPagination({
      page: this.page,
      pageSize: this.pageSize,
      sortKey: this.sortKey,
      sortType: this.sortType,
    });
  }
}
