import { isDefined } from 'class-validator';
import { TinyType } from 'tiny-types';
import { ensure } from 'tiny-types/lib/ensure';
import { Predicate } from 'tiny-types/lib/predicates';

function isValidMileage(): Predicate<number> {
  return Predicate.to('be valid Vulog mileage in Km', (value: number) =>
    isDefined(value),
  );
}

export class VulogMileage extends TinyType {
  constructor(public readonly value: number) {
    ensure(VulogMileage.name, value, isValidMileage());

    super();
  }
}
