import { TinyTypeOf } from 'tiny-types';
import { UUID } from './uuid.type';

export class CarModel extends TinyTypeOf<string>() {}
export class CarId extends TinyTypeOf<string>() {}
export class VulogCarId extends TinyTypeOf<string>() {}
export class CarPlateNumber extends TinyTypeOf<string>() {}
export class BsgUserId extends TinyTypeOf<string>() {}
export class VulogUserId extends TinyTypeOf<string>() {}
export class VulogFleetId extends TinyTypeOf<string>() {}
export class VulogServiceId extends TinyTypeOf<string>() {}
export class VulogJourneyOrTripId extends TinyTypeOf<string>() {}
export class StationId extends TinyTypeOf<string>() {}
export class ReservationId extends TinyTypeOf<string>() {}
export class RentalPackageId extends TinyTypeOf<string>() {}
export class CharingKioskId extends TinyTypeOf<string>() {}
export class SubscriptionPlanId extends TinyTypeOf<string>() {}
export class VulogProfileId extends TinyTypeOf<string>() {}
export class ConfigurationId extends TinyTypeOf<string>() {}
export class PricingPolicyId extends TinyTypeOf<string>() {}
export class CommentId extends UUID {}
export class VulogEventId extends UUID {}
export class VulogCityId extends UUID {}
export class VulogZoneId extends TinyTypeOf<string>() {}
