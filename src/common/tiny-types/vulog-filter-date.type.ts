import { isDefined } from 'class-validator';
import { DateTime } from 'luxon';
import { TinyType } from 'tiny-types';
import { ensure } from 'tiny-types/lib/ensure';
import { Predicate } from 'tiny-types/lib/predicates';
import { DateFormat } from '../constants/date-format';

function isValidDate(): Predicate<string> {
  return Predicate.to(
    'be valid Vulog date',
    (value: string) =>
      isDefined(value) &&
      DateTime.fromFormat(
        value,
        DateFormat.UTC_00_FULL_FORMAT_EXCLUDING_MILLISECONDS,
      ).isValid,
  );
}

/**
 * Vulog expects all date in `ISO-8601` with `UTC+00` format (in request and response)
 */
export class VulogFilterDate extends TinyType {
  constructor(public readonly value: string) {
    ensure(VulogFilterDate.name, value, isValidDate());

    super();
  }
}
