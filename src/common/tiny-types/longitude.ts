import { isDefined, isLongitude } from 'class-validator';
import { TinyType } from 'tiny-types';
import { ensure } from 'tiny-types/lib/ensure';
import { Predicate } from 'tiny-types/lib/predicates';

function isValidLongitude(): Predicate<string> {
  return Predicate.to(
    'be valid longitude',
    (value: string) => isDefined(value) && isLongitude(value),
  );
}

export class Longitude extends TinyType {
  constructor(public readonly value: string) {
    ensure(Longitude.name, value, isValidLongitude());

    super();
  }
}
