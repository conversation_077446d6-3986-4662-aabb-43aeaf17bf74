import { isDefined, isLatitude } from 'class-validator';
import { TinyType } from 'tiny-types';
import { ensure } from 'tiny-types/lib/ensure';
import { Predicate } from 'tiny-types/lib/predicates';

function isValidLatitude(): Predicate<string> {
  return Predicate.to(
    'be valid latitude',
    (value: string) => isDefined(value) && isLatitude(value),
  );
}

export class Latitude extends TinyType {
  constructor(public readonly value: string) {
    ensure(Latitude.name, value, isValidLatitude());

    super();
  }
}
