import { isDefined } from 'class-validator';
import { DateTime } from 'luxon';
import { TinyType } from 'tiny-types';
import { ensure } from 'tiny-types/lib/ensure';
import { Predicate } from 'tiny-types/lib/predicates';

function isValidDate(): Predicate<string> {
  return Predicate.to(
    'be valid Vulog date',
    (value: string) => isDefined(value) && DateTime.fromISO(value).isValid,
  );
}

export class VulogDate extends TinyType {
  constructor(public readonly value: string) {
    ensure(VulogDate.name, value, isValidDate());

    super();
  }

  static fromJsDate(date: Date): VulogDate {
    return new VulogDate(date.toISOString());
  }

  static now(): VulogDate {
    return VulogDate.fromJsDate(DateTime.now().toJSDate());
  }

  toDateTime(): DateTime {
    return DateTime.fromISO(this.value);
  }
}
