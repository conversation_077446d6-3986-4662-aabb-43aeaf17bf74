import { Injectable, PipeTransform } from '@nestjs/common';
import { parseAndValidate } from '../utils/parse-and-validate-dto';

@Injectable()
export class ParseTinyTypePipe<T extends object>
  implements PipeTransform<string, Promise<T>>
{
  constructor(private readonly tinyTypeClass: T) {}

  async transform(value: string): Promise<T> {
    return parseAndValidate<T>(
      value,
      this.tinyTypeClass as unknown as new () => T,
    ) as T;
  }
}
