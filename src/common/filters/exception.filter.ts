import { CorrelationIdService } from '@bluesg-2/monitoring';
import { ArgumentsHost, Catch, ExceptionFilter, Logger } from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { InjectSentry, SentryService } from '@ntegral/nestjs-sentry';
import * as _ from 'lodash';
import { ErrorDto } from '../errors/error.dto';
import { ExceptionResponseDto } from './exception-response.dto';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  constructor(
    private readonly httpAdapterHost: HttpAdapterHost,
    @InjectSentry() private readonly sentry: SentryService,
    private readonly correlationService: CorrelationIdService,
  ) {}

  catch(exception: Error, host: ArgumentsHost): void {
    Logger.debug('Exception Filter caught an exception', { exception });
    const { httpAdapter } = this.httpAdapterHost;

    const ctx = host.switchToHttp();

    const req = ctx.getRequest<Request>();
    const correlationId: string =
      this.correlationService.getCorrelationId().value;

    const errorDto = ErrorDto.from(exception);

    const responseBody = {
      ..._.omit(errorDto, 'details'),
      correlationId,
    } as ExceptionResponseDto;

    this.sentry.instance().setContext('AllExceptionsFilter', {
      ...errorDto.sentry_context(),
      correlationId,
    });

    Logger.error(
      errorDto.message,
      {
        ...errorDto,
        correlationId,
        url: req.url,
        cause: exception,
      },
      'Exception Filter',
      exception.stack,
    );

    httpAdapter.reply(ctx.getResponse(), responseBody, errorDto.httpStatus);
  }
}
