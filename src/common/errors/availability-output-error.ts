import { AVAILAB<PERSON><PERSON><PERSON>_OUTPUT_ERROR } from '../constants/error-codes';
import { A<PERSON><PERSON><PERSON><PERSON>ITY_OUTPUT_ERROR_MESSAGE } from '../constants/messages';
import { InternalError } from './internal-error';

export class AvailabilityOutputError extends InternalError {
  constructor(cause) {
    super(<PERSON><PERSON><PERSON><PERSON><PERSON>ITY_OUTPUT_ERROR, A<PERSON><PERSON><PERSON><PERSON>ITY_OUTPUT_ERROR_MESSAGE, cause);
  }
}
