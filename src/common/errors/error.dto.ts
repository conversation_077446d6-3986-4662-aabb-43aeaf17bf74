/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpException, HttpStatus } from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';

import { INTERNAL_SERVER_ERROR } from '../constants/error-codes';
import { InternalMicroserviceExceptionError } from '../errors/http-exception-error';
import { InternalError } from './internal-error';
import { VulogApiError } from './vulog-api-error';

export class ErrorDto {
  @ApiProperty()
  error: string;

  @ApiProperty()
  errorCode?: string;

  @ApiProperty()
  httpStatus?: number;

  @ApiProperty()
  details?: any;

  @ApiProperty()
  message?: string;

  @ApiProperty()
  error_description?: string;

  constructor(err: {
    error: string;
    errorCode?: string;
    httpStatus?: number;
    details?: any;
    message?: string;
    error_description?: string;
  }) {
    this.error = err.error;
    this.errorCode = err.errorCode || INTERNAL_SERVER_ERROR;
    this.httpStatus = err.httpStatus || HttpStatus.INTERNAL_SERVER_ERROR;
    this.details = err.details;
    this.message = err.message;
    this.error_description = err.error_description;
  }

  static from(error: Error) {
    if (error instanceof VulogApiError) {
      return ErrorDto.from_vulog_api_error(error);
    } else if (error instanceof InternalMicroserviceExceptionError) {
      return ErrorDto.fromInternalMicroserviceException(error);
    } else if (error instanceof HttpException) {
      return ErrorDto.from_http_exception(error);
    } else if (error instanceof InternalError) {
      return ErrorDto.from_internal_error(error);
    } else {
      return ErrorDto.from_error(error);
    }
  }

  static from_error(error: Error): ErrorDto {
    return new ErrorDto({
      error: error.name,
      message: error.message,
      details: error.stack,
    });
  }

  static from_internal_error(error: InternalError): ErrorDto {
    return new ErrorDto({
      error: error.name,
      errorCode: error.errorCode,
      httpStatus: error.statusCode,
      message: error.printMessage(),
      details: error.stack,
    });
  }

  static from_http_exception(exception: HttpException): ErrorDto {
    const rsp = exception.getResponse();

    if (typeof rsp === 'string') {
      return new ErrorDto({
        error: 'Unknown HTTP Error',
      });
    } else {
      return new ErrorDto({
        message: JSON.stringify(rsp),
        ...(rsp as ErrorDto),
        httpStatus: exception.getStatus(),
      });
    }
  }

  static fromInternalMicroserviceException(
    exception: InternalMicroserviceExceptionError,
  ): ErrorDto {
    return new ErrorDto({
      error: exception.error,
      errorCode: exception.errorCode,
      details: exception.details,
      message: exception.message,
      httpStatus: exception.statusCode,
    });
  }

  static from_vulog_api_error(error: VulogApiError): ErrorDto {
    return new ErrorDto({
      error: 'Vulog API Error',
      errorCode: error.errorCode,
      httpStatus: error.statusCode,
      message: error?.data?.message,
      details: error?.stack,
    });
  }

  sentry_fingerprint() {
    return [
      '{{default}}',
      String(this.message),
      String(this.error_description),
    ];
  }

  sentry_context() {
    return {
      message: this.message,
      details: this.details,
      statusCode: this.httpStatus,
      errorDescription: this.error_description,
    };
  }
}
