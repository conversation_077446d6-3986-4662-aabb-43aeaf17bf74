import { RENTAL_TECHNICAL_ERROR } from '../constants/error-codes';
import { RENTAL_TECHNICAL_ERROR_MESSAGE } from '../constants/messages';
import { InternalError } from './internal-error';

export class RentalTechnicalError extends InternalError {
  constructor(cause, httpStatusCode?: number) {
    super(
      RENTAL_TECHNICAL_ERROR,
      RENTAL_TECHNICAL_ERROR_MESSAGE,
      cause,
      httpStatusCode,
    );
  }
}
