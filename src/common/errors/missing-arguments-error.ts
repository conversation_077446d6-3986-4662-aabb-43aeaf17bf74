import { MISSING_ARGUMENTS_ERROR } from '../constants/error-codes';
import { MISSING_ARGUMENTS_ERROR_MESSAGE } from '../constants/messages';
import { InternalError } from './internal-error';

export class MissingArgumentsError extends InternalError {
  constructor(args: string | Array<string>) {
    super(
      MISSING_ARGUMENTS_ERROR,
      `${MISSING_ARGUMENTS_ERROR_MESSAGE} (${args})`,
    );
  }
}
