import { HttpStatusCode } from 'axios';
import { GRACE_PERIOD_ERROR } from '../../constants/error-codes';
import { RESERVATION_ERROR_MESSAGE } from '../../constants/messages';
import { InternalError } from '../internal-error';

export class GracePeriodError extends InternalError {
  constructor(cause) {
    super(
      GRACE_PERIOD_ERROR,
      RESERVATION_ERROR_MESSAGE,
      cause,
      HttpStatusCode.BadRequest,
    );
  }
}
