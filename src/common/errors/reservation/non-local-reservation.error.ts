import { HttpStatusCode } from 'axios';
import { NON_LOCAL_RESERVATION_ERROR } from 'src/common/constants/error-codes';
import { RESERVATION_ERROR_MESSAGE } from '../../constants/messages';
import { InternalError } from '../internal-error';

export class NonLocalReservationError extends InternalError {
  constructor(cause) {
    super(
      NON_LOCAL_RESERVATION_ERROR,
      RESERVATION_ERROR_MESSAGE,
      cause,
      HttpStatusCode.BadRequest,
    );
  }
}
