import { HttpStatusCode } from 'axios';
import { MAX_CONCURRENT_TRIP_REACHED_ERROR } from '../../constants/error-codes';
import { RESERVATION_ERROR_MESSAGE } from '../../constants/messages';
import { InternalError } from '../internal-error';

export class MaxConcurrentTripReachedError extends InternalError {
  constructor(cause) {
    super(
      MAX_CONCURRENT_TRIP_REACHED_ERROR,
      RESERVATION_ERROR_MESSAGE,
      cause,
      HttpStatusCode.BadRequest,
    );
  }
}
