import { HttpStatus } from '@nestjs/common';
import { STATION_NOT_FOUND_ERROR } from '../../constants/error-codes';
import { STATION_NOT_FOUND } from '../../constants/messages';
import { InternalError } from '../internal-error';

export class StationNotFoundNewError extends InternalError {
  constructor(message?: string, cause?) {
    super(
      STATION_NOT_FOUND_ERROR,
      message || STATION_NOT_FOUND,
      cause,
      HttpStatus.NOT_FOUND,
    );
  }
}
