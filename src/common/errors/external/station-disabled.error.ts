import { HttpStatus } from '@nestjs/common';
import { STATION_DISABLED_ERROR } from '../../constants/error-codes';
import { STATION_DISABLED_MESSAGE } from '../../constants/messages';
import { InternalError } from '../internal-error';

export class StationDisabledError extends InternalError {
  constructor(message?: string, cause?) {
    super(
      STATION_DISABLED_ERROR,
      message || STATION_DISABLED_MESSAGE,
      cause,
      HttpStatus.UNPROCESSABLE_ENTITY,
    );
  }
}
