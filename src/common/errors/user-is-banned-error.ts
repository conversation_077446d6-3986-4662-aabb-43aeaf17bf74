import { HttpStatusCode } from 'axios';
import { USER_BANNED_ERROR } from '../constants/error-codes';
import { RESERVATION_ERROR_MESSAGE } from '../constants/messages';
import { InternalError } from './internal-error';

export class BannedUserError extends InternalError {
  constructor(cause: any, message?: string) {
    super(
      USER_BANNED_ERROR,
      message || RESERVATION_ERROR_MESSAGE,
      cause,
      HttpStatusCode.Forbidden,
    );
  }
}
