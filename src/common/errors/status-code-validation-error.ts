import { InternalError } from './internal-error';

const STATUS_CODE_VALIDATION_ERROR = 'STATUS_CODE_VALIDATION_ERROR';
const STATUS_CODE_VALIDATION_ERROR_MESSAGE =
  'HTTP status code received is invalid: ';

export class StatusCodeValidationError extends InternalError {
  constructor(cause) {
    super(
      STATUS_CODE_VALIDATION_ERROR,
      STATUS_CODE_VALIDATION_ERROR_MESSAGE + cause,
      cause,
    );
  }
}
