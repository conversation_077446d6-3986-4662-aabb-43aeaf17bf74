/**
 * Currently we don't have time to write a Node package to control error handling consistency.
 * So this is temporary way to get all fields regarding error coming from every services
 */
export class InternalMicroserviceExceptionError {
  error: string;
  errorCode: string;
  details: object; // Often error call stack
  statusCode: number;
  message: string;

  constructor({
    message,
    statusCode,
    error,
    errorCode,
    details,
  }: {
    message: string;
    statusCode: number;
    error: string;
    errorCode: string;
    details: object;
  }) {
    this.error = error;
    this.errorCode = errorCode;
    this.details = details;
    this.statusCode = statusCode;
    this.message = message;
  }
}
