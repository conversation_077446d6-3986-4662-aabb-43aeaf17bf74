import { HttpStatusCode } from 'axios';
import { VULOG_EVENT_ERROR } from '../constants/error-codes';
import { VULOG_EVENT_ERROR_RESPONSE } from '../constants/messages';
import { InternalError } from './internal-error';

export class VulogEventProcessError extends InternalError {
  constructor(cause) {
    super(
      VULOG_EVENT_ERROR,
      VULOG_EVENT_ERROR_RESPONSE,
      cause,
      HttpStatusCode.InternalServerError,
    );
  }
}
