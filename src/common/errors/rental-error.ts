import { HttpStatus } from '@nestjs/common';
import { RENTAL_ERROR } from '../constants/error-codes';
import { RENTAL_ERROR_MESSAGE } from '../constants/messages';
import { InternalError } from './internal-error';

export class RentalError extends InternalError {
  constructor(
    message: string = RENTAL_ERROR_MESSAGE,
    statusCode: number = HttpStatus.INTERNAL_SERVER_ERROR,
    cause?: Error
  ) {
    super(RENTAL_ERROR, message, cause, statusCode);
  }
}
