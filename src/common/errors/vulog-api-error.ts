import { AxiosError } from 'axios';
import { VULOG_API_ERROR } from '../constants/error-codes';
import { VULOG_API_ERROR_MESSAGE } from '../constants/messages';
import { InternalError } from './internal-error';

export interface VvgApiErrorData {
  codeStr: string;
  code: number;
  transactionId: string;
  message: string;
  extraInfo?: { [key: string]: any };
}

export class VulogApiError extends InternalError {
  public statusCode: number;

  public data: any;

  constructor(cause: Error | string) {
    super(VULOG_API_ERROR, VULOG_API_ERROR_MESSAGE + `${cause}`, cause);

    if (cause instanceof AxiosError && cause.response) {
      this.statusCode = cause.response.status;
      this.data = cause.response.data;
    }
  }
}
