import { HttpStatusCode } from 'axios';
import { USER_DEACTIVATED_ERROR } from '../constants/error-codes';
import { RESERVATION_ERROR_MESSAGE } from '../constants/messages';
import { InternalError } from './internal-error';

export class DeactivatedUserError extends InternalError {
  constructor(message?: string) {
    super(
      USER_DEACTIVATED_ERROR,
      message || RESERVATION_ERROR_MESSAGE,
      HttpStatusCode.Forbidden,
    );
  }
}
