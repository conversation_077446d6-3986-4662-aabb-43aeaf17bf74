import { HttpStatus } from '@nestjs/common';
import { StatusCodeValidationError } from '../errors/status-code-validation-error';

const onInvalidStatusCode = (invalidStatusCode: HttpStatus) => {
  throw new StatusCodeValidationError(invalidStatusCode);
};

export const validateStatusCode = (
  expectedStatusCodes: HttpStatus[],
  statusCodeToValidate: HttpStatus,
  onInvalid: (invalidStatusCode?: HttpStatus) => void = onInvalidStatusCode,
): void => {
  if (!expectedStatusCodes.includes(statusCodeToValidate)) {
    onInvalid();
  }
};
