import * as _ from 'lodash';
export const LoggerUtils = Object.freeze({
  /**
   * New Relic's limits on attributes apply to data from any source,
   * including OTLP-sourced data. See metric attribute limits and event
   * attribute limits for other limits: Maximum length of attribute name: 255 characters.
   * Maximum length of attribute value: 4095 characters.
   */
  removeLongAttributes<T>(obj: T, longAttributes: (keyof T)[]): T {
    const clonedObj = _.cloneDeep(obj);

    longAttributes.forEach((attr) => delete clonedObj[attr]);

    clonedObj['longAttributesRemovedForNewRelic'] = longAttributes;

    return clonedObj;
  },
});
