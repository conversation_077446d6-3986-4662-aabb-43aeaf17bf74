import { PaginationSortingOrder } from '@bluesg-2/queue-pop/dist/constants/pagination';
import * as _ from 'lodash';
import { SelectQueryBuilder } from 'typeorm';
import { BasePaginationRequestDto } from '../api/base.pagination.request.dto';
import { BasePaginationResponseDto } from '../api/base.pagination.response.dto';

export class SupaPagination {
  constructor(
    private sortKey?: string,
    private sortType?: PaginationSortingOrder,
    private page?: number,
    private pageSize?: number,
    private isCamelCaseForSortkey = true,
  ) {}

  static fromBasePaginationRequest(req: Partial<BasePaginationRequestDto>) {
    return new SupaPagination(
      req.sortKey,
      req.sortType,
      req.page,
      req.pageSize,
    );
  }

  skip() {
    return this.page * this.pageSize - this.pageSize;
  }

  /**
   * Generate TypeORM pagination
   * isCamelCaseForSortkey = false when you want the sortKey is snake_case
   * @param object
   * @returns object
   */
  toRepositoryPagination = <T extends string>(): {
    order: { [key in string]?: PaginationSortingOrder };
    take: number;
    skip: number;
  } => ({
    order: this.sortKey
      ? ({
          [this.isCamelCaseForSortkey
            ? this.sortKey
            : (_.snakeCase(this.sortKey) as T)]: this.sortType,
        } as { [key in string]?: PaginationSortingOrder })
      : null,
    take: this.pageSize,
    skip: this.skip(),
  });

  /**
   * @param query queryBuilder entity that you want to append
   * @param queryParams pagination condition
   * @param entityName entity name that you use to name the query builder
   * @returns queryBuilder object
   */
  appendPagination<T>(
    query: SelectQueryBuilder<T>,
    entityName: string,
  ): SelectQueryBuilder<T> {
    const take = this.pageSize;

    query
      .orderBy(`${entityName}.${this.sortKey}`, this.sortType, 'NULLS LAST')
      .take(take)
      .skip(this.skip());

    return query;
  }

  toPaginationResponse(total: number): BasePaginationResponseDto {
    const paginationInfo = new BasePaginationResponseDto();

    paginationInfo.page = this.page;
    paginationInfo.pageSize = this.pageSize;
    paginationInfo.sortKey = this.sortKey;
    paginationInfo.sortType = this.sortType;
    paginationInfo.total = total;
    paginationInfo.totalPages = Math.ceil(total / this.pageSize);

    return paginationInfo;
  }
}
