import { ConfigurationType } from '../../shared/configuration/constants/configuration';

export const ConfigurationUtils = Object.freeze({
  /**
   * Transform configuration value based on its type
   * @param type ConfigurationType
   * @param value plain text value from database
   * @returns value in expected format
   */
  transformValue(type: ConfigurationType, value: string) {
    switch (type) {
      case ConfigurationType.JSON:
        return JSON.parse(value);
      case ConfigurationType.NUMBER:
        return Number(value);
      case ConfigurationType.PLAIN:
        return value;
    }
  },
});
