import {
  VulogZone,
  VulogZoneType,
} from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';

export const VulogZoneUtils = {
  filterZonesUnderSpecificFleetService(
    zones: VulogZone[],
    fleetZoneIdsMap: Map<string, string>,
  ): VulogZone[] {
    return zones?.length
      ? zones?.filter(
          (zone) =>
            (fleetZoneIdsMap?.size &&
              zone.type === VulogZoneType.Allowed &&
              fleetZoneIdsMap.has(zone.zoneId)) ||
            !fleetZoneIdsMap?.size,
        )
      : [];
  },
};
