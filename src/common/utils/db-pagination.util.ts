import { BasePaginationRequestDto } from '../api/base.pagination.request.dto';
import { BasePaginationResponseDto } from '../api/base.pagination.response.dto';

/**
 * Generate TypeORM pagination
 * @param object
 * @returns object
 */
export const generatePagination = ({
  sortKey,
  sortType,
  page,
  pageSize,
}: Omit<BasePaginationRequestDto, 'toPagination'>): {
  order: Record<string, unknown>;
  take: number;
  skip: number;
} => ({
  order: sortKey
    ? {
        [sortKey]: sortType,
      }
    : null,
  take: pageSize,
  skip: page * pageSize - pageSize,
});

export function createPaginationResponse(
  data: BasePaginationRequestDto,
  total: number,
): Omit<BasePaginationResponseDto, 'toPagination'> {
  const { page, pageSize, sortKey, sortType } = data;
  return {
    page,
    pageSize,
    sortKey,
    sortType,
    total,
    totalPages: Math.ceil(total / pageSize),
  };
}
