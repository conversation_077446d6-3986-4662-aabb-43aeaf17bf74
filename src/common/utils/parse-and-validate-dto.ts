import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { MissingArgumentsError } from '../errors/missing-arguments-error';
import { ValidationResponseError } from '../errors/validation-response-error';

export async function parseAndValidate<T extends object>(
  data: unknown,
  toClass: new () => T,
): Promise<T | T[]> {
  if (!data) return null;

  if (!toClass) throw new MissingArgumentsError('toClass');
  const instance: T | T[] = plainToInstance(toClass, data);

  if (instance instanceof Array) {
    const totalErrors = [];
    (await Promise.all(instance.map((elementT) => validate(elementT)))).forEach(
      (errors) => {
        if (errors && errors.length > 0) {
          totalErrors.push(errors);
        }
      },
    );

    if (totalErrors && totalErrors.length > 0) {
      throw new ValidationResponseError(totalErrors);
    }
  } else {
    const errors = await validate(instance);

    if (errors && errors['length'] > 0) {
      throw new ValidationResponseError(errors);
    }
  }

  return instance;
}
