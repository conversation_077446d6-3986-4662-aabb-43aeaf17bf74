import { Duration } from 'luxon';

export const BillingUtils = Object.freeze({
  /**
   * Function to round seconds to minutes according to billing rounding minutes rule
   *
   * rental.duration is seconds, apply rounding minutes. 26.3 --> 27
   * */
  roundToMinutes(durationInSeconds: number): number {
    return Math.ceil(
      Math.abs(
        Duration.fromObject({ seconds: durationInSeconds }).as('minutes'),
      ),
    );
  },
});
