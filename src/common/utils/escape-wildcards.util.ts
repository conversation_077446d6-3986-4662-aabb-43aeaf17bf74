import { WildCards } from 'src/common/constants/wildcards.constant';

export class EscapeWildCardsUtil {
  static containWildcards(stringInput: string): boolean {
    return (
      stringInput.includes(WildCards.PERCENT) ||
      stringInput.includes(WildCards.UNDERSCORE)
    );
  }

  static removeWildcards(stringInput: string): string {
    if (this.containWildcards(stringInput)) {
      const regexCheckMatchOneOfWildcards = /\%|\_/g;

      return stringInput.replace(
        regexCheckMatchOneOfWildcards,
        (matchValue) => `\\${matchValue}`,
      );
    }
    return stringInput;
  }
}
