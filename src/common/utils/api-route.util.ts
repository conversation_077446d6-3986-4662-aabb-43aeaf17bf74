import { RootRouteSegment } from '../constants/api-path';
import { ApiVersion, VERSIONING_PREFIX } from '../constants/api-version';

export const ApiRouteUtils = Object.freeze({
  /**
   * To build full API route
   * @param rootRouteSegment RootRouteSegment
   * @param version ApiVersion
   * @param routeSegment string[]
   * @returns a string such as `admin/api/v1/invoice/upload`
   */
  buildApiRouteSegment(
    rootRouteSegment: RootRouteSegment,
    version: ApiVersion,
    routeSegment: string[],
  ) {
    let api = '';

    if (rootRouteSegment) {
      api += `/${rootRouteSegment}`;
    }

    if (version) {
      api += `/${this.buildVersioningRouteSegment(version)}`;
    }

    return api.concat(`/${this.joinRouteSegments(routeSegment)}`);
  },
  /**
   * To build versioning route segment
   * @param version string
   * @param prefix string
   * @returns a string such as `v1`
   */
  buildVersioningRouteSegment(
    version: ApiVersion,
    prefix: string = VERSIONING_PREFIX,
  ) {
    return `${prefix}${version}`;
  },
  /**
   * To join route segments
   * @param routeSegment
   * @param separator such as '/'
   * @returns concatenated route segments
   */
  joinRouteSegments(routeSegment: string[], separator = '/') {
    return routeSegment.join(separator);
  },
});
