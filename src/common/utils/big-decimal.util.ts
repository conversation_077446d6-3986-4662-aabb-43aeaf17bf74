import { BigNumber } from 'bignumber.js';

/**
 * Wrapper function for bignumber.js
 * @param num
 * @param decimalPlaces
 * @returns BigNumber
 */
export function bigNumberize(
  num: number | string,
  decimalPlaces = 2,
): BigNumber {
  const CustomBigNumber = BigNumber.clone({ DECIMAL_PLACES: decimalPlaces });

  // Refer: https://mikemcl.github.io/bignumber.js/#:~:text=This%20includes%20base%2010%20so%20don%27t%20include%20a%20base%20parameter%20for%20decimal%20values%20unless%20this%20behaviour%20is%20wanted.
  return new CustomBigNumber(num, 10);
}
