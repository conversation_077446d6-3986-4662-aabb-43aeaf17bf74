import { v4 as uuidv4, validate as uuidValidate } from 'uuid';

export class ContinuationTokenManager {
  private static CONTINUATION_TOKEN_PREFIX = 'continuation_token_';

  public static generateInternalContinuationToken() {
    return this.CONTINUATION_TOKEN_PREFIX + uuidv4();
  }

  public static isValidInternalContinuationToken(contToken: string) {
    if (!contToken.includes(this.CONTINUATION_TOKEN_PREFIX)) {
      return false;
    }
    return uuidValidate(contToken.replace(this.CONTINUATION_TOKEN_PREFIX, ''));
  }
}
