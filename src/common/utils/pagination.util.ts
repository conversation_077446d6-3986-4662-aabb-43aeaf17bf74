import { config as AppConfig } from 'src/config';

export class PaginationUtil {
  constructor(
    public readonly page: number = 1,
    public readonly pageSize: number = AppConfig.paginationConfig.pageSize,
  ) {}

  getSkip(): number {
    return (this.page - 1) * this.pageSize;
  }

  getTake(): number {
    return this.pageSize;
  }

  paginateArray<T>(arr: T[]): Array<T> {
    const endIndex = this.page * this.pageSize;
    const startIndex = this.page == 1 ? 0 : this.getSkip();

    return arr.slice(startIndex, endIndex);
  }

  static getTotalPages(total: number, pageSize: number): number {
    return Math.ceil(total / pageSize);
  }
}
