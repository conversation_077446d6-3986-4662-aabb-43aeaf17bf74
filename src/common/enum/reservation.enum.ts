/**
    CREATING -> RESERVED / FAILED -> CA<PERSON><PERSON>LED / CONVERTED
    FAILED means we could not create it in Vulog.
    CONVERTED means we started the rental in Vulog.
    CANCELLED means the user has cancelled it.
    END_TRIP means vulog has sent a end-trip-report notify that the trip has ended
    ENDED means the trip has ended and billed
 */
export enum ReservationStatus {
  Creating = 'CREATING',
  Reserved = 'RESERVED',
  Failed = 'FAILED',
  Cancelled = 'CANCELLED',
  Converted = 'CONVERTED',
  EndTripToReview = 'END_TRIP_TO_REVIEW',
  EndedWaitingForInvoicing = 'ENDED_WAITING_FOR_INVOICING',
  EndTrip = 'END_TRIP',
  Expired = 'EXPIRED',
  Ended = 'ENDED',
}
