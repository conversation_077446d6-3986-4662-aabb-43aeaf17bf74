/* eslint-disable @typescript-eslint/ban-types */
import {
  isISO8601,
  registerDecorator,
  ValidationOptions,
} from 'class-validator';
import { DateTime } from 'luxon';

export function IsISO8601NotInFuture(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'IsISO8601NotInFuture',
      target: object.constructor,
      propertyName,
      constraints: [],
      options: {
        message: `${propertyName} must be a valid ISO date time and must not be in future`,
        ...validationOptions,
      },
      validator: {
        validate(value: any) {
          if (
            !isISO8601(value, {
              strict: true,
            })
          ) {
            return false;
          }

          const parsed = DateTime.fromISO(value);

          if (!parsed.isValid) {
            return false;
          }

          return parsed < DateTime.now();
        },
      },
    });
  };
}
