/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-types */
/* eslint-disable func-names */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
import {
  isISO8601,
  registerDecorator,
  ValidationOptions,
} from 'class-validator';
import { DateTime } from 'luxon';

export function IsValidLuxonISO8601(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'IsValidLuxonISO8601',
      target: object.constructor,
      propertyName,
      constraints: [],
      options: {
        message: `${propertyName} must conform to Luxon ISO-8601`,
        ...validationOptions,
      },
      validator: {
        validate(value: any) {
          if (
            !isISO8601(value, {
              strict: true,
            })
          ) {
            return false;
          }

          if (!DateTime.fromISO(value).isValid) {
            return false;
          }

          return true;
        },
      },
    });
  };
}
