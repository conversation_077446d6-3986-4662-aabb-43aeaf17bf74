import {
  createParamDecorator,
  ExecutionContext,
  HttpStatus,
  ValidationPipe,
} from '@nestjs/common';

const RawBody = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): any => {
    const request = ctx.switchToHttp().getRequest();
    return request.body;
  },
);

export const AllowNonWhitelistedBody = () =>
  RawBody(
    new ValidationPipe({
      errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      whitelist: true,
      forbidNonWhitelisted: false,
      validationError: {
        target: true,
        value: true,
      },
      validateCustomDecorators: true,
    }),
  );
