export enum MessageEventPattern {
  EndRentalSimulation = 'END_RENTAL_SIMULATION',
  CarReservationCreated = 'CRS_RESERVATION_CREATED',
  CarReservationCanceled = 'CRS_RESERVATION_CANCELED',
  CarReservationExpiry = 'CRS_RESERVATION_EXPIRY',
  CarStationSwitched = 'CRS_STATION_SWITCHED',
  ParkingReservationConfirmed = 'PSR_PARKING_RESERVATION_READY',
  VulogEndRental = 'VULOG_END_RENTAL',

  RentalEnded = 'RENTAL_ENDED',
  RentalBilled = 'RENTAL_BILLED',
  RentalEndedFinal = 'RENTAL_ENDED_FINAL',
  StartRental = 'RENTAL_STARTED',

  EndRentalAIMA = 'END_RENTAL_IN_AIMA',

  ReservationCancelationAfterSwitchingCar = 'RESERVATION_CANCELATION_AFTER_SWITCHING_CAR',
  StationStickyWithCar = 'STATION_STICKY_WITH_CAR',
  CarAvailabilityUpdated = 'CAR_AVAILABILITY_UPDATED',
}

export const localEventPattern = Object.freeze({
  reservation: {
    index: 'reservations.*',
    endTrip: 'reservations.endTrip',
    endTripReport: 'reservations.endTripReport',
    cancelTrip: 'reservations.cancelTrip',
    cancelTripReport: 'reservations.cancelTripReport',
  },
  rental: {
    index: 'rentals.*',
    startTrip: 'rentals.startTrip',
    adminStartTrip: 'rentals.adminStartTrip',
    tripStartRfid: 'rentals.tripStartRfid',
    postStartTrip: 'rentals.postStartTrip',
    endTrip: 'rentals.endTrip',
    tripEndRfid: 'rentals.tripEndRfid',
  },
  vg: {
    vehicleSessionEnd: 'vg.vehicleSessionEnd',
    vehicleSessionStart: 'vg.vehicleSessionStart',
    vehicleSessionCancel: 'vg.vehicleSessionCancel',
    chargingCablePlugged: 'vg.chargingCablePlugged',
    chargingCableUnplugged: 'vg.chargingCableUnplugged',
    vehicleSessionStarted: 'vg.vehicleSessionStarted',
  },
  car: {
    index: 'cars.*',
    chargingCablePlugged: 'cars.chargingCablePlugged',
    chargingCableUnplugged: 'cars.chargingCableUnplugged',
  },
  error: {
    index: 'error.*',
  },
});
