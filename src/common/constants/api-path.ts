export enum RootRouteSegment {
  Consumer = 'consumer/api',
  Admin = 'admin/api',
  Internal = 'internal/api',
  Testing = 'testing/api',
  Public = 'public/api',
}

export enum RentalServiceRootRouteSegment {
  Consumer = 'consumer',
  Admin = 'admin',
  Internal = 'internal',
  Testing = 'testing',
  Public = 'public',
}

export const RouteSegment = Object.freeze({
  ExternalStation: {
    Index: 'stations',

    List: '',
    Create: '',
    Detail: ':stationId',
    ZoneIdByStation: ':stationId/zoneId',
  },

  Availability: {
    Index: 'availabilities',

    List: '',
    Detail: ':stationId',
  },

  BulkManager: {
    Index: 'bulk-manager',

    Station: {
      Index: 'stations',

      UpdateMany: '',
    },
  },

  Reservation: {
    Index: 'reservations',
    List: '',
    Cancel: ':reservationId/cancel',

    Station: {
      Index: 'stations',

      Create: ':stationId',
    },
    AttachRentalPackage: ':reservationId/rental-package',

    DetachRentalPackage: ':reservationId/rental-package',
    UpdateRentalFeedBack: ':reservationId/feedback',
    Search: 'search',
    EndRental: ':reservationId/end-rental',
    EndTripWebhook: `fleets/${process.env.VULOG_FLEET_ID}/events/notify`,

    RecentlyUpdated: 'recently-updated',
  },

  Location: {
    Index: 'locations',
  },

  Comment: {
    Index: 'comments',
    Create: '',
  },

  Rental: {
    Index: 'rental',

    List: '',

    History: 'history',
  },

  EventNotifier: {
    Index: 'event-notifier',
    EventBridge: 'eventbridge',
  },

  Station: {
    Index: 'stations',

    AvailableCars: 'available-cars',
  },

  Car: {
    Index: 'cars',

    List: '',
    Lock: ':carId/lock',
    Unlock: ':carId/unlock',
    AttachStationToCar: ':plateNumber/stations/:stationId/attach',
    DetachStationFromCar: ':plateNumber/stations/:stationId/detach',
    SpecificCarInformation: ':plateNumber/detail',
    Realtime: 'realtime',
  },

  Customer: {
    Index: 'customers',

    StartRental: ':customerId/start-rental',
  },

  VulogZone: {
    Index: 'vulog-zones',

    List: '',
  },
});
