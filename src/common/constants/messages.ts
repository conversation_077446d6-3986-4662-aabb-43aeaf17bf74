export const AVAILABILITY_OUTPUT_ERROR_MESSAGE =
  'An error has occured while fetching car availability';
export const VULOG_AUTH_ERROR_MESSAGE =
  'Authentication with Vulog gateway failed';
export const VULOG_API_ERROR_MESSAGE =
  'An error happened while calling the Vulog API';
export const VULOG_EVENT_ERROR_RESPONSE =
  'An error happened while handling Vulog event';

const VULOG_INVALID_STATUS_CODE = 'Received an invalid status code';
export const VULOG_WAKE_UP_INVALID_STATUS_CODE = `Wake up car - ${VULOG_INVALID_STATUS_CODE}`;
export const VULOG_LOCK_INVALID_STATUS_CODE = `Lock car - ${VULOG_INVALID_STATUS_CODE}`;
export const VULOG_UNLOCK_INVALID_STATUS_CODE = `Unlock car - ${VULOG_INVALID_STATUS_CODE}`;
export const VULOG_IMMOBILIZE_INVALID_STATUS_CODE = `Immobilize car - ${VULOG_INVALID_STATUS_CODE}`;
export const VULOG_MOBILIZE_INVALID_STATUS_CODE = `Mobilize car - ${VULOG_INVALID_STATUS_CODE}`;
export const VULOG_BOOKING_CANCELLATION_ERROR =
  'Vulog was unable to cancel the booking';
export const STATION_API_ERROR_MESSAGE =
  'An error happened while calling the BlueSG Stations Service';
export const USS_API_ERROR_MESSAGE =
  'An error happened while calling the BlueSG User Subscription Service';
export const VALIDATION_RESPONSE_ERROR_MESSAGE =
  'The response data has some fields with wrong type';
export const MISSING_ARGUMENTS_ERROR_MESSAGE =
  'Some required arguments are missing';
export const INTERNAL_SERVER_ERROR_MESSAGE = 'Internal server error';
export const RESERVATION_ERROR_MESSAGE =
  'An error occurred during the reservation flow';
export const RENTAL_ERROR_MESSAGE = 'An error occurred during the rental flow';
export const INVALID_RENTAL_STATE = 'User has more than one active rental!';
export const NO_ACTIVE_RENTAL = 'User does not have any ongoing rental';

export const DATA_NOT_ALLOWED_ERR = 'Data is not allowed in domain!';

// NOTE: Reservation error message
export const RESERVATION_NOT_FOUND = 'Reservation not found';
export const TRIP_NOT_FOUND = 'Can not find any trip!';

export const GET_VULOG_TRIP_ERR = 'Can not get trip from VULOG';

// NOTE: Vulog error message
export const MISSING_VULOG_EVENT_HANDLER = 'Missing vulog event handler';

// NOTE: User error message
export const VULOG_USER_NOT_FOUND = 'Vulog user not found!';

// NOTE: external
export const BLUESG_USER_NOT_FOUND = 'BlueSG user not found!';

export const PACKAGE_NOT_AVAILABLE = 'This package is wrong or not available!';

export const STATION_NOT_FOUND = 'Station not found';
export const PRICING_POLICY_NOT_FOUND = 'Pricing policy not found!';
export const PRICING_POLICY_NO_RENTAL_RATE_ERROR =
  'Pricing policy has no rental rate!';
export const RENTAL_PACKAGE_INVALID_PRICE = 'Invalid rental package price';
export const RENTAL_PACKAGE_INVALID_DURATION =
  'Invalid rental package duration';

export const RENTAL_TECHNICAL_ERROR_MESSAGE =
  'The rental was canceled due to technical error. Sorry for the inconvenience';

export const STATION_DISABLED_MESSAGE =
  'This station is closed and the rental cannot end here';

export const USER_DEACTIVATED_ERROR_MESSAGE = `Your account has been deactivated. Please contact us for more information.`;
