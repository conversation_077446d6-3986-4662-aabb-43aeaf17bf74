import {
  TestingEndRentalCondition,
  ValidationData,
} from 'src/model/testing-end-rental.report';

export enum EndRentalConditionId {
  CAR_IN_SERVICE_ZONE = 'car-in-service-zone',
  DOORS_WINDOWS_CLOSED = 'doors-windows-closed',
  DOORS_LOCKED = 'doors-locked',
  ENGINE_OFF = 'engine-off',
  CAR_PLUGGED = 'car-plugged',
  PARKING_BRAKE_ENGAGED = 'parking-brake-engaged',
  VALID_QR_FORMAT = 'valid-qr-format',
  QR_IN_VALID_STATION_ZONE = 'qr-in-valid-station-zone',
  PARKING_HAS_AVAILABLE_SPACE = 'parking-has-available-space',
}

export const carInServiceZoneCond = new TestingEndRentalCondition(
  EndRentalConditionId.CAR_IN_SERVICE_ZONE,
  'Car in Service Zone',
  'Car is currently in Service Zone',
  'Please drive the car to Service Zone',
  (options: ValidationData) => {
    return !!(
      (options.onGoingRental && options.onGoingRental.currentStation) ||
      (options.telemetryCarInfo && options.telemetryCarInfo.currentStation)
    );
  },
  false,
  async (plate, userId, qrCode, fulfilled, eventService) => {
    await eventService.emitCarInServiceZoneValidation(
      plate,
      userId,
      qrCode,
      fulfilled,
    );
  },
);

export const doorsAndWindowsClosedCond = new TestingEndRentalCondition(
  EndRentalConditionId.DOORS_WINDOWS_CLOSED,
  'Doors and windows closed',
  'The doors and windows were closed properly',
  'Please close all doors and windows',
  (options: ValidationData) => {
    return !!(
      (options.telemetryCarStatus &&
        options.telemetryCarStatus.areDoorsAndWindowsClosed) ||
      (options.telemetryCarInfo &&
        options.telemetryCarInfo.areDoorsAndWindowsClosed)
    );
  },
  false,
  async (plate, userId, qrCode, fulfilled, eventService) => {
    await eventService.emitDoorsAndWindowsClosedValidation(
      plate,
      userId,
      qrCode,
      fulfilled,
    );
  },
);

export const doorsLockedCond = new TestingEndRentalCondition(
  EndRentalConditionId.DOORS_LOCKED,
  'Doors locked',
  'The doors were locked properly',
  'Please lock all doors',
  (options: ValidationData) => {
    return !!(
      (options.telemetryCarStatus &&
        options.telemetryCarStatus.locked &&
        options.telemetryCarStatus.immobilizerOn) ||
      (options.telemetryCarInfo &&
        options.telemetryCarInfo.locked &&
        options.telemetryCarInfo.immobilizerOn)
    );
  },
  false,
  async (plate, userId, qrCode, fulfilled, eventService) => {
    await eventService.emitDoorsLockedValidation(
      plate,
      userId,
      qrCode,
      fulfilled,
    );
  },
);

export const engineOffCond = new TestingEndRentalCondition(
  EndRentalConditionId.ENGINE_OFF,
  'Engine is turned off',
  'The engine is turned off',
  'Please turn the engine off',
  (options: ValidationData) =>
    !!(
      (options.telemetryCarStatus && !options.telemetryCarStatus.engineOn) ||
      (options.telemetryCarInfo && !options.telemetryCarInfo.engineOn)
    ),
  false,
  async (plate, userId, qrCode, fulfilled, eventService) => {
    await eventService.emitEngineOffValidation(
      plate,
      userId,
      qrCode,
      fulfilled,
    );
  },
);

export const carPluggedCond = new TestingEndRentalCondition(
  EndRentalConditionId.CAR_PLUGGED,
  'Car plugged to the station',
  'The car is correctly plugged to the station',
  'Please plug the car to a station',
  (options: ValidationData) => {
    return !!(
      (options.telemetryCarStatus && options.telemetryCarStatus.isPluggedIn) ||
      (options.telemetryCarInfo && options.telemetryCarInfo.isPluggedIn) ||
      (options.telemetryCarStatus && options.telemetryCarStatus.isCharging) || // Fallback to confirm if the vehicle is charging, plugged is false
      (options.telemetryCarInfo && options.telemetryCarInfo.isCharging)
    );
  },
  false,
  async (plate, userId, qrCode, fulfilled, eventService) => {
    await eventService.emitCarPluggedValidation(
      plate,
      userId,
      qrCode,
      fulfilled,
    );
  },
);

export const parkingEngageCond = new TestingEndRentalCondition(
  EndRentalConditionId.PARKING_BRAKE_ENGAGED,
  'Parking brake engaged',
  'Parking brake was engaged properly',
  'Please engage parking brake',
  (options: ValidationData) => {
    return !!(
      (options.telemetryCarStatus &&
        options.telemetryCarStatus.parkingBrakeOn) ||
      (options.telemetryCarInfo && options.telemetryCarInfo.parkingBrakeOn)
    );
  },
  false,
  async (plate, userId, qrCode, fulfilled, eventService) => {
    await eventService.emitParkingBrakeValidation(
      plate,
      userId,
      qrCode,
      fulfilled,
    );
  },
);

export const qrCodeFormatCond = new TestingEndRentalCondition(
  EndRentalConditionId.VALID_QR_FORMAT,
  'QR code format verification',
  'Station QR code was scanned successfully',
  'Please scan the station QR code properly',
  (options: ValidationData) => {
    return options.parkingLotValidationStatus?.isQRCodeValid() ?? false;
  },
  false,
  async (plate, userId, qrCode, fulfilled, eventService) => {
    await eventService.emitQRFormatValidation(plate, userId, qrCode, fulfilled);
  },
);

export const carInQRStationCond = new TestingEndRentalCondition(
  EndRentalConditionId.QR_IN_VALID_STATION_ZONE,
  'System cannot detect the car',
  'Car is parked at the scanned station',
  'Contact us via live chat for assistance with the issue',
  (options: ValidationData) => {
    return (
      options.parkingLotValidationStatus?.isAssociatedWithStation() ?? false
    );
  },
  false,
  async (plate, userId, qrCode, fulfilled, eventService) => {
    await eventService.emitQRStationValidation(
      plate,
      userId,
      qrCode,
      fulfilled,
    );
  },
);

export const parkingHasAvailableSpace = new TestingEndRentalCondition(
  EndRentalConditionId.PARKING_HAS_AVAILABLE_SPACE,
  'Parking is not available',
  'Parking is available',
  'Please find an available lot to park the car',
  (data: ValidationData) => {
    return data.parkingLotValidationStatus?.hasAvailableSpace() ?? false;
  },
  false,
  async (plate, userId, qrCode, fulfilled, eventService) => {
    await eventService.emitQRParkingSpaceValidation(
      plate,
      userId,
      qrCode,
      fulfilled,
    );
  },
);

export const testingEndRentalConditions: TestingEndRentalCondition[] = [
  carInServiceZoneCond,
  // parkingEngageCond,
  doorsAndWindowsClosedCond,
  doorsLockedCond,
  engineOffCond,
  carPluggedCond,
];

export const endRentalWithQrConditions: TestingEndRentalCondition[] = [
  qrCodeFormatCond,
  carInQRStationCond,
  parkingHasAvailableSpace,
];
