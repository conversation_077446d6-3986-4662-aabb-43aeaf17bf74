export const AVAILABILITY_OUTPUT_ERROR = 'AVA<PERSON>ABILITY_OUTPUT_ERROR';
export const VULOG_AUTH_ERROR = 'VULOG_AUTH_ERROR';
export const VULOG_API_ERROR = 'VULOG_API_ERROR';
export const VULOG_EVENT_ERROR = 'VULOG_EVENT_ERROR';
export const VALIDATION_RESPONSE_ERROR = 'VALIDATION_RESPONSE_ERROR';
export const MISSING_ARGUMENTS_ERROR = 'MISSING_ARGUMENTS_ERROR';
export const INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR';
export const STATION_API_ERROR = 'STATION_API_ERROR';
export const USS_API_ERROR = 'USS_API_ERROR';
export const RESERVATION_ERROR = 'RESERVATION_ERROR';
export const RENTAL_ERROR = 'RENTAL_ERROR';
export const CONFIGURATION_ERROR = 'CONFIGURATION_ERROR';
export const GRACE_PERIOD_ERROR = 'GRACE_PERIOD_ERROR';
export const MAX_CONCURRENT_TRIP_REACHED_ERROR =
  'MAX_CONCURRENT_TRIP_REACHED_ERROR';
export const CAR_NOT_FOUND_ERROR = 'CAR_NOT_FOUND_ERROR';
export const NON_LOCAL_RESERVATION_ERROR = 'NON_LOCAL_RESERVATION_ERROR';
export const RENTAL_TECHNICAL_ERROR = 'RENTAL_TECHNICAL_ERROR';
export const CAR_ERROR = 'CAR_ERROR';
export const STATION_ERROR = 'STATION_ERROR';
export const USER_BANNED_ERROR = 'USER_BANNED';
export const USER_DEACTIVATED_ERROR = 'USER_DEACTIVATED';
export const CAR_NOT_MATCHING_EXPECTED_MODEL_ERROR =
  'CAR_NOT_MATCHING_EXPECTED_MODEL_ERROR';
export const STATION_NOT_FOUND_ERROR = 'STATION_NOT_FOUND';
export const STATION_DISABLED_ERROR = 'STATION_DISABLED';
