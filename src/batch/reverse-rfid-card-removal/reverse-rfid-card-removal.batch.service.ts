import { Injectable } from '@nestjs/common';
import { DateTime } from 'luxon';
import { config } from 'src/config';
import { RfidUsageService } from 'src/logic/rfid-usage.service';
import { VulogAuthService } from 'src/logic/vulog/vulog-auth.service';
import { VulogUserService } from 'src/logic/vulog/vulog-user.service';
import { VulogUser } from 'src/model/vulog-user';
import { Counter } from './counter';
import { ReverseRfidCardRemovalBatchReportRepository } from './reserve-rfid-card-removal-batch-report.repository';
import { ReverseRfidCardRemovalBatchReport } from './reverse-rfid-card-removal-batch-report';

@Injectable()
export class ReverseRfidCardRemovalBatchService {
  constructor(
    private readonly vulogUserService: VulogUserService,
    private readonly reportRepository: ReverseRfidCardRemovalBatchReportRepository,
    private readonly counter: Counter,
    private readonly rfidUsageService: RfidUsageService,
    private readonly vulogAuthService: VulogAuthService,
  ) {}

  private readonly numberOfRecordsProcessedPerIteration =
    config.batchConfig.reverseRfidCardRemoval.processedPerIteration;

  async run(): Promise<void> {
    const start = DateTime.now();

    const initCount = await this.vulogUserService.countManyHavingRfidRemoved();

    const report: ReverseRfidCardRemovalBatchReport =
      new ReverseRfidCardRemovalBatchReport({ executedAt: DateTime.now() });

    await this.reportRepository.saveOne(report);

    const listOfTraversedRecordIds = [];

    do {
      const vulogUsersHavingRfidRemoved =
        await this.vulogUserService.findManyHavingRfidRemoved(
          1,
          this.numberOfRecordsProcessedPerIteration,
          listOfTraversedRecordIds,
        );

      vulogUsersHavingRfidRemoved.forEach((vgUser) => {
        listOfTraversedRecordIds.push(vgUser.id.value);
      });

      // Use callback queue to process task to avoid double loops, save some time
      setTimeout(() => {
        this.handleReverseRfidRemoval(vulogUsersHavingRfidRemoved);
      }, 0);
    } while (!this.counter.isFinished(initCount));

    console.log('Settle reverse RFID card removal');

    // Update the report
    const settledResult = this.counter.getSettledResult(initCount);

    report.settle(
      settledResult.nbTotal,
      settledResult.nbProcessed,
      settledResult.errors,
    );

    await this.reportRepository.saveOne(report);

    console.log('Settlement Result', settledResult);

    console.log('Report is saved into database');

    console.log('Time handled: ', DateTime.now().diff(start).toHuman());
  }

  private async handleReverseRfidRemoval(
    vulogUsersHavingRfidRemoved: VulogUser[],
  ): Promise<void> {
    await Promise.allSettled(
      vulogUsersHavingRfidRemoved.map(async (vgUser) => {
        try {
          await this.rfidUsageService.enableRfidUsage(
            vgUser.bsgUserId,
            await this.vulogAuthService.setupAuthConfig(),
          );

          this.counter.incrementProcessed();
          this.counter.incrementTotal();
        } catch (err) {
          this.counter.incrementTotal();

          this.counter.concatError({
            vulogUserId: vgUser.id.value,
            reason: err.message,
          });
        }
      }),
    );
  }
}
