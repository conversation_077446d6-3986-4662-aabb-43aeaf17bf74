import '../../env';

import 'newrelic';

import { AppLogger } from '@bluesg-2/monitoring';
import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { ReverseRfidCardRemovalAppModule } from './reverse-rfid-card-removal.app.module';
import { ReverseRfidCardRemovalBatchService } from './reverse-rfid-card-removal.batch.service';

async function bootstrap() {
  const app = await NestFactory.create(ReverseRfidCardRemovalAppModule, {
    bufferLogs: true,
  });

  app.useLogger(app.get(AppLogger));

  console.log('Logger ready');

  console.log('Start: Reverse RFID Card Removal');

  const reserveRfidCardRemovalBatchService: ReverseRfidCardRemovalBatchService =
    await app.get(ReverseRfidCardRemovalBatchService);

  await reserveRfidCardRemovalBatchService.run();

  await app.close();
}

bootstrap()
  .then(() => {
    process.exit();
  })
  .catch((error) => {
    console.error(`Error while starting the update: ${error.message}`, {
      cause: error,
    });
    Logger.error(`Error while starting the update: ${error.message}`, {
      cause: error,
    });
    process.exit(1);
  });
