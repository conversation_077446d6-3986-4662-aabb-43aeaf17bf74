import { BaseEntity } from 'src/model/repositories/entities/base.entity';
import { Column, Entity } from 'typeorm';
import { ReverseRfidCardRemovalBatchReport } from './reverse-rfid-card-removal-batch-report';

@Entity({ name: 'reverse_rfid_card_removal_batch_report' })
export class ReverseRfidCardRemovalBatchReportEntity extends BaseEntity {
  @Column({
    type: 'timestamptz',
    comment: 'The timestamp that the batch executed',
  })
  executedAt: Date;

  @Column({
    type: 'integer',
    default: 0,
    comment: 'Total of customers having RFID card removed',
  })
  total: number;

  @Column({
    type: 'integer',
    default: 0,
    comment:
      'Number of customers having RFID card enabled / Total of customers having RFID card removed',
  })
  processed: number;

  @Column({
    type: 'varchar',
    nullable: true,
    comment:
      'List of customer IDs having error, timestamp and correlated reason in raw text for debugging',
  })
  errors?: string;

  static from(
    domain: ReverseRfidCardRemovalBatchReport,
  ): ReverseRfidCardRemovalBatchReportEntity {
    const entity = new ReverseRfidCardRemovalBatchReportEntity();

    entity.id = domain.getId();
    entity.executedAt = domain.getExecutedAt().toJSDate();
    entity.total = domain.getTotal();
    entity.processed = domain.getProcessed();
    entity.errors = domain.getErrors();

    return entity;
  }
}
