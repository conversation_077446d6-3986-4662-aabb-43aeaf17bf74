import { MonitoringModule } from '@bluesg-2/monitoring';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmHealthIndicator } from '@nestjs/terminus';
import { ClsModule } from 'nestjs-cls';
import { FeatureFlagModule } from '~shared/feature-flag/feature-flag.module';
import { HttpLoggerModule } from '~shared/http-logger/http-logger.module';
import { LocalEventEmitterModule } from '~shared/local-event-emitter/local-event-emitter.module';
import { NewRelicModule } from '~shared/newrelic/newrelic.module';
import { CacheConfigModule } from '../../cache-config.module';
import { getEnvFilePath } from '../../common/utils/get-env-file-path.util';
import * as AppNestConfig from '../../config/app.nestjs.config';
import { DatabaseModule } from '../../database/database.module';
import { CacheService } from '../../logic/cache/cache.service';
import { LogicModule } from '../../logic/logic.module';
import { Counter } from './counter';
import { ReverseRfidCardRemovalBatchService } from './reverse-rfid-card-removal.batch.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [...Object.values(AppNestConfig)],
      envFilePath: getEnvFilePath(),
    }),
    ClsModule.forRoot({
      global: true,
    }),
    MonitoringModule.registerAsync({
      imports: [LogicModule],
      useFactory: async (db: TypeOrmHealthIndicator, cache: CacheService) => ({
        health: {
          dependencies: [
            // TODO: check health of Vulog gateway
            () => db.pingCheck('database'),
            () => cache.checkIfHealthy(),
          ],
        },
      }),
      inject: [TypeOrmHealthIndicator, CacheService],
    }),
    DatabaseModule,
    CacheConfigModule,
    LogicModule,
    HttpLoggerModule,
    LocalEventEmitterModule,
    FeatureFlagModule,
    NewRelicModule,
  ],
  providers: [ReverseRfidCardRemovalBatchService, Counter],
})
export class ReverseRfidCardRemovalAppModule {}
