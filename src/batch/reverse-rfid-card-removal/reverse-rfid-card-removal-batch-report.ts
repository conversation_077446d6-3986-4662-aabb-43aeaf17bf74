import { faker } from '@faker-js/faker';
import { DateTime } from 'luxon';
import { ReverseRfidCardRemovalBatchReportEntity } from './reverse-rfid-card-removal-batch-report.entity';

interface ReverseRfidCardRemovalBatchReportConstructorProps {
  id?: string;
  executedAt: DateTime;
}

export class ReverseRfidCardRemovalBatchReport {
  constructor(props?: ReverseRfidCardRemovalBatchReportConstructorProps) {
    Object.assign(this, props);
  }

  private id?: string = faker.string.uuid();

  private executedAt: DateTime;

  private total? = 0;

  private processed? = 0;

  private errors?: string = null;

  settle(total: number, processed: number, errors: string): void {
    this.total = total;
    this.processed = processed;
    this.errors = errors;
  }

  public getId(): string {
    return this.id;
  }

  public getExecutedAt(): DateTime {
    return this.executedAt;
  }

  public getTotal(): number {
    return this.total;
  }

  public getProcessed(): number {
    return this.processed;
  }

  public getErrors(): string {
    return this.errors;
  }

  static from(
    entity: ReverseRfidCardRemovalBatchReportEntity,
  ): ReverseRfidCardRemovalBatchReport {
    const report = new ReverseRfidCardRemovalBatchReport({
      id: entity.id,
      executedAt: DateTime.fromJSDate(entity.executedAt),
    });

    report.settle(entity.total, entity.processed, entity.errors);

    return report;
  }
}
