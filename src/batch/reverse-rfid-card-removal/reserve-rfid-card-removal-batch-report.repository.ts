import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TypeOrmBaseRepository } from 'src/model/repositories/typeorm-base-repository';
import { Repository } from 'typeorm';
import { ReverseRfidCardRemovalBatchReport } from './reverse-rfid-card-removal-batch-report';
import { ReverseRfidCardRemovalBatchReportEntity } from './reverse-rfid-card-removal-batch-report.entity';

@Injectable()
export class ReverseRfidCardRemovalBatchReportRepository extends TypeOrmBaseRepository<ReverseRfidCardRemovalBatchReportEntity> {
  constructor(
    @InjectRepository(ReverseRfidCardRemovalBatchReportEntity)
    private reservationRepository: Repository<ReverseRfidCardRemovalBatchReportEntity>,
  ) {
    super(reservationRepository);
  }

  async saveOne(
    domain: ReverseRfidCardRemovalBatchReport,
  ): Promise<ReverseRfidCardRemovalBatchReport> {
    return ReverseRfidCardRemovalBatchReport.from(
      (
        await this.save([ReverseRfidCardRemovalBatchReportEntity.from(domain)])
      )[0],
    );
  }

  async getOne(id: string): Promise<ReverseRfidCardRemovalBatchReport> {
    const value = await this.findOne({
      where: {
        id,
      },
    });

    return value ? ReverseRfidCardRemovalBatchReport.from(value) : null;
  }
}
