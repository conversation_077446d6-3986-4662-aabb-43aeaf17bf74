import { Injectable } from '@nestjs/common';

@Injectable()
export class Counter {
  private total? = 0;

  private processed? = 0;

  private errors?: string = '';

  incrementTotal(): void {
    this.total += 1;
  }

  incrementProcessed(): void {
    this.processed += 1;
  }

  reset(): void {
    this.total = 0;
    this.processed = 0;
    this.errors = '';
  }

  concatError({
    vulogUserId,
    reason,
  }: {
    vulogUserId: string;
    reason: string;
  }): void {
    this.errors = this.errors.concat(
      '----------------',
      JSON.stringify({ vulogUserId, reason }),
    );
  }

  getSettledResult(expectedTotal: number): {
    nbTotal: number;
    nbProcessed: number;
    errors: string;
  } {
    if (!this.isFinished(expectedTotal))
      return {
        nbTotal: 0,
        nbProcessed: 0,
        errors: null,
      };

    return {
      nbTotal: this.total,
      nbProcessed: this.processed,
      errors: this.errors,
    };
  }

  isFinished(expectedTotal: number): boolean {
    return this.total >= expectedTotal;
  }
}
