import '../../env';

import 'newrelic';

import { AppLogger } from '@bluesg-2/monitoring';
import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { UpdateCarsAppModule } from './update-cars.app.module';
import { UpdateCarsBatchService } from './update-cars.batch.service';

async function bootstrap() {
  const app = await NestFactory.create(UpdateCarsAppModule, {
    bufferLogs: true,
  });
  app.useLogger(app.get(AppLogger));

  Logger.log('Logger ready');

  const updateCarsBatchService: UpdateCarsBatchService = await app.get(
    UpdateCarsBatchService,
  );

  Logger.log('Start: Update of the Cars');

  const {
    numberOfVulogCars,
    numberOfBlueSGCars,
    numberOfBlueSGCarsUpdated,
    numberOfBlueSGCarsCreated,
    numberOfVulogCarsIgnored,
  } = await updateCarsBatchService.updateStaticCarInformation();

  Logger.log('End: Update of the Cars', {
    numberOfVulogCars,
    numberOfBlueSGCars,
    numberOfBlueSGCarsUpdated,
    numberOfBlueSGCarsCreated,
    numberOfVulogCarsIgnored,
  });

  await app.close();
}

bootstrap()
  .then(() => {
    process.exit();
  })
  .catch((error) => {
    console.error(`RAW: Error while starting the update: ${error.message}`, {
      cause: error,
    });

    Logger.error(`Error while starting the update: ${error.message}`, {
      cause: error,
    });

    process.exit(1);
  });
