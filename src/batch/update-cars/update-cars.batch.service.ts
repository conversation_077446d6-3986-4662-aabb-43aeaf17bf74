import { Injectable, Logger } from '@nestjs/common';
import { isNotEmpty } from 'class-validator';
import { MapUtils } from 'src/common/utils/map.util';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { CarService } from 'src/logic/car/car.service';
import { Car } from 'src/logic/car/domain/car';
import { CarRepository } from 'src/model/repositories/car.repository';
import { StaticCar } from 'src/model/static.car';

@Injectable()
export class UpdateCarsBatchService {
  constructor(
    private readonly carService: CarService,
    private readonly carRepo: CarRepository,
    private readonly vulogCarService: VulogCarService,
  ) {}

  private vulogStaticCarAccessPattern(vulogCarId: string, value: string) {
    return `${vulogCarId}_${value}`;
  }

  async updateStaticCarInformation(): Promise<{
    numberOfVulogCars: number;
    numberOfBlueSGCars: number;
    numberOfBlueSGCarsUpdated: number;
    numberOfBlueSGCarsCreated: number;
    numberOfBlueSGCarsCreatedToReplaceOldOnes: number;
    numberOfVulogCarsIgnored: number;
  }> {
    const carVinMap: Map<string, Car> = await this.carService.getMapByVin();
    const carPlateMap: Map<string, Car> = await this.carService.getMapByPlate();
    const carIdMap: Map<string, Car> = await this.carService.getMapByVulogId();

    const vulogStaticCars: StaticCar[] =
      await this.vulogCarService.getAllStaticCars();

    const vulogStaticCarsVinMap: Map<string, StaticCar> = MapUtils.build(
      vulogStaticCars,
      (vulogStaticCar) =>
        this.vulogStaticCarAccessPattern(
          vulogStaticCar.id.value,
          vulogStaticCar.vin,
        ),
    );
    const vulogStaticCarsPlateMap: Map<string, StaticCar> = MapUtils.build(
      vulogStaticCars,
      (vulogStaticCar) =>
        this.vulogStaticCarAccessPattern(
          vulogStaticCar.id.value,
          vulogStaticCar.plate.value,
        ),
    );

    const numberOfVulogCars: number = vulogStaticCars.length;
    let numberOfBlueSGCarsUpdated = 0;
    let numberOfBlueSGCarsCreated = 0;
    let numberOfVulogCarsIgnored = 0;
    let numberOfBlueSGCarsCreatedToReplaceOldOnes = 0;
    let numberOfBlueSGCars = 0;

    await Promise.allSettled(
      vulogStaticCars.map(async (vulogStaticCar) => {
        const existingOneByVin: Car = carVinMap.get(vulogStaticCar.vin);
        const existingOneByPlate: Car = carPlateMap.get(
          vulogStaticCar.plate?.value,
        );

        let isVinExisting = false;
        let isPlateExisting = false;

        let existingCar: Car;

        if (existingOneByVin) {
          isVinExisting = true;

          existingCar = existingOneByVin;
        }

        if (existingOneByPlate) {
          isPlateExisting = true;

          existingCar = existingOneByPlate;
        }

        // There is another case when plate and vin of existing car is modified, it should go into this case
        if (!existingCar && carIdMap.has(vulogStaticCar.id.value)) {
          await this.carService.upsertOne(vulogStaticCar.toCar());

          numberOfBlueSGCarsUpdated++;
        }
        // If there is no car having same VIN or plate in database, then insert it into DB.
        else if (!existingCar) {
          await this.carService.save(vulogStaticCar.toCar());

          numberOfBlueSGCarsCreated++;
        }
        // If there is existing car having same VIN or plate with pulled Vulog static car
        else {
          if (existingCar.vulogId.value !== vulogStaticCar.sourceId.value) {
            if (existingCar.model !== vulogStaticCar.model.value) {
              Logger.warn(
                `The new car (Vulog Car ID: ${vulogStaticCar.sourceId.value}) has the same VIN or Plate with existing car (Vulog Car ID: ${existingCar.vulogId.value}) but they have different car model. No action should be performed further.`,
              );

              numberOfVulogCarsIgnored++;

              return;
            }

            if (
              !vulogStaticCar.archived &&
              isNotEmpty(existingCar.isActive) &&
              !existingCar.isActive
            ) {
              await this.carService.save(vulogStaticCar.toCar());

              numberOfBlueSGCarsCreatedToReplaceOldOnes++;

              return;
            }

            let existingVulogStaticCar: StaticCar;

            if (isVinExisting) {
              existingVulogStaticCar = vulogStaticCarsVinMap.get(
                this.vulogStaticCarAccessPattern(
                  existingCar.vulogId.value,
                  existingCar.vin,
                ),
              );
            }

            if (isPlateExisting) {
              existingVulogStaticCar = vulogStaticCarsPlateMap.get(
                this.vulogStaticCarAccessPattern(
                  existingCar.vulogId.value,
                  existingCar.plate.value,
                ),
              );
            }

            // Is Vulog static car of existing car "archived"?
            if (
              existingVulogStaticCar &&
              existingVulogStaticCar.archived &&
              !vulogStaticCar.archived
            ) {
              // Update the existing one to inactive
              existingCar.isActive = false;
              await this.carService.upsertOne(existingCar);

              // Save the new one. In this step, there are 2 cases:
              // - Same VIN as existing car, but different plate
              // - Same plate as existing car, but different VIN
              await this.carService.save(vulogStaticCar.toCar());

              numberOfBlueSGCarsUpdated++;
              numberOfBlueSGCarsCreatedToReplaceOldOnes++;
            }
            // This case happens when CCO made vulog static car of existing car to be archived and new vulog static car to be archived also
            // The new one could be marked "not archived" afterwards, so better to keep the new one "archived" until CCO performs manual new insert in Vulog AiMa, then the batch runs
            else if (
              existingVulogStaticCar &&
              existingVulogStaticCar.archived &&
              vulogStaticCar.archived
            ) {
              await this.carService.upsertOne(
                existingVulogStaticCar.toCar(existingCar.id),
              );

              await this.carService.save(vulogStaticCar.toCar());

              numberOfBlueSGCarsUpdated++;
              numberOfBlueSGCarsCreatedToReplaceOldOnes++;
            }
            // The existing car is not yet inactive (Also, archived on Vulog AiMa). Hence, no action allowed until CCO double checks with engineering team
            else {
              Logger.warn(
                `The new car (Vulog Car ID: ${vulogStaticCar.sourceId.value}) has the same VIN or Plate with existing car (Vulog Car ID: ${existingCar.vulogId.value}) but the existing car is not archived yet. No action should be performed further.`,
              );

              numberOfVulogCarsIgnored++;
            }
          }
          // For the case, same vulog ID, just update it accordingly
          else {
            await this.carService.upsertOne(
              vulogStaticCar.toCar(existingCar.id),
            );

            numberOfBlueSGCarsUpdated++;
          }
        }
      }),
    ).then((values) => {
      const errorResults = values.filter(
        (v) => v.status === 'rejected',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ) as any;

      if (errorResults.length) {
        Logger.error('Settled Error: ', errorResults);
      }
    });

    numberOfBlueSGCars = await this.carRepo.count();

    return {
      numberOfVulogCars,
      numberOfBlueSGCars,
      numberOfBlueSGCarsUpdated,
      numberOfBlueSGCarsCreated,
      numberOfVulogCarsIgnored,
      numberOfBlueSGCarsCreatedToReplaceOldOnes,
    };
  }
}
