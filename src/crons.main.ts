import './env';

// import 'newrelic';

import { AppLogger } from '@bluesg-2/monitoring';
import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { config } from './config';
import { CronsAppModule } from './crons.app.module';

const crsCronConfig = config.cronServer;

async function bootstrap() {
  Logger.log(`${crsCronConfig.serverName} is starting!`);

  const app = await NestFactory.create(CronsAppModule, {
    bufferLogs: true,
  });

  app.useLogger(app.get(AppLogger));

  await app.listen(crsCronConfig.port, () =>
    Logger.log(
      `${crsCronConfig.serverName} is listening at port ${crsCronConfig.port}`,
    ),
  );
}

bootstrap().catch((error) => {
  console.error(
    `Error while starting ${crsCronConfig.serverName}: ${error.message}`,
    {
      cause: error,
    },
  );

  process.exit(1);
});
