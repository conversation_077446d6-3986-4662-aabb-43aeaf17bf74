import { Module } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import { config } from 'src/config';
import { httpServiceConfig } from 'src/config/app.nestjs.config';
import { StationExternalService } from 'src/external/station/station.external.service';
import { HttpModule } from 'src/shared/http/http.module';

@Module({
  imports: [
    HttpModule.registerAsync({
      serviceName: config.httpServiceConfig.stationName,
      useFactory: (
        httpServiceConfiguration: ConfigType<typeof httpServiceConfig>,
      ) => ({
        baseURL: httpServiceConfiguration.stationUrl,
        timeout: httpServiceConfiguration.timeout,
      }),
      inject: [httpServiceConfig.KEY],
      imports: [],
    }),
  ],
  providers: [StationExternalService],
  exports: [StationExternalService],
})
export class StationExternalModule {}
