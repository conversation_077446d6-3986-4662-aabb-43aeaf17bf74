import { StationService as IStationService } from '@bluesg-2/queue-pop';
import { Inject, Injectable } from '@nestjs/common';
import { config } from 'src/config';
import { InternalApiService } from 'src/shared/http/internal-api.service';
import { StationInternalApiService } from './apis/station.internal.api.service';

@Injectable()
export class StationExternalService implements IStationService {
  /**
   * API Service to call `internal` APIs
   */
  public internalApi: StationInternalApiService;

  constructor(
    @Inject(config.httpServiceConfig.stationName)
    private readonly stationApiService: InternalApiService,
  ) {
    this.internalApi = new StationInternalApiService(this.stationApiService);
  }

  exist: (id: string) => Promise<boolean> = async (id: string) => {
    const station = await this.internalApi.getStationDetail(id);

    return !!station;
  };

  existMany: (ids: string[]) => Promise<boolean> = async (ids: string[]) => {
    const results = await Promise.all(ids.map((id) => this.exist(id)));

    return results.every((item) => item);
  };
}
