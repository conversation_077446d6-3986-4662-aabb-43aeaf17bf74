import { ApiProperty } from '@nestjs/swagger';

import { faker } from '@faker-js/faker';
import { Type } from 'class-transformer';
import { StationId } from 'src/common/tiny-types';
import { Station } from 'src/model/station';
import { StationStatus } from '../station-status';

export enum ProviderName {
  LEGACY = 'legacy',
  BLUECHARGE = 'bluecharge',
}

export enum ExternalStationCapability {
  Rental = 'rental',
  Charging = 'charging',
  Subscription = 'subscription',
}

/**
 * A station contains many charing slots, and it has many capabilities.
 * Each capability has its own status, such as "nonexistent", "operational"
 */
export class StationCapabilities {
  constructor(props: StationCapabilities) {
    Object.assign(this, props);
  }

  /**
   * supportsMobileBadgeActivation
   */
  cardActivation?: boolean;

  /**
   * Rental/Parking
   */
  [ExternalStationCapability.Rental]?: boolean;

  /**
   * Subscription Kiosk
   */
  [ExternalStationCapability.Subscription]?: boolean;

  /**
   * Public Charging
   */
  [ExternalStationCapability.Charging]?: boolean;

  static fromExternalStation(
    supportsMobileBadgeActivation: boolean,
    capabilities: ExternalStationCapability[],
  ): StationCapabilities {
    return new StationCapabilities({
      cardActivation: supportsMobileBadgeActivation,
      rental: !!capabilities.find(
        (capability) => capability === ExternalStationCapability.Rental,
      ),
      charging: !!capabilities.find(
        (capability) => capability === ExternalStationCapability.Charging,
      ),
      subscription: !!capabilities.find(
        (capability) => capability === ExternalStationCapability.Subscription,
      ),
    });
  }
}

class StationCapabilitiesDto extends StationCapabilities {
  @ApiProperty({
    type: 'boolean',
    description: "One of station's capabilities - Support RFID card pairing",
    required: false,
  })
  cardActivation?: boolean;

  @ApiProperty({
    type: 'boolean',
    description: "One of station's capabilities - Rental",
    required: false,
  })
  [ExternalStationCapability.Rental]?: boolean;

  @ApiProperty({
    type: 'boolean',
    description: "One of station's capabilities - Subscription",
    required: false,
  })
  [ExternalStationCapability.Subscription]?: boolean;

  @ApiProperty({
    type: 'boolean',
    description: "One of station's capabilities - Charging",
    required: false,
  })
  [ExternalStationCapability.Charging]?: boolean;
}

export class InternalStationResponseDtoV1 {
  constructor(props?: InternalStationResponseDtoV1) {
    Object.assign(this, props);
  }

  @ApiProperty({
    description: 'Internally assigned UUID that identifies a station',
    example: faker.string.uuid(),
  })
  public readonly id: string;

  @ApiProperty({
    description:
      'UUID string assigned by the source station provider that identifies a station',
    example: faker.string.uuid(),
  })
  public readonly sourceId: string;

  @ApiProperty({
    description: 'Name of the station provider',
    enum: ProviderName,
  })
  public source: ProviderName;

  @ApiProperty({
    description: 'Display name of the station',
  })
  public displayName: string;

  @ApiProperty({
    description: "Latitude of the station's coordinates",
    example: '37.7833',
  })
  public latitude: string;

  @ApiProperty({
    description: "Longitude of the station's coordinates",
    example: '-122.4167',
  })
  public longitude: string;

  @ApiProperty({
    description: "Street of the station's address",
  })
  public street: string;

  @ApiProperty({
    description: "Postal code of the station's address",
  })
  public postalCode: string;

  @ApiProperty({
    description: "City of the station's address",
  })
  public city: string;

  @ApiProperty({
    description:
      'UUID representing the Vulog zone that represents this station',
  })
  public zoneId: string;

  @ApiProperty({
    description: 'The capabilities of a station',
  })
  @Type(() => StationCapabilitiesDto)
  public capabilities?: StationCapabilitiesDto;

  @ApiProperty({
    description: 'Date and time the station was created',
  })
  public readonly createdAt?: string;

  @ApiProperty({
    description: 'Date and time the station details were last updated',
  })
  public updatedAt?: string;

  @ApiProperty({
    description: 'Parking lots available in a chargeless station',
  })
  parkingSpacesAvailable: number | null;

  @ApiProperty({
    description: 'Total number of parking lots in a chargeless station',
  })
  totalParkingSpaces: number | null;

  @ApiProperty({
    description: 'Status of station',
    enum: StationStatus,
  })
  status: StationStatus;

  toStation(): Station {
    return new Station({
      id: new StationId(this.id),
      sourceId: this.sourceId,
      zoneId: this.zoneId,
      displayName: this.displayName,
      latitude: Number(this.latitude),
      longitude: Number(this.longitude),
      postalCode: this.postalCode,
      street: this.street,
      parkingSpacesAvailable: this.parkingSpacesAvailable,
      totalParkingSpaces: this.totalParkingSpaces,
      status: this.status,
    });
  }
}
