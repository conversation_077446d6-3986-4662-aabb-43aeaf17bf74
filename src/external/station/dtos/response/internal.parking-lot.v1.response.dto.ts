import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from 'class-transformer';
import { ParkingLot } from 'src/model/parking-lot';
import { InternalStationResponseDtoV1 } from './internal.station-response.v1.response.dto';

export enum ParkingLotStatus {
  AVAILABLE = 'AVAILABLE',
  CHARGING = 'CHARGING',
  OUTOFORDER = 'OUTOFORDER',
  UNKNOWN = 'UNKNOWN',
}

export class InternalParkingLotResponseDtoV1 {
  @ApiProperty({
    description: 'Status of parking lot',
    enum: ParkingLotStatus,
  })
  public status: ParkingLotStatus;

  @ApiProperty({
    description: 'Station details',
    type: InternalStationResponseDtoV1,
  })
  @Type(() => InternalStationResponseDtoV1)
  public station: InternalStationResponseDtoV1;

  @ApiPropertyOptional({
    description: 'Does station have available space',
  })
  isAvailable?: boolean;

  toParkingLot(): ParkingLot {
    return new ParkingLot({
      status: this.status,
      station: this.station.toStation(),
      isAvailable: this.isAvailable,
    });
  }
}
