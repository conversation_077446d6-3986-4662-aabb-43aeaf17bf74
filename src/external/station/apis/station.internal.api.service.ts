import { HttpStatus, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as qs from 'qs';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { ApiVersion } from 'src/common/constants/api-versions';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { InternalApiService } from 'src/shared/http/internal-api.service';
import { InternalStationResponseDtoV1 } from '../dtos/response/internal.station-response.v1.response.dto';
import { InternalParkingLotResponseDtoV1 } from '../dtos/response/internal.parking-lot.v1.response.dto';
import { StationError } from 'src/common/errors/station.error';
import { InternalMicroserviceExceptionError } from 'src/common/errors/http-exception-error';

@Injectable()
export class StationInternalApiService {
  private rootRouteSegment = 'api';

  constructor(private readonly stationApiService: InternalApiService) {}

  async getStationDetail(
    stationId: string,
  ): Promise<InternalStationResponseDtoV1> {
    const response: BaseResponseDto<InternalStationResponseDtoV1> =
      await this.stationApiService.get(
        ApiRouteUtils.buildApiRouteSegment(
          this.rootRouteSegment as any,
          ApiVersion.V1,
          ['stations', stationId],
        ),
        { omitAuthorization: true },
      );

    return response?.result
      ? plainToInstance(InternalStationResponseDtoV1, response?.result)
      : null;
  }

  async getParkingLotByQR(qrCode: string): Promise<InternalParkingLotResponseDtoV1> {
    try {
      const response: BaseResponseDto<InternalParkingLotResponseDtoV1> =
        await this.stationApiService.post(
          ApiRouteUtils.buildApiRouteSegment(
            this.rootRouteSegment as any,
            ApiVersion.V1,
            ['parking-lots', 'parking-lots', 'qr'],
          ),
          { qrCode },
          { omitAuthorization: true },
        );
      return response?.result
        ? plainToInstance(InternalParkingLotResponseDtoV1, response?.result)
        : null;
    } catch (err) {
      if (err instanceof InternalMicroserviceExceptionError) {
        throw new StationError(
          err.message,
          err.statusCode
        );
      } else {
        throw err;
      }
    }
  }

  async getStationByZones(
    zoneIds: string[],
  ): Promise<InternalStationResponseDtoV1[]> {
    const response: BaseResponseDto<InternalStationResponseDtoV1[]> =
      await this.stationApiService.get(
        `${ApiRouteUtils.buildApiRouteSegment(
          this.rootRouteSegment as any,
          ApiVersion.V1,
          ['stations'],
        )}?${qs.stringify({ zoneIds: zoneIds.join(',') })}`,
        { omitAuthorization: true },
      );

    return response?.result
      ? response.result.map((item) =>
          plainToInstance(InternalStationResponseDtoV1, item),
        )
      : [];
  }
}
