import { Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as QueryString from 'qs';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { Page } from 'src/common/query/page';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { parseAndValidate } from 'src/common/utils/parse-and-validate-dto';
import { InternalApiService } from 'src/shared/http/internal-api.service';
import { InternalGetListSubscriptionReqDescriptionV1 } from '../dtos/request/internal.get-list-subscriptions.v1.req.dto';
import { InternalUserDetailResponseDtoV1 } from '../dtos/response/internal.user-detail.v1.response.dto';
import { SubscriptionUssDto } from '../dtos/response/subscription.uss.dto';

@Injectable()
export class UserSubscriptionInternalApiService {
  private rootRouteSegment = RootRouteSegment.Internal;

  constructor(
    private readonly userSubscriptionApiService: InternalApiService,
  ) {}

  async getUserDetail(
    userId: string,
  ): Promise<InternalUserDetailResponseDtoV1> {
    const response: BaseResponseDto<InternalUserDetailResponseDtoV1> =
      await this.userSubscriptionApiService.get(
        ApiRouteUtils.buildApiRouteSegment(
          this.rootRouteSegment,
          ApiVersion.V1,
          ['users', userId],
        ),
        { omitAuthorization: true },
      );

    return response?.result
      ? plainToInstance(InternalUserDetailResponseDtoV1, response.result)
      : null;
  }

  async getAllActiveUsers(
    page = 1,
    pageSize = 20,
  ): Promise<Page<InternalUserDetailResponseDtoV1[]>> {
    const response = await this.userSubscriptionApiService.get<
      InternalUserDetailResponseDtoV1[]
    >(
      `${ApiRouteUtils.buildApiRouteSegment(
        this.rootRouteSegment,
        ApiVersion.V1,
        ['users'],
      )}?${QueryString.stringify({ page, pageSize })}`,
    );

    return new Page(
      response?.result
        ? ((await parseAndValidate(
            response.result,
            InternalUserDetailResponseDtoV1,
          )) as unknown as InternalUserDetailResponseDtoV1[])
        : [],
      pageSize,
      !response || response?.pagination.totalPages === page,
    );
  }

  async getListSubscriptions(
    req: InternalGetListSubscriptionReqDescriptionV1,
  ): Promise<SubscriptionUssDto[]> {
    const response: BaseResponseDto<SubscriptionUssDto[]> =
      await this.userSubscriptionApiService.post(
        ApiRouteUtils.buildApiRouteSegment(
          this.rootRouteSegment,
          ApiVersion.V1,
          ['subscription', 'list'],
        ),
        req,
        { omitAuthorization: true },
      );

    return response?.result;
  }
}
