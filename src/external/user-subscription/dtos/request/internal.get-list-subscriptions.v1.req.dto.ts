import { SubscriptionStatus } from '../../constants';

export class InternalGetListSubscriptionReqDescriptionV1 {
  constructor(props?: {
    ids?: string[];
    loadHistories?: boolean;
    loadPlan?: boolean;
    loadUser?: boolean;
    loadNextPlan?: boolean;
    loadDocument?: boolean;
    status?: SubscriptionStatus[];
    userIds?: string[];
  }) {
    this.ids = props?.ids || null;
    this.loadHistories = props?.loadHistories || false;
    this.loadUser = props?.loadUser || false;
    this.loadNextPlan = props?.loadNextPlan || false;
    this.loadPlan = props?.loadPlan || false;
    this.loadDocument = props?.loadDocument || false;
    this.status = props?.status || null;
    this.userIds = props?.userIds || null;
  }

  ids: string[] = null;
  userIds: string[] = null;
  status: SubscriptionStatus[] = null;
  loadHistories = false;
  loadPlan = false;
  loadUser = false;
  loadNextPlan = false;
  loadDocument = false;
}
