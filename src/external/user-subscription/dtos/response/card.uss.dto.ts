import { IsBoolean, IsOptional, IsUUID } from 'class-validator';
import { BsgUserId } from '../../../../common/tiny-types';
import { AccessCard } from '../../../../model/uss/access-card';
import { AccessCardId } from '../../../../model/uss/access-card-id';
import { AccessCardPayload } from '../../../../model/uss/access-card-payload';

export class CardUssDto {
  @IsUUID()
  @IsOptional()
  id: string;

  payload: string;

  label: string;

  usage: string;

  @IsUUID()
  @IsOptional()
  ownerId?: string;

  @IsBoolean()
  mayUseRental = true;

  createdAt: Date;

  modifiedAt: Date;

  @IsOptional()
  legacyId: number;

  constructor(
    id: string,
    payload: string,
    label: string,
    usage: string,
    ownerId: string,
    mayUseRental: boolean,
    createdAt: Date,
    modifiedAt: Date,
    legacyId: number,
  ) {
    this.id = id;
    this.payload = payload;
    this.label = label;
    this.usage = usage;
    this.ownerId = ownerId;
    this.mayUseRental = mayUseRental;
    this.createdAt = createdAt;
    this.modifiedAt = modifiedAt;
    this.legacyId = legacyId;
  }

  toAccessCard(ownerId?: BsgUserId): AccessCard {
    return new AccessCard(
      new AccessCardId(this.id),
      new AccessCardPayload(this.payload),
      this.label,
      this.usage,
      ownerId ? ownerId : new BsgUserId(this.ownerId),
      this.mayUseRental,
      this.createdAt,
      this.modifiedAt,
    );
  }
}
