import { SubscriptionStatus } from '../../constants';
import { InternalUserDetailResponseDtoV1 } from './internal.user-detail.v1.response.dto';
import { SubscriptionPlanUssDto } from './subscription-plan.uss.dto';

export class SubscriptionUssDto {
  id: string;

  subscriptionPlanId: string;

  nextSubscriptionPlanId: string;

  userId: string;

  status: SubscriptionStatus;

  isAutoRenewal: boolean;

  hrid?: string;

  activatedBy?: string;

  activatedAt?: Date;

  subscribedAt?: Date;

  isSystemActivated?: boolean;

  legacySubscriptionId?: number;

  subscribedWithThirdPartyRfid?: boolean;

  canceledAt?: Date;

  canceledReason?: string;

  terminatedAt?: Date;

  terminatedReason?: string;

  sentNotiTerminated?: boolean;

  paymentMethodLinked?: boolean;

  user?: InternalUserDetailResponseDtoV1;

  subscriptionPlan?: SubscriptionPlanUssDto;

  nextSubscriptionPlan?: SubscriptionPlanUssDto;
}
