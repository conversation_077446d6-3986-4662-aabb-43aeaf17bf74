import { Type } from 'class-transformer';
import { IsEmail, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { BsgUserId } from '../../../../common/tiny-types';
import { User } from '../../../../model/user';
import { Subscription } from '../../../../model/uss/subscription';
import { SubscriptionId } from '../../../../model/uss/subscriptionId';
import { SubscriptionPlanId } from '../../../../model/uss/subscriptionPlanId';
import { SubscriptionStatus, UserStatus, UserTitle } from '../../constants';
import { CardUssDto } from './card.uss.dto';

export class UserSubscriptionDetail {
  @IsUUID()
  id: string;

  @IsUUID()
  subscriptionPlanId: string;

  @IsUUID()
  nextSubscriptionPlanId: string;

  @IsUUID()
  userId: string;

  @IsEnum(SubscriptionStatus)
  status: SubscriptionStatus;

  @IsUUID()
  statusLabel: string;

  activatedAt: string;

  constructor(
    id: string,
    subscriptionPlanId: string,
    nextSubscriptionPlanId: string,
    userId: string,
    status: SubscriptionStatus,
    statusLabel: string,
    activatedAt: string,
  ) {
    this.id = id;
    this.subscriptionPlanId = subscriptionPlanId;
    this.nextSubscriptionPlanId = nextSubscriptionPlanId;
    this.userId = userId;
    this.status = status;
    this.statusLabel = statusLabel;
    this.activatedAt = activatedAt;
  }

  toSubscription(): Subscription {
    return new Subscription(
      new SubscriptionId(this.id),
      new SubscriptionPlanId(this.subscriptionPlanId),
      new BsgUserId(this.userId),
      this.status,
    );
  }
}

export class InternalUserDetailResponseDtoV1 {
  constructor(props?: InternalUserDetailResponseDtoV1) {
    Object.assign(this, props);
  }

  @IsUUID()
  id: string;

  @IsEnum(UserStatus)
  status: UserStatus;

  statusLabel: string;

  firstName: string;

  lastName: string;

  dateOfBirth: string;

  title: UserTitle;

  titleLabel: string;

  mobileNo: string;

  @IsEmail()
  email: string;

  preferredLanguage: string;

  preferredLanguageLabel: string;

  addressLine1: string;

  addressLine2: string;

  postalCode: string;

  @Type(() => UserSubscriptionDetail)
  currentSubscription: UserSubscriptionDetail;

  @Type(() => CardUssDto)
  @IsOptional()
  cards?: CardUssDto[];

  toUser(): User {
    const userId = new BsgUserId(this.id);
    return new User(
      userId,
      this.email,
      this.status,
      this.firstName,
      this.lastName,
      new Date(this.dateOfBirth),
      this.title,
      this.mobileNo,
      this.preferredLanguage,
      this.currentSubscription.toSubscription(),
      this.cards?.map((c) => c.toAccessCard(userId)),
    );
  }
}
