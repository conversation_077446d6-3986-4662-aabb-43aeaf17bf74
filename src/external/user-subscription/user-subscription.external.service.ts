import { Inject, Injectable } from '@nestjs/common';
import { USER_DEACTIVATED_ERROR_MESSAGE } from 'src/common/constants/messages';
import { DeactivatedUserError } from 'src/common/errors/deactivated-user.error';
import { InvalidUserConstraintError } from 'src/common/errors/invalid-user-contraint-error';
import { BannedUserError } from 'src/common/errors/user-is-banned-error';
import { BsgUserId } from 'src/common/tiny-types';
import { config } from 'src/config';
import { InternalApiService } from 'src/shared/http/internal-api.service';
import { UserSubscriptionInternalApiService } from './apis/user-subscription.internal.api.service';
import { SubscriptionStatus, UserStatus } from './constants';
import { InternalGetListSubscriptionReqDescriptionV1 } from './dtos/request/internal.get-list-subscriptions.v1.req.dto';
import { InternalUserDetailResponseDtoV1 } from './dtos/response/internal.user-detail.v1.response.dto';
import { SubscriptionUssDto } from './dtos/response/subscription.uss.dto';

@Injectable()
export class UserSubscriptionExternalService {
  /**
   * API Service to call `internal` APIs
   */
  public internalApi: UserSubscriptionInternalApiService;

  constructor(
    @Inject(config.httpServiceConfig.userSubscriptionName)
    private readonly userSubscriptionApiService: InternalApiService,
  ) {
    this.internalApi = new UserSubscriptionInternalApiService(
      this.userSubscriptionApiService,
    );
  }

  async validateUssUser(
    bsgUserId: BsgUserId,
  ): Promise<InternalUserDetailResponseDtoV1> {
    const ussUserDetail = await this.internalApi.getUserDetail(bsgUserId.value);

    if (!ussUserDetail) {
      throw new InvalidUserConstraintError(
        `BlueSG customer (ID: ${bsgUserId.value}) is not found`,
      );
    }

    if (ussUserDetail && ussUserDetail.status === UserStatus.BANNED) {
      throw new BannedUserError(
        `You’ve been banned due to a breach of policy. Please contact us for more information.`,
      );
    }

    if (ussUserDetail && ussUserDetail.status === UserStatus.DEACTIVATED) {
      throw new DeactivatedUserError(USER_DEACTIVATED_ERROR_MESSAGE);
    }

    if (ussUserDetail && ussUserDetail.status !== UserStatus.VERIFIED) {
      throw new InvalidUserConstraintError(
        `BlueSG customer must be verified customer to take part in reservation and rental flow`,
      );
    }

    if (
      ussUserDetail.currentSubscription &&
      ussUserDetail.currentSubscription.status !== SubscriptionStatus.ACTIVE
    ) {
      throw new InvalidUserConstraintError(
        `BlueSG customer must have an active subscription to take part in reservation and rental flow`,
      );
    }

    return ussUserDetail;
  }

  async getListSubscriptions(
    req: InternalGetListSubscriptionReqDescriptionV1,
  ): Promise<SubscriptionUssDto[]> {
    return this.internalApi.getListSubscriptions(req);
  }
}
