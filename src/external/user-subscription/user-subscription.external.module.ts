import { Module } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import { config } from 'src/config';
import { httpServiceConfig } from 'src/config/app.nestjs.config';
import { HttpModule } from 'src/shared/http/http.module';
import { UserSubscriptionExternalService } from './user-subscription.external.service';

@Module({
  imports: [
    HttpModule.registerAsync({
      serviceName: config.httpServiceConfig.userSubscriptionName,
      useFactory: (
        httpServiceConfiguration: ConfigType<typeof httpServiceConfig>,
      ) => ({
        baseURL: httpServiceConfiguration.userSubscriptionUrl,
        timeout: httpServiceConfiguration.timeout,
      }),
      inject: [httpServiceConfig.KEY],
      imports: [],
    }),
  ],
  providers: [UserSubscriptionExternalService],
  exports: [UserSubscriptionExternalService],
})
export class UserSubscriptionExternalModule {}
