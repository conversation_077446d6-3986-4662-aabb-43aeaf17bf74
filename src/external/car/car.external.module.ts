import { Module } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import { httpServiceConfig } from 'src/config/app.nestjs.config';
import { HttpModule } from 'src/shared/http/http.module';
import { CAR_SERVICE_DI_TOKEN } from '../constants';
import { CarExternalService } from './car.external.service';

@Module({
  imports: [
    HttpModule.registerAsync({
      serviceName: CAR_SERVICE_DI_TOKEN,
      useFactory: (
        httpServiceConfiguration: ConfigType<typeof httpServiceConfig>,
      ) => ({
        baseURL: httpServiceConfiguration.carServiceUrl,
        timeout: httpServiceConfiguration.timeout,
      }),
      inject: [httpServiceConfig.KEY],
      imports: [],
    }),
  ],
  providers: [CarExternalService],
  exports: [CarExternalService],
})
export class CarExternalModule {}
