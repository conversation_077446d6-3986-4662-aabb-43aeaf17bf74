import { Inject, Injectable } from '@nestjs/common';
import { InternalApiService } from 'src/shared/http/internal-api.service';
import { CAR_SERVICE_DI_TOKEN } from '../constants';
import { CarInternalApiService } from './apis/car.internal.api.service';

@Injectable()
export class CarExternalService {
  /**
   * API Service to call `internal` APIs
   */
  public internalApi: CarInternalApiService;

  constructor(
    @Inject(CAR_SERVICE_DI_TOKEN)
    private readonly carApiService: InternalApiService,
  ) {
    this.internalApi = new CarInternalApiService(this.carApiService);
  }
}
