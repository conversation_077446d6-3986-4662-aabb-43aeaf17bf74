export interface CarStateFilter {
  minBatteryLevel?: number;
}

export interface CarModelFilter {
  [carModelName: string]: CarStateFilter;
}

export class InternalAvailabilityMapRequestV1 {
  readonly stationIds?: string[];
  readonly default?: CarStateFilter;
  readonly carModels?: CarModelFilter;

  constructor(props?: {
    stationIds?: string[];
    default?: {
      minBatteryLevel?: number;
    };
    carModels?: { [carModelName: string]: { minBatteryLevel?: number } };
  }) {
    this.stationIds = props.stationIds;
    this.default = props.default;
    this.carModels = props.carModels;
  }
}
