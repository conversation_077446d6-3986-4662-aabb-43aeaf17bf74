export enum CSCarAvailability {
  AVAILABLE = 'AVAILABLE',
}

export class InternalCarsRequestV1 {
  readonly externalCarId?: string;
  readonly availability?: CSCarAvailability;
  readonly stationId?: string;
  readonly includeState: boolean;

  constructor(props?: {
    externalCarId?: string;
    availability?: CSCarAvailability;
    stationId?: string;
    includeState?: boolean;
  }) {
    this.externalCarId = props?.externalCarId;
    this.availability = props?.availability;
    this.stationId = props?.stationId;
    // Defaults to true if not provided
    this.includeState = props?.includeState ?? true;
  }
}
