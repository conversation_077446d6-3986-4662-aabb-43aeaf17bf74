import { Type } from 'class-transformer';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { Car, CarRealtimeMetadata } from 'src/logic/car/domain/car';

export class CSCarModel {
  id: string; // CS car model id
  name: string;
  hrid: string;

  constructor(props?: CSCarModel) {
    Object.assign(this, props);
  }

  static toCarModelEnum(dto: CSCarModel): CarModelEnum {
    return dto.name as CarModelEnum;
  }
}

export class CSCarState {
  locked: boolean;
  batteryLevel: number;
  allDoorsWindowsClosed: boolean;
  engineOn: boolean;
  cablePlugged: boolean;
  charging: boolean;

  constructor(props?: CSCarState) {
    Object.assign(this, props);
  }

  static toCarRealtimeMetadata(dto: CSCarState): CarRealtimeMetadata {
    return {
      isPlugged: dto.cablePlugged,
      batteryPercentage: dto.batteryLevel,
      isLocked: dto.locked,
    };
  }
}

export class InternalCarResponseV1 {
  id: string; // CS car id

  @Type(() => CSCarModel)
  carModel: CSCarModel;

  plateNumber: string;
  vin: string;
  externalFleetId: string;
  externalCarId: string; // Vulog car id
  stationId: string;
  availability: string;
  availabilityUpdatedAt: string;

  @Type(() => CSCarState)
  state: CSCarState;

  constructor(props?: InternalCarResponseV1) {
    Object.assign(this, props);
  }

  static toCar(dto: InternalCarResponseV1): Car {
    return new Car({
      id: null,
      model: CSCarModel.toCarModelEnum(dto.carModel),
      plate: dto.plateNumber,
      vin: dto.vin,
      vulogId: dto.externalCarId,
      realtimeMetadata: dto.state
        ? CSCarState.toCarRealtimeMetadata(dto.state)
        : undefined,
    });
  }
}
