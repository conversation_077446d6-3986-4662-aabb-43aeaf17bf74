import { Injectable } from '@nestjs/common';
import * as qs from 'qs';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { InternalApiService } from 'src/shared/http/internal-api.service';
import { InternalAvailabilityMapRequestV1 } from '../dtos/internal.availability-map.request.v1.dto';
import { InternalAvailabilityMapResponseV1 } from '../dtos/internal.availability-map.response.v1.dto';
import { InternalCarsRequestV1 } from '../dtos/internal.cars.request.v1.dto';
import { InternalCarResponseV1 } from '../dtos/internal.cars.response.v1.dto';

@Injectable()
export class CarInternalApiService {
  private rootRouteSegment = RootRouteSegment.Internal;

  constructor(private readonly carApiService: InternalApiService) {}

  async getAvailabilityMap(
    request: InternalAvailabilityMapRequestV1,
  ): Promise<InternalAvailabilityMapResponseV1> {
    const response: BaseResponseDto<InternalAvailabilityMapResponseV1> =
      await this.carApiService.post(
        `${ApiRouteUtils.buildApiRouteSegment(
          this.rootRouteSegment,
          ApiVersion.V1,
          ['availability-map'],
        )}`,
        request,
        { omitAuthorization: true },
      );

    return response?.result ? response.result : null;
  }

  async getCars(
    request: InternalCarsRequestV1,
  ): Promise<InternalCarResponseV1[]> {
    const response: BaseResponseDto<InternalCarResponseV1[]> =
      await this.carApiService.get(
        `${ApiRouteUtils.buildApiRouteSegment(
          this.rootRouteSegment,
          ApiVersion.V1,
          ['cars'],
        )}?${qs.stringify({
          'external-car-id': request.externalCarId,
          availability: request.availability,
          'station-id': request.stationId,
          'include-state': request.includeState,
        })}`,
        { omitAuthorization: true },
      );

    const result = response?.result ? response.result : null;

    // Sort by availabilityUpdatedAt ascending
    return result
      ? result.sort((a, b) => {
          if (!a.availabilityUpdatedAt && !b.availabilityUpdatedAt) return 0;
          if (!a.availabilityUpdatedAt) return -1;
          if (!b.availabilityUpdatedAt) return 1;
          return a.availabilityUpdatedAt.localeCompare(b.availabilityUpdatedAt);
        })
      : null;
  }
}
