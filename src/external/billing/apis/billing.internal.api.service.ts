import { Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import * as QueryString from 'qs';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { SubscriptionPlanId } from 'src/common/tiny-types';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { InternalApiService } from 'src/shared/http/internal-api.service';
import { InternalCheckRentalPackageAvailableResponseDtoV1 } from '../dtos/response/internal.check-rental-package-available.v1.response.dto';
import { InternalGetRentalPackageV1ResponseDto } from '../dtos/response/internal.get-rental-packges.v1.response.dto';
import { InternalPricingPolicyResponseDtoV1 } from '../dtos/response/internal.pricing-policy.v1.response.dto';

@Injectable()
export class BillingInternalApiService {
  private rootRouteSegment = RootRouteSegment.Internal;

  constructor(private readonly billingApiService: InternalApiService) {}

  async getPricingPolicy(
    carModel: CarModelEnum,
    subscriptionPlanId: SubscriptionPlanId,
    createdDateTo: VulogDate,
  ): Promise<InternalPricingPolicyResponseDtoV1[]> {
    const response: BaseResponseDto<InternalPricingPolicyResponseDtoV1[]> =
      await this.billingApiService.get(
        `${ApiRouteUtils.buildApiRouteSegment(
          this.rootRouteSegment,
          ApiVersion.V1,
          ['pricing-policy'],
        )}?${QueryString.stringify({
          carModel,
          subscriptionPlanId: subscriptionPlanId?.value,
          createdDateTo: createdDateTo.value,
        })}`,
        { omitAuthorization: true },
      );

    return response?.result
      ? response.result.map((item) =>
          plainToInstance(InternalPricingPolicyResponseDtoV1, item),
        )
      : [];
  }

  async getAvailableRentalPackages(
    carModel: CarModelEnum,
  ): Promise<InternalGetRentalPackageV1ResponseDto[]> {
    const response: BaseResponseDto<InternalGetRentalPackageV1ResponseDto[]> =
      await this.billingApiService.get(
        `${ApiRouteUtils.buildApiRouteSegment(
          this.rootRouteSegment,
          ApiVersion.V1,
          ['rental-package', 'car-model', carModel],
        )}`,
      );

    return response?.result
      ? response.result.map((item) =>
          plainToInstance(InternalGetRentalPackageV1ResponseDto, item),
        )
      : [];
  }

  async checkPackageAvailable(queryParams: {
    packageId: string;
    time: string;
    duration: number;
    price: number;
    carModel: CarModelEnum;
    hrid: string;
    minAllowedBatteryPercentage: number;
  }): Promise<InternalCheckRentalPackageAvailableResponseDtoV1> {
    const {
      packageId,
      time,
      duration,
      price,
      carModel,
      hrid,
      minAllowedBatteryPercentage,
    } = queryParams;

    const url = `${ApiRouteUtils.buildApiRouteSegment(
      this.rootRouteSegment,
      ApiVersion.V1,
      ['rental-package', packageId, 'available', time],
    )}?${QueryString.stringify({
      duration,
      price,
      carModel,
      hrid,
      minAllowedBatteryPercentage,
    })}`;

    const response: BaseResponseDto<InternalCheckRentalPackageAvailableResponseDtoV1> =
      await this.billingApiService.get(url);

    return response?.result ? response.result : null;
  }
}
