import { Inject, Injectable } from '@nestjs/common';
import { InternalApiService } from 'src/shared/http/internal-api.service';
import { BILLING_SERVICE_DI_TOKEN } from '../constants';
import { BillingInternalApiService } from './apis/billing.internal.api.service';

@Injectable()
export class BillingExternalService {
  /**
   * API Service to call `internal` APIs
   */
  public internalApi: BillingInternalApiService;

  constructor(
    @Inject(BILLING_SERVICE_DI_TOKEN)
    private readonly billingApiService: InternalApiService,
  ) {
    this.internalApi = new BillingInternalApiService(this.billingApiService);
  }
}
