import { Module } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import { httpServiceConfig } from 'src/config/app.nestjs.config';
import { HttpModule } from 'src/shared/http/http.module';
import { BILLING_SERVICE_DI_TOKEN } from '../constants';
import { BillingExternalService } from './billing.external.service';

@Module({
  imports: [
    HttpModule.registerAsync({
      serviceName: BILLING_SERVICE_DI_TOKEN,
      useFactory: (
        httpServiceConfiguration: ConfigType<typeof httpServiceConfig>,
      ) => ({
        baseURL: httpServiceConfiguration.billingUrl,
        timeout: httpServiceConfiguration.timeout,
      }),
      inject: [httpServiceConfig.KEY],
      imports: [],
    }),
  ],
  providers: [BillingExternalService],
  exports: [BillingExternalService],
})
export class BillingExternalModule {}
