import { plainToInstance } from 'class-transformer';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { PricingPolicy } from 'src/model/pricing-policy';

export class InternalPricingPolicyResponseDtoV1 {
  id: string;

  carModel: CarModelEnum;

  subscriptionPlanId: string;

  rentalRate: number;

  isActive: boolean;

  isApproved: boolean;

  toDomain(): PricingPolicy {
    return plainToInstance(PricingPolicy, {
      pricingPolicyId: this.id,
      rentalRate: this.rentalRate,
    });
  }
}
