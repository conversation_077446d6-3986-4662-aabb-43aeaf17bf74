import { ApiProperty } from '@nestjs/swagger';

export class InternalCheckRentalPackageAvailableResponseDtoV1 {
  constructor(props: { isAvailable: boolean; isDynamic: boolean }) {
    this.isAvailable = props.isAvailable;
    this.isDynamic = props.isDynamic;
  }

  @ApiProperty({
    type: 'boolean',
    required: true,
    description: 'Is the package available and has the right info',
  })
  isAvailable: boolean;

  @ApiProperty({
    type: 'boolean',
    required: true,
    description: 'Is the package a dynamic package or a base package',
  })
  isDynamic: boolean;
}
