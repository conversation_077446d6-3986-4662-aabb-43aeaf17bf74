import { ApiProperty } from '@nestjs/swagger';
import { RentalPackageId } from 'src/common/tiny-types';
import { RentalPackage } from 'src/model/rental-package';

export class InternalGetRentalPackageV1ResponseDto {
  @ApiProperty({
    type: 'string',
    required: true,
  })
  id: string;

  @ApiProperty({
    type: 'string',
    required: false,
  })
  hrid?: string;

  @ApiProperty({
    type: 'string',
    required: true,
  })
  name: string;

  @ApiProperty({
    type: 'number',
    required: true,
  })
  minutes: number;

  @ApiProperty({
    type: 'number',
    required: true,
  })
  basePrice: number;

  @ApiProperty({
    type: 'number',
    required: true,
  })
  minAllowedBatteryPercentage: number;

  toRentalPackage(): RentalPackage {
    return new RentalPackage({
      packageId: new RentalPackageId(this.id),
      hrid: this.hrid,
      name: this.name,
      duration: this.minutes,
      price: this.basePrice,
      minAllowedBatteryPercentage: this.minAllowedBatteryPercentage,
    });
  }
}
