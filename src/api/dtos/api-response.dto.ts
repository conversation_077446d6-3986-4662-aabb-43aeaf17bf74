import { CorrelationId } from '@bluesg-2/monitoring/dist/correlation-id/correlation-id.service';
import { ApiProperty } from '@nestjs/swagger';
import { BasePaginationResponseDto } from 'src/common/api/base.pagination.response.dto';
export class ApiResponse<T = unknown> {
  @ApiProperty({
    description: 'Status of the API response',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'UUID of the API process used for traceability',
    format: 'uuid',
    example: 'd91ce6fe-c968-49a5-8d28-87dd3ed4e90d',
  })
  correlationId?: CorrelationId;

  @ApiProperty({
    description: 'Result data of the API response',
  })
  result?: T;

  @ApiProperty({
    description: 'Message or list of messages returned with API response',
  })
  message?: string | string[];

  @ApiProperty({
    type: BasePaginationResponseDto,
    description: 'pagination info',
    required: false,
  })
  pagination?: BasePaginationResponseDto;

  constructor(
    success: boolean,
    result?: T,
    correlationId?: CorrelationId,
    message?: string | string[],
    pagination?: BasePaginationResponseDto,
  ) {
    this.success = success;
    this.correlationId = correlationId;
    this.result = result;
    this.message = message;
    this.pagination = pagination;
  }
}
