import {
  INestApplication,
  RequestMethod,
  ValidationPipe,
  VersioningType,
} from '@nestjs/common';

export class ApiSetup {
  setup(app: INestApplication) {
    app.setGlobalPrefix('api', {
      exclude: [
        {
          path: 'health',
          method: RequestMethod.GET,
        },
      ],
    });
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
        validationError: {
          target: false,
          value: false,
        },
        validateCustomDecorators: false,
      }),
    );
    app.enableVersioning({
      type: VersioningType.URI,
      defaultVersion: '1',
    });
  }
}
