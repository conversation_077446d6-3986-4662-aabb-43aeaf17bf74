import { EventProps } from '@bluesg-2/sqs-event';
import { MessageEventPattern } from 'src/common/constants/event';
import { RentalFinalEndedPayloadDto } from 'src/controllers/dtos/rental/request/rental-final-ended.request.dto.v1';

export class RentalFinalEndedEventPayloadDto
  implements EventProps<RentalFinalEndedPayloadDto>
{
  public payload: RentalFinalEndedPayloadDto;

  public readonly event = MessageEventPattern.RentalEndedFinal;

  public correlationId?: string;

  constructor(payload: RentalFinalEndedPayloadDto, corId?: string) {
    this.payload = payload;
    this.correlationId = corId;
  }
}
