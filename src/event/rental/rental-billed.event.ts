import { EventProps } from '@bluesg-2/sqs-event';
import { MessageEventPattern } from 'src/common/constants/event';
import { RentalBilledDto } from 'src/controllers/dtos/rental/request/rental-billed.dto.v1';

export class RentalBilledEventPayloadDto
  implements EventProps<RentalBilledDto>
{
  public payload: RentalBilledDto;

  public readonly event = MessageEventPattern.RentalBilled;

  public correlationId?: string;

  constructor(payload: RentalBilledDto, corId?: string) {
    this.payload = payload;
    this.correlationId = corId;
  }
}
