import { EventProps } from '@bluesg-2/sqs-event';
import { MessageEventPattern } from 'src/common/constants/event';
import { EndRentalSimulationRequestDto } from 'src/controllers/dtos/rental/request/end-rental-simulation.request.dto';

export class EndRentalSimulationEventPayloadDto
  implements EventProps<EndRentalSimulationRequestDto>
{
  public payload: EndRentalSimulationRequestDto;

  public readonly event = MessageEventPattern.EndRentalSimulation;

  public correlationId?: string;

  constructor(payload: EndRentalSimulationRequestDto, corId?: string) {
    this.payload = payload;
    this.correlationId = corId;
  }
}
