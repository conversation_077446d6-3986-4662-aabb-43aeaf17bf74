import { EventProps } from '@bluesg-2/sqs-event';
import { MessageEventPattern } from 'src/common/constants/event';
import { CarModelEnum } from 'src/common/enum/car-model.enum';

export class TripInfoEventDto {
  constructor(props?: TripInfoEventDto) {
    Object.assign(this, props);
  }

  id: string;

  startedAt: string;

  endedAt: string;

  duration: number;
}

export class CarInfoEventDto {
  constructor(props?: CarInfoEventDto) {
    Object.assign(this, props);
  }

  id: string;

  model: CarModelEnum;
}

export class UserInfoEventDto {
  constructor(props?: UserInfoEventDto) {
    Object.assign(this, props);
  }

  id: string;
}

export class SubscriptionInfoEventDto {
  constructor(props?: SubscriptionInfoEventDto) {
    Object.assign(this, props);
  }

  id: string;

  subscriptionPlanId: string;
}

export class StationInfoEventDto {
  constructor(props?: StationInfoEventDto) {
    Object.assign(this, props);
  }

  id: string;

  sourceId: string;

  name: string;
}

export class RentalPackageDto {
  constructor(props?: RentalPackageDto) {
    Object.assign(this, props);
  }

  id: string;

  hrid: string;

  name: string;

  title: string;

  duration: number;

  price: number;
}

export class RentalEndedEventPayloadDto {
  constructor(props?: RentalEndedEventPayloadDto) {
    Object.assign(this, props);
  }

  tripInfo: TripInfoEventDto;

  carInfo: CarInfoEventDto;

  userInfo: UserInfoEventDto;

  subscriptionInfo: SubscriptionInfoEventDto;

  startStationInfo: StationInfoEventDto;

  endStationInfo?: StationInfoEventDto;

  rentalPackageInfo?: RentalPackageDto;
}

export class RentalEndedEventDto
  implements EventProps<RentalEndedEventPayloadDto>
{
  public payload: RentalEndedEventPayloadDto;

  public readonly event = MessageEventPattern.RentalEnded;

  public correlationId?: string;

  constructor(payload: RentalEndedEventPayloadDto, corId?: string) {
    this.payload = payload;
    this.correlationId = corId;
  }
}
