import { EventProps } from '@bluesg-2/sqs-event';
import { MessageEventPattern } from 'src/common/constants/event';
import { RentalVulogEndedPaylodDto } from 'src/controllers/dtos/rental/request/rental-vulog-ended.dto.v1';

export class RentalVulogEndedEventPayloadDto
  implements EventProps<RentalVulogEndedPaylodDto>
{
  public payload: RentalVulogEndedPaylodDto;

  public readonly event = MessageEventPattern.EndRentalAIMA;

  public correlationId?: string;

  constructor(payload: RentalVulogEndedPaylodDto, corId?: string) {
    this.payload = payload;
    this.correlationId = corId;
  }
}
