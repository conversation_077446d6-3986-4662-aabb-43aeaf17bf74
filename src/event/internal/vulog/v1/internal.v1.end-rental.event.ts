import { EventProps } from '@bluesg-2/sqs-event';
import { MessageEventPattern } from 'src/common/constants/event';
import { UUID } from 'src/common/tiny-types/uuid.type';
import { LocalVulogEndTripReportEventV1Payload } from 'src/event/local/vulog/aima/local.v1.vulog-end-trip-report.event';
import { InternalUserDetailResponseDtoV1 } from 'src/external/user-subscription/dtos/response/internal.user-detail.v1.response.dto';
import { Reservation } from 'src/model/reservation.domain';
import { StaticCar } from 'src/model/static.car';
import { Station } from 'src/model/station';

export interface InternalEndRentalEventV1Payload {
  user: {
    id: string;
    name?: string;
    email?: string;
    subscriptionId: string;
    subscriptionPlanId: string;
  };
  rental: {
    reservationId: string;
    car: {
      model: string;
      id: string;
      plateNumber: string;
      vin: string;
    };
    duration: number;
    tripDuration: number;
    startTime: string;
    endTime: string;
    startStation: Station;
    endStation: Station;
    rentalPackageData?: unknown;
  };
}

/**
 * Internal mean event will be consume between
 * BlueSG Services
 */
export class InternalEndRentalEventV1
  implements EventProps<InternalEndRentalEventV1Payload>
{
  public payload: InternalEndRentalEventV1Payload;

  public readonly event: MessageEventPattern =
    MessageEventPattern.VulogEndRental;

  public correlationId?: string;

  constructor(
    payload: InternalEndRentalEventV1Payload,
    corId: string = new UUID().value,
  ) {
    this.payload = payload;
    this.correlationId = corId;
  }

  static from(
    info: {
      user: InternalUserDetailResponseDtoV1;
      startStation: Station;
      endStation: Station;
      vulogEndTripReport: LocalVulogEndTripReportEventV1Payload;
      car: StaticCar;
      reservation: Reservation;
    },
    corId?: string,
  ): InternalEndRentalEventV1 {
    const {
      user,
      startStation,
      endStation,
      vulogEndTripReport,
      car,
      reservation,
    } = info;

    return new InternalEndRentalEventV1(
      {
        user: {
          id: user.id,
          subscriptionId: user.currentSubscription.id,
          subscriptionPlanId: user.currentSubscription.subscriptionPlanId,
          email: user.email,
          name: user.firstName + ' ' + user.lastName,
        },
        rental: {
          car: {
            model: car.model.value,
            id: car.id.value,
            plateNumber: car.plate.value,
            vin: car.vin,
          },
          endStation: endStation,
          startStation: startStation,
          endTime: reservation.endedAt.value,
          startTime: reservation.startedAt?.value,
          reservationId: reservation.getId,
          duration: reservation.getBilledDuration(),
          tripDuration: vulogEndTripReport.tripDuration,
          rentalPackageData: reservation.rentalPackageData,
        },
      },
      corId,
    );
  }
}
