import { GeoLocation } from '@bluesg-2/queue-pop/dist/common/domains/value-objects/geo-location';
import { EventProps } from '@bluesg-2/sqs-event';
import { MessageEventPattern } from 'src/common/constants/event';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import {
  BsgUserId,
  StationId,
  VulogJourneyOrTripId,
} from 'src/common/tiny-types';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { Reservation } from 'src/model/reservation.domain';

export class ParkingReservationConfirmedEventDto
  implements EventProps<ParkingReservationConfirmedPayloadDto>
{
  public payload: ParkingReservationConfirmedPayloadDto;

  public readonly event = MessageEventPattern.ParkingReservationConfirmed;

  public correlationId?: string;

  constructor(reservation: Reservation, corId?: string) {
    this.payload = new ParkingReservationConfirmedPayloadDto(reservation);
    this.correlationId = corId;
  }
}

class ParkingReservationConfirmedPayloadDto {
  reservation: ParkingReservationConfirmedDto;

  constructor(reservation: Reservation) {
    this.reservation = new ParkingReservationConfirmedDto(reservation);
  }
}

class ParkingReservationConfirmedDto {
  bsgUserId: BsgUserId;
  carId?: string;
  carModel?: CarModelEnum;
  startStationId: StationId;
  endStationId?: StationId;
  vulogTripId?: VulogJourneyOrTripId;
  status: ReservationStatus;
  plateNumber?: string;
  rentalPackageData?: unknown;
  local?: boolean;
  startedAt?: VulogDate;
  endedAt?: VulogDate;
  reservedAt?: VulogDate;
  expiresAt?: VulogDate;
  canceledAt?: VulogDate;
  location?: GeoLocation;
  constructor(reservation: Reservation) {
    this.bsgUserId = reservation.bsgUserId;
    this.carId = reservation.carId.value;
    this.carModel = reservation.carModel;
    this.startStationId = reservation.startStationId;
    this.endStationId = reservation.endStationId;
    this.vulogTripId = reservation.vulogTripId;
    this.status = reservation.status;
    this.plateNumber = reservation.plateNumber.value;
    this.rentalPackageData = reservation.rentalPackageData;
    this.local = reservation.local;
    this.startedAt = reservation.startedAt;
    this.endedAt = reservation.endedAt;
    this.reservedAt = reservation.reservedAt;
    this.expiresAt = reservation.expiresAt;
    this.canceledAt = reservation.canceledAt;
  }
}
