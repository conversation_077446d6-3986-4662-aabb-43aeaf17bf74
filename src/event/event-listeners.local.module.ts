import { Module } from '@nestjs/common';
import { LocalCarListenerV1 } from 'src/controllers/internal/v1/car/local.car.v1.listener';
import { LocalRentalListenerV1 } from 'src/controllers/internal/v1/rental/local.rental.v1.listener';
import { LocalReservationListenerV1 } from 'src/controllers/internal/v1/reservation/local.reservation.v1.listener';
import { LocalVgEventListener } from 'src/controllers/internal/vg/local.vg-event.listener';
import { LogicModule } from 'src/logic/logic.module';
import { CancelReservationService } from 'src/logic/vulog/cancel-reservation.service';
import { CarPlugService } from 'src/logic/vulog/car-plug.service';
import { EndRentalService } from 'src/logic/vulog/end-rental.service';
import { StartReservationService } from 'src/logic/vulog/start-reservation.service';

@Module({
  imports: [LogicModule],
  controllers: [],
  providers: [
    LocalReservationListenerV1,
    LocalCarListenerV1,
    LocalRentalListenerV1,
    LocalVgEventListener,

    EndRentalService,
    CarPlugService,
    CancelReservationService,
    StartReservationService,
  ],
})
export class EventListenersLocalModule {}
