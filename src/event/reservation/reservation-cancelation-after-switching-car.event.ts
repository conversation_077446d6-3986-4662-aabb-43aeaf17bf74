import { EventProps } from '@bluesg-2/sqs-event';
import { MessageEventPattern } from 'src/common/constants/event';

export class ReservationCancelationAfterSwitchingCarPayload {
  reservationId: string;

  bsgUserId: string;

  correlationId: string;
}

export class ReservationCancelationAfterSwitchingCarEventDto
  implements EventProps<ReservationCancelationAfterSwitchingCarPayload>
{
  public payload: ReservationCancelationAfterSwitchingCarPayload;

  public readonly event =
    MessageEventPattern.ReservationCancelationAfterSwitchingCar;

  public correlationId?: string;

  constructor(
    payload: ReservationCancelationAfterSwitchingCarPayload,
    corId?: string,
  ) {
    this.payload = payload;
    this.correlationId = corId;
  }
}
