import { Modu<PERSON> } from '@nestjs/common';
import { ConfigurationModule } from '~shared/configuration/configuration.module';

import { EventBusModule } from '~shared/event-bus/event-bus.module';
import { LogicModule } from '../../logic/logic.module';
import { BsEventConsumerController } from './controller/bs/ bs.event.consumer.controller';
import { SelfEventConsumerController } from './controller/self/self.event.consumer.controller';

@Module({
  imports: [ConfigurationModule, EventBusModule, LogicModule],
  controllers: [BsEventConsumerController, SelfEventConsumerController],
  providers: [],
})
export class EventExternalConsumerModule {}
