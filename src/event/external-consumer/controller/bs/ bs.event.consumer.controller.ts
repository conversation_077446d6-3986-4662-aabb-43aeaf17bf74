import { Controller, Logger } from '@nestjs/common';
import { EventPattern, RpcException } from '@nestjs/microservices';
import { RentalFinalEndedPayloadDto } from 'src/controllers/dtos/rental/request/rental-final-ended.request.dto.v1';
import { RentalBilledEventPayloadDto } from 'src/event/rental/rental-billed.event';
import { RentalVulogEndedEventPayloadDto } from 'src/event/rental/rental-vulog-ended.event';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { CarService } from 'src/logic/car/car.service';
import { StationService } from 'src/logic/station/station.service';
import { VulogTelemetryService } from 'src/logic/telemetry-repository/vulog/vulog-telemetry.service';
import { VulogUserService } from 'src/logic/vulog/vulog-user.service';
import { Station } from 'src/model/station';
import { v4 } from 'uuid';
import { EventBusService } from '~shared/event-bus/event-bus.service';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { CarInfo } from '~shared/feature-flag/models/car-info';
import { NewRelicRentalEventService } from '~shared/newrelic/event/event-type/rental.event.newrelic.service';
import { EndRentalInAiMAEventPayload } from '~shared/queue-client/payload/ending-rental-in-aima.event.payload';
import { MessageEventPattern } from '../../../../common/constants/event';
import {
  ReservationId,
  VulogCarId,
  VulogJourneyOrTripId,
} from '../../../../common/tiny-types';
import {
  InvoiceItemType,
  RentalBilledDto,
} from '../../../../controllers/dtos/rental/request/rental-billed.dto.v1';
import { ReservationService } from '../../../../logic/reservation.service';
import { Reservation } from '../../../../model/reservation.domain';
import { RentalFinalEndedEventPayloadDto } from '../../../rental/rental-final-ended.event';

@Controller()
export class BsEventConsumerController {
  constructor(
    private reservationService: ReservationService,
    private readonly eventBusService: EventBusService,
    private vulogUserService: VulogUserService,
    private vulogCarService: VulogCarService,
    private carService: CarService,
    private stationService: StationService,
    private flag: FeatureFlagService,
    private vulogTelemetryService: VulogTelemetryService,
    private readonly rentalEventService: NewRelicRentalEventService,
  ) {}

  // consume the event rental-billed sent from BS
  @EventPattern(MessageEventPattern.RentalBilled)
  async handleRentalBilledEvent(
    payload: RentalBilledDto & RentalBilledEventPayloadDto,
  ) {
    const correlationId = payload.correlationId || v4();

    try {
      Logger.log('handleRentalBilledEvent', {
        payload: payload,
        corId: correlationId,
      });

      const firstItem = (payload.rentalBilledItems || [])[0];
      const pureReservationId = firstItem?.reservationId;
      const reservationId = new ReservationId(pureReservationId);
      const isRentalPackage = payload.rentalBilledItems.some(
        (item) => item.type === InvoiceItemType.RENTAL_PACKAGE_FEE,
      );

      // update the final status of the rental, and price
      const rental: Reservation = await this.reservationService.updateWithBill(
        reservationId,
        payload.rentalBilledItems,
      );

      const endStation: Station = rental.endStationId
        ? await this.stationService.getStation(rental.endStationId)
        : null;

      await this.rentalEventService.emitEndedByBlueSG(
        rental.getMetricNewRelicRentalType(),
        rental.getMetricNewRelicCarModel(),
        endStation?.displayName,
        endStation?.id?.value,
      );

      const vulogUser = await this.vulogUserService.findOneByBsgUserId(
        rental.bsgUserId,
      );

      const car = await this.carService.getOneByVulogId(rental.carId);

      const startStation = await this.stationService.getStation(
        rental.startStationId,
      );

      // These are synchronous calls, should be handled after Promises queue
      setTimeout(() => {
        this.eventBusService.nonBlockingEmit(
          new RentalVulogEndedEventPayloadDto(
            {
              rentalInfo: {
                reservationId: rental.getId,
                vulogTripId: rental.vulogTripId.value,
                startedAt: rental.startedAt?.toDateTime().toUTC().toISO(),
                endedAt: rental.endedAt?.toDateTime().toUTC().toISO(),
                billedDuration: rental.getBilledDuration(),
                billedAmount: rental.amount,
                local: rental.local,
              },
              userInfo: {
                bsgUserId: vulogUser.bsgUserId.value,
                vulogUserId: vulogUser.id.value,
              },
              carInfo: {
                carId: car.id.value,
                vulogCarId: car.vulogId.value,
                model: car.model,
                plate: car.plate.value,
              },
              startStationInfo: {
                stationId: startStation.id.value,
                zoneId: startStation.zoneId,
                stationName: startStation.displayName,
              },
              endStationInfo: endStation
                ? {
                    stationId: endStation.id.value,
                    zoneId: endStation.zoneId,
                    stationName: endStation.displayName,
                  }
                : null,
              drivingStarted: true,
              correlationId,
            },
            correlationId,
          ),
        );
        // send an event rental final ended from CRS to NS
        const nsPayload = new RentalFinalEndedPayloadDto(
          rental,
          car,
          isRentalPackage,
        );

        this.eventBusService.nonBlockingEmit(
          new RentalFinalEndedEventPayloadDto(nsPayload, correlationId),
        );
      }, 0);
    } catch (error) {
      Logger.error('🔴 Error when handle rental billed', {
        error,
        corId: correlationId,
      });

      Logger.error('🔴 Error when handle rental billed (only error stack)', {
        errorStack: error.stack,
        corId: correlationId,
      });

      throw new RpcException(error);
    }
  }

  @EventPattern(MessageEventPattern.EndRentalAIMA)
  async handleEndRentalAIMA(payload: EndRentalInAiMAEventPayload) {
    const correlationId = payload.correlationId;

    try {
      Logger.log('Payload when end rental in AiMA', {
        payload: payload,
        corId: correlationId,
      });

      // send request to vulog to check the status of the rental
      const vulogCarId = new VulogCarId(payload.carInfo.vulogCarId);
      const carInfo =
        await this.vulogTelemetryService.getCarStatusSessionCommand(vulogCarId);

      Logger.log('Car Info when end rental in AiMA', {
        carInfo: carInfo,
        corId: payload.correlationId,
      });

      // terminate only if car is not used by another user already
      if (carInfo.activeSession?.sessionId === payload.rentalInfo.vulogTripId) {
        await this.vulogCarService.terminateTrip(vulogCarId);

        const rental = await this.reservationService.findOneByTripId(
          new VulogJourneyOrTripId(payload.rentalInfo.vulogTripId),
        );

        await this.rentalEventService.emitEndedByAiMA(
          rental.getMetricNewRelicRentalType(),
          rental.getMetricNewRelicCarModel(),
        );

        if (
          await this.flag.disableCarIfMissingEndStationWhenEndRental(
            CarInfo.fromRealtimeCar(carInfo),
          )
        ) {
          // Ending rental without end station
          if (!payload.endStationInfo) {
            await this.vulogCarService.disableCar(
              carInfo.id,
              'Missing end station',
            );

            Logger.log(
              'Make vehicle non-serviceable after ending rental without end station',
              {
                rental,
                carInfo,
                corId: correlationId,
              },
            );
          }
        }

        Logger.log('handleEndRentalAIMA 🟢', {
          carInfo: carInfo,
          corId: correlationId,
        });
      }
    } catch (error) {
      Logger.error('🔴 Error when trying to end rental in AiMA', {
        error,
        corId: correlationId,
      });

      Logger.error(
        '🔴 Error when trying to end rental in AiMA (only error stack)',
        {
          errorStack: error.stack,
          corId: correlationId,
        },
      );

      throw new RpcException(error);
    }
  }
}
