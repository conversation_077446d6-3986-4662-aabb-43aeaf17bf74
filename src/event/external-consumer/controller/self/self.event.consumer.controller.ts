import { Controller, Logger } from '@nestjs/common';
import { EventPattern, RpcException } from '@nestjs/microservices';
import { ReservationId } from 'src/common/tiny-types';
import { ReservationCancelationAfterSwitchingCarPayload } from 'src/event/reservation/reservation-cancelation-after-switching-car.event';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';

import { VulogApiError } from 'src/common/errors/vulog-api-error';
import { ReservationService } from 'src/logic/reservation.service';
import { ReservationCacheService } from 'src/logic/reservation/reservation.cache.service';
import { Reservation } from 'src/model/reservation.domain';
import { MessageEventPattern } from '../../../../common/constants/event';

@Controller()
export class SelfEventConsumerController {
  constructor(
    private readonly reservationService: ReservationService,
    private readonly vulogCarService: VulogCarService,
    private readonly reservationCacheService: ReservationCacheService,
  ) {}

  @EventPattern(MessageEventPattern.ReservationCancelationAfterSwitchingCar)
  async handleReservationCancelationAfterSwitchingCar(
    payload: ReservationCancelationAfterSwitchingCarPayload,
  ) {
    const correlationId = payload.correlationId;

    Logger.log('ReservationCancelationAfterSwitchingCar - Start', {
      payload,
      corId: correlationId,
    });

    if (!payload.reservationId || !payload.bsgUserId) {
      Logger.error('Payload is not aligned with current code implementation', {
        corId: correlationId,
      });

      throw new RpcException(
        'Payload is not aligned with current code implementation',
      );
    }

    const existingReservation: Reservation =
      await this.reservationService.findOneById(
        new ReservationId(payload.reservationId),
      );

    if (!existingReservation) {
      Logger.error(`Reservation (ID: ${payload.reservationId}) is not found`, {
        corId: correlationId,
      });

      throw new RpcException(
        `Reservation (ID: ${payload.reservationId}) is not found`,
      );
    }

    await this.reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.delete(
      existingReservation.bsgUserId,
    );

    try {
      await this.vulogCarService.cancelCarReservationOrAllocationAsAdmin(
        existingReservation.carId,
      );
    } catch (err) {
      Logger.error(
        'Error when trying to cancel reservation after car-switched reservation is expired',
        {
          err,
          stack: err?.stack,
          corId: correlationId,
        },
      );

      if (err instanceof VulogApiError) {
        const vulogApiError = err as VulogApiError;

        // If the reservation is not expired or cancelled yet but an error is thrown
        if (
          ![vulogApiError.data?.code, vulogApiError.data?.codeStr].includes(
            'vehicleNotFound',
          )
        ) {
          Logger.error(
            'Unexpected error when trying cancel reservation. Need to consume the message again to handle.',
            {
              corId: correlationId,
            },
          );

          throw new RpcException(
            'Unexpected error when trying cancel reservation. Need to consume the message again to handle.',
          );
        }
      }
    }

    Logger.log('ReservationCancelationAfterSwitchingCar - End', {
      corId: correlationId,
    });

    return true;
  }
}
