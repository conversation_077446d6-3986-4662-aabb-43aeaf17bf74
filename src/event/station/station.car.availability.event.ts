import { EventProps } from '@bluesg-2/sqs-event';
import { MessageEventPattern } from 'src/common/constants/event';


export class CarAvailabilityUpdatedEventDto implements EventProps<CarAvailabilityUpdatedEventPayload> {
  readonly payload: CarAvailabilityUpdatedEventPayload;
  public readonly event = MessageEventPattern.CarAvailabilityUpdated;
  readonly correlationId?: string;

  constructor(payload: CarAvailabilityUpdatedEventPayload, correlationId?: string) {
    this.payload = payload;
    this.correlationId = correlationId;
  }
}

export interface CarAvailabilityUpdatedEventPayload {
  stations: StationAvailability[];
  timestamp: string;
}

interface StationAvailability {
  stationId: string;
  carAvailability: {
    byModel: {
      [key: string]: number;
    };
    total: number;
  };
}
