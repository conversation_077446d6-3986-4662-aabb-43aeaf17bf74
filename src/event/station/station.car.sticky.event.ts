import { EventProps } from '@bluesg-2/sqs-event';
import { MessageEventPattern } from 'src/common/constants/event';

export class StationStickyWithCarEventDto implements EventProps<StationStickyWithCarEventPayload> {
    readonly payload: StationStickyWithCarEventPayload;
    public readonly event = MessageEventPattern.StationStickyWithCar;
    readonly correlationId?: string;
  
    constructor(payload: StationStickyWithCarEventPayload, correlationId?: string) {
      this.payload = payload;
      this.correlationId = correlationId;
    }
}

interface StationStickyWithCarEventPayload {
    carId: string;
    stationId: string;
    plateNumber: string;
    isSticky: boolean;
}
