import { EventProps } from '@bluesg-2/sqs-event';
import { localEventPattern } from 'src/common/constants/event';
import { UUID } from 'src/common/tiny-types/uuid.type';
import { VulogCancelTripReportBodyV1 } from 'src/logic/vulog/dto/webhook/aima/vulog-cancel-trip-report-webhook.v1.dto';
import { VulogAimaEvent } from 'src/logic/vulog/vulog.event';
import { NewRelicMetricTrigger } from '~shared/newrelic/metric/enums/newrelic-metric.enum';

export interface LocalVulogCancelTripReportEventV1Payload {
  cancelledAt: string;
  vulogTripId: string;
  endZoneIds: string[];
  correlationId?: string;
  error?: string; // Undocumented, but sent by Vulog
  trigger?: NewRelicMetricTrigger;
}

/**
 * Local mean event will be consume by this service (aka CRS Service)
 */
export class LocalVulogCancelTripReportEventV1
  implements EventProps<LocalVulogCancelTripReportEventV1Payload>
{
  public payload: LocalVulogCancelTripReportEventV1Payload;

  public readonly event: string =
    localEventPattern.reservation.cancelTripReport;

  public correlationId?: string;

  public readonly sentAt?: number = new Date().getTime();

  constructor(
    payload: LocalVulogCancelTripReportEventV1Payload,
    corId: string = new UUID().value,
  ) {
    this.payload = payload;
    this.correlationId = corId;
  }

  static from(
    event: VulogAimaEvent,
    corId: string = new UUID().value,
    trigger?: NewRelicMetricTrigger,
  ): LocalVulogCancelTripReportEventV1 {
    const body = event.eBody as VulogCancelTripReportBodyV1;

    return new LocalVulogCancelTripReportEventV1(
      {
        cancelledAt: body.endDate,
        vulogTripId: event.eTripId,
        endZoneIds: body.endZones,
        correlationId: corId,
        error: body.error,
        trigger,
      },
      corId,
    );
  }
}
