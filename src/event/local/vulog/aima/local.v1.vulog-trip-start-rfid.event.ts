import { EventProps } from '@bluesg-2/sqs-event';
import { localEventPattern } from 'src/common/constants/event';
import { UUID } from 'src/common/tiny-types/uuid.type';
import { VulogAimaEvent } from 'src/logic/vulog/vulog.event';

export interface LocalVulogTripStartRfidEventV1Payload {
  authorized: boolean;
  rfid: string;
  tripId: string;
  vulogUserId: string;
  vehicleId: string;
  vehiclePlate: string;
  date: string;
  correlationId: string;
}

/**
 * Local mean event will be consume by this service (aka CRS Service)
 */
export class LocalVulogTripStartRfidEventV1
  implements EventProps<LocalVulogTripStartRfidEventV1Payload>
{
  public payload: LocalVulogTripStartRfidEventV1Payload;

  public readonly event: string = localEventPattern.rental.tripStartRfid;

  public correlationId?: string;

  public readonly sentAt?: number = new Date().getTime();

  constructor(
    payload: LocalVulogTripStartRfidEventV1Payload,
    corId: string = new UUID().value,
  ) {
    this.payload = payload;
    this.correlationId = corId;
  }

  static from(
    event: VulogAimaEvent,
    corId: string = new UUID().value,
  ): LocalVulogTripStartRfidEventV1 {
    const body = event.eBody;

    return new LocalVulogTripStartRfidEventV1(
      {
        authorized: body.authorized,
        rfid: body.rfid,
        tripId: event.eTripId,
        vulogUserId: event.eUserId,
        vehicleId: event.eVehicleId,
        vehiclePlate: event.eVehiclePlate,
        correlationId: corId,
        date: event.eDate,
      },
      corId,
    );
  }
}
