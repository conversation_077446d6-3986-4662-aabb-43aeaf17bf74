import { EventProps } from '@bluesg-2/sqs-event';
import { localEventPattern } from 'src/common/constants/event';
import { UUID } from 'src/common/tiny-types/uuid.type';
import { VulogAimaEvent } from 'src/logic/vulog/vulog.event';
import { NewRelicMetricTrigger } from '~shared/newrelic/metric/enums/newrelic-metric.enum';

export interface LocalVulogEndTripReportEventV1Payload {
  tripId: string;
  vulogUserId: string;
  vehicleId: string;
  finishDate: string;
  totalDuration: number;
  tripDuration: number;
  vehiclePlate: string;
  startZones: string[];
  endZones: string[];
  correlationId?: string;
  error?: string; // Undocumented, but sent by Vulog
  trigger?: NewRelicMetricTrigger;
  qrCode?: string;
}

/**
 * Local mean event will be consume by this service (aka CRS Service)
 */
export class LocalVulogEndTripReportEventV1
  implements EventProps<LocalVulogEndTripReportEventV1Payload>
{
  public payload: LocalVulogEndTripReportEventV1Payload;

  public readonly event: string = localEventPattern.reservation.endTripReport;

  public correlationId?: string;

  public readonly sentAt?: number = new Date().getTime();

  constructor(
    payload: LocalVulogEndTripReportEventV1Payload,
    corId: string = new UUID().value,
  ) {
    this.payload = payload;
    this.correlationId = corId;
  }

  static from(
    event: VulogAimaEvent,
    corId: string = new UUID().value,
    trigger?: NewRelicMetricTrigger,
  ): LocalVulogEndTripReportEventV1 {
    const body = event.eBody;

    return new LocalVulogEndTripReportEventV1(
      {
        finishDate: body.endDate,
        totalDuration: body.duration,
        tripDuration: body.tripDuration,
        tripId: event.eTripId,
        vehicleId: event.eVehicleId,
        vulogUserId: event.eUserId,
        vehiclePlate: event.eVehiclePlate,
        startZones: body.startZones,
        endZones: body.endZones,
        correlationId: corId,
        error: body.error,
        trigger: trigger,
        qrCode: event.qrCode,
      },
      corId,
    );
  }
}
