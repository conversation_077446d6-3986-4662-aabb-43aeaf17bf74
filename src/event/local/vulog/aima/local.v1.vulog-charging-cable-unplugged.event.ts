import { EventProps } from '@bluesg-2/sqs-event';
import { localEventPattern } from 'src/common/constants/event';
import { UUID } from 'src/common/tiny-types/uuid.type';
import { VulogAimaEvent } from 'src/logic/vulog/vulog.event';

export interface LocalVulogChargingCableUnpluggedEventV1Payload {
  vehicleId: string;
  vehicleVin: string;
  vehiclePlate: string;
  correlationId?: string;
  error?: string; // Undocumented, but sent by Vulog
}

/**
 * Local mean event will be consume by this service (aka CRS Service)
 */
export class LocalVulogChargingCableUnpluggedEventV1
  implements EventProps<LocalVulogChargingCableUnpluggedEventV1Payload>
{
  public payload: LocalVulogChargingCableUnpluggedEventV1Payload;

  public readonly event: string = localEventPattern.car.chargingCableUnplugged;

  public correlationId?: string;

  public readonly sentAt?: number = new Date().getTime();

  constructor(
    payload: LocalVulogChargingCableUnpluggedEventV1Payload,
    corId: string = new UUID().value,
  ) {
    this.payload = payload;
    this.correlationId = corId;
  }

  static from(
    event: VulogAimaEvent,
    corId: string = new UUID().value,
  ): LocalVulogChargingCableUnpluggedEventV1 {
    const body = event.eBody;

    return new LocalVulogChargingCableUnpluggedEventV1(
      {
        vehicleId: event.eVehicleId,
        vehiclePlate: event.eVehiclePlate,
        vehicleVin: event.eVehicleVin,
        correlationId: corId,
        error: body.error ? body.errorMessage : null,
      },
      corId,
    );
  }
}
