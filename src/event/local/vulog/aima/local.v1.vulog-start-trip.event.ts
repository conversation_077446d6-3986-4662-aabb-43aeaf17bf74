import { EventProps } from '@bluesg-2/sqs-event';
import { localEventPattern } from 'src/common/constants/event';
import { UUID } from 'src/common/tiny-types/uuid.type';
import { VulogAimaEvent } from 'src/logic/vulog/vulog.event';

export interface LocalVulogStartTripEventV1Payload {
  tripId: string;
  vulogUserId: string;
  vehicleId: string;
  vehiclePlate: string;
  correlationId?: string;
  date?: string;
}

/**
 * Local mean event will be consume by this service (aka CRS Service)
 */
export class LocalVulogAdminStartTripEventV1
  implements EventProps<LocalVulogStartTripEventV1Payload>
{
  public payload: LocalVulogStartTripEventV1Payload;

  public readonly event: string = localEventPattern.rental.adminStartTrip;

  public correlationId?: string;

  public readonly sentAt?: number = new Date().getTime();

  constructor(
    payload: LocalVulogStartTripEventV1Payload,
    corId: string = new UUID().value,
  ) {
    this.payload = payload;
    this.correlationId = corId;
  }

  static from(
    event: VulogAimaEvent,
    corId: string = new UUID().value,
  ): LocalVulogAdminStartTripEventV1 {
    return new LocalVulogAdminStartTripEventV1(
      {
        tripId: event.eTripId,
        vehicleId: event.eVehicleId,
        vulogUserId: event.eUserId,
        vehiclePlate: event.eVehiclePlate,
        correlationId: corId,
        date: event.eDate,
      },
      corId,
    );
  }
}
