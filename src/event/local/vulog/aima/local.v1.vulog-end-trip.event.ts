import { EventProps } from '@bluesg-2/sqs-event';
import { localEventPattern } from 'src/common/constants/event';
import { UUID } from 'src/common/tiny-types/uuid.type';
import { VulogAimaEvent } from 'src/logic/vulog/vulog.event';

export interface LocalVulogEndTripEventV1Payload {
  tripId: string;
  vulogUserId: string;
  vehicleId: string;
  vehiclePlate: string;
  date: string;
  correlationId: string;
}

/**
 * Local mean event will be consume by this service (aka CRS Service)
 */
export class LocalVulogEndTripEventV1
  implements EventProps<LocalVulogEndTripEventV1Payload>
{
  public payload: LocalVulogEndTripEventV1Payload;

  public readonly event: string = localEventPattern.rental.endTrip;

  public correlationId?: string;

  public readonly sentAt?: number = new Date().getTime();

  constructor(
    payload: LocalVulogEndTripEventV1Payload,
    corId: string = new UUID().value,
  ) {
    this.payload = payload;
    this.correlationId = corId;
  }

  static from(
    event: VulogAimaEvent,
    corId: string = new UUID().value,
  ): LocalVulogEndTripEventV1 {
    return new LocalVulogEndTripEventV1(
      {
        tripId: event.eTripId,
        vulogUserId: event.eUserId,
        vehicleId: event.eVehicleId,
        vehiclePlate: event.eVehiclePlate,
        correlationId: corId,
        date: event.eDate,
      },
      corId,
    );
  }
}
