import { EventProps } from '@bluesg-2/sqs-event';
import { localEventPattern } from 'src/common/constants/event';
import { UUID } from 'src/common/tiny-types/uuid.type';
import { VulogAimaEvent } from 'src/logic/vulog/vulog.event';

export interface LocalVulogChargingCablePluggedEventV1Payload {
  vehicleId: string;
  vehicleVin: string;
  vehiclePlate: string;
  correlationId?: string;
  error?: string; // Undocumented, but sent by Vulog
}

/**
 * Local mean event will be consume by this service (aka CRS Service)
 */
export class LocalVulogChargingCablePluggedEventV1
  implements EventProps<LocalVulogChargingCablePluggedEventV1Payload>
{
  public payload: LocalVulogChargingCablePluggedEventV1Payload;

  public readonly event: string = localEventPattern.car.chargingCablePlugged;

  public correlationId?: string;

  public readonly sentAt?: number = new Date().getTime();

  constructor(
    payload: LocalVulogChargingCablePluggedEventV1Payload,
    corId: string = new UUID().value,
  ) {
    this.payload = payload;
    this.correlationId = corId;
  }

  static from(
    event: VulogAimaEvent,
    corId: string = new UUID().value,
  ): LocalVulogChargingCablePluggedEventV1 {
    const body = event.eBody;

    return new LocalVulogChargingCablePluggedEventV1(
      {
        vehicleId: event.eVehicleId,
        vehiclePlate: event.eVehiclePlate,
        vehicleVin: event.eVehicleVin,
        correlationId: corId,
        error: body.error ? body.errorMessage : null,
      },
      corId,
    );
  }
}
