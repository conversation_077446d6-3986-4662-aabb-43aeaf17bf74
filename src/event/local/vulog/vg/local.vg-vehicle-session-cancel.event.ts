import { EventProps } from '@bluesg-2/sqs-event';
import { localEventPattern } from 'src/common/constants/event';
import { UUID } from 'src/common/tiny-types/uuid.type';
import { VulogVgEvent } from 'src/logic/vulog/vulog.event';
import { NewRelicMetricTrigger } from '~shared/newrelic/metric/enums/newrelic-metric.enum';

export interface LocalVgVehicleSessionCancelEventPayload {
  finishDate: string;
  sessionId: string;
  vehicleId: string;
  latitude: number;
  longitude: number;
  correlationId: string;
  error: string;
  trigger?: NewRelicMetricTrigger;
}

/**
 * Local mean event will be consume by this service (aka CRS Service)
 */
export class LocalVgVehicleSessionCancelEvent
  implements EventProps<LocalVgVehicleSessionCancelEventPayload>
{
  public payload: LocalVgVehicleSessionCancelEventPayload;

  public readonly event: string = localEventPattern.vg.vehicleSessionCancel;

  public correlationId?: string;

  public readonly sentAt?: number = new Date().getTime();

  constructor(
    payload: LocalVgVehicleSessionCancelEventPayload,
    corId: string = new UUID().value,
  ) {
    this.payload = payload;
    this.correlationId = corId;
  }

  static from(
    event: VulogVgEvent,
    corId: string = new UUID().value,
    trigger?: NewRelicMetricTrigger,
  ): LocalVgVehicleSessionCancelEvent {
    const { eDate, eErrorCode, eLatitude, eLongitude, eSessionId, eVehicleId } =
      event;

    return new LocalVgVehicleSessionCancelEvent(
      {
        finishDate: eDate,
        sessionId: eSessionId,
        vehicleId: eVehicleId,
        latitude: eLatitude,
        longitude: eLongitude,
        correlationId: corId,
        error: eErrorCode,
        trigger,
      },
      corId,
    );
  }
}
