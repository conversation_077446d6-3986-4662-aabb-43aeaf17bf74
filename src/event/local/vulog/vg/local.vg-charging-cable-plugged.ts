import { EventProps } from '@bluesg-2/sqs-event';
import { localEventPattern } from 'src/common/constants/event';
import { UUID } from 'src/common/tiny-types/uuid.type';
import { VulogVgEvent } from 'src/logic/vulog/vulog.event';

export interface LocalVgChargingCablePluggedEventPayload {
  vehicleId: string;
  correlationId?: string;
  error?: string;
}

export class LocalVgChargingCablePluggedEvent
  implements EventProps<LocalVgChargingCablePluggedEventPayload>
{
  public payload: LocalVgChargingCablePluggedEventPayload;

  public readonly event: string = localEventPattern.vg.chargingCablePlugged;

  public correlationId?: string;

  public readonly sentAt?: number = new Date().getTime();

  constructor(
    payload: LocalVgChargingCablePluggedEventPayload,
    corId: string = new UUID().value,
  ) {
    this.payload = payload;
    this.correlationId = corId;
  }

  static from(
    event: VulogVgEvent,
    corId: string = new UUID().value,
  ): LocalVgChargingCablePluggedEvent {
    const { eErrorCode } = event;

    return new LocalVgChargingCablePluggedEvent(
      {
        vehicleId: event.eVehicleId,
        correlationId: corId,
        error: eErrorCode,
      },
      corId,
    );
  }
}
