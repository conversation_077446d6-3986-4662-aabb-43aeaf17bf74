import { EventProps } from '@bluesg-2/sqs-event';
import { localEventPattern } from 'src/common/constants/event';
import { UUID } from 'src/common/tiny-types/uuid.type';
import { VulogVgEvent } from 'src/logic/vulog/vulog.event';

export interface LocalVgVehicleSessionEndEventPayload {
  finishDate: string;
  sessionId: string;
  vehicleId: string;
  latitude: number;
  longitude: number;
  correlationId: string;
  error: string;
}

/**
 * Local mean event will be consume by this service (aka CRS Service)
 */
export class LocalVgVehicleSessionEndEvent
  implements EventProps<LocalVgVehicleSessionEndEventPayload>
{
  public payload: LocalVgVehicleSessionEndEventPayload;

  public readonly event: string = localEventPattern.vg.vehicleSessionEnd;

  public correlationId?: string;

  public readonly sentAt?: number = new Date().getTime();

  constructor(
    payload: LocalVgVehicleSessionEndEventPayload,
    corId: string = new UUID().value,
  ) {
    this.payload = payload;
    this.correlationId = corId;
  }

  static from(
    event: VulogVgEvent,
    corId: string = new UUID().value,
  ): LocalVgVehicleSessionEndEvent {
    const { eDate, eErrorCode, eLatitude, eLongitude, eSessionId, eVehicleId } =
      event;

    return new LocalVgVehicleSessionEndEvent(
      {
        finishDate: eDate,
        sessionId: eSessionId,
        vehicleId: eVehicleId,
        latitude: eLatitude,
        longitude: eLongitude,
        correlationId: corId,
        error: eErrorCode,
      },
      corId,
    );
  }
}
