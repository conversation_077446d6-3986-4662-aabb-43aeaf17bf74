import { EventProps } from '@bluesg-2/sqs-event';
import { localEventPattern } from 'src/common/constants/event';
import { UUID } from 'src/common/tiny-types/uuid.type';
import { VulogVgEvent } from 'src/logic/vulog/vulog.event';

export interface LocalVgChargingCableUnpluggedEventPayload {
  vehicleId: string;
  correlationId?: string;
  error?: string;
}

export class LocalVgChargingCableUnpluggedEvent
  implements EventProps<LocalVgChargingCableUnpluggedEventPayload>
{
  public payload: LocalVgChargingCableUnpluggedEventPayload;

  public readonly event: string = localEventPattern.vg.chargingCableUnplugged;

  public correlationId?: string;

  public readonly sentAt?: number = new Date().getTime();

  constructor(
    payload: LocalVgChargingCableUnpluggedEventPayload,
    corId: string = new UUID().value,
  ) {
    this.payload = payload;
    this.correlationId = corId;
  }

  static from(
    event: VulogVgEvent,
    corId: string = new UUID().value,
  ): LocalVgChargingCableUnpluggedEvent {
    const { eErrorCode } = event;

    return new LocalVgChargingCableUnpluggedEvent(
      {
        vehicleId: event.eVehicleId,
        correlationId: corId,
        error: eErrorCode,
      },
      corId,
    );
  }
}
