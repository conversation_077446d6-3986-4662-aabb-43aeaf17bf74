import { Signer } from '@aws-sdk/rds-signer';
import { DataSourceOptions } from 'typeorm';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';

// DB_SSL_CERT is encode in base64 type
const ssl =
  process.env.DB_SSL && process.env.DB_SSL_CERT
    ? {
        ca: Buffer.from(process.env.DB_SSL_CERT, 'base64').toString(),
        rejectUnauthorized: false,
      }
    : null;
let userName = process.env.DB_USERNAME;
let password: any = process.env.DB_PASSWORD;
if (process.env.DB_AUTH_MODE === 'rds_iam') {
  userName = process.env.DB_IAMUSERNAME;
  password = async (): Promise<string> => {
    const signer = new Signer({
      region: process.env.AWS_DEFAULT_REGION,
      hostname: process.env.DB_HOST,
      port: Number(process.env.DB_PORT),
      username: process.env.DB_IAMUSERNAME,
    });
    return await signer.getAuthToken();
  };
}

export const dataSourceOptions = {
  type: process.env.DB_TYPE,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  username: userName,
  password: password,
  database: process.env.DB_NAME,
  entities: ['dist/**/*.entity.{js,ts}'],
  migrations: ['migrations/*.js'],
  synchronize: false,
  namingStrategy: new SnakeNamingStrategy(),
  ssl,
  autoLoadEntities: true,
  logging: false,
} as DataSourceOptions;
