import { plainToInstance, Type } from 'class-transformer';
import { DateTime } from 'luxon';
import {
  CarId,
  CarModel,
  CarPlateNumber,
  VulogCarId,
  VulogServiceId,
  VulogZoneId,
} from 'src/common/tiny-types';
import { CarModelEnum } from '../common/enum/car-model.enum';
import { MapUtils } from '../common/utils/map.util';
import { Car } from '../logic/car/domain/car';
import {
  VulogCarRealTimeDto,
  VulogRealTimeVehicleStatus,
} from './dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogFleetServiceType } from './dtos/vulog-fleet-service/vulog-fleet-service.dto';
import {
  VulogZone,
  VulogZoneType,
} from './dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { Station } from './station';

export class Location {
  constructor(props: Location) {
    Object.assign(this, props);
  }

  latitude: number;

  longitude: number;
}

export class Alerts {
  constructor(props: Alerts) {
    Object.assign(this, props);
  }

  hasFlatTire: boolean;
  hasTireUnderPressure: boolean;
  hasCrash: boolean;
}

export class RealtimeCar {
  constructor(
    props: Omit<
      RealtimeCar,
      | 'toVulogCarRealTimeDto'
      | 'toCar'
      | 'isAllowedToAttachPackage'
      | 'isOutOfService'
      | 'isUnderCustomerTripService'
      | 'isUnderServiceTripService'
    >,
  ) {
    Object.assign(this, props);

    this.zoneMaps = this.zoneIds?.length
      ? MapUtils.build(this.zoneIds, (vgZone) => vgZone.value)
      : new Map<string, VulogZoneId>();
  }

  @Type(() => VulogCarId)
  id: VulogCarId;

  vuboxId?: string;

  status?: VulogRealTimeVehicleStatus;

  statusLabel?: string;

  @Type(() => CarModel)
  model: CarModel;

  @Type(() => CarPlateNumber)
  plate: CarPlateNumber;

  vin: string;

  @Type(() => VulogCarId)
  sourceId: VulogCarId;

  energyLevel: number;

  isCharging: boolean;

  isPluggedIn: boolean;

  @Type(() => Location)
  location: Location;

  areDoorsAndWindowsClosed?: boolean;

  @Type(() => Alerts)
  alerts?: Alerts;

  zoneIds?: VulogZoneId[];

  zoneMaps?: Map<string, VulogZoneId>;

  isDoorLocked?: boolean;

  isDoorClosed?: boolean;

  isEngineOn?: boolean;

  serviceType?: VulogFleetServiceType;

  // The service ID. Returned only when the vehicle has the In Use status.
  serviceId?: VulogServiceId;

  disabled: boolean;

  outOfServiceReason: string;

  allowedStickyZone: VulogZone;

  immobilizerOn?: boolean;

  currentStation?: Station;

  lastActiveDate?: DateTime;

  invalidZones?: VulogZone[]; // Such as zones under Service Trip fleet service

  isAllowedToAttachPackage(minAllowedBatteryPercentage: number): boolean {
    return this.energyLevel >= minAllowedBatteryPercentage;
  }

  toCar(id?: CarId): Car {
    return new Car({
      id: id ? id.value : null,
      vulogId: this.id.value,
      model: this.model.value as CarModelEnum,
      plate: this.plate.value,
      vin: this.vin,
      realtimeMetadata: {
        isPlugged: this.isPluggedIn,
        disabled: this.disabled,
        batteryPercentage: this.energyLevel,
        isLocked: this.isDoorLocked,
      },
    });
  }

  toVulogCarRealTimeDto(): VulogCarRealTimeDto {
    const {
      id,
      vuboxId,
      status,
      model,
      plate,
      vin,
      sourceId,
      energyLevel,
      isCharging,
      location,
      zoneIds,
      isDoorLocked,
      isDoorClosed,
      isEngineOn,
      serviceType,
      serviceId,
      disabled,
      outOfServiceReason,
      allowedStickyZone,
      immobilizerOn,
      lastActiveDate,
      invalidZones,
    } = this;

    const zones =
      zoneIds?.map(
        (zoneId) =>
          new VulogZone(zoneId.value, null, VulogZoneType.Allowed, null),
      ) || [];

    return plainToInstance(VulogCarRealTimeDto, {
      id: id.value,
      boxid: vuboxId,
      vehicle_status: status,
      plate: null,
      vin,
      autonomy: energyLevel,
      isCharging,
      location: location
        ? {
            latitude: location.latitude,
            longitude: location.longitude,
          }
        : null,
      zones: zones,
      isDoorLocked,
      isDoorClosed,
      engineOn: isEngineOn,
      serviceType,
      serviceId: serviceId?.value,
      disabled,
      outOfServiceReason,
      immobilizerOn,
      last_active_date: lastActiveDate?.toISO(),
      booking_status: 0,
    });
  }
}
