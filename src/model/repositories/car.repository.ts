import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { Repository } from 'typeorm';
import { TypeOrmBaseRepository } from './typeorm-base-repository';

@Injectable()
export class CarRepository extends TypeOrmBaseRepository<CarEntity> {
  constructor(
    @InjectRepository(CarEntity)
    private readonly carRepository: Repository<CarEntity>,
  ) {
    super(carRepository);
  }
}
