import { BsgUserId, VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { VulogUser, VulogUserStatus } from 'src/model/vulog-user';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityProps } from './entity-props';

@Entity({ name: 'vulog_user' })
export class VulogUserEntity extends BaseEntity {
  constructor(props?: Omit<EntityProps<VulogUserEntity>, 'toVulogUser'>) {
    super();
    if (props) Object.assign(this, props);
  }

  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true, nullable: true })
  vulogUserId?: string;

  @Column({ unique: true })
  @Index()
  bsgUserId: string;

  @Column({ nullable: true })
  status?: VulogUserStatus;

  @Column({
    nullable: true,
  })
  profileId?: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  password?: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  email?: string;

  @Column({ type: 'boolean', nullable: true })
  isRegistered?: boolean;

  @Column({
    type: 'text',
    nullable: true,
  })
  rfid?: string;

  @CreateDateColumn()
  createdAt?: Date;

  @UpdateDateColumn()
  updatedAt?: Date;

  @Column({
    type: 'text',
    nullable: false,
    default: 'TBD',
  })
  private userInfoHash: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: false,
    default: new Date(2000, 0, 1),
  })
  private ussUpdatedAt: Date;

  toVulogUser(): VulogUser {
    return new VulogUser(
      this.bsgUserId ? new BsgUserId(this.bsgUserId) : null,
      this.email,
      this.status,
      this.password,
      this.profileId ? new VulogProfileId(this.profileId) : null,
      this.vulogUserId ? new VulogUserId(this.vulogUserId) : null,
      this.isRegistered,
      this.createdAt,
      this.updatedAt,
      this.id,
      this.rfid,
    );
  }

  static from(vulogUser: VulogUser): VulogUserEntity {
    return new VulogUserEntity({
      id: vulogUser.entityId,
      vulogUserId: vulogUser.id?.value,
      status: vulogUser.status,
      password: vulogUser.getPassword(),
      bsgUserId: vulogUser.bsgUserId?.value,
      profileId: vulogUser.profileId?.value,
      email: vulogUser.email,
      isRegistered: vulogUser.isRegistered,
      rfid: vulogUser.rfid,
    });
  }
}
