import {
  BaseEntity as Base,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export class BaseEntity extends Base {
  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @CreateDateColumn({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    select: true,
  })
  createdAt?: Date;

  @Column({
    nullable: true,
    select: true,
  })
  createdBy?: string;

  @UpdateDateColumn({
    type: 'timestamptz',
    select: true,
  })
  modifiedAt?: Date;

  @Column({
    nullable: true,
    select: true,
  })
  modifiedBy?: string;

  @DeleteDateColumn({
    type: 'timestamptz',
    select: false,
  })
  deletedAt?: Date;

  @Column({
    nullable: true,
    select: true,
  })
  deletedBy?: string;
}
