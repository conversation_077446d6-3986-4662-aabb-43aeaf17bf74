import { ReservationStatus } from 'src/common/enum/reservation.enum';

import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { PricingPolicy } from 'src/model/pricing-policy';
import { Column, Entity, Index, Point } from 'typeorm';
import { BaseEntity } from './base.entity';
import { EntityProps } from './entity-props';
import { VulogUserEntity } from './vulog-user.entity';

@Entity({ name: 'reservation' })
export class ReservationEntity extends BaseEntity {
  constructor(props?: Omit<EntityProps<ReservationEntity>, ''>) {
    super();

    if (props) Object.assign(this, props);
  }

  @Index()
  @Column({
    type: 'uuid',
    nullable: false,
  })
  bsgUserId: string;

  vulogUser?: VulogUserEntity;

  //NOTE: This is actually vulog car id
  @Column({
    type: 'uuid',
    nullable: true,
  })
  carId?: string;

  car?: CarEntity;

  @Column({
    type: 'enum',
    nullable: true,
    enum: CarModelEnum,
  })
  carModel?: CarModelEnum;

  @Column({
    type: 'uuid',
    nullable: false,
  })
  startStationId: string;

  @Column({
    type: 'uuid',
    nullable: true,
  })
  endStationId?: string;

  @Column({
    nullable: true,
    type: 'geometry',
    comment:
      'Point geometry object. `coordinates` should be [longitude, latitude]',
  })
  vulogEndGpsCoordinates?: Point;

  /**
   * Point geometry object. `coordinates` should be `[longitude, latitude]`
   */
  @Column({
    nullable: true,
    type: 'geometry',
    comment:
      'Point geometry object. `coordinates` should be [longitude, latitude]',
  })
  mobileEndGpsCoordinates?: Point;

  @Column({
    nullable: true,
  })
  plateNumber?: string;

  @Column({
    nullable: true,
  })
  vulogTripId?: string;

  @Index()
  @Column({
    type: 'enum',
    nullable: false,
    enum: ReservationStatus,
  })
  status: ReservationStatus;

  @Column({
    type: 'jsonb',
    nullable: true,
  })
  rentalPackageData?: unknown;

  @Column({
    type: 'boolean',
    nullable: true,
    default: false,
  })
  local?: boolean;

  @Column({
    type: 'boolean',
    nullable: true,
    default: false,
    comment:
      'In case, the same reservation is to be allocated again once more time (keep the same reservation ID)',
  })
  allocateAgain?: boolean;

  @Column({
    type: 'timestamptz',
    nullable: true,
    comment: 'The time that the rental starts',
  })
  startedAt?: Date;

  @Column({
    type: 'timestamptz',
    nullable: true,
    comment: 'The time that the rental ends',
  })
  endedAt?: Date;

  @Index()
  @Column({
    type: 'timestamptz',
    nullable: true,
    comment: 'The time that reservation is confirmed by VULOG',
  })
  reservedAt?: Date;

  @Index()
  @Column({
    type: 'timestamptz',
    nullable: true,
    comment: 'The time that reservation is expired based on VULOG config',
  })
  expiresAt?: Date;

  @Column({
    type: 'timestamptz',
    nullable: true,
  })
  canceledAt?: Date;

  @Column({
    type: 'timestamptz',
    nullable: true,
  })
  abuseReleasesAt?: Date;

  @Column({
    nullable: true,
  })
  rating?: string;

  @Column({
    nullable: true,
  })
  carCleanliness?: string;

  @Column({
    type: 'decimal',
    precision: 14,
    scale: 2,
    nullable: true,
  })
  amount?: number;

  @Column({
    type: 'decimal',
    precision: 14,
    scale: 2,
    nullable: true,
  })
  duration?: number;

  @Column({
    type: 'jsonb',
    nullable: true,
  })
  pricingPolicy?: PricingPolicy;

  @Column({
    type: 'boolean',
    nullable: true,
    default: false,
    comment: 'Mark as true when the data for ending rental data is added',
  })
  isEndingRentalDataAdded?: boolean;

  @Column({
    type: 'varchar',
    nullable: true,
    comment: 'This column is formed by first name and last name',
  })
  userFullName?: string; // full name = first name + last name

  @Column({
    type: 'varchar',
    nullable: true,
  })
  userEmail?: string;
}
