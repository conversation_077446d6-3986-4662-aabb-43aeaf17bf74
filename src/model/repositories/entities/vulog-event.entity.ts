import {
  BaseEntity,
  Column,
  Entity,
  Index,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { EntityProps } from './entity-props';

export enum VulogEventSource {
  AiMA = 'AIMA',
  VEHICLE_GATEWAY = 'VEHICLE_GATEWAY',
}

@Entity({ name: 'vulog_event' })
@Index(['receivedAt', 'type'])
export class VulogEventEntity extends BaseEntity {
  constructor(props?: Omit<EntityProps<VulogEventEntity>, ''>) {
    super();
    if (props) Object.assign(this, props);
  }

  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @Index()
  @Column({
    type: 'text',
    nullable: true,
    comment: 'The id from the event sent by Vulog',
  })
  eventId: string;

  @Index()
  @Column({
    type: 'varchar',
    nullable: false,
    comment: 'the type of event from Vulog',
  })
  type: string;

  @Index()
  @Column({
    type: 'varchar',
    nullable: false,
    comment: 'Actor sends the event (Vubox, API, etc)',
  })
  origin: string;

  @Column({
    type: 'varchar',
    default: VulogEventSource.AiMA,
    nullable: false,
    comment: 'The source of the event (AiMA or Vehicle Gateway)',
  })
  source: VulogEventSource;

  @Column({
    type: 'varchar',
    nullable: true,
    comment: 'Is the event triggered periodically or realtime?',
  })
  trigger?: string;

  @Column({
    type: 'jsonb',
    nullable: false,
    comment: 'Raw webhook response from Vulog',
  })
  payload: unknown;

  @Column({
    type: 'timestamptz',
    nullable: false,
    comment: 'The time when BlueSG receives webhook response from Vulog',
  })
  receivedAt: Date;

  @Column({
    type: 'varchar',
    nullable: true,
    comment:
      'The request ID to trace the requests going through internal services and external ones',
  })
  correlationId?: string;
}
