import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginationSortingOrder } from 'src/common/constants/pagination';
import { BsgUserId, VulogUserId } from 'src/common/tiny-types';
import { generatePagination } from 'src/common/utils/db-pagination.util';
import { Repository, SelectQueryBuilder } from 'typeorm';
import {
  DISABLED_RFID_SUFFIX,
  SECOND_DISABLED_RFID_SUFFIX,
  VulogUser,
  VulogUserStatus,
} from '../vulog-user';
import { VulogUserEntity } from './entities/vulog-user.entity';
import { TypeOrmBaseRepository } from './typeorm-base-repository';

@Injectable()
export class VulogUserRepository extends TypeOrmBaseRepository<VulogUserEntity> {
  constructor(
    @InjectRepository(VulogUserEntity)
    vulogUserRepository: Repository<VulogUserEntity>,
  ) {
    super(vulogUserRepository);
  }

  async findVulogUserByBsgUserId(
    bsgUserId: BsgUserId,
  ): Promise<VulogUserEntity> {
    return this.findOne({
      where: {
        bsgUserId: bsgUserId.value,
      },
    });
  }

  async findVulogUser(userId: VulogUserId): Promise<VulogUserEntity> {
    return this.findOne({
      where: {
        id: userId.value,
      },
    });
  }

  private async prepareQueryVulogUsersHavingRfidRemoved(
    excludedVulogUserIds?: string[],
  ): Promise<{
    queryBuilder: SelectQueryBuilder<VulogUserEntity>;
    alias: string;
  }> {
    const vulogUserAlias = 'vulogUsers';

    let baseQueryBuilder = await this.createQueryBuilder(vulogUserAlias)
      .select()
      .where(
        `(${vulogUserAlias}.rfid LIKE '%${DISABLED_RFID_SUFFIX}' OR ${vulogUserAlias}.rfid LIKE '%${SECOND_DISABLED_RFID_SUFFIX}')`,
      )
      .andWhere(`${vulogUserAlias}.status IN (:...status)`, {
        status: [VulogUserStatus.UPDATING, VulogUserStatus.SYNCHRONIZED],
      });

    if (excludedVulogUserIds?.length) {
      baseQueryBuilder = baseQueryBuilder.andWhere(
        `${vulogUserAlias}.vulogUserId NOT IN (:...excludedIds)`,
        { excludedIds: excludedVulogUserIds },
      );
    }

    return {
      queryBuilder: baseQueryBuilder,
      alias: vulogUserAlias,
    };
  }

  async getPaginatedVulogUsersHavingRfidRemoved(queryParams: {
    page: number;
    pageSize: number;
    sortKey: string;
    sortType: PaginationSortingOrder;
    excludedVulogUserIds: string[];
  }): Promise<VulogUser[]> {
    const { page, pageSize, sortKey, sortType } = queryParams;

    const { take, skip } = generatePagination({
      page,
      pageSize,
      sortKey,
      sortType,
    });

    const { queryBuilder, alias } =
      await this.prepareQueryVulogUsersHavingRfidRemoved(
        queryParams.excludedVulogUserIds,
      );

    const resultSets = await queryBuilder
      .addOrderBy(`${alias}.rfid`, sortType)
      .skip(skip)
      .take(take)
      .getMany();

    return resultSets.length ? resultSets.map((rs) => rs.toVulogUser()) : [];
  }

  async countVulogUsersHavingRfidRemoved(): Promise<number> {
    const { queryBuilder } =
      await this.prepareQueryVulogUsersHavingRfidRemoved();

    const count = await queryBuilder.getCount();

    return count;
  }
}
