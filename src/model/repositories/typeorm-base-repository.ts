import {
  BaseEntity,
  DeepPartial,
  FindManyOptions,
  FindOneOptions,
  InsertResult,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { UpsertOptions } from 'typeorm/repository/UpsertOptions';

export class TypeOrmBaseRepository<T extends BaseEntity> {
  constructor(protected readonly genericRepository: Repository<T>) {}

  async find(options?: FindManyOptions<T>): Promise<T[]> {
    return this.genericRepository.find(options);
  }

  async findOne(options: FindOneOptions<T>): Promise<T> {
    return this.genericRepository.findOne(options);
  }

  async insert(
    entityList: QueryDeepPartialEntity<T> | QueryDeepPartialEntity<T>[],
  ): Promise<InsertResult> {
    return this.genericRepository.insert(entityList);
  }

  async upsert(
    entityList: QueryDeepPartialEntity<T> | QueryDeepPartialEntity<T>[],
    conflictPathsOrOptions: string[] | UpsertOptions<T>,
  ): Promise<InsertResult> {
    return this.genericRepository.upsert(entityList, conflictPathsOrOptions);
  }

  async savePartials(entityList: DeepPartial<T>[]): Promise<DeepPartial<T>[]> {
    return this.genericRepository.save(entityList);
  }

  async save(entities: T[]): Promise<T[]> {
    return await this.genericRepository.save(entities);
  }

  async clearAll() {
    await this.genericRepository.clear();
  }

  async count(options?: FindManyOptions<T>): Promise<number> {
    return this.genericRepository.count(options);
  }

  createQueryBuilder(alias: string): SelectQueryBuilder<T> {
    return this.genericRepository.createQueryBuilder(alias);
  }
}
