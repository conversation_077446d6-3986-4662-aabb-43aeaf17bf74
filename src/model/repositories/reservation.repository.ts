import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DateTime } from 'luxon';
import { BasePaginationRequestDto } from 'src/common/api/base.pagination.request.dto';
import { PaginationSortingOrder } from 'src/common/constants/pagination';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { DatabaseUpdateError } from 'src/common/errors/database-update-error';
import { ReservationError } from 'src/common/errors/reservation-error';
import { ReservationNotFoundError } from 'src/common/errors/reservation/reservation-not-found.error';
import {
  BsgUserId,
  ReservationId,
  StationId,
  VulogCarId,
  VulogJourneyOrTripId,
} from 'src/common/tiny-types';
import { generatePagination } from 'src/common/utils/db-pagination.util';
import { EscapeWildCardsUtil } from 'src/common/utils/escape-wildcards.util';
import { SupaPagination } from 'src/common/utils/supa-pagination.util';
import { GetReservationsByPlateNoAndTimeDtoV1 } from 'src/controllers/internal/v1/reservation/dto/request/get-reservations-by-plate-no.v1.dto';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import {
  Brackets,
  In,
  IsNull,
  LessThan,
  MoreThan,
  MoreThanOrEqual,
  Not,
  Repository,
  UpdateResult,
} from 'typeorm';
import { BasePaginatedDataInterface } from '../interfaces/base-paginated-data.interface';
import { GetReservationsDescription } from '../interfaces/get-reservations.interface';
import { RentalPackage } from '../rental-package';
import { Reservation, ReservationDescription } from '../reservation.domain';
import { ReservationEntity } from './entities/reservation.entity';
import { VulogUserEntity } from './entities/vulog-user.entity';
import { TypeOrmBaseRepository } from './typeorm-base-repository';

@Injectable()
export class ReservationRepository extends TypeOrmBaseRepository<ReservationEntity> {
  constructor(
    @InjectRepository(ReservationEntity)
    private reservationRepository: Repository<ReservationEntity>,
  ) {
    super(reservationRepository);
  }

  async getReservationAndThrowIfNotFound(id: string) {
    const reserve = await this.findOne({
      where: {
        id,
      },
    });

    if (!reserve) {
      throw new ReservationNotFoundError();
    }

    return reserve;
  }

  async findOneByCarIdAndTripId(
    carId: VulogCarId,
    vulogTripId: VulogJourneyOrTripId,
  ): Promise<Reservation> {
    const reservation = await this.findOne({
      where: {
        carId: carId.value,
        vulogTripId: vulogTripId.value,
      },
    });

    return reservation ? Reservation.from(reservation) : null;
  }

  async findOneById(id: ReservationId): Promise<Reservation> {
    const reservation = await this.findOne({
      where: {
        id: id.value,
      },
    });

    return reservation ? Reservation.from(reservation) : null;
  }

  async findOneByTripId(tripId: VulogJourneyOrTripId): Promise<Reservation> {
    const reservation = await this.findOne({
      where: {
        vulogTripId: tripId.value,
      },
    });

    return reservation ? Reservation.from(reservation) : null;
  }

  async saveOne(reservation: Reservation): Promise<Reservation> {
    await this.save([reservation.toEntity()]);

    const resultEntity = await this.findOneByTripId(reservation.vulogTripId);

    return resultEntity;
  }

  async updatePackage(
    reservation: Reservation,
    rentalPackageData?: unknown,
  ): Promise<Reservation> {
    reservation.rentalPackageData =
      rentalPackageData != null
        ? new RentalPackage(rentalPackageData as RentalPackage)
        : null;

    return this.saveOne(reservation);
  }

  async createReservation(domain: Reservation): Promise<Reservation> {
    const [resultPersistence] = await this.save([domain.toEntity()]);

    return Reservation.from(
      await this.findOne({
        where: {
          id: resultPersistence.id,
        },
      }),
    );
  }

  async updateReservation(domain: Reservation): Promise<Reservation> {
    const existedPersistence = await this.findOne({
      where: { id: domain.getId },
    });

    if (!existedPersistence) {
      throw new ReservationNotFoundError();
    }

    const [resultPersistence] = await this.save([domain.toEntity()]);

    return Reservation.from(resultPersistence);
  }

  getReservationsByPlateNumberAndTime({
    plateNumberAndTxnDate,
  }: GetReservationsByPlateNoAndTimeDtoV1): Promise<ReservationEntity[]> {
    const query = this.createQueryBuilder('rev').andWhere(
      new Brackets((query) => {
        plateNumberAndTxnDate.forEach((pair) => {
          query.orWhere(
            `(rev.plateNumber = '${pair.plateNumber}' and rev.startedAt <= '${pair.time}' and rev.endedAt >= '${pair.time}' and rev.status = 'ENDED')`,
          );
        });
      }),
    );

    return query.getMany();
  }

  /**
   * A reservation that is considered "finished" should include statuses `[CANCELLED,CONVERTED,EXPIRED]`
   */
  async getLatestFinishedReservation(
    bsgUserId: BsgUserId,
  ): Promise<Reservation> {
    const reservation = await this.findOne({
      where: {
        bsgUserId: bsgUserId.value,
        status: In([
          ReservationStatus.Cancelled,
          ReservationStatus.Converted,
          ReservationStatus.Expired,
        ]),
      },
      order: {
        createdAt: PaginationSortingOrder.DESCENDING,
      },
    });

    return reservation ? Reservation.from(reservation) : null;
  }

  async getCurrentOnGoingReservation(
    bsgUserId: BsgUserId,
    startStationId?: StationId,
    carModel?: CarModelEnum,
  ): Promise<Reservation> {
    const where: Record<string, unknown> = {
      bsgUserId: bsgUserId.value,
      status: ReservationStatus.Reserved,
      expiresAt: MoreThan(DateTime.now().toJSDate()),
    };

    if (startStationId) {
      where.startStationId = startStationId.value;
    }

    if (carModel) {
      where.carModel = carModel;
    }

    const reservation = await this.findOne({
      where,
      order: {
        createdAt: PaginationSortingOrder.DESCENDING,
      },
    });

    return reservation ? Reservation.from(reservation) : null;
  }

  async getToBeExpiredReservations(): Promise<Reservation[]> {
    const dtNow = DateTime.now().toJSDate();

    const expiredReservations = await this.find({
      where: {
        status: ReservationStatus.Reserved,
        expiresAt: LessThan(dtNow),
      },
    });

    return expiredReservations.map((reservation) =>
      Reservation.from(reservation),
    );
  }

  async getListReservations<
    T extends GetReservationsDescription & BasePaginationRequestDto,
  >(queryOption: T): Promise<[Reservation[], number]> {
    const {
      status,
      bsgUserId,
      from,
      to,
      endStationIds,
      startStationIds,
      rentalId,
      plateNumber,
      vulogTripId,
      userFullName,
      userEmail,
      ...paginateData
    } = queryOption;

    let query = this.createQueryBuilder('reserve')
      .leftJoinAndMapOne(
        'reserve.car',
        CarEntity,
        'car',
        'reserve.carId = "car"."vulog_id"::uuid',
      )
      .leftJoinAndMapOne(
        'reserve.vulogUser',
        VulogUserEntity,
        'vulogUser',
        'reserve.bsgUserId = "vulogUser"."bsg_user_id"::uuid',
      );

    if (status && status.length) {
      query.andWhere('reserve.status IN (:...status)', {
        status,
      });
    }

    if (bsgUserId) {
      query.andWhere('reserve.bsgUserId = :bsgUserId', { bsgUserId });
    }

    if (rentalId) {
      query.andWhere('reserve.id = :rentalId', { rentalId });
    }

    if (endStationIds && endStationIds.length) {
      query.andWhere('reserve.endStationId IN (:...endStationIds)', {
        endStationIds,
      });
    }

    if (startStationIds && startStationIds.length) {
      query.andWhere('reserve.startStationId IN (:...startStationIds)', {
        startStationIds,
      });
    }

    if (from) {
      query.andWhere('reserve.createdAt >= :from', { from });
    }

    if (to) {
      query.andWhere('reserve.createdAt <= :to', { to });
    }

    if (plateNumber) {
      query.andWhere('car.plate ILIKE :plateNumber', {
        plateNumber: `%${EscapeWildCardsUtil.removeWildcards(plateNumber)}%`,
      });
    }

    if (vulogTripId) {
      query.andWhere('reserve.vulogTripId ILIKE :vulogTripId', {
        vulogTripId: `%${EscapeWildCardsUtil.removeWildcards(vulogTripId)}%`,
      });
    }

    if (userFullName) {
      query.andWhere('reserve.userFullName ILIKE :userFullName', {
        userFullName: `%${EscapeWildCardsUtil.removeWildcards(userFullName)}%`,
      });
    }

    if (userEmail) {
      query.andWhere('reserve.userEmail ILIKE :userEmail', {
        userEmail: `%${EscapeWildCardsUtil.removeWildcards(userEmail)}%`,
      });
    }

    query = SupaPagination.fromBasePaginationRequest(
      paginateData,
    ).appendPagination(query, 'reserve');

    const [result, total] = await query.getManyAndCount();

    return [result.map((reservation) => Reservation.from(reservation)), total];
  }

  async getCronCronTerminatedReservations(queryParams: {
    page: number;
    pageSize: number;
    sortKey: string;
    sortType: PaginationSortingOrder;
  }): Promise<BasePaginatedDataInterface<Reservation[]>> {
    const { page, pageSize, sortKey, sortType } = queryParams;

    const { take, skip } = generatePagination({
      page,
      pageSize,
      sortKey,
      sortType,
    });

    const reservationsAlias = 'reservations';

    const baseQueryBuilder = await this.createQueryBuilder(reservationsAlias)
      .select()
      .where(`${reservationsAlias}.status IN (:...terminatedStatuses)`, {
        terminatedStatuses: [
          ReservationStatus.Expired,
          ReservationStatus.Cancelled,
        ],
      })
      .andWhere(`${reservationsAlias}.startedAt IS NOT NULL`)
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('1')
          .from(ReservationEntity, 'rsrv')
          .where(`rsrv.carId = ${reservationsAlias}.carId`)
          .andWhere(`rsrv.startedAt > ${reservationsAlias}.startedAt`) // No need to check on `reservedAt` as zone detachment happens only when a rental started in Vulog
          .andWhere('rsrv.status IN (:...onGoingTripStatuses)')
          .getQuery();

        return `NOT EXISTS (${subQuery})`;
      })
      .setParameter('onGoingTripStatuses', [ReservationStatus.Converted]);

    const resultSets = await baseQueryBuilder
      .addOrderBy(`${reservationsAlias}.startedAt`, sortType)
      .skip(skip)
      .take(take)
      .getMany();

    const resultCount = await baseQueryBuilder.getCount();

    return {
      result: resultSets.map((reservationEntity) =>
        Reservation.from(reservationEntity),
      ),
      pagination: {
        page,
        pageSize,
        sortKey,
        sortType,
        total: resultCount,
        totalPages: Math.ceil(resultCount / pageSize),
      },
    };
  }

  async softDelete(id: ReservationId): Promise<void> {
    const reservationEntity = await this.reservationRepository.findOne({
      where: {
        id: id.value,
      },
    });

    if (!reservationEntity) {
      throw new ReservationError(
        `Reservation ${reservationEntity.id} is not found`,
      );
    }

    await this.reservationRepository.softRemove(reservationEntity);
  }

  async updateStatus(
    reservation: Reservation,
    newStatus: ReservationStatus,
    expectedStatuses: ReservationStatus[],
    additionalUpdates?: Partial<ReservationDescription>,
  ) {
    const partialUpdates = {
      ...(additionalUpdates
        ? Reservation.toPartialEntity(additionalUpdates)
        : {}),
      status: newStatus,
    };

    const { affected }: UpdateResult = await this.genericRepository.update(
      {
        id: reservation.getId,
        status: In(expectedStatuses),
      },
      partialUpdates,
    );

    const possiblyUpdatedReservation: Reservation = await this.findOneById(
      new ReservationId(reservation.getId),
    );

    if (!affected) {
      Logger.error('Could not update reservation status', {
        passedReservation: reservation.toEntity(),
        currentReservation: possiblyUpdatedReservation.toEntity(),
        newStatus,
        expectedStatuses,
        partialUpdates,
      });

      throw new DatabaseUpdateError(
        `Could not update specified reservation because where conditions did not satisfy. Reservation ID: ${reservation.getId}`,
      );
    }

    Logger.log(`Number of reservations updated: ${affected}`);

    return possiblyUpdatedReservation;
  }

  async getRecentlyUpdatedReservations(
    updatedFrom: DateTime,
  ): Promise<Reservation[]> {
    const reservationEntities = await this.find({
      where: {
        carId: Not(IsNull()),
        modifiedAt: MoreThanOrEqual(updatedFrom.toJSDate()),
      },
      order: {
        modifiedAt: PaginationSortingOrder.DESCENDING,
      },
    });

    const latestByCarId = new Map<string, Reservation>();

    for (const entity of reservationEntities) {
      const carId = entity.carId;

      if (carId && !latestByCarId.has(carId)) {
        latestByCarId.set(carId, Reservation.from(entity));
      }
    }

    return Array.from(latestByCarId.values());
  }
}
