import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  VulogCarId,
  VulogJourneyOrTripId,
  VulogUserId,
} from 'src/common/tiny-types';
import { FindOptionsWhere, Raw, Repository } from 'typeorm';
import { VulogEvent } from '../vulog-event';
import { VulogEventEntity } from './entities/vulog-event.entity';
import { TypeOrmBaseRepository } from './typeorm-base-repository';

export type FindOneVulogEventConditions = {
  type?: string;
  tripId?: VulogJourneyOrTripId;
  userId?: VulogUserId;
  vehicleId?: VulogCarId;
};

@Injectable()
export class VulogEventRepository extends TypeOrmBaseRepository<VulogEventEntity> {
  constructor(
    @InjectRepository(VulogEventEntity)
    private readonly vulogEventRepository: Repository<VulogEventEntity>,
  ) {
    super(vulogEventRepository);
  }

  async createVulogEvent(vulogEvent: VulogEvent): Promise<VulogEvent> {
    const [entity] = await this.save([vulogEvent.toEntity()]);

    return VulogEvent.from(entity);
  }

  async findOneByConditions(
    conditions: FindOneVulogEventConditions,
  ): Promise<VulogEvent> {
    let where: FindOptionsWhere<VulogEventEntity> = {};

    if (conditions?.tripId || conditions?.userId || conditions?.vehicleId) {
      where = {
        ...where,
        payload: Raw(
          (alias) => {
            let query = '';

            if (conditions.tripId) {
              query = `${alias} ->> 'tripId' = :tripId`;
            }

            if (conditions.userId) {
              const q = `${alias} ->> 'userId' = :userId`;

              query = query ? query.concat(` AND ${q} `) : q;
            }

            if (conditions.vehicleId) {
              const q = `${alias} ->> 'vehicleId' = :vehicleId`;

              query = query ? query.concat(` AND ${q} `) : q;
            }

            return query;
          },
          {
            tripId: conditions.tripId?.value,
            userId: conditions.userId?.value,
            vehicleId: conditions.vehicleId?.value,
          },
        ),
      };
    }

    if (conditions?.type) {
      where = {
        ...where,
        type: conditions.type,
      };
    }

    const entity = await this.findOne({ where });

    return entity ? VulogEvent.from(entity) : null;
  }
}
