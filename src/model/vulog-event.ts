import { CorrelationId } from '@bluesg-2/monitoring/dist/correlation-id/correlation-id.service';
import { VulogEventId } from 'src/common/tiny-types';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import {
  VulogEventEntity,
  VulogEventSource,
} from './repositories/entities/vulog-event.entity';

export class VulogEvent {
  constructor(props?: Omit<VulogEvent, 'toEntity'>) {
    Object.assign(this, {
      ...props,
      payload: JSON.parse(JSON.stringify(props.payload)),
    });
  }

  id?: VulogEventId = new VulogEventId();

  source: VulogEventSource;

  eventId: string;

  type: string;

  origin: string;

  trigger?: string;

  payload: unknown;

  receivedAt: VulogDate;

  correlationId?: CorrelationId;

  toEntity(): VulogEventEntity {
    return new VulogEventEntity({
      id: this.id.value,
      source: this.source,
      eventId: this.eventId,
      type: this.type,
      origin: this.origin,
      trigger: this.trigger || null,
      payload: this.payload,
      receivedAt: this.receivedAt.toDateTime().toJSDate(),
      correlationId: this.correlationId?.value,
    });
  }

  static from(entity: VulogEventEntity): VulogEvent {
    return new VulogEvent({
      id: new VulogEventId(entity.id),
      source: entity.source as VulogEventSource,
      eventId: entity.eventId,
      type: entity.type,
      origin: entity.origin,
      trigger: entity.trigger || null,
      payload: entity.payload,
      receivedAt: VulogDate.fromJsDate(entity.receivedAt),
      correlationId: entity.correlationId
        ? new CorrelationId(entity.correlationId)
        : null,
    });
  }
}
