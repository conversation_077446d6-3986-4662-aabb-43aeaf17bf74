import { OnGoingRental } from './on-going-rental';
import { Reservation } from './reservation.domain';

export class EndRentalCondition {
  constructor(
    readonly id: string,
    readonly label: string,
    readonly successMessage: string,
    readonly failureMessage: string,
    private readonly checkCondition: (rental: OnGoingRental) => boolean,
    readonly disabled: boolean = false, // Disable the check
  ) {}

  validate(rental: OnGoingRental): EndRentalConditionResult {
    if (this.disabled) {
      return new EndRentalConditionResult(this, true);
    }

    const isFulfilled = this.checkCondition(rental);

    return new EndRentalConditionResult(this, isFulfilled);
  }
}

export class EndRentalConditionResult {
  constructor(
    private readonly condition: EndRentalCondition,
    readonly fulfilled: boolean,
  ) {}

  get conditionId(): string {
    return this.condition.id;
  }

  get label(): string {
    return this.condition.label;
  }

  get message(): string {
    return this.fulfilled
      ? this.condition.successMessage
      : this.condition.failureMessage;
  }
}

export class EndRentalReport {
  constructor(
    readonly reservation: Reservation,
    readonly conditions: EndRentalConditionResult[],
    readonly endRentalSuccess: boolean,
    readonly price: number,
    readonly durationInSeconds: number,
  ) {}
}
