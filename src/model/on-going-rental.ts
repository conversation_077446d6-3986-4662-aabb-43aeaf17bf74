import { ReservationId, VulogJourneyOrTripId } from 'src/common/tiny-types';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { Car } from '../logic/car/domain/car';
import { GpsCoordinates } from './gps-coordinates';
import { CarRealtimeMetadata } from './rental-info';
import { RentalPackage } from './rental-package';
import { Station } from './station';
import { VulogZone } from './dtos/vulog-trip/vulog-trip-fields/vulog-zone';

export class OnGoingRental {
  constructor(props: OnGoingRental) {
    Object.assign(this, props);
  }

  id: ReservationId;

  vulogTripId: VulogJourneyOrTripId;

  startDate: VulogDate;

  endDate: VulogDate;

  duration: number;

  billedDuration: number;

  distance: number;

  price: number;

  startStation: Station;

  endStation: Station;

  currentStation?: Station;

  currentGpsCoordinates?: GpsCoordinates;
  
  vulogZones?: VulogZone[];

  car: Car;

  hasStartedTripInVulog: boolean;

  rentalPackage: RentalPackage;

  realtimeInfo: CarRealtimeMetadata;
}
