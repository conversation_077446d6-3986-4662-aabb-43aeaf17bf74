import { Latitude } from 'src/common/tiny-types/latitude';
import { Longitude } from 'src/common/tiny-types/longitude';
import { bigNumberize } from 'src/common/utils/big-decimal.util';
import { Point } from 'typeorm';

export class GpsCoordinates {
  constructor(props?: Omit<GpsCoordinates, 'toPointGeometry'>) {
    Object.assign(this, props);
  }

  latitude: Latitude;

  longitude: Longitude;

  toPointGeometry(): Point {
    return {
      type: 'Point',
      coordinates: [
        // See why decimal places is precised to 7. See: https://stackoverflow.com/a/23914607
        bigNumberize(this.longitude.value, 7).toNumber(),
        bigNumberize(this.latitude.value, 7).toNumber(),
      ],
    };
  }
}
