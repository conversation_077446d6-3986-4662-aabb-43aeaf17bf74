import { EndRentalConditionId } from 'src/common/constants/ending-rental-conditions';
import { NewRelicRentalEventService } from '~shared/newrelic/event/event-type/rental.event.newrelic.service';
import { OnGoingRental } from './on-going-rental';
import { ParkingLotValidationStatus } from './parking-lot';
import { Reservation } from './reservation.domain';
import { TelemetryCarInfo } from './telemetry-car-info';
import { TelemetryCarStatus } from './telemetry-car-status';

export type ValidationData = {
  telemetryCarInfo?: TelemetryCarInfo;
  onGoingRental?: OnGoingRental;
  telemetryCarStatus?: TelemetryCarStatus;
  parkingLotValidationStatus?: ParkingLotValidationStatus;
};

export class TestingEndRentalCondition {
  constructor(
    readonly id: EndRentalConditionId,
    readonly label: string,
    readonly successMessage: string,
    readonly failureMessage: string,
    private readonly checkCondition: (data: ValidationData) => boolean,
    readonly disabled: boolean = false,

    private readonly onValidation?: (
      plate: string,
      userId: string,
      qrCode: string | undefined,
      fulfilled: boolean,
      eventService: NewRelicRentalEventService,
    ) => Promise<void>,
  ) {}

  validate(options: ValidationData): TestingEndRentalConditionResult {
    if (this.disabled) {
      return new TestingEndRentalConditionResult(this, true);
    }

    const isFulfilled = this.checkCondition(options);

    return new TestingEndRentalConditionResult(this, isFulfilled);
  }

  async emitValidationEvent(
    plate: string,
    userId: string,
    qrCode: string | undefined,
    fulfilled: boolean,
    eventService: NewRelicRentalEventService,
  ): Promise<void> {
    if (this.onValidation) {
      await this.onValidation(plate, userId, qrCode, fulfilled, eventService);
    }
  }
}

export class TestingEndRentalConditionResult {
  constructor(
    private readonly condition: TestingEndRentalCondition,
    readonly fulfilled: boolean,
  ) {}

  get conditionId(): EndRentalConditionId {
    return this.condition.id;
  }

  get label(): string {
    return this.condition.label;
  }

  get message(): string {
    return this.fulfilled
      ? this.condition.successMessage
      : this.condition.failureMessage;
  }

  async emitValidationEvent(
    plate: string,
    userId: string,
    qrCode: string | undefined,
    eventService: NewRelicRentalEventService,
  ): Promise<void> {
    await this.condition.emitValidationEvent(
      plate,
      userId,
      qrCode,
      this.fulfilled,
      eventService,
    );
  }
}

export class TestingEndRentalReport {
  constructor(
    readonly reservation: Reservation,
    readonly endRentalSuccess: boolean,
    readonly qrConditions?: TestingEndRentalConditionResult[],
    readonly conditions?: TestingEndRentalConditionResult[],
    readonly totalPrice?: number,
    readonly durationInSeconds?: number,
  ) {}
}
