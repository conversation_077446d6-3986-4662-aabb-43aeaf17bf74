import { Type } from 'class-transformer';
import {
  VulogCityId,
  VulogFleetId,
  VulogServiceId,
} from 'src/common/tiny-types';
import { MapUtils } from 'src/common/utils/map.util';
import {
  VulogFleetServiceStatus,
  VulogFleetServiceType,
  VulogFleetServiceVisibility,
} from './dtos/vulog-fleet-service/vulog-fleet-service.dto';

export class FleetService {
  constructor(props?: Omit<FleetService, 'getZoneIdsAsMap'>) {
    Object.assign(this, props);
  }

  @Type(() => VulogServiceId)
  id: VulogServiceId;

  name: string;

  status: VulogFleetServiceStatus;

  type: VulogFleetServiceType;

  @Type(() => VulogFleetId)
  fleetId: VulogFleetId;

  @Type(() => VulogCityId)
  cityId: VulogCityId;

  zoneIds: string[];

  vehicleIds: string[];

  // Applied for all vehicles under this fleet service
  maximumBookingTime: number;

  visibility: VulogFleetServiceVisibility;

  maxConcurrentTrips: number;

  getZoneIdsAsMap(): Map<string, string> {
    return MapUtils.build(this.zoneIds, (value) => value);
  }
}
