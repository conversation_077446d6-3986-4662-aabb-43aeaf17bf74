import { cloneDeep } from 'lodash';
import { StationId } from 'src/common/tiny-types';
import { Latitude } from 'src/common/tiny-types/latitude';
import { Longitude } from 'src/common/tiny-types/longitude';
import { StationStatus } from '../external/station/dtos/station-status';
import { GpsCoordinates } from './gps-coordinates';

export interface StationDescription {
  id: StationId;
  sourceId: string;
  zoneId?: string;
  displayName: string;
  latitude: number;
  longitude: number;
  street: string;
  postalCode: string;
  parkingSpacesAvailable?: number | null;
  totalParkingSpaces?: number | null;
  status: StationStatus;
}

export class Station implements StationDescription {
  id: StationId;

  sourceId: string;

  zoneId?: string;

  displayName: string;

  latitude: number;

  longitude: number;

  street: string;

  postalCode: string;

  parkingSpacesAvailable?: number | null;

  totalParkingSpaces?: number | null;

  status: StationStatus;

  constructor(props: StationDescription) {
    Object.assign(this, props);
  }

  public toObject(): StationDescription {
    const propsCopy = {
      ...cloneDeep(this),
      id: this.id.value,
    };

    return Object.freeze(propsCopy);
  }

  public extractGpsCoordinates(): GpsCoordinates {
    return new GpsCoordinates({
      longitude: new Longitude(this.longitude.toString()),
      latitude: new Latitude(this.latitude.toString()),
    });
  }
}
