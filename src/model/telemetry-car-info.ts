import { VulogZoneId } from 'src/common/tiny-types';
import { MapUtils } from 'src/common/utils/map.util';
import { VulogZone } from './dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { VvgCarSession } from './dtos/vvg-car/vvg-car-fields/vvg-car-session';
import {
  VulogVehicleCardsAndKey,
  VulogVehicleSimCard,
} from './dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { RealtimeCar } from './realtime.car';
import { Station } from './station';

export class TelemetryCarInfo extends RealtimeCar {
  constructor(
    props: Omit<
      TelemetryCarInfo,
      'toCar' | 'toVulogCarRealTimeDto' | 'isAllowedToAttachPackage' | 'disabled' | 'outOfServiceReason'
    >,
  ) {
    super({ ...props, disabled: null, outOfServiceReason: null });

    Object.assign(this, props);

    this.zoneMaps = this.zoneIds?.length
      ? MapUtils.build(this.zoneIds, (vgZone) => vgZone.value)
      : new Map<string, VulogZoneId>();
  }

  mileage: number;

  cardsAndKey: VulogVehicleCardsAndKey;

  simCard: VulogVehicleSimCard;

  engineOn: boolean;

  locked?: boolean;

  parkingBrakeOn?: boolean;

  immobilizerOn?: boolean;

  currentZones?: VulogZone[];

  activeSession?: VvgCarSession;

  currentStation?: Station;
}
