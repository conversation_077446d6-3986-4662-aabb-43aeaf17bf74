import { ParkingLotStatus } from 'src/external/station/dtos/response/internal.parking-lot.v1.response.dto';
import { Station } from './station';

export class ParkingLot {
  status: ParkingLotStatus;
  station: Station;
  isAvailable?: boolean;

  constructor(props?: Omit<ParkingLot, 'toEntity'>) {
    Object.assign(this, props);
  }
}

export class ParkingLotValidationStatus {
  private _isQRCodeValid = false;
  private _isAssociatedWithStation = false; // car is at the station
  private _hasAvailableSpace = false; // only for charge-less. Check if parking lots > 0

  public setIsQRCodeValid(value: boolean) {
    this._isQRCodeValid = value;
  }

  public isQRCodeValid() {
    return this._isQRCodeValid;
  }

  public setIsAssociatedWithStation(value: boolean) {
    this._isAssociatedWithStation = value;
  }

  public isAssociatedWithStation() {
    return this._isAssociatedWithStation;
  }

  public setHasAvailableSpace(value: boolean) {
    this._hasAvailableSpace = value;
  }

  public hasAvailableSpace() {
    return this._hasAvailableSpace;
  }
}
