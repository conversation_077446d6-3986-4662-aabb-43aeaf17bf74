import { Logger } from '@nestjs/common';
import { BigNumber } from 'bignumber.js';
import { PricingPolicyNoRentalRate } from 'src/common/errors/external/pricing-policy-no-rental-rate.error';
import { PricingPolicyNotFound } from 'src/common/errors/external/pricing-policy-not-found.error';
import {
  RentalPackageInvalidDuration,
  RentalPackageInvalidPrice,
} from 'src/common/errors/external/rental-package.error';
import { bigNumberize } from 'src/common/utils/big-decimal.util';
import { config } from 'src/config';
import { PricingPolicy } from './pricing-policy';
import { RentalPackage } from './rental-package';

export class RentalPricing {
  constructor(
    private readonly pricingPolicy: PricingPolicy,
    private readonly rentalPackage: RentalPackage,
  ) {}

  private getPricePerMinute(durationMinutes: BigNumber): number {
    const rate = bigNumberize(this.pricingPolicy.rentalRate);
    return rate.multipliedBy(durationMinutes).toNumber();
  }

  private convertToMinutes(durationInSeconds: number): BigNumber {
    return bigNumberize(
      Math.ceil(bigNumberize(durationInSeconds).dividedBy(60).toNumber()),
    );
  }

  private getPriceWithPackage(tripDurationMinutes: BigNumber): number {
    if (
      this.rentalPackage.duration === null ||
      this.rentalPackage.duration === undefined
    ) {
      throw new RentalPackageInvalidDuration();
    }
    if (
      this.rentalPackage.price === null ||
      this.rentalPackage.price === undefined
    ) {
      throw new RentalPackageInvalidPrice();
    }
    const unpaidDurationMinutes = BigNumber.max(
      tripDurationMinutes.minus(this.rentalPackage.duration),
      0,
    );
    return (
      this.rentalPackage.price + this.getPricePerMinute(unpaidDurationMinutes)
    );
  }

  getPrice(tripDurationSeconds: number): number {
    if (!this.pricingPolicy) {
      throw new PricingPolicyNotFound();
    }

    if (
      this.pricingPolicy.rentalRate === null ||
      this.pricingPolicy.rentalRate === undefined
    ) {
      throw new PricingPolicyNoRentalRate();
    }

    let durationMinutes = this.convertToMinutes(tripDurationSeconds);
    if (this.rentalPackage) {
      return this.getPriceWithPackage(durationMinutes);
    }
    Logger.log(
      'minimumRentalDuration',
      config.rentalPricingConfig.minimumRentalDuration,
    );

    if (
      durationMinutes.isLessThan(
        config.rentalPricingConfig.minimumRentalDuration,
      )
    ) {
      durationMinutes = bigNumberize(
        config.rentalPricingConfig.minimumRentalDuration,
      );
    }
    return this.getPricePerMinute(durationMinutes);
  }
}
