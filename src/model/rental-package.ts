import { RentalPackageId } from 'src/common/tiny-types';

export class RentalPackage {
  constructor(props?: Omit<RentalPackage, 'toJson'>) {
    Object.assign(this, props);
  }

  packageId: RentalPackageId;

  hrid?: string;

  name: string;

  price: number;

  duration: number;

  minAllowedBatteryPercentage: number;

  isDynamic?: boolean;

  toJson() {
    return {
      packageId: this.packageId.value,
      hrid: this.hrid,
      name: this.name,
      price: this.price,
      duration: this.duration,
      minAllowedBatteryPercentage: this.minAllowedBatteryPercentage,
      isDynamic: this.isDynamic,
    };
  }
}
