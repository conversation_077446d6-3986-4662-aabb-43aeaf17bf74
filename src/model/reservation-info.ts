import { GeoLocation } from '@bluesg-2/queue-pop/dist/common/domains/value-objects/geo-location';
import { isNotEmptyObject } from 'class-validator';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import {
  BsgUserId,
  CarPlateNumber,
  ReservationId,
  StationId,
  VulogCarId,
  VulogJourneyOrTripId,
} from 'src/common/tiny-types';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { PricingPolicy } from './pricing-policy';
import { RealtimeCar } from './realtime.car';
import { RentalPackage } from './rental-package';
import { Reservation } from './reservation.domain';

export class ReservationInfo {
  constructor(props: Partial<ReservationInfo>) {
    Object.assign(this, props);
  }

  carId?: VulogCarId;

  sourceCarId?: VulogCarId;

  sourceId?: VulogJourneyOrTripId;

  bsgUserId: BsgUserId;

  carModel: CarModelEnum;

  startStationId: StationId;

  endStationId?: StationId;

  status: ReservationStatus;

  rentalPackageData?: unknown;

  local: boolean;

  startedAt?: VulogDate;

  endedAt?: VulogDate;

  reservedAt?: VulogDate;

  expiresAt?: VulogDate;

  abuseReleasesAt?: VulogDate;

  location: GeoLocation;

  entityId?: ReservationId;

  plateNumber?: CarPlateNumber;

  canceledAt?: VulogDate;

  realtimeCar?: RealtimeCar;

  rating?: string;

  carCleanliness?: string;

  amount?: number;

  duration?: number;

  pricingPolicy?: PricingPolicy;

  static from(domain: Reservation): ReservationInfo {
    const domainProps = domain.getPropsCopy();
    return new ReservationInfo({
      carId: domainProps.carId ? domainProps.carId : null,
      sourceCarId: domainProps.carId ? domainProps.carId : null,
      sourceId: domainProps.vulogTripId ? domainProps.vulogTripId : null,
      bsgUserId: domainProps.bsgUserId,
      carModel: domainProps.carModel,
      startStationId: domainProps.startStationId,
      endStationId: domainProps.endStationId || null,
      status: domainProps.status,
      rentalPackageData: domainProps.rentalPackageData || null,
      local: domainProps.local,
      startedAt: domainProps.startedAt || null,
      endedAt: domainProps.endedAt || null,
      reservedAt: domainProps.reservedAt || null,
      expiresAt: domainProps.expiresAt || null,
      canceledAt: domainProps.canceledAt || null,
      abuseReleasesAt: domainProps.abuseReleasesAt || null,
      location: isNotEmptyObject(domainProps.location)
        ? new GeoLocation(domainProps.location)
        : null,
      entityId: domainProps?.id,
    });
  }

  toDomain(): Reservation {
    return new Reservation(
      {
        bsgUserId: this.bsgUserId,
        carId: this.carId,
        carModel: this.carModel,
        startStationId: this.startStationId,
        endStationId: this.endStationId,
        vulogTripId: this.sourceId,
        status: this.status,
        plateNumber: this.plateNumber,
        rentalPackageData:
          this.rentalPackageData != null
            ? new RentalPackage(this.rentalPackageData as RentalPackage)
            : null,
        local: this.local,
        startedAt: this.startedAt,
        endedAt: this.endedAt,
        reservedAt: this.reservedAt,
        expiresAt: this.expiresAt,
        canceledAt: this.canceledAt,
        abuseReleasesAt: this.abuseReleasesAt,
        location: this.location,
      },
      this.entityId,
    );
  }
}
