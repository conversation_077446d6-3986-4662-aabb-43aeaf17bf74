import { isDefined, isNotEmpty } from 'class-validator';
import { BaseDomain } from 'src/common/ddd/domain/class/base.domain';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { UUID } from 'src/common/tiny-types/uuid.type';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';

import { GeoLocation } from '@bluesg-2/queue-pop/dist/common/domains/value-objects/geo-location';
import { plainToInstance } from 'class-transformer';
import * as _ from 'lodash';
import { DateTime } from 'luxon';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { Latitude } from 'src/common/tiny-types/latitude';
import { Longitude } from 'src/common/tiny-types/longitude';
import { bigNumberize } from 'src/common/utils/big-decimal.util';
import { BillingUtils } from 'src/common/utils/billing.util';
import { Car } from 'src/logic/car/domain/car';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import {
  NewRelicMetricCarModel,
  NewRelicMetricRentalType,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import {
  BsgUserId,
  CarPlateNumber,
  RentalPackageId,
  ReservationId,
  StationId,
  VulogCarId,
  VulogJourneyOrTripId,
} from '../common/tiny-types';
import { GpsCoordinates } from './gps-coordinates';
import { PricingPolicy } from './pricing-policy';
import { RentalPackage } from './rental-package';
import { RentalPricing } from './rental-pricing.domain';
import { ReservationEntity } from './repositories/entities/reservation.entity';
import { VulogUser } from './vulog-user';

export interface ReservationDescription {
  bsgUserId: BsgUserId;
  carId?: VulogCarId;
  carModel?: CarModelEnum;
  startStationId: StationId;
  endStationId?: StationId;
  mobileEndGpsCoordinates?: GpsCoordinates;
  vulogEndGpsCoordinates?: GpsCoordinates;
  vulogTripId?: VulogJourneyOrTripId;
  status: ReservationStatus;
  plateNumber?: CarPlateNumber;
  rentalPackageData?: RentalPackage;
  local?: boolean;
  allocateAgain?: boolean;
  startedAt?: VulogDate;
  endedAt?: VulogDate;
  reservedAt?: VulogDate;
  expiresAt?: VulogDate;
  canceledAt?: VulogDate;
  abuseReleasesAt?: VulogDate;
  location?: GeoLocation;
  rating?: string;
  carCleanliness?: string;
  car?: Car;
  vulogUser?: VulogUser;

  amount?: number;
  /**
   * Duration in seconds
   */
  duration?: number;
  pricingPolicy?: PricingPolicy;
  isEndingRentalDataAdded?: boolean;

  userFullName?: string;
  userEmail?: string;

  // Quick fix
  createdAt?: VulogDate;
  modifiedAt?: VulogDate;
  deletedBy?: string;

  createdBy?: string;
  modifiedBy?: string;
}

export class Reservation extends BaseDomain<
  ReservationDescription,
  ReservationEntity
> {
  private rentalPricing: RentalPricing;

  protected equalWith(): boolean {
    throw new Error('Method not implemented.');
  }

  get reservationId(): ReservationId {
    return new ReservationId(this.getId);
  }

  public get carId(): VulogCarId {
    return this.props.carId;
  }

  public set carId(carId: VulogCarId) {
    this.props.carId = carId;
  }

  public get carModel(): CarModelEnum {
    return this.props.carModel;
  }

  public get bsgUserId(): BsgUserId {
    return this.props.bsgUserId;
  }

  public get startStationId(): StationId {
    return this.props.startStationId;
  }

  public get endStationId(): StationId {
    return this.props.endStationId;
  }

  public set endStationId(endStationId: StationId) {
    this.props.endStationId = endStationId;
  }

  public set rentalPackageData(rentalPackageData: RentalPackage) {
    this.props.rentalPackageData = rentalPackageData;
  }

  public get rentalPackageData(): RentalPackage {
    return this.props.rentalPackageData;
  }

  public get vulogTripId(): VulogJourneyOrTripId {
    return this.props.vulogTripId;
  }

  public get status(): ReservationStatus {
    return this.props.status;
  }

  public set status(status: ReservationStatus) {
    this.props.status = status;
  }

  public get startedAt(): VulogDate {
    return this.props.startedAt;
  }

  public set startedAt(startedAt: VulogDate) {
    this.props.startedAt = startedAt;
  }

  public get endedAt(): VulogDate {
    return this.props.endedAt;
  }

  public set endedAt(endedAt: VulogDate) {
    this.props.endedAt = endedAt;
  }

  public get reservedAt(): VulogDate {
    return this.props.reservedAt;
  }

  public get expiresAt(): VulogDate {
    return this.props.expiresAt;
  }

  public get canceledAt(): VulogDate {
    return this.props.canceledAt;
  }

  public set canceledAt(canceledAt: VulogDate) {
    this.props.canceledAt = canceledAt;
  }

  public get abuseReleasesAt(): VulogDate {
    return this.props.abuseReleasesAt;
  }

  public get pricingPolicy(): PricingPolicy {
    return this.props.pricingPolicy;
  }

  public get car(): Car {
    return this.props.car;
  }

  public get vulogUser(): VulogUser {
    return this.props.vulogUser;
  }

  public get isEndingRentalDataAdded(): boolean {
    return this.props.isEndingRentalDataAdded;
  }

  public set isEndingRentalDataAdded(isEndingRentalDataAdded: boolean) {
    this.props.isEndingRentalDataAdded = isEndingRentalDataAdded;
  }

  public get mobileEndGpsCoordinates(): GpsCoordinates {
    return this.props.mobileEndGpsCoordinates;
  }

  public set mobileEndGpsCoordinates(v: GpsCoordinates) {
    this.props.mobileEndGpsCoordinates = v;
  }

  public get vulogEndGpsCoordinates(): GpsCoordinates {
    return this.props.vulogEndGpsCoordinates;
  }

  public set vulogEndGpsCoordinates(v: GpsCoordinates) {
    this.props.vulogEndGpsCoordinates = v;
  }

  public set updateDeletedBy(v: string) {
    this.props.deletedBy = v;
  }

  public setAbuseReleasesAt(gracePeriodInMinutes: number) {
    if (this.props.canceledAt) {
      this.props.abuseReleasesAt = new VulogDate(
        this.props.canceledAt
          .toDateTime()
          .plus({ minutes: gracePeriodInMinutes })
          .toISO(),
      );
    } else if (this.isExpired) {
      this.props.abuseReleasesAt = new VulogDate(
        this.props.expiresAt
          .toDateTime()
          .plus({ minutes: gracePeriodInMinutes })
          .toISO(),
      );
    }
  }

  public isInGracePeriod(): boolean {
    return (
      this.props.abuseReleasesAt &&
      this.props.abuseReleasesAt.toDateTime().diffNow().toMillis() > 0
    );
  }

  public get gracePeriodDuration(): string {
    let durationInMinutes;

    if (this.isInGracePeriod()) {
      if (this.props.canceledAt) {
        durationInMinutes = this.abuseReleasesAt
          .toDateTime()
          .diff(this.props.canceledAt.toDateTime(), ['minutes']).minutes;
      } else if (this.isExpired) {
        durationInMinutes = this.abuseReleasesAt
          .toDateTime()
          .diff(this.props.expiresAt.toDateTime(), ['minutes']).minutes;
      }

      return `${durationInMinutes} minutes`;
    } else {
      return null;
    }
  }

  public get local(): boolean {
    return this.props.local;
  }

  public set local(local: boolean) {
    this.props.local = local;
  }

  public get allocateAgain(): boolean {
    return this.props.allocateAgain;
  }

  public set allocateAgain(value: boolean) {
    this.props.allocateAgain = value;
  }

  public get plateNumber(): CarPlateNumber {
    return this.props.plateNumber;
  }

  public set plateNumber(plateNumber: CarPlateNumber) {
    this.props.plateNumber = plateNumber;
  }

  public set setRating(val: string) {
    this.props.rating = val;
  }

  public get rating(): string {
    return this.props.rating;
  }

  public set setCarCleanliness(val: string) {
    this.props.carCleanliness = val;
  }

  public get carCleanliness(): string {
    return this.props.carCleanliness;
  }

  public get amount(): number {
    return this.props.amount;
  }

  public set amount(amount: number) {
    this.props.amount = amount;
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  public validate(): void {}

  /**
   * Is the reservation normal rental (with pricing policy applied) or rental with package (with rental package applied)
   */
  isNormalRental(): boolean {
    return !isDefined(this.props.rentalPackageData);
  }

  public get isCanceled(): boolean {
    return this.props.status === ReservationStatus.Cancelled;
  }

  public get isExpired(): boolean {
    return (
      this.props.status === ReservationStatus.Expired ||
      this.props.expiresAt.toDateTime().diffNow().toMillis() <= 0
    );
  }

  public get userFullName(): string {
    return this.props.userFullName;
  }

  public get userEmail(): string {
    return this.props.userEmail;
  }

  /**
   * Get current duration in seconds
   * @returns number
   */
  getCurrentDuration(): number {
    // Rental not started yet
    if (!this.props.startedAt) {
      return null;
    }

    return (this.endedAt ? this.endedAt.toDateTime() : DateTime.now()).diff(
      this.startedAt.toDateTime(),
      ['seconds'],
    ).seconds;
  }

  /**
   * Get billed duration in seconds
   *
   * This function should be used when:
   * - saving final duration in database
   * - calculating money activity
   */
  getBilledDuration(): number {
    const durationInSeconds = isNotEmpty(this.props.duration)
      ? this.props.duration // Retrieved from DB or after setter
      : this.getCurrentDuration();

    return isNotEmpty(durationInSeconds)
      ? BillingUtils.roundToMinutes(durationInSeconds) * 60
      : null;
  }

  getRentalPrice(): number {
    return this.rentalPricing.getPrice(this.getBilledDuration());
  }

  /**
   * Set billed duration for billable purpose
   * @param duration
   */
  setBilledDuration() {
    this.props.duration = this.getBilledDuration();
  }

  public get isReserved(): boolean {
    return this.props.status === ReservationStatus.Reserved;
  }

  getRentalPackageData(): RentalPackage {
    if (!this.props.rentalPackageData) return null;

    const rentalPackageData = this.props.rentalPackageData as RentalPackage & {
      packageId: string;
    };

    return new RentalPackage({
      packageId: new RentalPackageId(rentalPackageData.packageId),
      duration: rentalPackageData.duration,
      name: rentalPackageData.name,
      price: rentalPackageData.price,
      hrid: rentalPackageData.hrid,
      minAllowedBatteryPercentage:
        rentalPackageData.minAllowedBatteryPercentage || 0,
      isDynamic: !!rentalPackageData.isDynamic,
    });
  }

  constructor(payload: ReservationDescription, id: ReservationId) {
    super({
      props: payload,
      id,
      createdBy: payload.createdBy,
      modifiedBy: payload.modifiedBy,
      createdAt: payload.createdAt?.toDateTime(),
      modifiedAt: payload.modifiedAt?.toDateTime(),
    });

    this.rentalPricing = new RentalPricing(
      this.pricingPolicy,
      this.rentalPackageData,
    );
  }

  isOnGoingRental(): boolean {
    return (
      (this.status === ReservationStatus.Converted && !this.endedAt) ||
      (this.status === ReservationStatus.EndedWaitingForInvoicing &&
        !this.amount)
    );
  }

  isRentalEnded(): boolean {
    return [
      ReservationStatus.EndTrip,
      ReservationStatus.EndedWaitingForInvoicing,
      ReservationStatus.Ended,
    ].includes(this.status);
  }

  toLogs(): Record<string, unknown> {
    return {
      id: this.getId,
      tripId: this.vulogTripId?.value,
      car: {
        id: this.carId,
        model: this.carModel,
      },
      station: this.startStationId?.value,
    };
  }

  attachPricingPolicy(pricingPolicy: PricingPolicy): void {
    this.props.pricingPolicy = pricingPolicy;
  }

  getMetricNewRelicCarModel(): NewRelicMetricCarModel {
    switch (this.carModel) {
      case CarModelEnum.BlueCar:
        return NewRelicMetricCarModel.Bluecar;

      case CarModelEnum.OpelCorsaE:
        return NewRelicMetricCarModel.CorsaE;
    }
  }

  getMetricNewRelicRentalType(): NewRelicMetricRentalType {
    if (this.isNormalRental()) {
      return NewRelicMetricRentalType.Subscription;
    } else {
      return NewRelicMetricRentalType.RentalPackage;
    }
  }

  isRated(): boolean {
    return !!(this.props.rating && this.props.carCleanliness);
  }

  static from(val: ReservationEntity) {
    const {
      id,
      bsgUserId,
      vulogTripId,
      startStationId,
      endStationId,
      mobileEndGpsCoordinates,
      vulogEndGpsCoordinates,
      status,
      abuseReleasesAt,
      allocateAgain,
      amount,
      canceledAt,
      carCleanliness,
      carId,
      carModel,
      duration,
      endedAt,
      expiresAt,
      isEndingRentalDataAdded,
      local,
      plateNumber,
      pricingPolicy,
      rating,
      rentalPackageData,
      reservedAt,
      startedAt,
      userFullName,
      userEmail,
      createdAt,
      modifiedAt,
      deletedBy,
      createdBy,
      modifiedBy,
    } = val;

    return new Reservation(
      {
        status,
        allocateAgain,
        carModel,
        isEndingRentalDataAdded,
        local,
        rentalPackageData:
          rentalPackageData != null
            ? new RentalPackage(rentalPackageData as RentalPackage)
            : null,
        plateNumber: plateNumber ? new CarPlateNumber(plateNumber) : null,
        carId: carId ? new VulogCarId(carId) : null,
        bsgUserId: new BsgUserId(bsgUserId),
        vulogTripId: vulogTripId ? new VulogJourneyOrTripId(vulogTripId) : null,
        startStationId: startStationId ? new StationId(startStationId) : null,
        endStationId: endStationId ? new StationId(endStationId) : null,
        mobileEndGpsCoordinates: mobileEndGpsCoordinates
          ? new GpsCoordinates({
              longitude: new Longitude(
                mobileEndGpsCoordinates.coordinates[0].toString(),
              ),
              latitude: new Latitude(
                mobileEndGpsCoordinates.coordinates[1].toString(),
              ),
            })
          : null,
        vulogEndGpsCoordinates: vulogEndGpsCoordinates
          ? new GpsCoordinates({
              longitude: new Longitude(
                vulogEndGpsCoordinates.coordinates[0].toString(),
              ),
              latitude: new Latitude(
                vulogEndGpsCoordinates.coordinates[1].toString(),
              ),
            })
          : null,
        startedAt: startedAt ? VulogDate.fromJsDate(startedAt) : null,
        endedAt: endedAt ? VulogDate.fromJsDate(endedAt) : null,
        reservedAt: reservedAt ? VulogDate.fromJsDate(reservedAt) : null,
        expiresAt: expiresAt ? VulogDate.fromJsDate(expiresAt) : null,
        canceledAt: canceledAt ? VulogDate.fromJsDate(canceledAt) : null,
        abuseReleasesAt: abuseReleasesAt
          ? VulogDate.fromJsDate(abuseReleasesAt)
          : null,
        rating,
        carCleanliness,
        amount: amount ? bigNumberize(amount).toNumber() : null,
        duration: isNotEmpty(duration)
          ? bigNumberize(duration).toNumber()
          : null,
        pricingPolicy: pricingPolicy
          ? plainToInstance(PricingPolicy, pricingPolicy)
          : null,
        car: val.car ? val.car.toCar() : null,
        vulogUser: val.vulogUser ? val.vulogUser.toVulogUser() : null,
        userFullName,
        userEmail,
        createdAt: createdAt ? VulogDate.fromJsDate(createdAt) : null,
        modifiedAt: modifiedAt ? VulogDate.fromJsDate(modifiedAt) : null,
        deletedBy,
        createdBy,
        modifiedBy,
      },
      new UUID(id),
    );
  }

  static toPartialEntity(
    props: Partial<ReservationDescription>,
  ): QueryDeepPartialEntity<ReservationEntity> {
    // `undefined` is neutral value that used popular in JavaScript packages to ignore values to be processed
    const partialEntityProps: Partial<ReservationEntity> = {
      bsgUserId: props.bsgUserId?.value,
      carId: props.carId?.value,
      plateNumber: props?.plateNumber?.value,
      createdAt: props?.createdAt?.value
        ? new Date(props.createdAt.value)
        : undefined,
      modifiedAt: props?.modifiedAt?.value
        ? new Date(props.modifiedAt.value)
        : undefined,
      carModel: props.carModel,
      startStationId: props.startStationId?.value,
      endStationId: props.endStationId?.value,
      mobileEndGpsCoordinates: props.mobileEndGpsCoordinates
        ? props.mobileEndGpsCoordinates.toPointGeometry()
        : undefined,
      vulogEndGpsCoordinates: props.vulogEndGpsCoordinates
        ? props.vulogEndGpsCoordinates.toPointGeometry()
        : undefined,
      rentalPackageData: props.rentalPackageData,
      status: props.status,
      vulogTripId: props.vulogTripId?.value,
      local: props.local,
      allocateAgain: props.allocateAgain,
      startedAt: props?.startedAt?.value
        ? new Date(props.startedAt.value)
        : undefined,
      endedAt: props?.endedAt?.value
        ? new Date(props.endedAt.value)
        : undefined,
      reservedAt: props?.reservedAt?.value
        ? new Date(props.reservedAt.value)
        : undefined,
      expiresAt: props?.expiresAt?.value
        ? new Date(props.expiresAt.value)
        : undefined,
      canceledAt: props?.canceledAt?.value
        ? new Date(props.canceledAt.value)
        : undefined,
      abuseReleasesAt: props?.abuseReleasesAt?.value
        ? new Date(props.abuseReleasesAt.value)
        : undefined,
      carCleanliness: props.carCleanliness,
      rating: props.rating,
      amount: props?.amount,
      duration: props?.duration,
      pricingPolicy: props?.pricingPolicy,
      isEndingRentalDataAdded: props?.isEndingRentalDataAdded,
      userFullName: props.userFullName,
      userEmail: props.userEmail,
      deletedBy: props?.deletedBy,
      createdBy: props?.createdBy,
      modifiedBy: props?.modifiedBy,
    };

    return _.omitBy(partialEntityProps, _.isUndefined);
  }

  public toEntity(): ReservationEntity {
    return new ReservationEntity({
      bsgUserId: this.props.bsgUserId.value,
      carId: this.props.carId?.value,
      id: this.getId,
      plateNumber: this.props?.plateNumber?.value,
      createdAt: this.props?.createdAt?.value
        ? new Date(this.props.createdAt.value)
        : undefined,
      modifiedAt: this.props?.modifiedAt?.value
        ? new Date(this.props.modifiedAt.value)
        : undefined,
      deletedAt: this.getDeletedAt?.toJSDate(),
      carModel: this.props.carModel,
      startStationId: this.props.startStationId?.value,
      endStationId: this.props.endStationId?.value,
      mobileEndGpsCoordinates: this.props.mobileEndGpsCoordinates
        ? this.props.mobileEndGpsCoordinates.toPointGeometry()
        : null,
      vulogEndGpsCoordinates: this.props.vulogEndGpsCoordinates
        ? this.props.vulogEndGpsCoordinates.toPointGeometry()
        : null,
      rentalPackageData: this.props.rentalPackageData,
      status: this.props.status,
      vulogTripId: this.props.vulogTripId?.value,
      createdBy: this.props.createdBy,
      modifiedBy: this.props.modifiedBy,
      local: this.props.local,
      allocateAgain: this.props.allocateAgain,
      startedAt: this.props?.startedAt?.value
        ? new Date(this.props.startedAt.value)
        : null,
      endedAt: this.props?.endedAt?.value
        ? new Date(this.props.endedAt.value)
        : null,
      reservedAt: this.props?.reservedAt?.value
        ? new Date(this.props.reservedAt.value)
        : null,
      expiresAt: this.props?.expiresAt?.value
        ? new Date(this.props.expiresAt.value)
        : null,
      canceledAt: this.props?.canceledAt?.value
        ? new Date(this.props.canceledAt.value)
        : null,
      abuseReleasesAt: this.props?.abuseReleasesAt?.value
        ? new Date(this.props.abuseReleasesAt.value)
        : null,
      carCleanliness: this.props.carCleanliness,
      rating: this.props.rating,
      amount: this.props?.amount,
      duration: this.isRentalEnded() ? this.getBilledDuration() : null,
      pricingPolicy: this.props?.pricingPolicy,
      isEndingRentalDataAdded: this.props?.isEndingRentalDataAdded,
      userFullName: this.props?.userFullName,
      userEmail: this.props?.userEmail,
      deletedBy: this.props?.deletedBy,
    });
  }
}
