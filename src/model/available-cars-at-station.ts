import { plainToInstance } from 'class-transformer';
import { CarModel, CarPlateNumber, VulogCarId } from 'src/common/tiny-types';
import { RealtimeCar } from './realtime.car';
import { Station } from './station';

export class AvailableCarsAtStation {
  constructor(props: AvailableCarsAtStation) {
    Object.assign(this, props);
  }

  station: Station;

  availableCars: RealtimeCar[];

  static toInstance(object) {
    if (object instanceof AvailableCarsAtStation) {
      return object;
    }

    const station = plainToInstance(Station, object.station);

    let availableCars = object.availableCars.map((car) =>
      plainToInstance(RealtimeCar, car),
    );

    // Workaround for class-transformer problem
    availableCars = availableCars.map((car) => {
      return new RealtimeCar({
        ...car,
        id: new VulogCarId(car.id),
        model: new CarModel(car.model),
        plate: new CarPlateNumber(car.plate),
        sourceId: new VulogCarId(car.id),
      });
    });

    return new AvailableCarsAtStation({ station, availableCars });
  }
}
