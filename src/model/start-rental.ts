import { ReservationId, StationId, VulogCarId } from 'src/common/tiny-types';
import { CarsCounter } from 'src/logic/car-availability';
import { Car } from '../logic/car/domain/car';
import { CarModel } from './car-model';
import { Reservation } from './reservation.domain';
import { Station } from './station';
import { StationAvailability } from './station-availability';

export class StartRental {
  // id: VulogJourneyOrTripId;

  // Return a reservationId stored in our db
  id: ReservationId;

  startStationId: StationId;

  carModel: CarModel;

  carId: VulogCarId;

  stationAvailability: StationAvailability;

  local: boolean;

  constructor(props: StartRental) {
    Object.assign(this, props);
  }

  static from(
    reservation: Reservation,
    station: Station,
    car: Car,
    carsCounter: CarsCounter,
  ) {
    return new StartRental({
      id: reservation.reservationId,
      startStationId: reservation.startStationId,
      carModel: new CarModel(car.model),
      carId: car.vulogId,
      stationAvailability: StationAvailability.from(station, carsCounter),
      local: reservation.local,
    });
  }
}
