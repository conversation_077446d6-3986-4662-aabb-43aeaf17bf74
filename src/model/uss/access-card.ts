import { BsgUserId } from '../../common/tiny-types';
import { AccessCardId } from './access-card-id';
import { AccessCardPayload } from './access-card-payload';

export class AccessCard {
  constructor(
    readonly id: AccessCardId,
    readonly payload: AccessCardPayload,
    readonly label: string,
    readonly usage: string,
    readonly ownerId: BsgUserId,
    readonly mayUseRental: boolean,
    readonly createdAt: Date,
    readonly modifiedAt: Date,
  ) {}
}
