import { StationId } from 'src/common/tiny-types';
import { CarsCounter } from 'src/logic/car-availability';
import { CarModel } from './car-model';
import { Station } from './station';

function carsCounterToMap(carsCounter: CarsCounter): Map<CarModel, number> {
  const { carAvailability } = carsCounter;

  const map = new Map<CarModel, number>();

  Object.keys(carAvailability).forEach((carModel) => {
    map.set(new CarModel(carModel), carAvailability[carModel]);
  });

  return map;
}

export class StationAvailability {
  stationId: StationId;

  carsCounter: Map<CarModel, number>;

  slots: number; // TODO: Should get directly from OCPI. Right now, leave it as `null` until OCPI integration is done

  pricingInformation: {
    carReservation: number; // TODO: Will have value when yield management is ready

    parkReservation: number; // TODO: Will have value when yield management is ready

    rentalStart: number; // TODO: Will have value when yield management is ready

    rentalEnd: number; // TODO: Will have value when yield management is ready
  };

  constructor(props: StationAvailability) {
    Object.assign(this, props);
  }

  static from(station: Station, carsCounter: CarsCounter) {
    return new StationAvailability({
      stationId: station.id,
      carsCounter: carsCounterToMap(carsCounter),
      slots: null,
      pricingInformation: null,
    });
  }
}
