import { ClassConstructor, plainToInstance, Type } from 'class-transformer';
import { ConfigurationError } from 'src/common/errors/configuration.error';
import { ConfigurationId } from 'src/common/tiny-types';
import { ConfigurationUtils } from 'src/common/utils/configuration.util';
import {
  ConfigurationKey,
  ConfigurationType,
} from '../shared/configuration/constants/configuration';
import { ConfigurationEntity } from '../shared/configuration/entities/configuration.entity';

export class Configuration<T = unknown> {
  constructor(props?: Omit<Configuration<T>, 'toEntity' | 'valueAsInstance'>) {
    Object.assign(this, props);
  }

  @Type(() => ConfigurationId)
  id?: ConfigurationId;

  key: ConfigurationKey;

  type: ConfigurationType;

  value: T;

  description?: string;

  note?: string;

  public async valueAsInstance<T>(
    expectedClass: ClassConstructor<T>,
  ): Promise<T> {
    if (this.type !== ConfigurationType.JSON) {
      throw new ConfigurationError(
        `The value is not "${ConfigurationType.JSON}". Could not transform to your expected class`,
      );
    }

    return plainToInstance(expectedClass, this.value);
  }

  static fromEntity<T>(entity: ConfigurationEntity): Configuration {
    const { key, type, value, description, note, id } = entity;

    return new Configuration({
      id: id ? new ConfigurationId(id) : null,
      key,
      type,
      value: ConfigurationUtils.transformValue(type, value) as T,
      description,
      note,
    });
  }

  toEntity(): ConfigurationEntity {
    return new ConfigurationEntity({
      id: this.id?.value,
      key: this.key,
      type: this.type,
      value: JSON.stringify(this.value),
      description: this.description,
      note: this.note,
    });
  }
}
