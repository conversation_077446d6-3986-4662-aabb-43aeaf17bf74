import { Type } from 'class-transformer';
import {
  CarId,
  CarModel,
  CarPlateNumber,
  VulogCarId,
  VulogServiceId,
} from 'src/common/tiny-types';
import { CarModelEnum } from '../common/enum/car-model.enum';
import { Car } from '../logic/car/domain/car';
import { VulogFleetServiceType } from './dtos/vulog-fleet-service/vulog-fleet-service.dto';

export class StaticCar {
  @Type(() => VulogCarId)
  id: VulogCarId;

  @Type(() => CarModel)
  model: CarModel;

  @Type(() => CarPlateNumber)
  plate: CarPlateNumber;

  vin: string;

  @Type(() => VulogCarId)
  sourceId: VulogCarId;

  @Type(() => VulogServiceId)
  serviceId: VulogServiceId;

  serviceType?: VulogFleetServiceType;

  archived: boolean;

  constructor(props: Omit<StaticCar, 'toCar'>) {
    Object.assign(this, props);
  }

  toCar(
    id?: CarId,
    realtimeMetadata?: {
      isPlugged?: boolean;
    },
  ): Car {
    return new Car({
      id: id ? id.value : null,
      vulogId: this.id.value,
      model: this.model.value as CarModelEnum,
      plate: this.plate.value,
      vin: this.vin,
      realtimeMetadata: realtimeMetadata ? realtimeMetadata : null,
      isActive: !this.archived,
    });
  }
}
