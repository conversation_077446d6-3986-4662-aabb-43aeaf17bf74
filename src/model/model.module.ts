import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ReverseRfidCardRemovalBatchReportRepository } from 'src/batch/reverse-rfid-card-removal/reserve-rfid-card-removal-batch-report.repository';
import { ReverseRfidCardRemovalBatchReportEntity } from 'src/batch/reverse-rfid-card-removal/reverse-rfid-card-removal-batch-report.entity';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { CommentEntity } from 'src/logic/comment/entity/comment.entity';
import { CarRepository } from './repositories/car.repository';
import { ReservationEntity } from './repositories/entities/reservation.entity';
import { VulogEventEntity } from './repositories/entities/vulog-event.entity';
import { VulogUserEntity } from './repositories/entities/vulog-user.entity';
import { ReservationRepository } from './repositories/reservation.repository';
import { VulogEventRepository } from './repositories/vulog-event.repository';
import { VulogUserRepository } from './repositories/vulog-user.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      VulogUserEntity,
      ReservationEntity,
      VulogEventEntity,
      CarEntity,
      CommentEntity,
      ReverseRfidCardRemovalBatchReportEntity,
    ]),
  ],
  providers: [
    VulogUserRepository,
    ReservationRepository,
    VulogEventRepository,
    CarRepository,
    ReverseRfidCardRemovalBatchReportRepository,
  ],
  exports: [
    VulogUserRepository,
    ReservationRepository,
    VulogEventRepository,
    CarRepository,
    ReverseRfidCardRemovalBatchReportRepository,
  ],
})
export class ModelModule {}
