import { Type } from 'class-transformer';
import { VulogCarId, VulogJourneyOrTripId } from 'src/common/tiny-types';

import { GeoLocation } from '@bluesg-2/queue-pop/dist/common/domains/value-objects/geo-location';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { RentalInfo } from '../../rental-info';
import { ReservationInfo } from '../../reservation-info';
import { VulogPosition } from '../vulog-position';
import { VulogBleKey } from './vulog-journey-fields/vulog-ble-key';
import { VulogBooking } from './vulog-journey-fields/vulog-booking';
import { VulogTripField } from './vulog-journey-fields/vulog-trip';

export enum VulogRelActions {
  BookingCreate = 'bookingCreate',
  BookingDelete = 'bookingDelete',
  TripCreate = 'tripCreate',
  TripDelete = 'tripDelete',
  TripPause = 'tripPause',
  LockDoor = 'lockDoor',
  UnlockDoor = 'unlockDoor',
  TripResume = 'tripResume',
}

// why oh why Vulog does your actual data differ from the documentation :")
export class VulogJourneyWrapperDto {
  @Type(() => VulogJourneyDto)
  public journeys: VulogJourneyDto[];
}

export class VulogJourneyDto {
  public id: string; // this is the bookingId = orderId = tripId
  public energyLevel: string;
  public energyLevel2?: string;
  public inCharge?: boolean;
  public vehicleId: string;

  @Type(() => VulogBleKey)
  public key?: VulogBleKey;

  @Type(() => VulogPosition)
  public position: VulogPosition;

  @Type(() => VulogBooking)
  public booking?: VulogBooking;

  @Type(() => VulogTripField)
  public trip?: VulogTripField;

  public isVehicleInRange: boolean;

  public rel: VulogRelActions[];
  public serviceId: string;
  public pricingId?: string;
  public profileId?: string;
  public preAuthEnabled: boolean;
  public originCancelJourney?: string;
  public vehicleInRange: boolean;

  toReservationInfo() {
    return new ReservationInfo({
      carId: new VulogCarId(String(this.vehicleId)),
      sourceCarId: new VulogCarId(String(this.vehicleId)),
      sourceId: new VulogJourneyOrTripId(this.id),
      location: new GeoLocation({
        longitude: this.position.lon.toString(),
        latitude: this.position.lat.toString(),
      }),
      reservedAt: new VulogDate(this.booking.bookingDate),
      expiresAt: new VulogDate(this.booking.maxTripStartDate),
      // This is id from our db, it will be mapped later
      entityId: null,
    });
  }

  toRentalInfo() {
    return new RentalInfo({
      carId: new VulogCarId(String(this.vehicleId)),
      sourceCarId: new VulogCarId(String(this.vehicleId)),
      sourceId: new VulogJourneyOrTripId(this.id),
      entityId: null,
    });
  }

  toRawJsonObject() {
    return {
      id: this.id,
      energyLevel: this.energyLevel,
      energyLevel2: this.energyLevel2,
      inCharge: this.inCharge,
      vehicleId: this.vehicleId,
      key: this.key,
      position: this.position,
      booking: this.booking,
      trip: this.trip,
      isVehicleInRange: this.isVehicleInRange,
      rel: this.rel,
      serviceId: this.serviceId,
      pricingId: this.pricingId,
      profileId: this.profileId,
      preAuthEnabled: this.preAuthEnabled,
      originCancelJourney: this.originCancelJourney,
      vehicleInRange: this.vehicleInRange,
    };
  }
}
