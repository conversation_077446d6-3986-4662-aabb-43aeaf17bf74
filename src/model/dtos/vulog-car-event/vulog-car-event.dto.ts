import { DateTime } from 'luxon';
import { VulogCarId } from 'src/common/tiny-types';
import { CarEvent } from 'src/model/car-event';

export enum VulogCarEventType {
  VEHICLE_ADDED_TO_STATION = 'VEHICLE_ADDED_TO_STATION',
  VEHICLE_REMOVED_FROM_STATION = 'VEHICLE_REMOVED_FROM_STATION',
  VEHICLE_ALLOCATED = 'VEHICLE_ALLOCATED',
  VEHICLE_TOWED = 'VEHICLE_TOWED',
  VEHICLE_INTEGRATE_DEL = 'VEHICLE_INTEGRATE_DEL',
  VEHICLE_ARCHIVED = 'VEHICLE_ARCHIVED',
  VEHICLE_DELETE = 'VEHICLE_DELETE',
  VEHICLE_UPDATED = 'VEHICLE_UPDATED',
  VEHICLE_UPDATED_VUBOX = 'VEHICLE_UPDATED_VUBOX',
  VEHICLE_REMOVE_VUBOX = 'VEHICLE_REMOVE_VUBOX',
  VEHICLE_ASSIGN_VUBOX = 'VEHICLE_ASSIGN_VUBOX',
  VEHICLE_OUT_OF_SERVICE = 'VEHICLE_OUT_OF_SERVICE',
  VEHICLE_SCHEDULED_OUT_OF_SERVICE = 'VEHICLE_SCHEDULED_OUT_OF_SERVICE',
  VEHICLE_SCHEDULED_OUT_OF_SERVICE_CANCELED = 'VEHICLE_SCHEDULED_OUT_OF_SERVICE_CANCELED',
  VEHICLE_UNLOCK = 'VEHICLE_UNLOCK',
  VEHICLE_LOCK = 'VEHICLE_LOCK',
  VEHICLE_BATTERY_LOCK = 'VEHICLE_BATTERY_LOCK',
  VEHICLE_BATTERY_UNLOCK = 'VEHICLE_BATTERY_UNLOCK',
  VEHICLE_HELMET_LOCK = 'VEHICLE_HELMET_LOCK',
  VEHICLE_HELMET_UNLOCK = 'VEHICLE_HELMET_UNLOCK',
  VEHICLE_SPARE_LOCK = 'VEHICLE_SPARE_LOCK',
  VEHICLE_SPARE_UNLOCK = 'VEHICLE_SPARE_UNLOCK',
  VEHICLE_PILE_LOCK = 'VEHICLE_PILE_LOCK',
  VEHICLE_PILE_UNLOCK = 'VEHICLE_PILE_UNLOCK',
  VEHICLE_SPEED_LIMIT_CHANGED = 'VEHICLE_SPEED_LIMIT_CHANGED',
  VEHICLE_WAKE_UP = 'VEHICLE_WAKE_UP',
  VEHICLE_WAKE_UP_ACCEL = 'VEHICLE_WAKE_UP_ACCEL',
  VEHICLE_WAKE_UP_BLE = 'VEHICLE_WAKE_UP_BLE',
  VEHICLE_CONNECTIVITY_ONLINE = 'VEHICLE_CONNECTIVITY_ONLINE',
  VEHICLE_CONNECTIVITY_OFFLINE = 'VEHICLE_CONNECTIVITY_OFFLINE',
  VEHICLE_CONNECTIVITY_INTERRUPTED = 'VEHICLE_CONNECTIVITY_INTERRUPTED',
  VEHICLE_OUT_OF_COM = 'VEHICLE_OUT_OF_COM',
  VEHICLE_BOOKING_STATUS_CHANGED = 'VEHICLE_BOOKING_STATUS_CHANGED',
  VEHICLE_GET_STATUS = 'VEHICLE_GET_STATUS',
  VEHICLE_SCHEDULED_REBOOT = 'VEHICLE_SCHEDULED_REBOOT',
  VEHICLE_WARNING = 'VEHICLE_WARNING',
  VEHICLE_CRUISING_RANGE_LOW = 'VEHICLE_CRUISING_RANGE_LOW',
  VEHICLE_CRUISING_RANGE_CRITICAL = 'VEHICLE_CRUISING_RANGE_CRITICAL',
  VEHICLE_ACCEL_ALERT = 'VEHICLE_ACCEL_ALERT',
  VEHICLE_SPEED_ALERT = 'VEHICLE_SPEED_ALERT',
  VEHICLE_CKH_UPDATE = 'VEHICLE_CKH_UPDATE',
  VEHICLE_RECOVERY = 'VEHICLE_RECOVERY',
  VEHICLE_SYNC = 'VEHICLE_SYNC',
  VEHICLE_STANDBY = 'VEHICLE_STANDBY',
  VEHICLE_AUTOCLOSE_ON = 'VEHICLE_AUTOCLOSE_ON',
  VEHICLE_AUTOCLOSE_OFF = 'VEHICLE_AUTOCLOSE_OFF',
  VEHICLE_IGNITION_ON = 'VEHICLE_IGNITION_ON',
  VEHICLE_IGNITION_OFF = 'VEHICLE_IGNITION_OFF',
  VEHICLE_REBOOT = 'VEHICLE_REBOOT',
  VEHICLE_MOBILIZE = 'VEHICLE_MOBILIZE',
  VEHICLE_IMMOBILIZE = 'VEHICLE_IMMOBILIZE',
  VEHICLE_IMMOBILIZER_OOT = 'VEHICLE_IMMOBILIZER_OOT',
  VEHICLE_ENABLE = 'VEHICLE_ENABLE',
  VEHICLE_MAGIC_CARD = 'VEHICLE_MAGIC_CARD',
  VEHICLE_CLEANLINESS_RESET = 'VEHICLE_CLEANLINESS_RESET',
  VEHICLE_ADD_ZONE = 'VEHICLE_ADD_ZONE',
  VEHICLE_REMOVE_ZONE = 'VEHICLE_REMOVE_ZONE',
  VEHICLE_RELEASE_TRIP = 'VEHICLE_RELEASE_TRIP',
  VEHICLE_START_TRIP = 'VEHICLE_START_TRIP',
  VEHICLE_END_TRIP = 'VEHICLE_END_TRIP',
  VEHICLE_EDIT_TRIP = 'VEHICLE_EDIT_TRIP',
  VEHICLE_MANUAL_END_TRIP = 'VEHICLE_MANUAL_END_TRIP',
  VEHICLE_TRACKING_ON = 'VEHICLE_TRACKING_ON',
  VEHICLE_TRACKING_OFF = 'VEHICLE_TRACKING_OFF',
  VEHICLE_TRIP_TERMINATION = 'VEHICLE_TRIP_TERMINATION',
  VEHICLE_PAUSE_TRIP = 'VEHICLE_PAUSE_TRIP',
  VEHICLE_FORCED_PAUSE_TRIP = 'VEHICLE_FORCED_PAUSE_TRIP',
  VEHICLE_RESUME_TRIP = 'VEHICLE_RESUME_TRIP',
  VEHICLE_EXPERT = 'VEHICLE_EXPERT',
  VEHICLE_PASSTHROUGH = 'VEHICLE_PASSTHROUGH',
  VEHICLE_PING = 'VEHICLE_PING',
  VEHICLE_CANCEL_TRIP = 'VEHICLE_CANCEL_TRIP',
  VEHICLE_REMOVE_PRODUCT_TRIP = 'VEHICLE_REMOVE_PRODUCT_TRIP',
  VEHICLE_BOOK = 'VEHICLE_BOOK',
  VEHICLE_ADD_PRODUCT_TRIP = 'VEHICLE_ADD_PRODUCT_TRIP',
  VEHICLE_UPDATE_REASON = 'VEHICLE_UPDATE_REASON',
  VEHICLE_RESET_CLEAN_DATE = 'VEHICLE_RESET_CLEAN_DATE',
  VEHICLE_RESET_REDISTRIB_DATE = 'VEHICLE_RESET_REDISTRIB_DATE',
  VEHICLE_RESET_MOVING_DATE = 'VEHICLE_RESET_MOVING_DATE',
  VEHICLE_RESET_BOOKING_CONTEXT = 'VEHICLE_RESET_BOOKING_CONTEXT',
  VEHICLE_ARCHIVED_FM = 'VEHICLE_ARCHIVED_FM',
  VEHICLE_ANTITHEFT_ON = 'VEHICLE_ANTITHEFT_ON',
  VEHICLE_ANTITHEFT_OFF = 'VEHICLE_ANTITHEFT_OFF',
  VEHICLE_CHARGING_ACTIVE = 'VEHICLE_CHARGING_ACTIVE',
  VEHICLE_CHARGING_ENDED = 'VEHICLE_CHARGING_ENDED',
  VEHICLE_CHARGING_PLUGGED = 'VEHICLE_CHARGING_PLUGGED',
  VEHICLE_CHARGING_UNPLUGGED = 'VEHICLE_CHARGING_UNPLUGGED',
  VEHICLE_BATTERY_OK = 'VEHICLE_BATTERY_OK',
  VEHICLE_BATTERY_LOW = 'VEHICLE_BATTERY_LOW',
  VEHICLE_BATTERY_CRITICAL = 'VEHICLE_BATTERY_CRITICAL',
  VEHICLE_DOORS_OPEN = 'VEHICLE_DOORS_OPEN',
  VEHICLE_DOORS_CLOSED = 'VEHICLE_DOORS_CLOSED',
  VEHICLE_WINDOWS_OPEN = 'VEHICLE_WINDOWS_OPEN',
  VEHICLE_WINDOWS_CLOSED = 'VEHICLE_WINDOWS_CLOSED',
  VEHICLE_ALERT = 'VEHICLE_ALERT',
  VEHICLE_FATAL = 'VEHICLE_FATAL',
  VEHICLE_PRICING_CHANGED = 'VEHICLE_PRICING_CHANGED',
  VEHICLE_GROUP_CHANGED = 'VEHICLE_GROUP_CHANGED',
  VEHICLE_UPDATE_ODOMETER = 'VEHICLE_UPDATE_ODOMETER',
  VEHICLE_UPDATE_STATUS = 'VEHICLE_UPDATE_STATUS',
  VEHICLE_UPDATE_GPS_MANUAL = 'VEHICLE_UPDATE_GPS_MANUAL',
  VEHICLE_UPDATE_GPS_SIM = 'VEHICLE_UPDATE_GPS_SIM',
  VEHICLE_RFID_SYNC = 'VEHICLE_RFID_SYNC',
  VEHICLE_PREAUTH_CONFIRMED = 'VEHICLE_PREAUTH_CONFIRMED',
  VEHICLE_PREAUTH_REJECTED = 'VEHICLE_PREAUTH_REJECTED',
  VEHICLE_PREAUTH_EXPIRED = 'VEHICLE_PREAUTH_EXPIRED',
  VEHICLE_PREAUTH_CANCELLED = 'VEHICLE_PREAUTH_CANCELLED',
  VEHICLE_ADDED_TO_SERVICE = 'VEHICLE_ADDED_TO_SERVICE',
  VEHICLE_REMOVED_FROM_SERVICE = 'VEHICLE_REMOVED_FROM_SERVICE',
  VEHICLE_REFILLED = 'VEHICLE_REFILLED',
  VEHICLE_DIAG_GPS = 'VEHICLE_DIAG_GPS',
  VEHICLE_DIAG_CAN = 'VEHICLE_DIAG_CAN',
  VEHICLE_DIAG_MOBILIZER = 'VEHICLE_DIAG_MOBILIZER',
  VEHICLE_DIAG_IMMOBILIZER = 'VEHICLE_DIAG_IMMOBILIZER',
  VEHICLE_DIAG_LOCK = 'VEHICLE_DIAG_LOCK',
  VEHICLE_DIAG_UNLOCK = 'VEHICLE_DIAG_UNLOCK',
  VEHICLE_DIAG_ANALOG = 'VEHICLE_DIAG_ANALOG',
  VEHICLE_DIAG_CKH = 'VEHICLE_DIAG_CKH',
  VEHICLE_DIAG_RFID = 'VEHICLE_DIAG_RFID',
  VEHICLE_DIAG_SPEAK = 'VEHICLE_DIAG_SPEAK',
  VEHICLE_DIAG_HZ_LED = 'VEHICLE_DIAG_HZ_LED',
  VEHICLE_BLE_AUTH = 'VEHICLE_BLE_AUTH',
  VEHICLE_FIRMWARE_UPDATE = 'VEHICLE_FIRMWARE_UPDATE',
}

export class VulogCarEventDto<ExtraInfo extends object = object> {
  fleetId: string;
  vehicleId: string;
  origin: string;
  type: VulogCarEventType;
  date: string;
  failed: boolean;
  originId: string;
  insertionDate: string;
  extraInfo?: ExtraInfo;

  toCarEvent(): CarEvent {
    return new CarEvent({
      type: this.type,
      date: DateTime.fromISO(this.date),
      vehicleId: new VulogCarId(this.vehicleId),
    });
  }
}
