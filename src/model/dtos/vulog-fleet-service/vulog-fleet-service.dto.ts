import {
  VulogCityId,
  VulogFleetId,
  VulogServiceId,
} from 'src/common/tiny-types';
import { FleetService } from 'src/model/fleet-service';

export enum VulogFleetServiceStatus {
  Active = 'ACTIVE',
  Disabled = 'DISABLED',
}

export enum VulogFleetServiceType {
  FreeFloating = 'FREE_FLOATING',
  RoundTripBooking = 'ROUND_TRIP_BOOKING',
  ScheduledBookingStation = 'SCHEDULED_BOOKING_STATION',
  ServiceTrip = 'SERVICE_TRIP',
  Subscription = 'SUBSCRIPTION',
}

export enum VulogFleetServiceVisibility {
  Public = 'PUBLIC',
  Private = 'PRIVATE',
}

export class VulogFleetServiceDto {
  id: string;

  name: string;

  status: VulogFleetServiceStatus;

  type: VulogFleetServiceType;

  fleetId: string;

  cityId: string;

  zones: string[];

  vehicles: string[];

  stations: string[]; // Usually empty as `zones` is used

  pois: string[];

  bookingValidity: number;

  visibility: VulogFleetServiceVisibility;

  autoApprovalStrategy: string; // It is actually enum but not found in the current-received documentation (April)

  tripPrivacyStrategy: string; // It is actually enum but not found in the current-received documentation (April)

  zoneFillColor: string;

  zoneOutlineColor: string;

  maxConcurrentTrips: number;

  highestRangeEnabled: boolean;

  scheduleAtripEnabled: boolean;

  zoneCheckEnabled: boolean;

  maxAdditionalDrivers: number;

  nearEndThreshold: number;

  tripDurationStrategy: string; // It is actually enum but not found in the current-received documentation (April)

  customPricingEnabled: boolean;

  toFleetService(): FleetService {
    return new FleetService({
      id: new VulogServiceId(this.id),
      name: this.name,
      status: this.status,
      type: this.type,
      fleetId: this.fleetId ? new VulogFleetId(this.fleetId) : null,
      cityId: this.cityId ? new VulogCityId(this.cityId) : null,
      zoneIds: this.zones?.length ? this.zones : [],
      vehicleIds: this.vehicles?.length ? this.vehicles : [],
      maximumBookingTime: this.bookingValidity,
      visibility: this.visibility,
      maxConcurrentTrips: this.maxConcurrentTrips,
    });
  }
}
