import { Type } from 'class-transformer';
import { RealtimeCar } from '../../realtime.car';
import { VvgCarDto } from './vvg-car.dto';

export class VvgCarListDto {
  @Type(() => VvgCarDto)
  public content: VvgCarDto[];
  public continuationToken: string;

  toCarList(): { content: RealtimeCar[]; continuationToken: string } {
    return {
      content: this.content.map((carDto) => carDto.toRealTimeCar()),
      continuationToken: this.continuationToken,
    };
  }
}
