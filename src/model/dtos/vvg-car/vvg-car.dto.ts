import { plainToInstance, Type } from 'class-transformer';
import { CarModel, VulogCarId } from 'src/common/tiny-types';

import { Location, RealtimeCar } from '../../realtime.car';
import { VvgCarAlerts } from './vvg-car-fields/vvg-car-alerts';
import { VvgCarAltTracking } from './vvg-car-fields/vvg-car-alt-tracking';
import { VvgCarInformation } from './vvg-car-fields/vvg-car-information';
import { VvgCarSession } from './vvg-car-fields/vvg-car-session';
import { VvgCarStatus } from './vvg-car-fields/vvg-car-status';
import { VvgCarTracking } from './vvg-car-fields/vvg-car-tracking';
import { VvgCarZones } from './vvg-car-fields/vvg-car-zones';

export class VvgCarDto {
  public id: string;
  public fleetId: string;
  public boxId: string;

  @Type(() => VvgCarStatus)
  public status: VvgCarStatus;

  @Type(() => VvgCarSession)
  public sessions: VvgCarSession[];

  @Type(() => VvgCarInformation)
  public information: VvgCarInformation;

  @Type(() => VvgCarTracking)
  public tracking: VvgCarTracking;

  @Type(() => VvgCarAltTracking)
  public simTracking: VvgCarAltTracking;

  @Type(() => VvgCarAltTracking)
  public manualTracking: VvgCarAltTracking;

  @Type(() => VvgCarZones)
  public zones: VvgCarZones;

  @Type(() => VvgCarAlerts)
  public alerts: VvgCarAlerts;

  toRealTimeCar(): RealtimeCar {
    return new RealtimeCar({
      id: new VulogCarId(this.id),
      model: new CarModel(this.information.name),
      plate: null,
      vin: this.information.vin,
      sourceId: new VulogCarId(this.id),
      energyLevel: this.status.tractionBattery.percentage,
      isCharging: this.status.charging,
      isPluggedIn: this.status.cablePlugged,
      location: plainToInstance(Location, {
        latitude: this.tracking.latitude,
        longitude: this.tracking.longitude,
      }),
      areDoorsAndWindowsClosed: this.status.doorsAndWindowsClosed,
      alerts: {
        hasFlatTire: this.alerts.flatTire,
        hasTireUnderPressure: this.alerts.tireUnderPressure,
        hasCrash: this.alerts.crash,
      },
      zoneIds: null,
      disabled: null,
      outOfServiceReason: null,
      allowedStickyZone: null,
    });
  }

  toRawJsonObject() {
    return {
      id: this.id,
      fleetId: this.fleetId,
      boxId: this.boxId,
      status: this.status,
      sessions: this.sessions,
      information: this.information,
      tracking: this.tracking,
      simTracking: this.simTracking,
      manualTracking: this.manualTracking,
      zones: this.zones,
      alerts: this.alerts,
    };
  }
}
