export class VvgCarStatus {
  constructor(
    public boxStatus: string,
    public primaryFuelTank: { km: number; percentage: number },
    public secondaryFuelTank: {
      km: number;
      percentage: number;
    },
    public tractionBattery: {
      km: number;
      percentage: number;
    },
    public secondaryTractionBattery: {
      km: number;
      percentage: number;
    },
    public mileage: number,
    public auxBatteryVoltage: number,
    public charging: boolean,
    public locked: boolean,
    public engineOn: boolean,
    public ecoMode: boolean,
    public reverseGear: boolean,
    public immobilizerOn: boolean,
    public doorsAndWindowsClosed: boolean,
    public doors: {
      frontLeftClosed: boolean;
      frontRightClosed: boolean;
      rearLeftClosed: boolean;
      rearRightClosed: boolean;
      trunkClosed: boolean;
    },
    public windows: {
      frontLeftClosed: boolean;
      frontRightClosed: boolean;
      rearLeftClosed: boolean;
      rearRightClosed: boolean;
      trunkClosed: boolean;
    },
    public secured: boolean,
    public cablePlugged: boolean,
    public spareLockOn: boolean,
    public helmetPresent: boolean,
    public helmet2Present: boolean,
    public helmetBoxLockOn: boolean,
    public helmet2LockOn: boolean,
    public rssi: number,
    public updateDate: string,
    public onGoing: string,
  ) {}
}
