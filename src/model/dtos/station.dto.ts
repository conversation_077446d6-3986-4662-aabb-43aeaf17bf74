import { StationId } from 'src/common/tiny-types';
import { StationStatus } from '../../external/station/dtos/station-status';
import { Station } from '../station';

export class StationDto {
  public id: string;
  public sourceId: string;
  public source: string;
  public displayName: string;
  public latitude: string;
  public longitude: string;
  public street: string;
  public postalCode: string;
  public city: string;
  public zoneId?: string;
  public createdAt?: string;
  public updatedAt?: string;
  public status: StationStatus;

  toStation(): Station {
    return new Station({
      id: new StationId(this.id),
      sourceId: this.sourceId,
      zoneId: this.zoneId,
      displayName: this.displayName,
      latitude: Number(this.latitude),
      longitude: Number(this.longitude),
      postalCode: this.postalCode,
      street: this.street,
      status: this.status,
    });
  }

  toRawJsonObject() {
    return {
      id: this.id,
      sourceId: this.sourceId,
      source: this.source,
      displayName: this.displayName,
      latitude: this.latitude,
      longitude: this.longitude,
      street: this.street,
      postalCode: this.postalCode,
      city: this.city,
      zoneId: this.zoneId,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      status: this.status,
    };
  }
}
