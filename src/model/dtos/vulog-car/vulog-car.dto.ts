import { plainToInstance, Type } from 'class-transformer';
import { CarModel, CarPlateNumber, VulogCarId } from 'src/common/tiny-types';
import { Location, RealtimeCar } from '../../realtime.car';

import { isNotEmptyObject } from 'class-validator';
import { VulogCarDescription } from './vulog-car-fields/vulog-car-description';
import { VulogCarLocation } from './vulog-car-fields/vulog-car-location';
import { VulogCarStatus } from './vulog-car-fields/vulog-car-status';

// Car DTO returned by the Vulog mobile API
export class VulogCarDto {
  @Type(() => VulogCarStatus)
  public status: VulogCarStatus;

  @Type(() => VulogCarDescription)
  public description: VulogCarDescription;

  @Type(() => VulogCarLocation)
  public location: VulogCarLocation;

  toRealtimeCar(): RealtimeCar {
    return new RealtimeCar({
      id: new VulogCarId(this.description.id),
      model: new CarModel(this.description.model),
      plate: this?.description?.plate
        ? new CarPlateNumber(this.description.plate)
        : null,
      vin: null,
      sourceId: new VulogCarId(this.description.id),
      energyLevel: this.status.energyLevel,
      isCharging: this.status.isCharging,
      isPluggedIn: this.status.isCharging,
      location: isNotEmptyObject(this.location)
        ? plainToInstance(Location, {
            latitude: this.location.position.lat,
            longitude: this.location.position.lon,
          })
        : null,
      areDoorsAndWindowsClosed: null,
      alerts: null,
      zoneIds: null,
      disabled: null,
      outOfServiceReason: null,
      allowedStickyZone: null,
    });
  }

  toRawJsonObject() {
    return {
      status: this.status.toRawJsonObject(),
      description: this.description.toRawJsonObject(),
      location: this.location ? this.location.toRawJsonObject() : null,
    };
  }
}
