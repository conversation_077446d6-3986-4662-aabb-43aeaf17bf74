import { Type } from 'class-transformer';
import { VulogPosition } from '../../vulog-position';
import { VulogAddress } from './vulog-address';

export class VulogCarLocation {
  @Type(() => VulogPosition)
  public position: VulogPosition;

  @Type(() => VulogAddress)
  public address?: VulogAddress;

  toRawJsonObject() {
    return {
      position: this.position.toRawJsonObject(),
      address: this.address && this.address.toRawJsonObject(),
    };
  }
}
