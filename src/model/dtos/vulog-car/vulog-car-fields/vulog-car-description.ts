export class VulogCarDescription {
  constructor(
    public id: string,
    public model: string,
    public plate: string,
    public name: string,
    public modelId: string,
    public optionsId: number[],
    public cityId?: string,
    public serviceId?: string,
    public iconUrl?: string,
    public tokenIconsUrl?: string,
    public pricingInfo?: {
      pricingId: string;
      type?: string;
      defaultPricing?: string;
    },
  ) {}

  toRawJsonObject() {
    return {
      id: this.id,
      model: this.model,
      plate: this.plate,
      name: this.name,
      modelId: this.modelId,
      optionsId: this.optionsId,
      cityId: this.cityId,
      serviceId: this.serviceId,
      iconUrl: this.iconUrl,
      tokenIconsUrl: this.tokenIconsUrl,
      pricingInfo: this.pricingInfo,
    };
  }
}
