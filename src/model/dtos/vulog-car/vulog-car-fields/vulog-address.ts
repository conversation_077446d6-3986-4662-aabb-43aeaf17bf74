export class VulogAddress {
  constructor(
    public streetAddress?: string,
    public locality?: string,
    public postalCode?: string,
    public region?: string,
    public country?: string,
  ) {}

  toRawJsonObject() {
    return {
      streetAddress: this.streetAddress,
      locality: this.locality,
      postalCode: this.postalCode,
      region: this.region,
      country: this.country,
    };
  }
}
