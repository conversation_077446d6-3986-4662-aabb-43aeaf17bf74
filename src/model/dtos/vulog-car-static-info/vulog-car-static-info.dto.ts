import { Type } from 'class-transformer';
import {
  CarModel,
  CarPlateNumber,
  VulogCarId,
  VulogServiceId,
} from 'src/common/tiny-types';
import { StaticCar } from 'src/model/static.car';
import { VulogCarModelInfo } from './vulog-car-static-info-fields/vulog-car-model-info';
import { VulogCarOption } from './vulog-car-static-info-fields/vulog-car-option';

export class VulogCarStaticInfoDto {
  constructor(
    props?: Omit<VulogCarStaticInfoDto, 'toStaticCar' | 'toRawJsonObject'>,
  ) {
    Object.assign(this, props);
  }

  public id: string;
  public vin: string;
  public name: string;
  public plate: string;

  @Type(() => VulogCarModelInfo)
  public model: VulogCarModelInfo;

  @Type(() => VulogCarOption)
  public options: VulogCarOption[];

  public createDate: string;
  public updateDate: string;
  public fleetId: string;
  public vuboxId: string;
  public externalId: string;
  public wakeupProvider: string;
  public msisdn: string;
  public iccid: string;
  public imsi: string;
  public published: boolean;
  public archived: boolean;
  public serviceId: string;

  toStaticCar() {
    return new StaticCar({
      id: new VulogCarId(this.id),
      model: this.model.name ? new CarModel(this.model.name) : null,
      plate: this.plate ? new CarPlateNumber(this.plate) : null,
      vin: this.vin,
      sourceId: new VulogCarId(this.id),
      serviceId: this.serviceId ? new VulogServiceId(this.serviceId) : null,
      archived: !!this.archived,
    });
  }

  toRawJsonObject() {
    return {
      id: this.id,
      vin: this.vin,
      name: this.name,
      plate: this.plate,
      model: this.model,
      options: this.options,
      createDate: this.createDate,
      updateDate: this.updateDate,
      fleetId: this.fleetId,
      vuboxId: this.vuboxId,
      externalId: this.externalId,
      wakeupProvider: this.wakeupProvider,
      msisdn: this.msisdn,
      iccid: this.iccid,
      imsi: this.imsi,
      published: this.published,
      archived: this.archived,
    };
  }
}
