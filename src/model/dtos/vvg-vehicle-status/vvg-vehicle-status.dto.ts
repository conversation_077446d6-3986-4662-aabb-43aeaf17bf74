import { plainToInstance, Type } from 'class-transformer';
import { CarModel, VulogCarId } from 'src/common/tiny-types';
import { VulogZoneUtils } from 'src/common/utils/zone.util';
import { Car } from 'src/logic/car/domain/car';
import { Location } from 'src/model/realtime.car';
import { Station } from 'src/model/station';
import { TelemetryCarInfo } from 'src/model/telemetry-car-info';
import { TelemetryCarStatus } from 'src/model/telemetry-car-status';
import { StationDto } from '../station.dto';
import { VulogZone } from '../vulog-trip/vulog-trip-fields/vulog-zone';
import { VvgCarSession } from '../vvg-car/vvg-car-fields/vvg-car-session';

export class VulogVehicleInformation {
  vin: string;

  name: string;
}

export class VulogVehicleStatusAutonomy {
  percentage: number;

  km: number;
}

export class VulogVehicleStatusDoors {
  frontLeftClosed: boolean;

  frontRightClosed: boolean;

  rearLeftClosed: boolean;

  rearRightClosed: boolean;

  trunkClosed: boolean;
}

export class VulogVehicleStatusWindows {
  frontLeftClosed: boolean;

  frontRightClosed: boolean;

  rearLeftClosed: boolean;

  rearRightClosed: boolean;

  trunkClosed: boolean;
}

export class VulogVehicleStatus {
  boxStatus: string;

  mileage: number;

  @Type(() => VulogVehicleStatusAutonomy)
  tractionBattery: VulogVehicleStatusAutonomy;

  auxBatteryVoltage: number;

  @Type(() => VulogVehicleStatusDoors)
  doors: VulogVehicleStatusDoors;

  @Type(() => VulogVehicleStatusWindows)
  windows: VulogVehicleStatusWindows;

  doorsAndWindowsClosed: boolean;

  locked: boolean;

  engineOn: boolean;

  parkingBrakeOn: boolean; // This field only exists with the API https://vg-sta.vulog.net/v2/fleets/BLUESG-SINGA/vehicles/:vehecleId/status

  charging: boolean;

  cablePlugged: boolean;

  plugged: boolean;

  immobilizerOn: boolean;

  ecoMode: boolean;

  primaryFuelTank: VulogVehicleStatusAutonomy;

  secondaryFuelTank: VulogVehicleStatusAutonomy;

  reverseGear: boolean;

  rssi: number;

  secured: boolean;

  updateDate: string;
}

export class VulogVehicleStatusTracking {
  latitude: number;

  longitude: number;

  altitude: number;

  head: number;

  hdop: number;

  speed: number;

  captureDate: string;
}

export class VulogVehicleCardsAndKey {
  key: boolean;

  card1: boolean;

  card2: boolean;
}

export class VulogVehicleSimCard {
  imsi: string;

  iccid: string;

  provider: string;
}

export class VulogVehicleZones {
  @Type(() => VulogZone)
  current: VulogZone[];
}

export class VvgVehicleStatusDto {
  id: string;

  fleetId: string;

  boxId: string;

  hasAlerts: boolean;

  @Type(() => VvgCarSession)
  sessions: VvgCarSession[];

  @Type(() => VulogVehicleInformation)
  information: VulogVehicleInformation;

  @Type(() => VulogVehicleStatus)
  status: VulogVehicleStatus;

  @Type(() => VulogVehicleStatusTracking)
  tracking: VulogVehicleStatusTracking;

  @Type(() => VulogVehicleCardsAndKey)
  cardsAndKey: VulogVehicleCardsAndKey;

  @Type(() => VulogVehicleSimCard)
  simCard: VulogVehicleSimCard;

  @Type(() => VulogVehicleZones)
  zones: VulogVehicleZones;

  toCarStatus(staticCarInfo: Car, fleetZoneIds: Map<string, string>) {
    const { model, plate } = staticCarInfo;

    const validZones = VulogZoneUtils.filterZonesUnderSpecificFleetService(
      this.zones?.current,
      fleetZoneIds,
    );

    const allowedStickyZone = validZones?.length
      ? validZones.find((zone) => zone.isSticky())
      : null;

    return new TelemetryCarStatus({
      id: new VulogCarId(this.id),
      model: model ? new CarModel(model) : null,
      plate: plate || null,
      vin: this.information?.vin || null,
      sourceId: new VulogCarId(this.id),
      energyLevel: this.status?.tractionBattery?.percentage,
      isCharging: !!this.status?.charging,
      isPluggedIn: !!this.status?.cablePlugged || !!this.status?.plugged,
      location:
        this.tracking?.latitude && this.tracking?.longitude
          ? plainToInstance(Location, {
              latitude: this.tracking.latitude,
              longitude: this.tracking.longitude,
            })
          : null,
      areDoorsAndWindowsClosed: !!this.status?.doorsAndWindowsClosed,
      alerts: null,
      zoneIds: validZones ? validZones.map((zone) => zone.vulogZoneId) : [],
      cardsAndKey: this.cardsAndKey,
      mileage: this.status?.mileage,
      simCard: this.simCard,
      engineOn: !!this.status?.engineOn,
      parkingBrakeOn: !!this.status?.parkingBrakeOn,
      locked: !!this.status?.locked,
      immobilizerOn: !!this.status?.immobilizerOn,
      activeSession: this.sessions?.length ? this.sessions[0] : null,
      allowedStickyZone,
    });
  }

  toTelemetryCarInfo(
    staticCarInfo: Car,
    fleetZoneIds: Map<string, string>,
    knownZoneStationMap?: { [zoneId: string]: StationDto },
  ) {
    const { model, plate } = staticCarInfo;

    const validZones = VulogZoneUtils.filterZonesUnderSpecificFleetService(
      this.zones?.current,
      fleetZoneIds,
    );

    const allowedStickyZone = validZones?.length
      ? validZones.find((zone) => zone.isSticky())
      : null;

    const zoneThatMatchedStation: VulogZone = validZones.find(
      (zone) => knownZoneStationMap && !!knownZoneStationMap[zone.zoneId],
    );

    const currentStation: Station = zoneThatMatchedStation
      ? knownZoneStationMap[zoneThatMatchedStation?.zoneId]?.toStation()
      : null;

    return new TelemetryCarInfo({
      id: new VulogCarId(this.id),
      model: model ? new CarModel(model) : null,
      plate: plate || null,
      vin: this.information?.vin || null,
      sourceId: new VulogCarId(this.id),
      energyLevel: this.status?.tractionBattery?.percentage,
      isCharging: !!this.status?.charging,
      isPluggedIn: !!this.status?.cablePlugged || !!this.status?.plugged,
      location:
        this.tracking?.latitude && this.tracking?.longitude
          ? plainToInstance(Location, {
              latitude: this.tracking.latitude,
              longitude: this.tracking.longitude,
            })
          : null,
      areDoorsAndWindowsClosed: !!this.status?.doorsAndWindowsClosed, // < becomes false
      alerts: null,
      zoneIds: validZones ? validZones.map((zone) => zone.vulogZoneId) : [],
      cardsAndKey: this.cardsAndKey,
      mileage: this.status?.mileage,
      simCard: this.simCard,
      engineOn: !!this.status?.engineOn,
      locked: !!this.status?.locked,
      parkingBrakeOn: false, // Car Info does not have parking brake information
      immobilizerOn: !!this.status?.immobilizerOn,
      currentZones: validZones,
      activeSession: this.sessions?.length ? this.sessions[0] : null,
      allowedStickyZone,
      currentStation,
    });
  }
}
