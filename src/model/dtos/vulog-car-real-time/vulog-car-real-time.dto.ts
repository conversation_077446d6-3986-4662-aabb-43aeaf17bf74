import { Logger } from '@nestjs/common';
import { plainToInstance, Type } from 'class-transformer';
import { isNotEmptyObject } from 'class-validator';
import { DateTime } from 'luxon';
import { RentalError } from 'src/common/errors/rental-error';
import {
  CarModel,
  CarPlateNumber,
  VulogCarId,
  VulogJourneyOrTripId,
  VulogServiceId,
} from 'src/common/tiny-types';
import { Latitude } from 'src/common/tiny-types/latitude';
import { Longitude } from 'src/common/tiny-types/longitude';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { VulogMileage } from 'src/common/tiny-types/vulog-mileage.type';
import { bigNumberize } from 'src/common/utils/big-decimal.util';
import { VulogZoneUtils } from 'src/common/utils/zone.util';
import { config } from 'src/config';
import { GpsCoordinates } from 'src/model/gps-coordinates';
import { RentalInfo } from 'src/model/rental-info';
import { Location, RealtimeCar } from '../../realtime.car';
import { VulogFleetServiceType } from '../vulog-fleet-service/vulog-fleet-service.dto';
import { VulogBleKey } from '../vulog-journey/vulog-journey-fields/vulog-ble-key';
import { VulogLocation } from '../vulog-trip/vulog-trip-fields/vulog-location';
import { VulogZone } from '../vulog-trip/vulog-trip-fields/vulog-zone';
import { VulogLocationRealTime } from './vulog-location-real-time';

export enum VulogRealTimeVehicleStatus {
  Available = 0,
  InUse = 1,
  OutOfService = 2,
  Unsync = 5,
}

export enum VulogRealTimeBookingStatus {
  Available = 0,
  Booking = 1,
  Booked = 2,
}

export enum VulogBoxStatus {
  Free = 1,
  BookedWaiting = 3,
  BookedPrepared = 4,
}

export class VulogCarRealTimeDto {
  public id: string;
  public name: string;
  public plate: string;
  public vin: string;
  public fleetid: string;
  public boxid: string;
  public vehicle_status: VulogRealTimeVehicleStatus;

  @Type(() => VulogZone)
  public zones: VulogZone[];

  public releasable: boolean;
  public box_status: VulogBoxStatus;
  public autonomy: number; // Battery Level, in %
  public autonomy2: number;
  public isCharging: boolean;
  public battery: number; // auxiliary Battery, in Volt
  public km: number;
  public speed: number;

  @Type(() => VulogLocationRealTime)
  public location: VulogLocationRealTime;

  @Type(() => VulogLocation)
  public endTripLocation: VulogLocation;

  public endZoneIds: string[];
  public productIds: string[];
  public isDoorClosed: boolean;
  public isDoorLocked: boolean;
  public engineOn: boolean;
  public immobilizerOn: boolean;
  public secureOn: boolean;
  public spareLockOn: boolean;
  public pileLockOn: boolean;
  public userId: string;
  public user_locale: string;
  public rfid: string;
  public orderId: string; // orderId === tripId === journeyId
  public gatewayUrl: string;
  public booking_status: number;
  public booking_date: string;
  public expiresOn: string;
  public last_active_date: string;
  public last_wakeUp_date: string;
  public version: string;
  public pricingId: string;
  public start_date: string;
  public theorStartDate: string;
  public theorEndDate: string;

  @Type(() => VulogZone)
  public startZones: VulogZone[];

  @Type(() => VulogZone)
  public endZones: VulogZone[];

  public disabled: boolean;
  public outOfServiceReason: string;
  public cleanlinessStatus: boolean;
  public ignitionOffGeohash: string;
  public geohashNeighbours: string[];
  public needsRedistribution: boolean;
  public batteryUnderThreshold: boolean;
  public isBeingTowed: boolean;
  public automaticallyEnableVehicleAfterRangeRecovery: boolean;
  public key: VulogBleKey;
  public startTripLocation: VulogLocation;
  public preAuthStatus: string;
  public profileId: string;
  public username: string;
  public preAuthEnabled: boolean;
  public comeFromApp: string;
  public entityId: string;
  public profileType: string;
  public serviceType: string;
  public serviceId: string;
  public start_mileage: number;
  public lastCleanedDate: string;

  isRental(): boolean {
    return (
      this.vehicle_status === VulogRealTimeVehicleStatus.InUse &&
      !!this.start_date
    );
  }

  isAvailable(
    warningLowBatterLevel: number,
    customerUsableVehicleIds: string[],
    customerUsableZoneIds: Map<string, string>,
  ): boolean {
    const isBelongedToCustomerUsableFleetService =
      customerUsableVehicleIds?.length &&
      customerUsableVehicleIds?.includes(this.id);

    const isZonesUnderCustomerUsableFleetService =
      !!VulogZoneUtils.filterZonesUnderSpecificFleetService(
        this.zones,
        customerUsableZoneIds,
      ).length;

    if (!isZonesUnderCustomerUsableFleetService) {
      Logger.debug(
        `The current zone of vehicle ${this.id} is not belonged to customer-usable fleet service`,
        {
          fleetServiceId: config.vulogConfig.serviceId.value,
        },
      );
    }

    if (!isBelongedToCustomerUsableFleetService) {
      Logger.debug(
        `The vehicle ${this.id} is not belonged to customer-usable fleet service`,
        {
          fleetServiceId: config.vulogConfig.serviceId.value,
        },
      );
    }

    return (
      this.vehicle_status === VulogRealTimeVehicleStatus.Available &&
      this.booking_status === VulogRealTimeBookingStatus.Available &&
      this.autonomy >= warningLowBatterLevel &&
      isBelongedToCustomerUsableFleetService &&
      isZonesUnderCustomerUsableFleetService
    );
  }

  hasTripStarted(): boolean {
    return !!this.start_date;
  }

  toRentalInfo(fleetZoneIds: Map<string, string>): RentalInfo {
    if (!this.isRental()) {
      throw new RentalError(
        'Invalid VulogCarRealTimeDto conversion to RentalInfo - this trip is not a rental',
      );
    }

    const now = DateTime.now();

    const duration = bigNumberize(
      now.diff(DateTime.fromISO(this.start_date), ['seconds']).seconds,
    ).toNumber();

    /**
     * START: Purify zones (Remove invalid zones such as Service Zone that could be used by our customer)
     */
    const validStartZones = VulogZoneUtils.filterZonesUnderSpecificFleetService(
      this.startZones,
      fleetZoneIds,
    );

    const validCurrentZones =
      VulogZoneUtils.filterZonesUnderSpecificFleetService(
        this.zones,
        fleetZoneIds,
      );

    const validEndZones = VulogZoneUtils.filterZonesUnderSpecificFleetService(
      this.endZones,
      fleetZoneIds,
    );
    /**
     * END
     */

    return new RentalInfo({
      carId: new VulogCarId(this.id),
      sourceCarId: new VulogCarId(this.id),
      sourceId: new VulogJourneyOrTripId(this.orderId),
      startZones: validStartZones,
      endZones: validEndZones,
      currentZones: validCurrentZones,
      startDate: this.start_date ? new VulogDate(this.start_date) : null,
      endDate: this.theorEndDate ? new VulogDate(this.theorEndDate) : null,
      duration,
      distance: new VulogMileage(
        bigNumberize(this.km, 5)
          .minus(bigNumberize(this.start_mileage, 5))
          .toNumber(),
      ),
      hasTripStarted: this.hasTripStarted(),
      carRealtimeMetadata: {
        battery: this.autonomy,
        isEngineOn: this.engineOn,
        isCharging: this.isCharging,
        isDoorLocked: this.isDoorLocked,
        isDoorClosed: this.isDoorClosed,
      },
      vuboxId: this.boxid,
      currentGpsCoordinates: this.location
        ? new GpsCoordinates({
            longitude: new Longitude(this.location.longitude.toString()),
            latitude: new Latitude(this.location.latitude.toString()),
          })
        : null,
    });
  }

  toRealtimeCar(
    model?: CarModel,
    fleetZoneIds?: Map<string, string>,
    warnMultipleStickyZones?: boolean,
  ) {
    const validZones = VulogZoneUtils.filterZonesUnderSpecificFleetService(
      this.zones,
      fleetZoneIds,
    );

    const validZoneIds = validZones.map((zone) => zone.vulogZoneId);

    const rawValidZoneIds: string[] = validZoneIds.map(
      (zoneId) => zoneId.value,
    );

    const invalidZones =
      this.zones?.filter(
        (zone) =>
          rawValidZoneIds?.length && !rawValidZoneIds.includes(zone.zoneId),
      ) || [];

    // First find all sticky zones
    const stickyZones = validZones?.filter((zone) => zone.isSticky()) ?? [];

    // Log warning if multiple sticky zones found
    if (stickyZones.length > 1 && warnMultipleStickyZones) {
      Logger.warn(`Multiple sticky zones found for zones`, {
        carId: this.id,
        carPlate: this.plate,
        zoneIds: stickyZones.map((zone) => zone.vulogZoneId?.value),
        count: stickyZones.length,
      });
    }

    // Keep original logic of using the first sticky zone if any exist
    const allowedStickyZone = stickyZones.length > 0 ? stickyZones[0] : null;

    let statusLabel: string;

    switch (this.vehicle_status) {
      case VulogRealTimeVehicleStatus.Available:
        statusLabel = 'Available';

        break;

      case VulogRealTimeVehicleStatus.InUse:
        statusLabel = 'Car In Use';

        break;

      case VulogRealTimeVehicleStatus.OutOfService:
        statusLabel = 'Under Maintenance';

        break;

      case VulogRealTimeVehicleStatus.Unsync:
        statusLabel = 'Unsync';

        break;

      default:
        statusLabel = 'Unknown';

        break;
    }

    return new RealtimeCar({
      id: new VulogCarId(this.id),
      status: this.vehicle_status,
      statusLabel,
      model: model || null,
      plate: this.plate ? new CarPlateNumber(this.plate) : null,
      vin: this.vin,
      sourceId: new VulogCarId(this.id),
      energyLevel: this.autonomy,
      isCharging: this.isCharging,
      isPluggedIn: this.isCharging,
      location: isNotEmptyObject(this.location)
        ? plainToInstance(Location, {
            latitude: this.location.latitude,
            longitude: this.location.longitude,
          })
        : null,
      areDoorsAndWindowsClosed: this.isDoorClosed,
      alerts: null,
      zoneIds: validZoneIds,
      isDoorLocked: this.isDoorLocked,
      isDoorClosed: this.isDoorClosed,
      isEngineOn: this.engineOn,
      vuboxId: this.boxid,
      serviceId: this.serviceId ? new VulogServiceId(this.serviceId) : null,
      serviceType: this.serviceType
        ? (this.serviceType as VulogFleetServiceType)
        : null,
      disabled: this.disabled,
      outOfServiceReason: this.outOfServiceReason,
      allowedStickyZone,
      immobilizerOn: this.immobilizerOn,
      lastActiveDate: this.last_active_date
        ? DateTime.fromISO(this.last_active_date)
        : null,
      invalidZones: invalidZones,
    });
  }

  toRawJsonObject() {
    return {
      id: this.id,
      name: this.name,
      plate: this.plate,
      vin: this.vin,
      fleetid: this.fleetid,
      boxid: this.boxid,
      vehicle_status: this.vehicle_status,
      zones: this.zones,
      releasable: this.releasable,
      box_status: this.box_status,
      autonomy: this.autonomy,
      autonomy2: this.autonomy2,
      isCharging: this.isCharging,
      battery: this.battery,
      km: this.km,
      speed: this.speed,
      location: this.location,
      endTripLocation: this.endTripLocation,
      endZoneIds: this.endZoneIds,
      productIds: this.productIds,
      isDoorClosed: this.isDoorClosed,
      isDoorLocked: this.isDoorLocked,
      engineOn: this.engineOn,
      immobilizerOn: this.immobilizerOn,
      secureOn: this.secureOn,
      spareLockOn: this.spareLockOn,
      pileLockOn: this.pileLockOn,
      userId: this.userId,
      user_locale: this.user_locale,
      rfid: this.rfid,
      orderId: this.orderId,
      gatewayUrl: this.gatewayUrl,
      booking_status: this.booking_status,
      booking_date: this.booking_date,
      expiresOn: this.expiresOn,
      last_active_date: this.last_active_date,
      last_wakeUp_date: this.last_wakeUp_date,
      version: this.version,
      pricingId: this.pricingId,
      start_date: this.start_date,
      theorStartDate: this.theorStartDate,
      theorEndDate: this.theorEndDate,
      startZones: this.startZones,
      disabled: this.disabled,
      outOfServiceReason: this.outOfServiceReason,
      cleanlinessStatus: this.cleanlinessStatus,
      ignitionOffGeohash: this.ignitionOffGeohash,
      geohashNeighbours: this.geohashNeighbours,
      needsRedistribution: this.needsRedistribution,
      batteryUnderThreshold: this.batteryUnderThreshold,
      isBeingTowed: this.isBeingTowed,
      automaticallyEnableVehicleAfterRangeRecovery:
        this.automaticallyEnableVehicleAfterRangeRecovery,
      key: this.key,
      startTripLocation: this.startTripLocation,
      preAuthStatus: this.preAuthStatus,
      profileId: this.profileId,
      username: this.username,
      preAuthEnabled: this.preAuthEnabled,
      comeFromApp: this.comeFromApp,
      entityId: this.entityId,
      profileType: this.profileType,
      serviceType: this.serviceType,
      serviceId: this.serviceId,
      start_mileage: this.start_mileage,
      lastCleanedDate: this.lastCleanedDate,
    };
  }
}
