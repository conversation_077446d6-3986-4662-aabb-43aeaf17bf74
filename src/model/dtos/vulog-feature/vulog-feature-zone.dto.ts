import { VulogGeoZone } from 'src/model/vulog-geo-zone';
import { VulogZoneType } from '../vulog-trip/vulog-trip-fields/vulog-zone';

export class VulogFeatureZoneDto {
  constructor(props?: Omit<VulogFeatureZoneDto, 'toVulogGeoZone'>) {
    Object.assign(this, props);
  }

  name: string;

  version: number;

  type: VulogZoneType;

  zoneId: string;

  toVulogGeoZone(): VulogGeoZone {
    return new VulogGeoZone({
      name: this.name,
      version: this.version,
      type: this.type,
      zoneId: this.zoneId,
    });
  }
}
