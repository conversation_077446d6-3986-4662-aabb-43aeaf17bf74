import { VulogCatalog } from './vulog-user-services-fields/vulog-catalog';
import { VulogUserProfile } from './vulog-user-services-fields/vulog-user-profile';

export class VulogUserServicesDto {
  public userId: string;
  public catalog: VulogCatalog[];
  public profiles: VulogUserProfile[];

  toRawJsonObject() {
    return {
      userId: this.userId,
      catalog: this.catalog,
      profiles: this.profiles,
    };
  }

  getUserProfileId(): string {
    return this.profiles[0].profileId;
  }
}
