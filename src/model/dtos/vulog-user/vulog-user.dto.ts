import { BsgUserId, VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { VulogUser, VulogUserStatus } from '../../vulog-user';

export class VulogUserDto {
  public id: string;
  public fleetId: string;
  public userName: string;
  public firstName: string;
  public lastName: string;
  public email: string;
  public accountStatus: string;
  public gender: string;
  public dataPrivacyConsent: boolean;
  public profilingConsent: boolean;
  public marketingConsent: boolean;
  public membershipNumber: string;
  public phoneNumber: string;
  public alipayScore: number;
  public birthDate: string;
  public businessUser: boolean;
  public entityId: string;
  public businessEmail: string;
  public emailConsent: boolean;
  public locale: string;

  toVulogUser(
    bsgUserId: BsgUserId,
    profileId: string,
    email: string,
    status: VulogUserStatus = VulogUserStatus.CREATING,
    vulogUserId: VulogUserId,
    entityId: string,
    password: string,
  ) {
    return new VulogUser(
      bsgUserId,
      email,
      status,
      password,
      profileId ? new VulogProfileId(profileId) : null,
      vulogUserId,
      undefined,
      undefined,
      undefined,
      entityId,
    );
  }

  toRawJsonObject() {
    return {
      id: this.id,
      fleetId: this.fleetId,
      userName: this.userName,
      firstName: this.firstName,
      lastName: this.lastName,
      email: this.email,
      accountStatus: this.accountStatus,
      gender: this.gender,
      dataPrivacyConsent: this.dataPrivacyConsent,
      profilingConsent: this.profilingConsent,
      marketingConsent: this.marketingConsent,
      membershipNumber: this.membershipNumber,
      phoneNumber: this.phoneNumber,
      alipayScore: this.alipayScore,
      birthDate: this.birthDate,
      businessUser: this.businessUser,
      entityId: this.entityId,
      businessEmail: this.businessEmail,
      emailConsent: this.emailConsent,
      locale: this.locale,
    };
  }
}
