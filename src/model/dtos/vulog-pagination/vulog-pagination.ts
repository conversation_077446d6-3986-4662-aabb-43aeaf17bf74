import { DateTime } from 'luxon';
import { PaginationSortingOrder } from 'src/common/constants/pagination';
import { VulogCarEventType } from '../vulog-car-event/vulog-car-event.dto';

export class VulogPagination {
  constructor(props: Omit<VulogPagination, 'toDto'>) {
    Object.assign(this, props);
  }

  startDate?: DateTime;

  endDate?: DateTime;

  type?: VulogCarEventType;

  sort: [string, PaginationSortingOrder];

  page = 0;

  size = 1;

  toDto() {
    return {
      startDate: this.startDate?.toISO(),
      endDate: this.endDate?.toISO(),
      type: this.type,
      sort: this.sort?.join(','),
      page: this.page,
      size: this.size,
    };
  }
}
