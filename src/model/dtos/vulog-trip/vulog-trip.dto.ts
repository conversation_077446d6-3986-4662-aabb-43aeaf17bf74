import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { RentalError } from 'src/common/errors/rental-error';
import { ReservationError } from 'src/common/errors/reservation-error';
import { VulogCarId, VulogUserId } from 'src/common/tiny-types';
import { RentalInfo } from 'src/model/rental-info';
import { ReservationAdminInfo } from 'src/model/reservation-admin-info';

import { faker } from '@faker-js/faker';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { VulogMileage } from 'src/common/tiny-types/vulog-mileage.type';
import { bigNumberize } from 'src/common/utils/big-decimal.util';
import { StaticCar } from 'src/model/static.car';
import { VulogTrip } from '../../vulog-trip';
import { VulogAdditionalTripInfo } from './vulog-trip-fields/vulog-additional-trip-info';
import { VulogEventInfo } from './vulog-trip-fields/vulog-event-info';
import { VulogTicketInfo } from './vulog-trip-fields/vulog-ticket-info';

export class VulogTripDto {
  @ApiProperty({
    example: '5c79479290a2c8248dad6f7d', // Do not use code from `test` folder, we should not include them when we produce a production build (`/dist` folder)
  })
  public id: string;

  @ApiProperty({
    example: '9917DAC796ED758C571DDEF1EA3F149F',
  })
  public tripId: string; // tripId = journeyId = orderId

  @ApiProperty({
    example: faker.string.alphanumeric(8),
  })
  public fleetId: string;

  @ApiProperty({
    example: 'f8fb073b-9d23-4c3e-8fc7-3387f585c367',
  })
  public userId: string;

  @ApiProperty({
    example: 'e4dff9e3-c90d-43a2-a9a2-645149357ba2',
  })
  public profileId: string;

  @ApiProperty()
  public profileType: string;

  @ApiProperty({
    example: '587d0c3d-5eb6-4591-b9ab-0a080031e4ab',
  })
  public vehicleId: string;

  @ApiProperty({
    example: {
      id: 'e8908fb5-483e-4640-b8f4-981caadbd9ca',
      model: 'bluecar',
      plate: 'SVX01235',
      sourceId: '8a7119df-ee1e-4cb3-ad91-a0bc8d450112',
      energyLevel: faker.number.int(),
      isCharging: false,
      isPluggedIn: false,
      location: {
        latitude: faker.location.latitude(),
        longitude: faker.location.longitude(),
      },
      areDoorsAndWindowsClosed: true,
      alerts: {
        hasFlatTire: false,
        hasTireUnderPressure: false,
        hasCrash: false,
      },
      zoneId: '52014054-3426-4fd3-8bef-704e4c5b369a',
    },
  })
  @Type(() => StaticCar)
  car?: StaticCar;

  @ApiProperty()
  public length: number;

  /**
   * Duration in minutes
   */
  @ApiProperty()
  public duration: number;

  /**
   * Duration in minutes
   */
  @ApiProperty()
  public pauseDuration: number;

  /**
   * Duration in minutes
   */
  @ApiProperty()
  public tripDuration: number;

  /**
   * Duration in minutes
   */
  @ApiProperty()
  public bookingDuration: number;

  /**
   * Duration in minutes
   */
  @ApiProperty()
  public drivingDuration: number;

  @ApiProperty()
  public date: string;

  @ApiProperty()
  public endDate: string;

  @ApiProperty()
  @ApiProperty()
  @Type(() => VulogAdditionalTripInfo)
  public additionalInfo?: VulogAdditionalTripInfo;

  @ApiProperty({
    example: 'bcb7c6c4-e614-489b-82de-61c29c32838d',
  })
  public pricingId: string;

  @ApiProperty({
    example: ['7f909d9d-6a9f-4e4d-9252-13e4ccbf0d94'],
  })
  public productIds: string[];

  @ApiProperty({
    example: 'cc4d785a-173b-44b3-accb-b094b56b2148',
  })
  public serviceId: string;

  @ApiProperty()
  public serviceType: string;

  @ApiProperty({
    example: faker.date.past(),
  })
  public theorStartDate?: string;

  @ApiProperty({
    example: faker.date.future(),
  })
  public theorEndDate?: string;

  @ApiProperty()
  @Type(() => VulogTicketInfo)
  public ticketInfo?: VulogTicketInfo;

  @ApiProperty()
  @Type(() => VulogEventInfo)
  public tripEvents?: VulogEventInfo[];

  isRental(): boolean {
    return !this.additionalInfo.isCancel;
  }

  isReservationOrAllocation(): boolean {
    return this.additionalInfo.isCancel;
  }

  toVulogTrip(): VulogTrip {
    return new VulogTrip(
      this.tripId,
      this.userId,
      this.isRental(),
      this.isReservationOrAllocation(),
    );
  }

  toRentalInfo(): RentalInfo {
    if (!this.isRental()) {
      throw new RentalError(
        'Invalid VulogTripDto conversion to RentalInfo - this trip is not a rental',
      );
    }

    const startZones = this.additionalInfo.startZones;
    const endZones = this.additionalInfo.endZones;

    const startDate = new VulogDate(this.date);
    const endDate = new VulogDate(this.endDate);

    return new RentalInfo({
      carId: new VulogCarId(this.vehicleId),
      sourceCarId: new VulogCarId(this.vehicleId),
      sourceId: new VulogCarId(this.tripId),
      startZones,
      endZones,
      startDate,
      endDate,
      duration: bigNumberize(this.duration).multipliedBy(60).toNumber(),
      distance: new VulogMileage(this.length),
      hasTripStarted: !!startDate,
    });
  }

  toReservationAdminInfo(): ReservationAdminInfo {
    if (!this.isReservationOrAllocation) {
      throw new ReservationError(
        'Invalid VulogTripDto conversion to ReservationInfo - this trip is not a Vulog booking',
      );
    }
    return new ReservationAdminInfo(
      new VulogCarId(this.vehicleId),
      new VulogCarId(this.vehicleId),
      this.tripId,
      new VulogUserId(this.userId),
    );
  }
}
