import { VulogAdditionalTripInfo } from './vulog-trip-fields/vulog-additional-trip-info';
import { VulogEventInfo } from './vulog-trip-fields/vulog-event-info';
import { VulogTicketInfo } from './vulog-trip-fields/vulog-ticket-info';

export interface IVuLogTrip {
  id: string;
  tripId: string; // tripId = journeyId = orderId
  fleetId: string;
  userId: string;
  profileId: string;
  profileType: string;
  vehicleId: string;
  length: number;
  duration: number;
  pauseDuration: number;
  tripDuration: number;
  bookingDuration: number;
  drivingDuration: number;
  date: string;
  endDate: string;
  additionalInfo?: VulogAdditionalTripInfo;
  pricingId: string;
  productIds: string[];
  serviceId: string;
  serviceType: string;
  theorStartDate?: string;
  theorEndDate?: string;
  ticketInfo?: VulogTicketInfo;
  tripEvents?: VulogEventInfo[];
}
