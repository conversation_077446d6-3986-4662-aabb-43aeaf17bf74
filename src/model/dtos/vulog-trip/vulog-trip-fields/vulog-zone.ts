import { VulogZoneId } from 'src/common/tiny-types';

export enum VulogZoneType {
  Operational = 'operational',
  Allowed = 'allowed',
}

export class VulogZone {
  constructor(
    public zoneId: string,
    public version: number,
    public type: string,
    public sticky: boolean,
  ) {}

  public get vulogZoneId(): VulogZoneId {
    return new VulogZoneId(this.zoneId);
  }

  isSticky(): boolean {
    return this.type === VulogZoneType.Allowed && this.sticky;
  }
}
