import { Type } from 'class-transformer';
import { VulogLocation } from './vulog-location';
import { VulogZone } from './vulog-zone';

export class VulogAdditionalTripInfo {
  public firstDriveDelayMinutes: string;
  public bookDuration: string;

  @Type(() => VulogZone)
  public startZones: VulogZone[];

  @Type(() => VulogZone)
  public endZones: VulogZone[];

  public isAutolock: boolean;
  public isCancel: boolean;
  public isReleaseOnServer: boolean;
  public isBilled: boolean;
  public suspicious: boolean;

  @Type(() => VulogLocation)
  public startLocation: VulogLocation;

  @Type(() => VulogLocation)
  public endLocation: VulogLocation;

  public originCancelJourney: string;
  public ignoreOdometer: boolean;
}
