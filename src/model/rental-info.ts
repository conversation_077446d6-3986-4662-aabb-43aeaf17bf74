import {
  ReservationId,
  VulogCarId,
  VulogJourneyOrTripId,
} from 'src/common/tiny-types';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { VulogMileage } from 'src/common/tiny-types/vulog-mileage.type';
import { VulogZone } from './dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { GpsCoordinates } from './gps-coordinates';

export class CarRealtimeMetadata {
  constructor(
    readonly battery: number,
    readonly isDoorClosed: boolean,
    readonly isDoorLocked: boolean,
    readonly isEngineOn: boolean,
    readonly isCharging: boolean,
  ) {}
}
export class RentalInfo {
  constructor(props: Partial<RentalInfo>) {
    Object.assign(this, props);
  }

  vuboxId?: string;

  carId: VulogCarId;

  sourceCarId: VulogCarId;

  sourceId: VulogJourneyOrTripId;

  entityId: ReservationId;

  startZones?: VulogZone[];

  currentZones: VulogZone[];

  currentGpsCoordinates?: GpsCoordinates;

  endZones?: VulogZone[];

  startDate?: VulogDate;

  endDate?: VulogDate;

  /**
   * Duration in seconds
   */
  duration?: number;

  distance?: VulogMileage;

  hasTripStarted: boolean;

  carRealtimeMetadata?: CarRealtimeMetadata;

  local?: boolean;
}
