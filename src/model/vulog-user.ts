import { isEmpty } from 'class-validator';
import { BsgUserId, VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { config } from '../config';
import { User } from './user';

const VULOG_CONFIG = config.vulogConfig;

export const DISABLED_RFID_SUFFIX = '--DISABLED';
export const SECOND_DISABLED_RFID_SUFFIX = 'x';

export enum VulogUserStatus {
  CREATING = 'CREATING',
  WAITING_FOR_PROFILE = 'WAITING_FOR_PROFILE',
  UPDATING = 'UPDATING',
  SYNCHRONIZED = 'SYNC',
}

function randomString(length: number, chars: string): string {
  let mask = '';
  if (chars.indexOf('a') > -1) mask += 'abcdefghijklmnopqrstuvwxyz';
  if (chars.indexOf('A') > -1) mask += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  if (chars.indexOf('#') > -1) mask += '0123456789';
  if (chars.indexOf('!') > -1) mask += '~`!@#$%^&*()_+-={}[]:";\'<>?,./|\\';
  let result = '';
  for (let i = length; i > 0; --i)
    result += mask[Math.floor(Math.random() * mask.length)];
  return result;
}

export class VulogUser {
  constructor(
    public bsgUserId: BsgUserId,
    public email: string,
    public status: VulogUserStatus,
    private password: string,
    public profileId?: VulogProfileId,
    public id?: VulogUserId,
    public isRegistered: boolean = false,
    public readonly createdAt?: Date,
    public updatedAt?: Date,
    public entityId?: string,
    public rfid?: string,
  ) {}

  private static generatePassword(): string {
    return randomString(32, '#aA');
  }

  private static generateEmail(originalEmail: string): string {
    return `${VULOG_CONFIG.emailPlaceholderPrefix}${originalEmail}${VULOG_CONFIG.emailPlaceholderSuffix}`;
  }

  getUsername(): string {
    return this.bsgUserId.value;
  }

  getPassword(): string {
    return this.password;
  }

  private sanitizeRfid(rfid: string): string {
    return rfid?.replace(':', '');
  }

  formattedRfid(): string {
    return this.sanitizeRfid(this.rfid);
  }

  isEqualToUserRfid(rfid: string): boolean {
    return this.formattedRfid() === this.sanitizeRfid(rfid);
  }

  isRfidDisabled(): boolean {
    return [DISABLED_RFID_SUFFIX, SECOND_DISABLED_RFID_SUFFIX].some(
      (value) => !!this.formattedRfid()?.includes(value),
    );
  }

  disableRfidUsage(): boolean {
    if (isEmpty(this.rfid)) {
      return false;
    }

    this.rfid = this.rfid.concat(SECOND_DISABLED_RFID_SUFFIX);

    return true;
  }

  enableRfidUsage(): boolean {
    if (isEmpty(this.rfid)) {
      return false;
    }

    this.rfid = this.rfid.replace(DISABLED_RFID_SUFFIX, '');
    this.rfid = this.rfid.replace(SECOND_DISABLED_RFID_SUFFIX, '');

    return true;
  }

  static generateFrom(user: User): VulogUser {
    return new VulogUser(
      user.id,
      this.generateEmail(user.email),
      VulogUserStatus.CREATING,
      this.generatePassword(),
    );
  }
}
