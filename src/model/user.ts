import { BsgUserId } from '../common/tiny-types';
import { UserStatus, UserTitle } from '../external/user-subscription/constants';
import { AccessCard } from './uss/access-card';
import { Subscription } from './uss/subscription';

export class User {
  constructor(
    readonly id: BsgUserId,
    readonly email: string,
    readonly status: UserStatus,
    readonly firstName: string,
    readonly lastName: string,
    readonly dateOfBirth: Date,
    readonly title: UserTitle,
    readonly mobile: string,
    readonly preferredLanguage: string,
    readonly currentSubscription: Subscription,
    readonly cards?: AccessCard[],
  ) {}
}
