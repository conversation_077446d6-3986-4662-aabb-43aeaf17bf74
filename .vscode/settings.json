{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["typescript"], "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "files.eol": "\n", "prettier.prettierPath": "./node_modules/prettier", "jest.runMode": "on-demand", "jest.virtualFolders": [{"name": "unit", "jestCommandLine": "yarn test", "runMode": "on-demand"}, {"name": "e2e", "jestCommandLine": "yarn test:e2e", "runMode": "on-demand"}]}