NODE_ENV=local
PORT=8881
CRON_PORT=8890
NEW_RELIC_LICENSE_KEY=new_relic_license_key

DB_TYPE=postgres
DB_HOST=localhost
DB_PORT=5555
DB_USERNAME=postgres
DB_PASSWORD=secret
DB_NAME=rental-db

VULOG_URL=http://localhost:3000
VULOG_ADMIN_URL=http://localhost:3000
VULOG_CLIENT_ID=checkInTheWikiDoc
VULOG_CLIENT_SECRET=checkInTheWikiDoc
VULOG_FLEET_ID=checkInTheWikiDoc
VULOG_CITY_ID=123
VULOG_SERVICE_ID=mock-service-id
VULOG_PRICING_ID=mock-pricing-id
VULOG_DEFAULT_USERNAME=testUsername
VULOG_DEFAULT_PASSWORD=testUsername
VULOG_DEFAULT_USERS='[{"username":"testUsername","password":"testUsername"}]'
VULOG_CONTINUATION_TOKEN_TTL=86400
VULOG_API_KEY=fake-api-key
VULOG_CENTER_URL=https://bluesg-test.vulog.center/BLUESG-SINGA/app

REDIS_HOST=localhost
REDIS_PORT=6380

STATION_SERVICE_HOST=mock-station
STATION_SERVICE_PORT=8889
STATION_SERVICE_URL=mock-station

USER_SUBSCRIPTION_SERVICE_URL=http://mock-station:8888

JWT_SECRET=secret
JWT_SECRET_ADMIN=secret

# Fake SDK key with valid format, so that it wont be rejected by validation of ConfigCat
CONFIG_CAT_MAIN_CONFIG_KEY=3IHbCFPzjkqEnCW9xvj8kA/2ObIHlGaPE6OKFCna_FAKE
CONFIG_CAT_MIGRATION_CONFIG_KEY=3IHbCFPzjkqEnCW9xvj8kA/3UnukXGPD0udM5dY1_FAKE

ENCRYPT_STRING=secretToBeRotated

AWS_ACCESS_KEY_ID=mockKey5
AWS_SECRET_ACCESS_KEY=mockKey6
AWS_SESSION_TOKEN=

AWS_REGION=ap-southeast-1
USING_CREDENTIAL=true

SNS_TOPIC_ARN=mockSnsArn

CRON_EXPRESSION_EXPIRE_PRE_RESERVATIONS=0 */10 * * * *
CRON_EXPRESSION_POP_PRE_RESERVATIONS=0 */10 * * * *

BILLING_SERVICE_URL=http://localhost:3003

SQS_QUEUE_URL=sqs.url

VULOG_DEFAULT_USERNAME_1=testUsername
VULOG_DEFAULT_PASSWORD_1=testUsername

VULOG_DEFAULT_USERNAME_2=testUsername
VULOG_DEFAULT_PASSWORD_2=testUsername

CRON_EXPIRE_RESERVATIONS_ENABLED=false

# lib-bo-auth-guard config
AUTH0_SERVER=https://bluesg.auth0.com
AUTHENTICATION_CONFIG_KEY=3IHbCFPzjkqEnCW9xvj8kA/3UnukXGPD0ud4avqA_FAKE
