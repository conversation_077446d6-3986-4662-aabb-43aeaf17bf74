PORT=8881
CRON_PORT=8890

DB_TYPE=postgres
DB_HOST=localhost
DB_PORT=5555
DB_USERNAME=postgres
DB_PASSWORD=secret
DB_NAME=rental-db

VULOG_URL=https://java-sta.vulog.com
VULOG_ADMIN_URL=http://fakevulog.int-dev:3000
VULOG_CLIENT_ID=BLUESG-SINGA_secure
VULOG_CLIENT_SECRET=58643b5e-6c78-4877-8dfd-69568dba0f0f
VULOG_FLEET_ID=BLUESG-SINGA
VULOG_CITY_ID=123
VULOG_SERVICE_ID=1fb65e82-69ca-4278-b890-9d48ebb8e93a
VULOG_PRICING_ID=mock-pricing-id
VULOG_CONTINUATION_TOKEN_TTL=86400
VULOG_CENTER_URL=https://bluesg-test.vulog.center/BLUESG-SINGA/app

STATION_SERVICE_HOST=sts.int-dev
STATION_SERVICE_PORT=3006

SENTRY_DSN=https://<EMAIL>/4505362339266560
SENTRY_ENVIRONMENT=staging

AWS_REGION=ap-southeast-1

SNS_TOPIC_ARN=mockSnsArn

# Logs
LOG_ENABLE=true
LOG_LEVEL=info
LOG_FORMAT=json

CRON_EXPRESSION_EXPIRE_PRE_RESERVATIONS=0 */5 * * * *
CRON_EXPRESSION_POP_PRE_RESERVATIONS=0 */5 * * * *

# Cache
CACHE_STATION_TTL=300

END_RENTAL_IN_AIMA_SQS_QUEUE_URL=https://sqs.ap-southeast-1.amazonaws.com/116431628221/staging-end-rental-aima

SKIP_ENDING_RENTAL_CONDITIONS=false