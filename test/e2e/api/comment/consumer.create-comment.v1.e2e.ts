import * as supertest from 'supertest';

import { HttpStatus, INestApplication } from '@nestjs/common';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { getRepositoryToken } from '@nestjs/typeorm';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { CreateCommentDtoV1 } from 'src/controllers/consumer/v1/comment/dto/request/create-comment.v1.dto';
import { CommentEntity } from 'src/logic/comment/entity/comment.entity';
import { AppBuilder } from 'test/helpers/app.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { Repository } from 'typeorm';

const testingApiRoute = ApiRouteUtils.buildApiRouteSegment(
  '' as any,
  '' as any,
  [
    'api/v1',
    RentalServiceRootRouteSegment.Consumer,
    RouteSegment.Comment.Index,
  ],
);

describe(`[E2E] POST: ${testingApiRoute}`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;
  let commentRepo: Repository<CommentEntity>;

  const userId: string = aUUID();

  function requestFor(payload: CreateCommentDtoV1): supertest.Test {
    const superTest = supertest(app.getHttpServer())
      .post(testingApiRoute)
      .send(payload);
    return superTest;
  }

  beforeAll(async () => {
    app = await appBuilder
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(userId), true)
      .buildAndStart();
    commentRepo = app.get<Repository<CommentEntity>>(
      getRepositoryToken(CommentEntity),
    );
  });

  afterAll(async () => {
    await commentRepo.delete({});
    await app.close();
  });

  async function clean(): Promise<void> {
    await commentRepo.delete({});
  }

  describe('When send a valid create comment', () => {
    let responseObject: supertest.Response;

    beforeAll(async () => {
      responseObject = await requestFor({
        comment: 'hallelujah!',
        isAnonymous: true,
      });
    });

    afterAll(async () => {
      await clean();
    });

    it('Should return status CREATED', () => {
      expect(responseObject.status).toBe(HttpStatus.CREATED);
    });

    it('Should create a match comment', async () => {
      expect(responseObject.body.result).toMatchObject({
        comment: 'hallelujah!',
        isAnonymous: true,
        bsgUserId: userId,
      });
    });
  });
});
