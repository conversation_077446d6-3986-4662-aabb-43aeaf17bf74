import { CorrelationIdService } from '@bluesg-2/monitoring';
import { faker } from '@faker-js/faker';
import MockAdapter from 'axios-mock-adapter';
import * as QueryString from 'qs';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { config } from 'src/config';
import { ListAvailableCarsAtStationsRequestDtoV1 } from 'src/controllers/dtos/station/request/list-available-cars-at-stations.request.v1.dto';
import { ListAvailableCarsAtStationsResponseDtoV1 } from 'src/controllers/dtos/station/response/list-available-cars-at-stations.response.v1.dto';
import { AvailabilityService } from 'src/logic/availability.service';
import { CacheService } from 'src/logic/cache/cache.service';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { StationPaths } from 'src/logic/station/station-paths';
import { VulogAuthService } from 'src/logic/vulog/vulog-auth.service';
import { VulogFleetService } from 'src/logic/vulog/vulog-fleet-service.service';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { FleetService } from 'src/model/fleet-service';
import { InternalApiService } from 'src/shared/http/internal-api.service';
import * as request from 'supertest';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { VulogCarStaticInfoDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car-static-info.dto.builder';
import { VulogCarRealTimeDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { aUUID } from 'test/helpers/random-data.helper';
import { ApiTestContext, testApi } from 'test/helpers/test-api.util';

const email = faker.internet.email();

describe(
  'POST /api/v1/stations/available-cars',
  testApi((context: ApiTestContext) => {
    let mockHttpAdapterService: MockAdapter;
    let mockUssApiService: MockAdapter;

    let cacheService: CacheService;

    let availabilityService: AvailabilityService;

    let correlationIdService: CorrelationIdService;

    const url = '/api/v1/stations/available-cars';

    const testAuthResponse = createTestVulogAuthResponse();

    beforeEach(async () => {
      const httpAdapterService = context.app.get(HttpAdapterService);
      mockHttpAdapterService = new MockAdapter(
        httpAdapterService.httpService.axiosRef,
      );

      const internalApiServiceUss: InternalApiService = context.app.get(
        config.httpServiceConfig.userSubscriptionName,
      );
      mockUssApiService = new MockAdapter(
        internalApiServiceUss.httpService.axiosRef,
      );

      cacheService = context.app.get(CacheService);

      availabilityService = context.app.get(AvailabilityService);

      correlationIdService = context.app.get(CorrelationIdService);

      jest
        .spyOn(correlationIdService, 'getCorrelationId')
        .mockReturnValue({ value: aUUID() } as any);

      const userDto = createTestVulogUserDto();

      userDto.id = config.vulogConfig.defaultUsername;
      userDto.email = email;

      mockHttpAdapterService
        .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
        .reply(200, userDto);

      const userServicesAndProfiles = createTestVulogUserServicesDto();

      mockHttpAdapterService
        .onGet(
          `${VulogPaths.userServicesAndProfiles(
            config.vulogConfig.fleetId,
            new VulogUserId(userDto.id),
          )}`,
        )
        .reply(200, userServicesAndProfiles);

      mockHttpAdapterService
        .onPost(
          `${VulogPaths.registerUserProfileForService(
            config.vulogConfig.fleetId,
            new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
          )}`,
        )
        .reply(200, userServicesAndProfiles);

      mockHttpAdapterService
        .onPost(`${VulogPaths.auth()}`)
        .reply(200, testAuthResponse);

      mockUssApiService
        .onGet(
          `${
            config.httpServiceConfig.userSubscriptionUrl
          }/${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userDto.id],
          )}`,
        )
        .reply(200, { result: { id: userDto.id, email } });
    });

    afterEach(async () => {
      jest.clearAllMocks();

      mockHttpAdapterService.reset();

      mockUssApiService.reset();

      await cacheService.clearAll();
    });

    describe.each([undefined, null])(
      'should return validation error `IsNotEmpty` when `atStations` is null or undefined',
      (value) => {
        it('Check expectation', async () => {
          const response = await request(context.getHttpServer())
            .post(url)
            .send({
              atStations: value,
            } as ListAvailableCarsAtStationsRequestDtoV1);

          expect(
            response.body.message.some((item) =>
              item.includes('atStations should not be empty'),
            ),
          ).toBe(true);
        });
      },
    );

    describe('should return validation error `ArrayMinSize` when `atStations` less than specified constraint', () => {
      it('Check expectation', async () => {
        const response = await request(context.getHttpServer())
          .post(url)
          .send({
            atStations: [],
          } as ListAvailableCarsAtStationsRequestDtoV1);

        expect(
          response.body.message.some((item) =>
            item.includes('atStations must contain at least 1 elements'),
          ),
        ).toBe(true);
      });
    });

    describe.each([
      {
        caseName: 'one invalid UUID',
        sample: [aUUID() + 'invalid'],
      },
      {
        caseName: 'one or many invalid UUID',
        sample: [aUUID() + 'invalid', aUUID() + 'invalid'],
      },
      {
        caseName: 'one invalid UUID and one valid UUID',
        sample: [aUUID(), aUUID() + 'invalid'],
      },
    ])(
      'should return validation error `IsNotEmpty` when `atStations` is null or undefined',
      ({ caseName, sample }) => {
        it(caseName, async () => {
          const response = await request(context.getHttpServer())
            .post(url)
            .send({
              atStations: sample,
            } as ListAvailableCarsAtStationsRequestDtoV1);

          expect(
            response.body.message.some((item) =>
              item.includes('each value in atStations must be a UUID'),
            ),
          ).toBe(true);
        });
      },
    );

    describe('should be able to get available cars at 1 station', () => {
      it('Check expectation', async () => {
        const stationDtos = [new StationDtoBuilder().build()];

        const zone = new VulogZone(stationDtos[0].zoneId, 1, 'allowed', false);

        const realtimeCarDtos = [
          new VulogCarRealTimeDtoBuilder()
            .withZones([zone])
            .withAutonomy(55)
            .build(),
          new VulogCarRealTimeDtoBuilder()
            .withZones([zone])
            .withAutonomy(55)
            .build(),
        ];

        const staticCarDtos = [
          new VulogCarStaticInfoDtoBuilder()
            .withId(realtimeCarDtos[0].id)
            .build(),
          new VulogCarStaticInfoDtoBuilder()
            .withId(realtimeCarDtos[1].id)
            .build(),
        ];

        // StationService - Get all stations
        mockHttpAdapterService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 1,
              pageSize: 200,
            })}`,
          )
          .reply(200, {
            result: stationDtos.map((stn) => stn.toRawJsonObject()),
          });

        mockHttpAdapterService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 2,
              pageSize: 200,
            })}`,
          )
          .reply(200, {
            result: [], // Breaking loop
          });

        // Vulog Car Service - getCarsInRealtime
        mockHttpAdapterService
          .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
          .reply(200, realtimeCarDtos);

        // Vulog Car Service - getCarsInRealtime
        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeListingAllStaticVehicles(
              config.vulogConfig.fleetId,
            )}?${QueryString.stringify({
              page: 0,
              size: 200,
            })}`,
          )
          .reply(200, staticCarDtos, { last: true });

        // NOTE: mock auth service
        const vulogAuthService: VulogAuthService =
          context.app.get(VulogAuthService);

        jest
          .spyOn(vulogAuthService, 'getCachedAccessToken')
          .mockResolvedValue('Le nay cho em hay le nay cho anh');

        // NOTE: mock fleet service
        const vulogFleetService: VulogFleetService =
          context.app.get(VulogFleetService);

        jest
          .spyOn(vulogFleetService, 'getCustomerUsableFleetService')
          .mockResolvedValue(
            Object.assign(new FleetService(), {
              vehicleIds: realtimeCarDtos.map((car) => car.id),
            }),
          );

        const response = await request(context.getHttpServer())
          .post(url)
          .send({
            atStations: [stationDtos[0].id],
          } as ListAvailableCarsAtStationsRequestDtoV1);

        const availableCarsAtStationMap =
          await availabilityService.getAvailableCarsAtStations();

        expect(response.body.result).toEqual(
          JSON.parse(
            JSON.stringify(
              ListAvailableCarsAtStationsResponseDtoV1.from(
                availableCarsAtStationMap,
              ),
            ),
          ),
        );
      });
    });

    describe('should be able to get available cars at many stations', () => {
      it('Check expectation', async () => {
        const stationDtos = [
          new StationDtoBuilder().build(),
          new StationDtoBuilder().build(),
        ];

        const zone1 = new VulogZone(stationDtos[0].zoneId, 1, 'allowed', false);
        const zone2 = new VulogZone(stationDtos[1].zoneId, 1, 'allowed', false);

        const realtimeCarDtos = [
          new VulogCarRealTimeDtoBuilder()
            .withZones([zone1])
            .withAutonomy(55)
            .build(),
          new VulogCarRealTimeDtoBuilder()
            .withZones([zone2])
            .withAutonomy(55)
            .build(),
        ];

        const staticCarDtos = [
          new VulogCarStaticInfoDtoBuilder()
            .withId(realtimeCarDtos[0].id)
            .build(),
          new VulogCarStaticInfoDtoBuilder()
            .withId(realtimeCarDtos[1].id)
            .build(),
        ];

        // StationService - Get all stations
        mockHttpAdapterService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 1,
              pageSize: 200,
            })}`,
          )
          .reply(200, {
            result: stationDtos.map((stn) => stn.toRawJsonObject()),
          });

        mockHttpAdapterService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 2,
              pageSize: 200,
            })}`,
          )
          .reply(200, {
            result: [], // Breaking loop
          });

        // Vulog Car Service - getCarsInRealtime
        mockHttpAdapterService
          .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
          .reply(200, realtimeCarDtos);

        // Vulog Car Service - getCarsInRealtime
        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeListingAllStaticVehicles(
              config.vulogConfig.fleetId,
            )}?${QueryString.stringify({
              page: 0,
              size: 200,
            })}`,
          )
          .reply(200, staticCarDtos, { last: true });

        const response = await request(context.getHttpServer())
          .post(url)
          .send({
            atStations: stationDtos.map((item) => item.id),
          } as ListAvailableCarsAtStationsRequestDtoV1);

        const availableCarsAtStationMap =
          await availabilityService.getAvailableCarsAtStations();

        expect(response.body.result).toEqual(
          JSON.parse(
            JSON.stringify(
              ListAvailableCarsAtStationsResponseDtoV1.from(
                availableCarsAtStationMap,
              ),
            ),
          ),
        );
      });
    });

    describe('should be able to get available cars at many stations (including non-existing one)', () => {
      it('Check expectation', async () => {
        const stationDtos = [
          new StationDtoBuilder().build(),
          new StationDtoBuilder().build(),
        ];

        const zone1 = new VulogZone(stationDtos[0].zoneId, 1, 'allowed', false);
        const zone2 = new VulogZone(stationDtos[1].zoneId, 1, 'allowed', false);

        const realtimeCarDtos = [
          new VulogCarRealTimeDtoBuilder()
            .withZones([zone1])
            .withAutonomy(55)
            .build(),
          new VulogCarRealTimeDtoBuilder()
            .withZones([zone2])
            .withAutonomy(55)
            .build(),
        ];

        const staticCarDtos = [
          new VulogCarStaticInfoDtoBuilder()
            .withId(realtimeCarDtos[0].id)
            .build(),
          new VulogCarStaticInfoDtoBuilder()
            .withId(realtimeCarDtos[1].id)
            .build(),
        ];

        // StationService - Get all stations
        mockHttpAdapterService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 1,
              pageSize: 200,
            })}`,
          )
          .reply(200, {
            result: stationDtos.map((stn) => stn.toRawJsonObject()),
          });

        mockHttpAdapterService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 2,
              pageSize: 200,
            })}`,
          )
          .reply(200, {
            result: [], // Breaking loop
          });

        // Vulog Car Service - getCarsInRealtime
        mockHttpAdapterService
          .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
          .reply(200, realtimeCarDtos);

        // Vulog Car Service - getCarsInRealtime
        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeListingAllStaticVehicles(
              config.vulogConfig.fleetId,
            )}?${QueryString.stringify({
              page: 0,
              size: 200,
            })}`,
          )
          .reply(200, staticCarDtos, { last: true });

        const response = await request(context.getHttpServer())
          .post(url)
          .send({
            atStations: [
              ...stationDtos.map((item) => item.id),
              aUUID(), // non-existing one
            ],
          } as ListAvailableCarsAtStationsRequestDtoV1);

        const availableCarsAtStationMap =
          await availabilityService.getAvailableCarsAtStations();

        expect(response.body.result).toEqual(
          JSON.parse(
            JSON.stringify(
              ListAvailableCarsAtStationsResponseDtoV1.from(
                availableCarsAtStationMap,
              ),
            ),
          ),
        );
      });
    });

    describe('should return empty result when there is no matching stations', () => {
      it('Check expectation', async () => {
        const stationDtos = [
          new StationDtoBuilder().build(),
          new StationDtoBuilder().build(),
        ];

        const realtimeCarDtos = [
          new VulogCarRealTimeDtoBuilder().withAutonomy(55).build(),
          new VulogCarRealTimeDtoBuilder().withAutonomy(55).build(),
        ];

        const staticCarDtos = [
          new VulogCarStaticInfoDtoBuilder()
            .withId(realtimeCarDtos[0].id)
            .build(),
          new VulogCarStaticInfoDtoBuilder()
            .withId(realtimeCarDtos[1].id)
            .build(),
        ];

        // StationService - Get all stations
        mockHttpAdapterService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 1,
              pageSize: 200,
            })}`,
          )
          .reply(200, {
            result: stationDtos.map((stn) => stn.toRawJsonObject()),
          });

        mockHttpAdapterService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 2,
              pageSize: 200,
            })}`,
          )
          .reply(200, {
            result: [], // Breaking loop
          });

        // Vulog Car Service - getCarsInRealtime
        mockHttpAdapterService
          .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
          .reply(200, realtimeCarDtos);

        // Vulog Car Service - getCarsInRealtime
        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeListingAllStaticVehicles(
              config.vulogConfig.fleetId,
            )}?${QueryString.stringify({
              page: 0,
              size: 200,
            })}`,
          )
          .reply(200, staticCarDtos, { last: true });

        const response = await request(context.getHttpServer())
          .post(url)
          .send({
            atStations: [aUUID(), aUUID(), aUUID()],
          } as ListAvailableCarsAtStationsRequestDtoV1);

        expect(response.body.result).toEqual({});
      });
    });
  }),
);
