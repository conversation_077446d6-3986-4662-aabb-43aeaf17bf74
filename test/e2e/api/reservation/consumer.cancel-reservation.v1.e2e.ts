import MockAdapter from 'axios-mock-adapter';
import {
  BsgUserId,
  ReservationId,
  VulogJourneyOrTripId,
  VulogUserId,
} from 'src/common/tiny-types';
import { config } from 'src/config';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import * as request from 'supertest';
import { createTestVulogCarStaticInfoDto } from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestStationDto } from 'test/helpers/builders/dtos/test-station-dto.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { ApiTestContext, testApi } from 'test/helpers/test-api.util';
import { Repository } from 'typeorm';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';

import { EventName } from '@bluesg-2/event-library';
import { faker } from '@faker-js/faker';
import { HttpStatus } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DateTime } from 'luxon';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { ReservationResponseDtoV1 } from 'src/controllers/dtos/reservation.response.v1.dto';
import { CacheService } from 'src/logic/cache/cache.service';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { ReservationService } from 'src/logic/reservation.service';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { ReservationInfo } from 'src/model/reservation-info';
import { Reservation } from 'src/model/reservation.domain';
import { InternalApiService } from 'src/shared/http/internal-api.service';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import {
  VulogBookingBuilder,
  VulogJourneyDtoBuilder,
} from 'test/helpers/builders/dtos/vulog/vulog-journey.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import {
  CUSTOMER_JWT_MOCK_TOKEN,
  mockCustomerJwtGuard,
} from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';
import { EventBusService } from '~shared/event-bus/event-bus.service';
import { VulogUserBuilder } from '../../../helpers/builders/vulog-user.builder';

const userId = aUUID();

const email = faker.internet.email();

describe(
  'DELETE /api/v1/reservation',
  testApi(
    (context: ApiTestContext) => {
      let mockHttpAdapterService: MockAdapter;
      let mockUssApiService: MockAdapter;
      let mockStsApiService: MockAdapter;

      let cacheService: CacheService;
      let reservationService: ReservationService;

      let eventBusService: EventBusService;

      const reservationUrl = '/api/v1/reservation';

      const testAuthResponse = createTestVulogAuthResponse();
      const userServicesAndProfiles = createTestVulogUserServicesDto();

      const vulogUser = VulogUserBuilder.allSync()
        .withBsgUserId(new BsgUserId(userId))
        .build();

      let userRepo: Repository<VulogUserEntity>;
      let reservationRepo: Repository<ReservationEntity>;

      beforeEach(async () => {
        const httpAdapterService = context.app.get(HttpAdapterService);
        mockHttpAdapterService = new MockAdapter(
          httpAdapterService.httpService.axiosRef,
        );

        const internalApiServiceUss: InternalApiService = context.app.get(
          config.httpServiceConfig.userSubscriptionName,
        );
        mockUssApiService = new MockAdapter(
          internalApiServiceUss.httpService.axiosRef,
        );

        const internalApiServiceSts: InternalApiService = context.app.get(
          config.httpServiceConfig.stationName,
        );
        mockStsApiService = new MockAdapter(
          internalApiServiceSts.httpService.axiosRef,
        );

        cacheService = context.app.get(CacheService);

        reservationService = context.app.get(ReservationService);

        userRepo = context.app.get<Repository<VulogUserEntity>>(
          getRepositoryToken(VulogUserEntity),
        );

        reservationRepo = context.app.get<Repository<ReservationEntity>>(
          getRepositoryToken(ReservationEntity),
        );

        const userDto = createTestVulogUserDto();

        userDto.id = userId;
        userDto.email = email;

        mockHttpAdapterService
          .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
          .reply(200, userDto);

        mockHttpAdapterService
          .onGet(
            `${VulogPaths.userServicesAndProfiles(
              config.vulogConfig.fleetId,
              new VulogUserId(userDto.id),
            )}`,
          )
          .reply(200, userServicesAndProfiles);

        mockHttpAdapterService
          .onPost(
            `${VulogPaths.registerUserProfileForService(
              config.vulogConfig.fleetId,
              vulogUser.profileId,
            )}`,
          )
          .reply(200, userServicesAndProfiles);

        mockHttpAdapterService
          .onPost(`${VulogPaths.auth()}`)
          .reply(200, testAuthResponse);

        mockUssApiService
          .onGet(
            `${
              config.httpServiceConfig.userSubscriptionUrl
            }/${ApiRouteUtils.buildApiRouteSegment(
              RootRouteSegment.Internal,
              ApiVersion.V1,
              ['users', userId],
            )}`,
          )
          .reply(200, { result: { id: userId, email } });

        await userRepo.insert(VulogUserEntity.from(vulogUser));

        mockVulogCredential(context.app);

        eventBusService = context.app.get(EventBusService);
        eventBusService.nonBlockingEmit = jest.fn();
      });

      afterEach(async () => {
        mockHttpAdapterService.reset();

        mockUssApiService.reset();

        mockStsApiService.reset();

        await userRepo.clear();

        await reservationRepo.clear();

        await cacheService.clearAll();
      });

      describe('🔥 should be able to cancel all reservations, and grace period is applied for non-local reservations', () => {
        it('Check expectation', async () => {
          const sourceId = aUUID();

          const zoneId = aUUID();

          const mockCarStaticInfoDto = createTestVulogCarStaticInfoDto(
            sourceId,
            config.vulogConfig.fleetId.value,
          );

          const now = DateTime.now();

          const mockVulogJourneyDto = new VulogJourneyDtoBuilder()
            .withVehicleId(mockCarStaticInfoDto.id)
            .withServiceId(config.vulogConfig.serviceId.value)
            .withBooking(
              new VulogBookingBuilder()
                .withBookingDate(now.toUTC().toISO())
                .withMaxTripStartDate(now.plus({ minutes: 15 }).toUTC().toISO())
                .build(),
            )
            .build();

          const mockStationDto = createTestStationDto({
            zoneId: zoneId,
          });

          const reservation = await reservationService.saveOne(
            Reservation.from(
              new ReservationEntityBuilder()
                .makeBsgUserId(userId)
                .makeCarId(mockCarStaticInfoDto.id)
                .makeCarModel(mockCarStaticInfoDto.model.name as CarModelEnum)
                .makeStartStationId(mockStationDto.id)
                .makeVulogTripId(mockVulogJourneyDto.id)
                .makeStatus(ReservationStatus.Reserved)
                .makeReservedAt(
                  new Date(mockVulogJourneyDto.booking.bookingDate),
                )
                .makeExpiresAt(
                  new Date(mockVulogJourneyDto.booking.maxTripStartDate),
                )
                .makeLocal(false)
                .build(),
            ),
          );

          // VulogCarService.getUserCurrentReservations
          mockHttpAdapterService
            .onGet(`${VulogPaths.currentJourneys()}`)
            .replyOnce(200, {
              journeys: [mockVulogJourneyDto.toRawJsonObject()],
            });

          // VulogCarService.cancelCarReservationOrAllocation
          mockHttpAdapterService
            .onDelete(
              `${VulogPaths.cancelBooking(
                new VulogJourneyOrTripId(mockVulogJourneyDto.id),
              )}`,
            )
            .replyOnce(() => {
              reservation.allocateAgain = false; // Simulate CancelTripReport event handler

              reservationRepo.save(reservation.toEntity());

              return [200];
            });

          const authorizedUserToken = JSON.stringify({
            id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
          });

          const response = await request(context.getHttpServer())
            .delete(`${reservationUrl}`)
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          const updatedReservation = await reservationService.findOneById(
            new ReservationId(reservation.getId),
          );

          expect(!!updatedReservation.abuseReleasesAt).toBe(true);

          const reservationInfo = new ReservationInfo({
            ...mockVulogJourneyDto.toReservationInfo(),
            entityId: new ReservationId(updatedReservation.getId),
            startStationId: updatedReservation.startStationId,
            carModel: updatedReservation.carModel,
            status: updatedReservation.status,
            abuseReleasesAt: updatedReservation.abuseReleasesAt,
            canceledAt: updatedReservation.canceledAt,
            amount: updatedReservation?.amount,
            pricingPolicy: updatedReservation?.pricingPolicy,
            bsgUserId: updatedReservation?.bsgUserId,
            local: updatedReservation.local,
            plateNumber: updatedReservation.plateNumber,
          });

          expect(!!response.body.result[0].abuseReleasesAt).toEqual(true);

          expect(!!response.body.result[0].canceledAt).toEqual(true);

          expect(response.body.result[0].status).toEqual(
            ReservationStatus.Cancelled,
          );

          expect(response.body.result).toEqual([
            JSON.parse(
              JSON.stringify(
                ReservationResponseDtoV1.fromReservationInfo(reservationInfo),
              ),
            ),
          ]);

          expect(eventBusService.nonBlockingEmit as jest.Mock).toBeCalledTimes(
            1,
          );
          const emittedEvent = (eventBusService.nonBlockingEmit as jest.Mock)
            .mock.calls[0][0];
          expect(emittedEvent.event).toBe(
            EventName.RentalReservationCancelledEvent,
          );
          expect(emittedEvent.payload.id).toBe(reservation.getId);
          expect(emittedEvent.payload.carId).toBe(reservation.carId.value);
          expect(emittedEvent.payload.createdAt).toBeTruthy();
          expect(emittedEvent.payload.reservedAt).toBeTruthy();
          expect(emittedEvent.payload.cancelledAt).toBeTruthy();
          expect(emittedEvent.payload.status).toBe(ReservationStatus.Cancelled);
          expect(emittedEvent.payload.userId).toBe(reservation.bsgUserId.value);
        });
      });

      describe('🔥 should be able to cancel all reservations, and grace period is not applied for local reservations', () => {
        it('Check expectation', async () => {
          const sourceId = aUUID();

          const zoneId = aUUID();

          const mockCarStaticInfoDto = createTestVulogCarStaticInfoDto(
            sourceId,
            config.vulogConfig.fleetId.value,
          );

          const now = DateTime.now();

          const mockVulogJourneyDto = new VulogJourneyDtoBuilder()
            .withVehicleId(mockCarStaticInfoDto.id)
            .withServiceId(config.vulogConfig.serviceId.value)
            .withBooking(
              new VulogBookingBuilder()
                .withBookingDate(now.toUTC().toISO())
                .withMaxTripStartDate(now.plus({ minutes: 15 }).toUTC().toISO())
                .build(),
            )
            .build();

          const mockStationDto = createTestStationDto({
            zoneId: zoneId,
          });

          const reservation = await reservationService.saveOne(
            Reservation.from(
              new ReservationEntityBuilder()
                .makeBsgUserId(userId)
                .makeCarId(mockCarStaticInfoDto.id)
                .makeCarModel(mockCarStaticInfoDto.model.name as CarModelEnum)
                .makeStartStationId(mockStationDto.id)
                .makeVulogTripId(mockVulogJourneyDto.id)
                .makeStatus(ReservationStatus.Reserved)
                .makeReservedAt(
                  new Date(mockVulogJourneyDto.booking.bookingDate),
                )
                .makeExpiresAt(
                  new Date(mockVulogJourneyDto.booking.maxTripStartDate),
                )
                .makeLocal(true)
                .build(),
            ),
          );

          // VulogCarService.getUserCurrentReservations
          mockHttpAdapterService
            .onGet(`${VulogPaths.currentJourneys()}`)
            .replyOnce(200, {
              journeys: [mockVulogJourneyDto.toRawJsonObject()],
            });

          // VulogCarService.cancelCarReservationOrAllocation
          mockHttpAdapterService
            .onDelete(
              `${VulogPaths.cancelBooking(
                new VulogJourneyOrTripId(mockVulogJourneyDto.id),
              )}`,
            )
            .replyOnce(() => {
              reservation.allocateAgain = false; // Simulate CancelTripReport event handler

              reservationRepo.save(reservation.toEntity());

              return [200];
            });

          const authorizedUserToken = JSON.stringify({
            id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
          });

          const response = await request(context.getHttpServer())
            .delete(`${reservationUrl}`)
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          const updatedReservation = await reservationService.findOneById(
            new ReservationId(reservation.getId),
          );

          expect(!!updatedReservation.abuseReleasesAt).toBe(false);

          const reservationInfo = new ReservationInfo({
            ...mockVulogJourneyDto.toReservationInfo(),
            entityId: new ReservationId(updatedReservation.getId),
            startStationId: updatedReservation.startStationId,
            carModel: updatedReservation.carModel,
            status: updatedReservation.status,
            abuseReleasesAt: updatedReservation.abuseReleasesAt,
            canceledAt: updatedReservation.canceledAt,
            amount: updatedReservation?.amount,
            pricingPolicy: updatedReservation?.pricingPolicy,
            bsgUserId: updatedReservation?.bsgUserId,
            local: updatedReservation.local,
            plateNumber: updatedReservation.plateNumber,
          });

          expect(!!response.body.result[0].abuseReleasesAt).toEqual(false);

          expect(!!response.body.result[0].canceledAt).toEqual(true);

          expect(response.body.result[0].status).toEqual(
            ReservationStatus.Cancelled,
          );

          expect(response.body.result).toEqual([
            JSON.parse(
              JSON.stringify(
                ReservationResponseDtoV1.fromReservationInfo(reservationInfo),
              ),
            ),
          ]);

          expect(eventBusService.nonBlockingEmit as jest.Mock).toBeCalledTimes(
            1,
          );
          const emittedEvent = (eventBusService.nonBlockingEmit as jest.Mock)
            .mock.calls[0][0];
          expect(emittedEvent.event).toBe(
            EventName.RentalReservationCancelledEvent,
          );
          expect(emittedEvent.payload.id).toBe(reservation.getId);
          expect(emittedEvent.payload.carId).toBe(reservation.carId.value);
          expect(emittedEvent.payload.createdAt).toBeTruthy();
          expect(emittedEvent.payload.reservedAt).toBeTruthy();
          expect(emittedEvent.payload.cancelledAt).toBeTruthy();
          expect(emittedEvent.payload.status).toBe(ReservationStatus.Cancelled);
          expect(emittedEvent.payload.userId).toBe(reservation.bsgUserId.value);
        });
      });

      describe('💦 should forbid user from deleting reservation, as the user has on-going rental', () => {
        it('Check expectation', async () => {
          const sourceId = aUUID();

          const zoneId = aUUID();

          const mockCarStaticInfoDto = createTestVulogCarStaticInfoDto(
            sourceId,
            config.vulogConfig.fleetId.value,
          );

          const now = DateTime.now();

          const mockVulogJourneyDto = new VulogJourneyDtoBuilder()
            .withVehicleId(mockCarStaticInfoDto.id)
            .withServiceId(config.vulogConfig.serviceId.value)
            .withBooking(
              new VulogBookingBuilder()
                .withBookingDate(now.toUTC().toISO())
                .withMaxTripStartDate(now.plus({ minutes: 15 }).toUTC().toISO())
                .build(),
            )
            .build();

          const mockStationDto = createTestStationDto({
            zoneId: zoneId,
          });

          await reservationService.saveOne(
            Reservation.from(
              new ReservationEntityBuilder()
                .makeBsgUserId(userId)
                .makeCarId(mockCarStaticInfoDto.id)
                .makeCarModel(mockCarStaticInfoDto.model.name as CarModelEnum)
                .makeStartStationId(mockStationDto.id)
                .makeVulogTripId(mockVulogJourneyDto.id)
                .makeStatus(ReservationStatus.Converted)
                .makeReservedAt(
                  new Date(mockVulogJourneyDto.booking.bookingDate),
                )
                .makeExpiresAt(
                  new Date(mockVulogJourneyDto.booking.maxTripStartDate),
                )
                .makeStartedAt(
                  DateTime.fromJSDate(
                    new Date(mockVulogJourneyDto.booking.bookingDate),
                  )
                    .plus({ minutes: 5 })
                    .toJSDate(),
                )
                .makeLocal(true)
                .makeEndedAt(null)
                .build(),
            ),
          );

          const authorizedUserToken = JSON.stringify({
            id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
          });

          const response = await request(context.getHttpServer())
            .delete(`${reservationUrl}`)
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          expect(response.body.message).toEqual(
            `An error occurred during the reservation flow - Cause: Customer (ID: ${new BsgUserId(
              userId,
            )}) does not have on-going reservation, but having on-going rental`,
          );

          expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);
        });
      });

      describe('💦 should forbid user from deleting reservation, as the user does not have on-going reservation', () => {
        it('Check expectation', async () => {
          const authorizedUserToken = JSON.stringify({
            id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
          });

          const response = await request(context.getHttpServer())
            .delete(`${reservationUrl}`)
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          expect(response.body.message).toEqual(
            `An error occurred during the reservation flow - Cause: Customer (ID: ${new BsgUserId(
              userId,
            )}) does not have on-going reservation`,
          );

          expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);
        });
      });
    },
    [
      {
        name: CustomerJwtGuard,
        value: mockCustomerJwtGuard(userId),
        isGuard: true,
      },
    ],
  ),
);
