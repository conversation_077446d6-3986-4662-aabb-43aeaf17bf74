import * as supertest from 'supertest';

import { HttpStatus, INestApplication } from '@nestjs/common';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { AppBuilder } from 'test/helpers/app.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aMD5, aNumber, aPlate, aUUID } from 'test/helpers/random-data.helper';
import { Repository } from 'typeorm';

import MockAdapter from 'axios-mock-adapter';
import {
  BsgUserId,
  VulogCarId,
  VulogJourneyOrTripId,
  VulogProfileId,
  VulogUserId,
} from 'src/common/tiny-types';
import { config } from 'src/config';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';

import { EventName } from '@bluesg-2/event-library';
import { faker } from '@faker-js/faker';
import { DateTime } from 'luxon';
import * as QueryString from 'qs';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { ReservationResponseDtoV1 } from 'src/controllers/dtos/reservation.response.v1.dto';
import {
  SubscriptionStatus,
  UserStatus,
} from 'src/external/user-subscription/constants';
import { CacheService } from 'src/logic/cache/cache.service';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { ReservationService } from 'src/logic/reservation.service';
import { ReservationCacheService } from 'src/logic/reservation/reservation.cache.service';
import { StationPaths } from 'src/logic/station/station-paths';
import {
  VulogRealTimeBookingStatus,
  VulogRealTimeVehicleStatus,
} from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { Reservation } from 'src/model/reservation.domain';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { VulogCarStaticInfoDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car-static-info.dto.builder';
import { VulogCarRealTimeDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import {
  VulogBookingBuilder,
  VulogJourneyDtoBuilder,
} from 'test/helpers/builders/dtos/vulog/vulog-journey.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { CUSTOMER_JWT_MOCK_TOKEN } from 'test/helpers/customer-jwt-guard.mocker';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';
import { EventBusService } from '~shared/event-bus/event-bus.service';
import { VulogUserBuilder } from '../../../helpers/builders/vulog-user.builder';
import { UssApiMocker } from '../../../helpers/external-services/uss.api-mocker';
import { VulogApiMocker } from '../../../helpers/external-services/vulog.api-mocker';

const userId = aUUID();

const email = faker.internet.email();

const authorizedUserToken = JSON.stringify({
  id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
});

const reservationUrl = '/api/v1/reservation/:reservationId/cars/:plateNumber';

describe(`[E2E] POST: ${reservationUrl}`, () => {
  let app: INestApplication;

  let mockHttpAdapterService: MockAdapter;
  let mockUssApiService: MockAdapter;

  let cacheService: CacheService;
  let reservationService: ReservationService;
  let reservationCacheService: ReservationCacheService;

  let userRepo: Repository<VulogUserEntity>;
  let reservationRepo: Repository<ReservationEntity>;
  let carRepo: Repository<CarEntity>;

  let eventBusService: EventBusService;
  let spyOn_EventBusService__nonBlockingEmit: jest.SpyInstance;

  const testAuthResponse = createTestVulogAuthResponse();
  const testVulogUser = VulogUserBuilder.allSync()
    .withBsgUserId(new BsgUserId(userId))
    .build();

  function requestFor(
    reservationId: string,
    plateNumber: string,
  ): supertest.Test {
    const injectedUrl = reservationUrl
      .replace(':reservationId', reservationId)
      .replace(':plateNumber', plateNumber);

    const superTest = supertest(app.getHttpServer())
      .put(`${injectedUrl}`)
      .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
      .send();

    return superTest;
  }

  beforeAll(async () => {
    app = await new AppBuilder()
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(aUUID()), true)
      .buildAndStart();

    userRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );

    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );

    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );

    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));

    mockHttpAdapterService = new VulogApiMocker(app).mock();
    mockUssApiService = new UssApiMocker(app).mock();

    cacheService = app.get(CacheService);
    reservationService = app.get(ReservationService);
    reservationCacheService = app.get(ReservationCacheService);

    eventBusService = app.get<EventBusService>(EventBusService);
    spyOn_EventBusService__nonBlockingEmit = jest.spyOn(
      eventBusService,
      'nonBlockingEmit',
    );
  });

  afterAll(async () => {
    await cleanResources();

    await app.close();
  });

  async function mockCredential() {
    const userDto = createTestVulogUserDto();

    userDto.email = email;

    mockHttpAdapterService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, userDto);

    const userServicesAndProfiles = createTestVulogUserServicesDto();
    userServicesAndProfiles.userId = userDto.id;

    mockHttpAdapterService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(userDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockHttpAdapterService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockHttpAdapterService
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    mockHttpAdapterService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, testAuthResponse);

    await userRepo.insert(VulogUserEntity.from(testVulogUser));

    mockVulogCredential(app);
  }

  async function cleanResources(): Promise<void> {
    jest.clearAllMocks();

    mockHttpAdapterService.reset();
    mockUssApiService.reset();

    await userRepo.clear();
    await reservationRepo.clear();
    await cacheService.clearAll();
    await carRepo.delete({});
  }

  describe('should be able to switch car at the same station as specified in current reservation', () => {
    const now = DateTime.now();

    const zoneId = aUUID();

    const stationDto = new StationDtoBuilder().withZoneId(zoneId).build(); // same

    const realtimeCarDto = new VulogCarRealTimeDtoBuilder()
      .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
      .withBookingStatus(VulogRealTimeBookingStatus.Available)
      .withZones([new VulogZone(zoneId, 1, 'allowed', false)]) // same
      .withAutonomy(55)
      .withStartZones([new VulogZone(stationDto.zoneId, 1, 'allowed', true)])
      .build();

    const staticCarDto = new VulogCarStaticInfoDtoBuilder()
      .withId(realtimeCarDto.id)
      .withModel(CarModelEnum.BlueCar)
      .withPlate(realtimeCarDto.plate)
      .build();

    const reservedAt = now.toJSDate();
    const expiresAt = now.plus({ minutes: 15 }).toJSDate();

    const vulogJourneyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(realtimeCarDto.id)
      .withServiceId(config.vulogConfig.serviceId.value)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(reservedAt.toISOString())
          .withMaxTripStartDate(expiresAt.toISOString())
          .build(),
      )
      .build();

    const reservationEntity = new ReservationEntityBuilder()
      .makeBsgUserId(userId)
      .makeStatus(ReservationStatus.Reserved)
      .makeStartStationId(stationDto.id)
      .makeReservedAt(reservedAt)
      .makeExpiresAt(expiresAt)
      .build();

    const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withId(config.vulogConfig.serviceId.value)
      .withZones([stationDto.zoneId])
      .withVehicles([realtimeCarDto].map((rltCar) => rltCar.id))
      .build();

    let reservation: Reservation;

    beforeAll(async () => {
      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(
            JSON.stringify(
              new InternalUserDetailResponseDtoV1Builder()
                .withId(userId)
                .withUserStatus(UserStatus.VERIFIED)
                .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
                .build(),
            ),
          ),
        } as BaseResponseDto);

      await mockCredential();

      await carRepo.insert(CarEntity.from(staticCarDto.toStaticCar().toCar()));

      reservation = await reservationService.saveOne(
        Reservation.from(reservationEntity),
      );

      // VulogFleetService.getFleetServices
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .replyOnce(200, [mockVulogFleetServiceDto]);

      // Vulog Car Service - get real time car
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [realtimeCarDto]);

      // VulogCarService - get all static cars
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .replyOnce(200, [staticCarDto], { last: true });

      // VulogCarService - cancel current reservation
      mockHttpAdapterService
        .onDelete(
          `${VulogPaths.vehicleBookingBackOffice(
            config.vulogConfig.fleetId,
            reservation.carId,
          )}`,
        )
        .replyOnce(async () => {
          reservation.allocateAgain = false;

          await reservationService.saveOne(reservation);

          return [200];
        });

      // VulogCarService - book target car
      mockHttpAdapterService
        .onPost(`${VulogPaths.bookJourney(new VulogCarId(staticCarDto.id))}`)
        .replyOnce(200, vulogJourneyDto);

      // StationService - Get station by zones
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [stationDto.toRawJsonObject()],
        });

      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });
    });

    it('Check expectation', async () => {
      const response = await requestFor(
        reservation.getId,
        realtimeCarDto.plate,
      );

      const updatedReservation = await reservationService.findOneByTripId(
        new VulogJourneyOrTripId(vulogJourneyDto.id),
      );

      expect(updatedReservation.allocateAgain).toEqual(false);
      expect(updatedReservation.carId.equals(reservation.carId)).toEqual(false);
      expect(updatedReservation.carModel).toEqual(staticCarDto.model.name);
      expect(updatedReservation.plateNumber.value).toEqual(staticCarDto.plate);
      expect(updatedReservation.reservedAt.toDateTime().toMillis()).toEqual(
        reservation.reservedAt.toDateTime().toMillis(),
      );
      expect(updatedReservation.expiresAt.toDateTime().toMillis()).toEqual(
        reservation.expiresAt.toDateTime().toMillis(),
      );
      expect(
        updatedReservation.vulogTripId.value !== reservation.vulogTripId.value,
      ).toEqual(true);

      const carSwitchingPerformed =
        await reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.retrieve(
          updatedReservation.bsgUserId,
        );

      expect(carSwitchingPerformed).toEqual(true);

      expect(response.body.result).toEqual(
        JSON.parse(
          JSON.stringify(
            ReservationResponseDtoV1.fromReservation(
              updatedReservation,
              staticCarDto.model.name as CarModelEnum,
            ),
          ),
        ),
      );

      expect(spyOn_EventBusService__nonBlockingEmit).toBeCalledTimes(1);
      const emittedEvent =
        spyOn_EventBusService__nonBlockingEmit.mock.calls[0][0];
      expect(emittedEvent.event).toBe(EventName.RentalReservationSwitchedEvent);
      expect(emittedEvent.payload.id).toBe(reservation.getId);
      expect(emittedEvent.payload.carId).toBe(updatedReservation.carId.value);
      expect(emittedEvent.payload.previousCarId).toBe(reservation.carId.value);
      expect(emittedEvent.payload.createdAt).toBeTruthy();
      expect(emittedEvent.payload.reservedAt).toBeTruthy();
      expect(emittedEvent.payload.status).toBe(ReservationStatus.Reserved);
      expect(emittedEvent.payload.userId).toBe(reservation.bsgUserId.value);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('should be able to switch car at the different station from station specified in current reservation', () => {
    const now = DateTime.now();

    const zoneId = aUUID();

    const newStationDto = new StationDtoBuilder().build();

    const stationDto = new StationDtoBuilder().withZoneId(zoneId).build(); // different

    const realtimeCarDto = new VulogCarRealTimeDtoBuilder()
      .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
      .withBookingStatus(VulogRealTimeBookingStatus.Available)
      .withZones([new VulogZone(newStationDto.zoneId, 1, 'allowed', false)]) // different
      .withAutonomy(55)
      .build();

    const staticCarDto = new VulogCarStaticInfoDtoBuilder()
      .withId(realtimeCarDto.id)
      .withModel(CarModelEnum.BlueCar)
      .withPlate(realtimeCarDto.plate)
      .build();

    const reservedAt = now.toJSDate();
    const expiresAt = now.plus({ minutes: 15 }).toJSDate();

    const vulogJourneyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(realtimeCarDto.id)
      .withServiceId(config.vulogConfig.serviceId.value)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(reservedAt.toISOString())
          .withMaxTripStartDate(expiresAt.toISOString())
          .build(),
      )
      .build();

    const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withId(config.vulogConfig.serviceId.value)
      .withZones([stationDto.zoneId])
      .withVehicles([realtimeCarDto].map((rltCar) => rltCar.id))
      .build();

    let reservation: Reservation;

    beforeAll(async () => {
      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(
            JSON.stringify(
              new InternalUserDetailResponseDtoV1Builder()
                .withId(userId)
                .withUserStatus(UserStatus.VERIFIED)
                .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
                .build(),
            ),
          ),
        } as BaseResponseDto);

      await mockCredential();

      await carRepo.insert(CarEntity.from(staticCarDto.toStaticCar().toCar()));

      reservation = await reservationService.saveOne(
        Reservation.from(
          new ReservationEntityBuilder()
            .makeBsgUserId(userId)
            .makeStatus(ReservationStatus.Reserved)
            .makeStartStationId(stationDto.id)
            .makeReservedAt(now.toJSDate())
            .makeExpiresAt(now.plus({ minutes: 15 }).toJSDate())
            .build(),
        ),
      );

      // VulogFleetService.getFleetServices
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .replyOnce(200, [mockVulogFleetServiceDto]);

      // Vulog Car Service - get real time car
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [realtimeCarDto]);

      // VulogCarService - get all static cars
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .replyOnce(200, [staticCarDto], { last: true });

      // VulogCarService - cancel current reservation
      mockHttpAdapterService
        .onDelete(
          `${VulogPaths.vehicleBookingBackOffice(
            config.vulogConfig.fleetId,
            reservation.carId,
          )}`,
        )
        .replyOnce(async () => {
          reservation.allocateAgain = false;

          await reservationService.saveOne(reservation);

          return [200];
        });

      // VulogCarService - book target car
      mockHttpAdapterService
        .onPost(`${VulogPaths.bookJourney(new VulogCarId(staticCarDto.id))}`)
        .replyOnce(200, vulogJourneyDto);

      // StationService - Get station by zones
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [newStationDto.toRawJsonObject()],
        });

      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });
    });

    it('check expectation', async () => {
      const response = await requestFor(
        reservation.getId,
        realtimeCarDto.plate,
      );

      const updatedReservation = await reservationService.findOneByTripId(
        new VulogJourneyOrTripId(vulogJourneyDto.id),
      );

      expect(updatedReservation.carId.equals(reservation.carId)).toEqual(false);
      expect(updatedReservation.carModel).toEqual(staticCarDto.model.name);
      expect(updatedReservation.plateNumber.value).toEqual(staticCarDto.plate);
      expect(updatedReservation.allocateAgain).toEqual(false);
      expect(updatedReservation.reservedAt.toDateTime().toMillis()).toEqual(
        reservation.reservedAt.toDateTime().toMillis(),
      );
      expect(updatedReservation.expiresAt.toDateTime().toMillis()).toEqual(
        reservation.expiresAt.toDateTime().toMillis(),
      );
      expect(
        updatedReservation.vulogTripId.value !== reservation.vulogTripId.value,
      ).toEqual(true);

      const carSwitchingPerformed =
        await reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.retrieve(
          updatedReservation.bsgUserId,
        );

      expect(carSwitchingPerformed).toEqual(true);

      expect(response.body.result).toEqual(
        JSON.parse(
          JSON.stringify(
            ReservationResponseDtoV1.fromReservation(
              updatedReservation,
              staticCarDto.model.name as CarModelEnum,
            ),
          ),
        ),
      );

      expect(spyOn_EventBusService__nonBlockingEmit).toBeCalledTimes(1);
      const emittedEvent =
        spyOn_EventBusService__nonBlockingEmit.mock.calls[0][0];
      expect(emittedEvent.event).toBe(EventName.RentalReservationSwitchedEvent);
      expect(emittedEvent.payload.id).toBe(reservation.getId);
      expect(emittedEvent.payload.carId).toBe(updatedReservation.carId.value);
      expect(emittedEvent.payload.previousCarId).toBe(reservation.carId.value);
      expect(emittedEvent.payload.createdAt).toBeTruthy();
      expect(emittedEvent.payload.reservedAt).toBeTruthy();
      expect(emittedEvent.payload.status).toBe(ReservationStatus.Reserved);
      expect(emittedEvent.payload.userId).toBe(reservation.bsgUserId.value);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe.each([
    {
      reservationId: aNumber().toString(),
      carPlate: aPlate(),
      caseName: 'reservationId',
    },
  ])(
    'should throw error when the parameter is not UUID',
    ({ reservationId, carPlate, caseName }) => {
      it('Check expectation', async () => {
        const response = await requestFor(reservationId, carPlate);

        expect(response.body.message).toEqual([`${caseName} must be a UUID`]);
      });
    },
  );

  describe('should throw error when `reservationId` is not found', () => {
    it('Check expectation', async () => {
      const response = await requestFor(aUUID(), aPlate());

      const message = `No reservation is found`;

      expect(response.body.message).toEqual(
        `An error occurred during the reservation flow - Cause: ${message}`,
      );
    });
  });

  describe('should be able to return the same reservation when the user tries to switch the same car', () => {
    const now = DateTime.now();

    const zoneId = aUUID();

    const stationDto = new StationDtoBuilder().withZoneId(zoneId).build(); // same

    const plateNumber = 'existing-plate-number';

    const staticCarDto = new VulogCarStaticInfoDtoBuilder()
      .withModel(CarModelEnum.BlueCar)
      .withPlate(plateNumber)
      .build();

    const vulogTripId = aMD5();

    let reservation;

    beforeAll(async () => {
      await carRepo.insert(CarEntity.from(staticCarDto.toStaticCar().toCar()));

      reservation = await reservationService.saveOne(
        Reservation.from(
          new ReservationEntityBuilder()
            .makeBsgUserId(userId)
            .makeStatus(ReservationStatus.Reserved)
            .makeStartStationId(stationDto.id)
            .makeReservedAt(now.toJSDate())
            .makeExpiresAt(now.plus({ minutes: 15 }).toJSDate())
            .makePlateNumber(staticCarDto.plate)
            .makeCarModel(staticCarDto.model.name as CarModelEnum)
            .makeVulogTripId(vulogTripId)
            .makeCarId(staticCarDto.id)
            .build(),
        ),
      );
    });

    it('check expectation', async () => {
      const response = await requestFor(reservation.getId, plateNumber);

      const updatedReservation = await reservationService.findOneByTripId(
        new VulogJourneyOrTripId(vulogTripId),
      );

      expect(response.body.result).toEqual(
        JSON.parse(
          JSON.stringify(
            ReservationResponseDtoV1.fromReservation(
              updatedReservation,
              reservation.carModel,
            ),
          ),
        ),
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('should throw error when target car is not available right now', () => {
    const now = DateTime.now();

    const zoneId = aUUID();

    const stationDto = new StationDtoBuilder().withZoneId(zoneId).build(); // different

    const realtimeCarDto = new VulogCarRealTimeDtoBuilder()
      .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
      .withBookingStatus(VulogRealTimeBookingStatus.Available)
      .withZones([new VulogZone(aUUID(), 1, 'allowed', false)]) // different
      .withAutonomy(0) // not available
      .build();

    const staticCarDto = new VulogCarStaticInfoDtoBuilder()
      .withId(realtimeCarDto.id)
      .withModel(CarModelEnum.BlueCar)
      .withPlate(realtimeCarDto.plate)
      .build();

    let reservation;

    beforeAll(async () => {
      await carRepo.insert(CarEntity.from(staticCarDto.toStaticCar().toCar()));

      reservation = await reservationService.saveOne(
        Reservation.from(
          new ReservationEntityBuilder()
            .makeBsgUserId(userId)
            .makeStatus(ReservationStatus.Reserved)
            .makeStartStationId(stationDto.id)
            .makeReservedAt(now.toJSDate())
            .makeExpiresAt(now.plus({ minutes: 15 }).toJSDate())
            .build(),
        ),
      );

      const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withId(config.vulogConfig.serviceId.value)
        .withZones([stationDto.zoneId])
        .withVehicles([realtimeCarDto].map((rltCar) => rltCar.id))
        .build();

      // VulogFleetService.getFleetServices
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .replyOnce(200, [mockVulogFleetServiceDto]);

      // Vulog Car Service - get real time car
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [realtimeCarDto]);

      // VulogCarService - get all static cars
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .replyOnce(200, [staticCarDto], { last: true });
    });

    it('check expectation', async () => {
      const response = await requestFor(
        reservation.getId,
        realtimeCarDto.plate,
      );

      const message = `The target car (Car ID = [${realtimeCarDto.id}]) is not available right now`;

      expect(response.body.message).toEqual(
        `An error occurred during the reservation flow - Cause: ${message}`,
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('when car availability is stale, switching to targeted car is failed, should book previous car and return reasonable error', () => {
    const now = DateTime.now();

    const zoneId = aUUID();
    const targetZoneId = aMD5();

    const stationDto = new StationDtoBuilder().withZoneId(zoneId).build(); // same
    const station2Dto = new StationDtoBuilder()
      .withZoneId(targetZoneId)
      .build(); // same

    const oldCarRealtimeCarDto = new VulogCarRealTimeDtoBuilder()
      .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
      .withBookingStatus(VulogRealTimeBookingStatus.Booked)
      .withZones([new VulogZone(zoneId, 1, 'allowed', false)]) // same
      .withAutonomy(55)
      .withStartZones([new VulogZone(stationDto.zoneId, 1, 'allowed', true)])
      .build();

    const oldStaticCarDto = new VulogCarStaticInfoDtoBuilder()
      .withId(oldCarRealtimeCarDto.id)
      .withModel(CarModelEnum.BlueCar)
      .withPlate(oldCarRealtimeCarDto.plate)
      .build();

    const targetCarRealtimeCarDto = new VulogCarRealTimeDtoBuilder()
      .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
      .withBookingStatus(VulogRealTimeBookingStatus.Available)
      .withZones([new VulogZone(targetZoneId, 1, 'allowed', false)]) // same
      .withAutonomy(55)
      .withStartZones([new VulogZone(station2Dto.zoneId, 1, 'allowed', true)])
      .build();

    const targetStaticCarDto = new VulogCarStaticInfoDtoBuilder()
      .withId(targetCarRealtimeCarDto.id)
      .withModel(CarModelEnum.BlueCar)
      .withPlate(targetCarRealtimeCarDto.plate)
      .build();

    const reservedAt = now.toJSDate();
    const expiresAt = now.plus({ minutes: 15 }).toJSDate();

    const vulogJourneyDtoForOldCar = new VulogJourneyDtoBuilder()
      .withVehicleId(oldCarRealtimeCarDto.id)
      .withServiceId(config.vulogConfig.serviceId.value)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(reservedAt.toISOString())
          .withMaxTripStartDate(expiresAt.toISOString())
          .build(),
      )
      .build();

    const reservationEntity = new ReservationEntityBuilder()
      .makeBsgUserId(userId)
      .makeStatus(ReservationStatus.Reserved)
      .makeStartStationId(stationDto.id)
      .makeReservedAt(reservedAt)
      .makeExpiresAt(expiresAt)
      .makeCarId(oldCarRealtimeCarDto.id)
      .makePlateNumber(oldStaticCarDto.plate)
      .build();

    const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withId(config.vulogConfig.serviceId.value)
      .withZones([stationDto.zoneId])
      .withVehicles(
        [oldCarRealtimeCarDto, targetCarRealtimeCarDto].map(
          (rltCar) => rltCar.id,
        ),
      )
      .build();

    let reservation;

    beforeAll(async () => {
      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(
            JSON.stringify(
              new InternalUserDetailResponseDtoV1Builder()
                .withId(userId)
                .withUserStatus(UserStatus.VERIFIED)
                .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
                .build(),
            ),
          ),
        } as BaseResponseDto);

      await mockCredential();

      await carRepo.insert([
        CarEntity.from(oldStaticCarDto.toStaticCar().toCar()),
        CarEntity.from(targetStaticCarDto.toStaticCar().toCar()),
      ]);

      reservation = await reservationService.saveOne(
        Reservation.from(reservationEntity),
      );

      // VulogFleetService.getFleetServices
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .replyOnce(200, [mockVulogFleetServiceDto]);

      // Vulog Car Service - get real time car
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [oldCarRealtimeCarDto, targetCarRealtimeCarDto]);

      // VulogCarService - get all static cars
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .replyOnce(200, [oldStaticCarDto], { last: true });

      // VulogCarService - cancel current reservation
      mockHttpAdapterService
        .onDelete(
          `${VulogPaths.vehicleBookingBackOffice(
            config.vulogConfig.fleetId,
            reservation.carId,
          )}`,
        )
        .replyOnce(async () => {
          reservation.allocateAgain = false;

          await reservationService.saveOne(reservation);

          return [200];
        });

      // VulogCarService - book target car
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.bookJourney(
            new VulogCarId(targetCarRealtimeCarDto.id),
          )}`,
        )
        .replyOnce(400);

      // Re-book previous car
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.bookJourney(new VulogCarId(oldCarRealtimeCarDto.id))}`,
        )
        .replyOnce(200, vulogJourneyDtoForOldCar);

      // StationService - Get station by zones
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [stationDto.toRawJsonObject()],
        });

      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });
    });

    it('Check expectation', async () => {
      const response = await requestFor(
        reservation.getId,
        targetCarRealtimeCarDto.plate,
      );

      expect(response.body.message).toEqual(
        `An error occurred during the reservation flow - Cause: Sorry that your attempt to switch from car ${reservationEntity.plateNumber} to car ${targetStaticCarDto.plate} is failed. Please choose another car to switch.`,
      );
      expect(response.statusCode).toEqual(HttpStatus.SERVICE_UNAVAILABLE);

      const updatedReservation = await reservationService.findOneByTripId(
        new VulogJourneyOrTripId(vulogJourneyDtoForOldCar.id),
      );

      expect(updatedReservation.allocateAgain).toEqual(false);
      expect(updatedReservation.reservedAt.toDateTime().toMillis()).toEqual(
        reservation.reservedAt.toDateTime().toMillis(),
      );
      expect(updatedReservation.expiresAt.toDateTime().toMillis()).toEqual(
        reservation.expiresAt.toDateTime().toMillis(),
      );
      expect(updatedReservation.carId.value).toEqual(oldCarRealtimeCarDto.id);
      expect(updatedReservation.carModel).toEqual(oldStaticCarDto.model.name);
      expect(updatedReservation.plateNumber.value).toEqual(
        oldStaticCarDto.plate,
      );
      expect(
        updatedReservation.vulogTripId.value !== reservationEntity.vulogTripId,
      ).toEqual(true);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('when car availability is stale, switching to targeted car is failed, then booking previous car is failed, should cancel current reservation due to technical issue, and return reasonable error', () => {
    const now = DateTime.now();

    const zoneId = aUUID();
    const targetZoneId = aUUID();

    const stationDto1 = new StationDtoBuilder().withZoneId(zoneId).build();
    const stationDto2 = new StationDtoBuilder()
      .withZoneId(targetZoneId)
      .build();

    const oldCarRealtimeCarDto = new VulogCarRealTimeDtoBuilder()
      .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
      .withBookingStatus(VulogRealTimeBookingStatus.Booked)
      .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
      .withAutonomy(55)
      .withStartZones([new VulogZone(stationDto1.zoneId, 1, 'allowed', true)])
      .build();

    const oldStaticCarDto = new VulogCarStaticInfoDtoBuilder()
      .withId(oldCarRealtimeCarDto.id)
      .withModel(CarModelEnum.BlueCar)
      .withPlate(oldCarRealtimeCarDto.plate)
      .build();

    const targetCarRealtimeCarDto = new VulogCarRealTimeDtoBuilder()
      .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
      .withBookingStatus(VulogRealTimeBookingStatus.Available)
      .withZones([new VulogZone(targetZoneId, 1, 'allowed', false)])
      .withAutonomy(55)
      .withStartZones([new VulogZone(stationDto2.zoneId, 1, 'allowed', true)])
      .build();

    const targetStaticCarDto = new VulogCarStaticInfoDtoBuilder()
      .withId(targetCarRealtimeCarDto.id)
      .withModel(CarModelEnum.BlueCar)
      .withPlate(targetCarRealtimeCarDto.plate)
      .build();

    const reservedAt = now.toJSDate();
    const expiresAt = now.plus({ minutes: 15 }).toJSDate();

    const oldCarVulogJourneyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(oldCarRealtimeCarDto.id)
      .withServiceId(config.vulogConfig.serviceId.value)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(reservedAt.toISOString())
          .withMaxTripStartDate(expiresAt.toISOString())
          .build(),
      )
      .build();

    const reservationEntity = new ReservationEntityBuilder()
      .makeBsgUserId(userId)
      .makeStatus(ReservationStatus.Reserved)
      .makeStartStationId(stationDto1.id)
      .makeReservedAt(reservedAt)
      .makeExpiresAt(expiresAt)
      .makeCarId(oldCarRealtimeCarDto.id)
      .makePlateNumber(oldStaticCarDto.plate)
      .makeVulogTripId(oldCarVulogJourneyDto.id)
      .build();

    const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withId(config.vulogConfig.serviceId.value)
      .withZones([stationDto1.zoneId, stationDto2.zoneId])
      .withVehicles(
        [oldCarRealtimeCarDto, targetCarRealtimeCarDto].map(
          (rltCar) => rltCar.id,
        ),
      )
      .build();

    let reservation;

    beforeAll(async () => {
      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(
            JSON.stringify(
              new InternalUserDetailResponseDtoV1Builder()
                .withId(userId)
                .withUserStatus(UserStatus.VERIFIED)
                .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
                .build(),
            ),
          ),
        } as BaseResponseDto);

      await mockCredential();

      await carRepo.insert([
        CarEntity.from(oldStaticCarDto.toStaticCar().toCar()),
        CarEntity.from(targetStaticCarDto.toStaticCar().toCar()),
      ]);

      reservation = await reservationService.saveOne(
        Reservation.from(reservationEntity),
      );

      // VulogFleetService.getFleetServices
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .replyOnce(200, [mockVulogFleetServiceDto]);

      // Vulog Car Service - get real time car
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [oldCarRealtimeCarDto, targetCarRealtimeCarDto]);

      // VulogCarService - get all static cars
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .replyOnce(200, [oldStaticCarDto], { last: true });

      // VulogCarService - cancel current reservation
      mockHttpAdapterService
        .onDelete(
          `${VulogPaths.vehicleBookingBackOffice(
            config.vulogConfig.fleetId,
            reservation.carId,
          )}`,
        )
        .replyOnce(async () => {
          reservation.allocateAgain = false;

          await reservationService.saveOne(reservation);

          return [200];
        });

      // VulogCarService - book target car
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.bookJourney(
            new VulogCarId(targetCarRealtimeCarDto.id),
          )}`,
        )
        .replyOnce(400);

      // Re-book previous car
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.bookJourney(new VulogCarId(oldCarRealtimeCarDto.id))}`,
        )
        .replyOnce(400);

      // StationService - Get station by zones
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [
            stationDto1.toRawJsonObject(),
            stationDto2.toRawJsonObject(),
          ],
        });

      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });
    });

    it('Check expectation', async () => {
      const response = await requestFor(
        reservation.getId,
        targetCarRealtimeCarDto.plate,
      );

      expect(response.body.message).toEqual(
        'The rental was canceled due to technical error. Sorry for the inconvenience - Cause: Your reservation was canceled due to technical problems. Please try to book a new car again.',
      );
      expect(response.statusCode).toEqual(HttpStatus.INTERNAL_SERVER_ERROR);

      const updatedReservation = await reservationService.findOneByTripId(
        new VulogJourneyOrTripId(oldCarVulogJourneyDto.id),
      );

      expect(updatedReservation.allocateAgain).toEqual(false);
      expect(updatedReservation.reservedAt.toDateTime().toMillis()).toEqual(
        reservation.reservedAt.toDateTime().toMillis(),
      );
      expect(updatedReservation.expiresAt.toDateTime().toMillis()).toEqual(
        reservation.expiresAt.toDateTime().toMillis(),
      );
      expect(updatedReservation.carId.value).toEqual(oldCarRealtimeCarDto.id);
      expect(updatedReservation.carModel).toEqual(oldStaticCarDto.model.name);
      expect(updatedReservation.plateNumber.value).toEqual(
        oldStaticCarDto.plate,
      );
      expect(updatedReservation.canceledAt).toBeDefined();
      expect(updatedReservation.startStationId.value).toEqual(stationDto1.id);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });
});
