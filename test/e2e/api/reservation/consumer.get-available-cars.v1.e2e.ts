import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import MockAdapter from 'axios-mock-adapter';
import { CarModel, VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { config } from 'src/config';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import * as request from 'supertest';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserEntity } from 'test/helpers/builders/entities/test-vulog-user-entity.builder';
import { ApiTestContext, testApi } from 'test/helpers/test-api.util';
import { Repository } from 'typeorm';

import { faker } from '@faker-js/faker';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DateTime } from 'luxon';
import * as QueryString from 'qs';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { CarDto } from 'src/controllers/dtos/rental/response/on-going-rental.response.v1.dto';
import { ReservationResponseDtoV1 } from 'src/controllers/dtos/reservation.response.v1.dto';
import { AvailableCarsForReservationResponseDtoV1 } from 'src/controllers/dtos/reservation/response/available-cars-for-reservation.response.v1.dto';
import { CacheService } from 'src/logic/cache/cache.service';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { ReservationService } from 'src/logic/reservation.service';
import { StationPaths } from 'src/logic/station/station-paths';
import {
  VulogRealTimeBookingStatus,
  VulogRealTimeVehicleStatus,
} from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { Reservation } from 'src/model/reservation.domain';
import { InternalApiService } from 'src/shared/http/internal-api.service';
import { AttachRentalPackageRequestDtoV1Builder } from 'test/helpers/builders/dtos/attach-rental-package-request-dto.builder';
import { createTestVulogCarStaticInfoDto } from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestStationDto } from 'test/helpers/builders/dtos/test-station-dto.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { VulogCarRealTimeDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import {
  CUSTOMER_JWT_MOCK_TOKEN,
  mockCustomerJwtGuard,
} from 'test/helpers/customer-jwt-guard.mocker';
import { aMD5, anUsername, aUUID } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';
import { StationDtoBuilder } from '../../../helpers/builders/dtos/station/station.dto.builder';

const userId = aUUID();

const email = faker.internet.email();

const authorizedUserToken = JSON.stringify({
  id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
});

describe(
  'GET /api/v1/reservation/:reservationId/available-cars',
  testApi(
    (context: ApiTestContext) => {
      let mockHttpAdapterService: MockAdapter;
      let mockUssApiService: MockAdapter;
      let mockStsApiService: MockAdapter;

      let cacheService: CacheService;
      let reservationService: ReservationService;

      const reservationUrl =
        '/api/v1/reservation/:reservationId/available-cars';

      const testAuthResponse = createTestVulogAuthResponse();
      const userServicesAndProfiles = createTestVulogUserServicesDto();
      const testVulogUserEntity = createTestVulogUserEntity(
        userId,
        userServicesAndProfiles.profiles[0].profileId,
      );

      let userRepo: Repository<VulogUserEntity>;
      let reservationRepo: Repository<ReservationEntity>;
      let carRepo: Repository<CarEntity>;

      const allocatedCarId: string = aUUID();

      let user: VulogUserEntity;

      beforeAll(async () => {
        carRepo = context.app.get<Repository<CarEntity>>(
          getRepositoryToken(CarEntity),
        );
      });

      beforeEach(async () => {
        const httpAdapterService = context.app.get(HttpAdapterService);
        mockHttpAdapterService = new MockAdapter(
          httpAdapterService.httpService.axiosRef,
        );

        const internalApiServiceUss: InternalApiService = context.app.get(
          config.httpServiceConfig.userSubscriptionName,
        );
        mockUssApiService = new MockAdapter(
          internalApiServiceUss.httpService.axiosRef,
        );

        const internalApiServiceSts: InternalApiService = context.app.get(
          config.httpServiceConfig.stationName,
        );
        mockStsApiService = new MockAdapter(
          internalApiServiceSts.httpService.axiosRef,
        );

        cacheService = context.app.get(CacheService);

        userRepo = context.app.get<Repository<VulogUserEntity>>(
          getRepositoryToken(VulogUserEntity),
        );

        reservationRepo = context.app.get<Repository<ReservationEntity>>(
          getRepositoryToken(ReservationEntity),
        );

        reservationService = context.app.get(ReservationService);

        const userDto = createTestVulogUserDto();

        userDto.id = userId;
        userDto.email = email;

        mockHttpAdapterService
          .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
          .reply(200, userDto);

        mockHttpAdapterService
          .onGet(
            `${VulogPaths.userServicesAndProfiles(
              config.vulogConfig.fleetId,
              new VulogUserId(userDto.id),
            )}`,
          )
          .reply(200, userServicesAndProfiles);

        mockHttpAdapterService
          .onPost(
            `${VulogPaths.registerUserProfileForService(
              config.vulogConfig.fleetId,
              new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
            )}`,
          )
          .reply(200, userServicesAndProfiles);

        mockHttpAdapterService
          .onPost(`${VulogPaths.auth()}`)
          .reply(200, testAuthResponse);

        mockUssApiService
          .onGet(
            `${
              config.httpServiceConfig.userSubscriptionUrl
            }/${ApiRouteUtils.buildApiRouteSegment(
              RootRouteSegment.Internal,
              ApiVersion.V1,
              ['users', userId],
            )}`,
          )
          .reply(200, { result: { id: userId, email } });

        user = await userRepo.save(testVulogUserEntity);

        mockVulogCredential(context.app);
      });

      afterEach(async () => {
        jest.clearAllMocks();

        mockHttpAdapterService.reset();

        mockUssApiService.reset();

        mockStsApiService.reset();

        await reservationRepo.clear();

        await carRepo.delete({});

        await userRepo.delete({});

        await cacheService.clearAll();
      });

      describe('`reservationId` is not UUID', () => {
        it('Check expectation', async () => {
          const response = await request(context.getHttpServer())
            .get(`${reservationUrl.replace(':reservationId', anUsername())}`)
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          expect(response.body.message).toEqual([
            'reservationId must be a UUID',
          ]);
        });
      });

      describe('client-specified reservation is not found', () => {
        it('Check expectation', async () => {
          const response = await request(context.getHttpServer())
            .get(`${reservationUrl.replace(':reservationId', aUUID())}`)
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          expect(response.body.message).toEqual(
            'An error occurred during the reservation flow - Cause: No reservation is found',
          );
        });
      });

      describe.each([
        {
          isExpired: true,
        },
        {
          isCancelled: true,
        },
      ])('current reservation has invalid status', (scenarioData) => {
        it('Check expectation', async () => {
          const stationDto = createTestStationDto({ zoneId: aMD5() });

          const carStaticDto = createTestVulogCarStaticInfoDto(
            allocatedCarId,
            config.vulogConfig.fleetId.value,
          );

          const vulogTripId = aMD5();

          const ownReservation = await reservationService.saveOne(
            Reservation.from(
              new ReservationEntityBuilder()
                .makeBsgUserId(userId)
                .makeCarId(allocatedCarId)
                .makeCarModel(carStaticDto.model.name as CarModelEnum)
                .makeReservedAt(
                  scenarioData.isExpired
                    ? DateTime.now().minus({ minutes: 30 }).toJSDate()
                    : DateTime.now().toJSDate(),
                )
                .makeExpiresAt(
                  scenarioData.isExpired
                    ? DateTime.now().minus({ minutes: 15 }).toJSDate()
                    : DateTime.now().plus({ minutes: 15 }).toJSDate(),
                )
                .makeStatus(
                  scenarioData.isCancelled
                    ? ReservationStatus.Cancelled
                    : ReservationStatus.Expired,
                )
                .makeVulogTripId(vulogTripId)
                .makeStartStationId(stationDto.id)
                .build(),
            ),
          );

          await reservationService.saveOne(ownReservation);

          const response = await request(context.getHttpServer())
            .get(
              `${reservationUrl.replace(
                ':reservationId',
                ownReservation.getId,
              )}`,
            )
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          expect(response.body.message).toEqual(
            'An error occurred during the reservation flow - Cause: Client-specified reservation is not valid',
          );
        });
      });

      describe('should be able to get available cars for reservation', () => {
        it('Check expectation', async () => {
          const stationDto = createTestStationDto({ zoneId: aMD5() });

          const carStaticDto = createTestVulogCarStaticInfoDto(
            allocatedCarId,
            config.vulogConfig.fleetId.value,
          );

          const staticDtos = [
            carStaticDto,
            createTestVulogCarStaticInfoDto(
              undefined,
              config.vulogConfig.fleetId.value,
              CarModelEnum.BlueCar,
            ),
            createTestVulogCarStaticInfoDto(
              undefined,
              config.vulogConfig.fleetId.value,
              CarModelEnum.BlueCar,
            ),
          ];

          await carRepo.save(
            staticDtos.map((it) => CarEntity.from(it.toStaticCar().toCar())),
          );

          const vulogTripId = aMD5();

          const currentAllocatedRealtimeCar = new VulogCarRealTimeDtoBuilder()
            .withId(allocatedCarId)
            .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
            .withFleetId(config.vulogConfig.fleetId.value)
            .withServiceId(config.vulogConfig.serviceId.value)
            .withZones([new VulogZone(stationDto.zoneId, 1, 'allowed', true)])
            .withStartZones([
              new VulogZone(stationDto.zoneId, 1, 'allowed', true),
            ])
            .withProfileId(user.profileId)
            .withVulogTripId(vulogTripId)
            .withAutonomy(55)
            .build();

          const carRealtimeDtos = [
            currentAllocatedRealtimeCar,
            new VulogCarRealTimeDtoBuilder()
              .withId(staticDtos[1].id)
              .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
              .withBookingStatus(VulogRealTimeBookingStatus.Available)
              .withFleetId(config.vulogConfig.fleetId.value)
              .withServiceId(config.vulogConfig.serviceId.value)
              .withZones([new VulogZone(stationDto.zoneId, 1, 'allowed', true)])
              .withStartZones([
                new VulogZone(stationDto.zoneId, 1, 'allowed', true),
              ])
              .withProfileId(user.profileId)
              .withAutonomy(55)
              .build(),

            new VulogCarRealTimeDtoBuilder()
              .withId(staticDtos[2].id)
              .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
              .withBookingStatus(VulogRealTimeBookingStatus.Available)
              .withFleetId(config.vulogConfig.fleetId.value)
              .withServiceId(config.vulogConfig.serviceId.value)
              .withZones([new VulogZone(stationDto.zoneId, 1, 'allowed', true)])
              .withStartZones([
                new VulogZone(stationDto.zoneId, 1, 'allowed', true),
              ])
              .withProfileId(user.profileId)
              .withAutonomy(55)
              .build(),
          ];

          const ownReservation = await reservationService.saveOne(
            Reservation.from(
              new ReservationEntityBuilder()
                .makeBsgUserId(userId)
                .makeCarId(allocatedCarId)
                .makeCarModel(carStaticDto.model.name as CarModelEnum)
                .makeReservedAt(DateTime.now().toJSDate())
                .makeExpiresAt(DateTime.now().plus({ minutes: 15 }).toJSDate())
                .makeStatus(ReservationStatus.Reserved)
                .makeVulogTripId(vulogTripId)
                .makeStartStationId(stationDto.id)
                .build(),
            ),
          );

          await reservationService.saveOne(ownReservation);

          const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
            .withId(config.vulogConfig.serviceId.value)
            .withZones([stationDto.zoneId])
            .withVehicles(carRealtimeDtos.map((rltCar) => rltCar.id))
            .build();

          // VulogFleetService.getFleetServices
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeFleetServices(
                config.vulogConfig.fleetId,
              )}`,
            )
            .replyOnce(200, [mockVulogFleetServiceDto]);

          // VulogCarService - get realtime cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`,
            )
            .replyOnce(
              200,
              carRealtimeDtos.map((car) => car.toRawJsonObject()),
            );

          // VulogCarService - get all static cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeListingAllStaticVehicles(
                config.vulogConfig.fleetId,
              )}?${QueryString.stringify({ page: 0, size: 200 })}`,
            )
            .replyOnce(200, staticDtos, { last: true });

          // VulogCarService - get realtime cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`,
            )
            .replyOnce(
              200,
              carRealtimeDtos.map((car) => car.toRawJsonObject()),
            );

          // VulogCarService - get all static cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeListingAllStaticVehicles(
                config.vulogConfig.fleetId,
              )}?${QueryString.stringify({ page: 0, size: 200 })}`,
            )
            .replyOnce(200, staticDtos, { last: true });

          // StationService.getAllStations
          mockHttpAdapterService
            .onGet(
              `${StationPaths.stations()}?${QueryString.stringify({
                page: 1,
                pageSize: 200,
              })}`,
            )
            .replyOnce(200, { result: [stationDto] });

          mockHttpAdapterService
            .onGet(
              `${StationPaths.stations()}?${QueryString.stringify({
                page: 2,
                pageSize: 200,
              })}`,
            )
            .replyOnce(200, { result: [] });

          const response = await request(context.getHttpServer())
            .get(
              `${reservationUrl.replace(
                ':reservationId',
                ownReservation.getId,
              )}`,
            )
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          const result = response.body
            .result as AvailableCarsForReservationResponseDtoV1;

          const currentAllocatedCar = currentAllocatedRealtimeCar.toRealtimeCar(
            new CarModel(carStaticDto.model.name),
          );

          expect(result.currentAllocatedCar).toEqual(
            JSON.parse(
              JSON.stringify(
                CarDto.fromVulogCar(
                  currentAllocatedCar,
                  currentAllocatedCar.energyLevel,
                  currentAllocatedCar.isDoorLocked,
                  null,
                ),
              ),
            ),
          );

          expect(
            result.availableCars.sort((a, b) => a.id.localeCompare(b.id)),
          ).toEqual(
            JSON.parse(
              JSON.stringify(
                carRealtimeDtos
                  .map((item, index) => {
                    const availCar = item.toRealtimeCar(
                      new CarModel(staticDtos[index].model.name),
                    );

                    return CarDto.fromVulogCar(
                      availCar,
                      availCar.energyLevel,
                      availCar.isDoorLocked,
                      null,
                    );
                  })
                  .sort((a, b) => a.id.localeCompare(b.id)),
              ),
            ),
          );

          expect(result.currentReservation).toEqual(
            JSON.parse(
              JSON.stringify(
                ReservationResponseDtoV1.fromReservation(ownReservation),
              ),
            ),
          );
        });
      });

      describe('should be able to get available cars for reservation and indicate if available cars are available for current-attached package', () => {
        it('Check expectation', async () => {
          const currentAttachedRentalPackageData =
            new AttachRentalPackageRequestDtoV1Builder()
              .withMinAllowedBatteryPercentage(40)
              .build();

          const fleetId = config.vulogConfig.fleetId;

          const stationDto = createTestStationDto({ zoneId: aMD5() });

          const carStaticDto = createTestVulogCarStaticInfoDto(
            allocatedCarId,
            fleetId.value,
          );

          const staticDtos = [
            carStaticDto,
            createTestVulogCarStaticInfoDto(
              undefined,
              fleetId.value,
              CarModelEnum.BlueCar,
            ),
            createTestVulogCarStaticInfoDto(
              undefined,
              fleetId.value,
              CarModelEnum.BlueCar,
            ),
          ];

          await carRepo.save(
            staticDtos.map((it) => CarEntity.from(it.toStaticCar().toCar())),
          );

          const vulogTripId = aMD5();

          const currentAllocatedRealtimeCar = new VulogCarRealTimeDtoBuilder()
            .withId(allocatedCarId)
            .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
            .withFleetId(fleetId.value)
            .withServiceId(config.vulogConfig.serviceId.value)
            .withZones([new VulogZone(stationDto.zoneId, 1, 'allowed', true)])
            .withStartZones([
              new VulogZone(stationDto.zoneId, 1, 'allowed', true),
            ])
            .withProfileId(user.profileId)
            .withVulogTripId(vulogTripId)
            .withAutonomy(60)
            .build();

          const carRealtimeDtos = [
            currentAllocatedRealtimeCar,
            new VulogCarRealTimeDtoBuilder()
              .withId(staticDtos[1].id)
              .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
              .withBookingStatus(VulogRealTimeBookingStatus.Available)
              .withFleetId(fleetId.value)
              .withServiceId(config.vulogConfig.serviceId.value)
              .withZones([new VulogZone(stationDto.zoneId, 1, 'allowed', true)])
              .withStartZones([
                new VulogZone(stationDto.zoneId, 1, 'allowed', true),
              ])
              .withProfileId(user.profileId)
              .withAutonomy(35)
              .withFleetId(fleetId.value)
              .build(),

            new VulogCarRealTimeDtoBuilder()
              .withId(staticDtos[2].id)
              .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
              .withBookingStatus(VulogRealTimeBookingStatus.Available)
              .withFleetId(fleetId.value)
              .withServiceId(config.vulogConfig.serviceId.value)
              .withZones([new VulogZone(stationDto.zoneId, 1, 'allowed', true)])
              .withStartZones([
                new VulogZone(stationDto.zoneId, 1, 'allowed', true),
              ])
              .withProfileId(user.profileId)
              .withAutonomy(45)
              .withFleetId(fleetId.value)
              .build(),
          ];

          const ownReservation = await reservationService.saveOne(
            Reservation.from(
              new ReservationEntityBuilder()
                .makeBsgUserId(userId)
                .makeCarId(allocatedCarId)
                .makeCarModel(carStaticDto.model.name as CarModelEnum)
                .makeReservedAt(DateTime.now().toJSDate())
                .makeExpiresAt(DateTime.now().plus({ minutes: 15 }).toJSDate())
                .makeStatus(ReservationStatus.Reserved)
                .makeVulogTripId(vulogTripId)
                .makeStartStationId(stationDto.id)
                .makeRentalPackageData(currentAttachedRentalPackageData)
                .build(),
            ),
          );

          await reservationService.saveOne(ownReservation);

          const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
            .withId(config.vulogConfig.serviceId.value)
            .withZones([stationDto.zoneId])
            .withVehicles(carRealtimeDtos.map((rltCar) => rltCar.id))
            .build();

          // VulogFleetService.getFleetServices
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeFleetServices(
                config.vulogConfig.fleetId,
              )}`,
            )
            .replyOnce(200, [mockVulogFleetServiceDto]);

          // VulogCarService - get realtime cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`,
            )
            .replyOnce(
              200,
              carRealtimeDtos.map((car) => car.toRawJsonObject()),
            );

          // VulogCarService - get all static cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeListingAllStaticVehicles(
                config.vulogConfig.fleetId,
              )}?${QueryString.stringify({ page: 0, size: 200 })}`,
            )
            .replyOnce(200, staticDtos);

          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeListingAllStaticVehicles(
                config.vulogConfig.fleetId,
              )}?${QueryString.stringify({ page: 1, size: 200 })}`,
            )
            .replyOnce(200, []);

          // VulogCarService - get realtime cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`,
            )
            .replyOnce(
              200,
              carRealtimeDtos.map((car) => car.toRawJsonObject()),
            );

          // VulogCarService - get all static cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeListingAllStaticVehicles(
                config.vulogConfig.fleetId,
              )}?${QueryString.stringify({ page: 0, size: 200 })}`,
            )
            .replyOnce(200, staticDtos);

          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeListingAllStaticVehicles(
                config.vulogConfig.fleetId,
              )}?${QueryString.stringify({ page: 1, size: 200 })}`,
            )
            .replyOnce(200, []);

          // StationService.getAllStations
          mockHttpAdapterService
            .onGet(
              `${StationPaths.stations()}?${QueryString.stringify({
                page: 1,
                pageSize: 200,
              })}`,
            )
            .replyOnce(200, { result: [stationDto] });

          mockHttpAdapterService
            .onGet(
              `${StationPaths.stations()}?${QueryString.stringify({
                page: 2,
                pageSize: 200,
              })}`,
            )
            .replyOnce(200, { result: [] });

          const response = await request(context.getHttpServer())
            .get(
              `${reservationUrl.replace(
                ':reservationId',
                ownReservation.getId,
              )}`,
            )
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          const result = response.body
            .result as AvailableCarsForReservationResponseDtoV1;

          const currentAllocatedCar = currentAllocatedRealtimeCar.toRealtimeCar(
            new CarModel(carStaticDto.model.name),
          );

          expect(result.currentAllocatedCar).toEqual(
            JSON.parse(
              JSON.stringify(
                CarDto.fromVulogCar(
                  currentAllocatedCar,
                  currentAllocatedCar.energyLevel,
                  currentAllocatedCar.isDoorLocked,
                  currentAttachedRentalPackageData.minAllowedBatteryPercentage,
                ),
              ),
            ),
          );

          expect(
            result.availableCars.sort((a, b) => a.id.localeCompare(b.id)),
          ).toEqual(
            JSON.parse(
              JSON.stringify(
                carRealtimeDtos
                  .map((item, index) => {
                    const availCar = item.toRealtimeCar(
                      new CarModel(staticDtos[index].model.name),
                    );

                    return CarDto.fromVulogCar(
                      availCar,
                      availCar.energyLevel,
                      availCar.isDoorLocked,
                      currentAttachedRentalPackageData.minAllowedBatteryPercentage,
                    );
                  })
                  .sort((a, b) => a.id.localeCompare(b.id)),
              ),
            ),
          );

          expect(result.currentReservation).toEqual(
            JSON.parse(
              JSON.stringify(
                ReservationResponseDtoV1.fromReservation(ownReservation),
              ),
            ),
          );
        });
      });

      describe('no available cars at station of current allocate reservation', () => {
        it('Check expectation', async () => {
          const stationDto = new StationDtoBuilder().build();

          const carStaticDto = createTestVulogCarStaticInfoDto(
            allocatedCarId,
            config.vulogConfig.fleetId.value,
          );

          const staticDtos = [
            carStaticDto,
            createTestVulogCarStaticInfoDto(
              undefined,
              config.vulogConfig.fleetId.value,
              CarModelEnum.BlueCar,
            ),
            createTestVulogCarStaticInfoDto(
              undefined,
              config.vulogConfig.fleetId.value,
              CarModelEnum.BlueCar,
            ),
          ];

          await carRepo.save(
            staticDtos.map((it) => CarEntity.from(it.toStaticCar().toCar())),
          );

          const vulogTripId = aMD5();

          const currentAllocatedRealtimeCar = new VulogCarRealTimeDtoBuilder()
            .withId(allocatedCarId)
            .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
            .withFleetId(config.vulogConfig.fleetId.value)
            .withServiceId(config.vulogConfig.serviceId.value)
            .withZones([new VulogZone(stationDto.zoneId, 1, 'allowed', true)])
            .withStartZones([
              new VulogZone(stationDto.zoneId, 1, 'allowed', true),
            ])
            .withProfileId(user.profileId)
            .withVulogTripId(vulogTripId)
            .build();

          const carRealtimeDtos = [
            currentAllocatedRealtimeCar,
            new VulogCarRealTimeDtoBuilder()
              .withId(staticDtos[1].id)
              .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
              .withBookingStatus(VulogRealTimeBookingStatus.Available)
              .withFleetId(config.vulogConfig.fleetId.value)
              .withServiceId(config.vulogConfig.serviceId.value)
              .withZones([new VulogZone(stationDto.zoneId, 1, 'allowed', true)])
              .withStartZones([
                new VulogZone(stationDto.zoneId, 1, 'allowed', true),
              ])
              .withProfileId(user.profileId)
              .withAutonomy(55)
              .build(),
            new VulogCarRealTimeDtoBuilder()
              .withId(staticDtos[2].id)
              .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
              .withBookingStatus(VulogRealTimeBookingStatus.Available)
              .withFleetId(config.vulogConfig.fleetId.value)
              .withServiceId(config.vulogConfig.serviceId.value)
              .withZones([new VulogZone(stationDto.zoneId, 1, 'allowed', true)])
              .withStartZones([
                new VulogZone(stationDto.zoneId, 1, 'allowed', true),
              ])
              .withProfileId(user.profileId)
              .withAutonomy(55)
              .build(),
          ];

          const ownReservation = await reservationService.saveOne(
            Reservation.from(
              new ReservationEntityBuilder()
                .makeBsgUserId(userId)
                .makeCarId(allocatedCarId)
                .makeCarModel(carStaticDto.model.name as CarModelEnum)
                .makeReservedAt(DateTime.now().toJSDate())
                .makeExpiresAt(DateTime.now().plus({ minutes: 15 }).toJSDate())
                .makeStatus(ReservationStatus.Reserved)
                .makeVulogTripId(vulogTripId)
                .makeStartStationId(stationDto.id)
                .build(),
            ),
          );

          await reservationService.saveOne(ownReservation);

          const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
            .withId(config.vulogConfig.serviceId.value)
            .withZones([stationDto.zoneId])
            .withVehicles(carRealtimeDtos.map((rltCar) => rltCar.id))
            .build();

          // VulogFleetService.getFleetServices
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeFleetServices(
                config.vulogConfig.fleetId,
              )}`,
            )
            .replyOnce(200, [mockVulogFleetServiceDto]);

          // VulogCarService - get realtime cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`,
            )
            .replyOnce(
              200,
              carRealtimeDtos.map((car) => car.toRawJsonObject()),
            );

          // VulogCarService - get all static cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeListingAllStaticVehicles(
                config.vulogConfig.fleetId,
              )}?${QueryString.stringify({ page: 0, size: 200 })}`,
            )
            .replyOnce(200, staticDtos, { last: true });

          // VulogCarService - get realtime cars - no available realtime car
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`,
            )
            .replyOnce(200, []);

          // VulogCarService - get all static cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeListingAllStaticVehicles(
                config.vulogConfig.fleetId,
              )}?${QueryString.stringify({ page: 0, size: 200 })}`,
            )
            .replyOnce(200, staticDtos, { last: true });

          // StationService.getAllStations
          mockHttpAdapterService
            .onGet(
              `${StationPaths.stations()}?${QueryString.stringify({
                page: 1,
                pageSize: 200,
              })}`,
            )
            .replyOnce(200, { result: [stationDto] });

          mockHttpAdapterService
            .onGet(
              `${StationPaths.stations()}?${QueryString.stringify({
                page: 2,
                pageSize: 200,
              })}`,
            )
            .replyOnce(200, { result: [] }); // Breaking loop

          const response = await request(context.getHttpServer())
            .get(
              `${reservationUrl.replace(
                ':reservationId',
                ownReservation.getId,
              )}`,
            )
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          // the booked car comes at the end of the list
          carRealtimeDtos.push(carRealtimeDtos.shift());

          expect(response.body.result).toStrictEqual(
            JSON.parse(
              JSON.stringify(
                AvailableCarsForReservationResponseDtoV1.from(
                  currentAllocatedRealtimeCar
                    .toRealtimeCar(new CarModel(carStaticDto.model.name))
                    .toCar(),
                  carRealtimeDtos.map((dto) =>
                    dto
                      .toRealtimeCar(new CarModel(carStaticDto.model.name))
                      .toCar(),
                  ),
                  ownReservation,
                ),
              ),
            ),
          );
        });
      });
    },
    [
      {
        name: CustomerJwtGuard,
        value: mockCustomerJwtGuard(userId),
        isGuard: true,
      },
    ],
  ),
);
