import MockAdapter from 'axios-mock-adapter';
import {
  BsgUserId,
  ReservationId,
  VulogProfileId,
  VulogUserId,
} from 'src/common/tiny-types';
import { config } from 'src/config';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import * as request from 'supertest';
import { createTestVulogCarStaticInfoDto } from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestStationDto } from 'test/helpers/builders/dtos/test-station-dto.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogJourneyDto } from 'test/helpers/builders/dtos/test-vulog-journey-dto.builder';
import { ApiTestContext, testApi } from 'test/helpers/test-api.util';
import { Repository } from 'typeorm';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';

import { faker } from '@faker-js/faker';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DateTime } from 'luxon';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { ReservationResponseDtoV1 } from 'src/controllers/dtos/reservation.response.v1.dto';
import { CacheService } from 'src/logic/cache/cache.service';
import { ReservationService } from 'src/logic/reservation.service';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { ReservationInfo } from 'src/model/reservation-info';
import { Reservation } from 'src/model/reservation.domain';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import {
  CUSTOMER_JWT_MOCK_TOKEN,
  mockCustomerJwtGuard,
} from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { VulogUserBuilder } from '../../../helpers/builders/vulog-user.builder';
import { StationApiMocker } from '../../../helpers/external-services/station.api-mocker';
import { UssApiMocker } from '../../../helpers/external-services/uss.api-mocker';
import { VulogApiMocker } from '../../../helpers/external-services/vulog.api-mocker';

const userId = aUUID();

const email = faker.internet.email();

describe(
  'GET /api/v1/reservation/on-going',
  testApi(
    (context: ApiTestContext) => {
      let mockHttpAdapterService: MockAdapter;
      let mockUssApiService: MockAdapter;
      let mockStsApiService: MockAdapter;

      let cacheService: CacheService;
      let reservationService: ReservationService;

      const reservationUrl = '/api/v1/reservation/on-going';

      const testAuthResponse = createTestVulogAuthResponse();
      const userServicesAndProfiles = createTestVulogUserServicesDto();

      const vulogUser = VulogUserBuilder.allSync()
        .withBsgUserId(new BsgUserId(userId))
        .build();

      let userRepo: Repository<VulogUserEntity>;
      let reservationRepo: Repository<ReservationEntity>;

      beforeEach(async () => {
        mockHttpAdapterService = new VulogApiMocker(context.app).mock();
        mockUssApiService = new UssApiMocker(context.app).mock();
        mockStsApiService = new StationApiMocker(context.app).mock();

        cacheService = context.app.get(CacheService);

        reservationService = context.app.get(ReservationService);

        userRepo = context.app.get<Repository<VulogUserEntity>>(
          getRepositoryToken(VulogUserEntity),
        );

        reservationRepo = context.app.get<Repository<ReservationEntity>>(
          getRepositoryToken(ReservationEntity),
        );

        const userDto = createTestVulogUserDto();

        userDto.id = userId;
        userDto.email = email;

        mockHttpAdapterService
          .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
          .reply(200, userDto);

        mockHttpAdapterService
          .onGet(
            `${VulogPaths.userServicesAndProfiles(
              config.vulogConfig.fleetId,
              new VulogUserId(userDto.id),
            )}`,
          )
          .reply(200, userServicesAndProfiles);

        mockHttpAdapterService
          .onPost(
            `${VulogPaths.registerUserProfileForService(
              config.vulogConfig.fleetId,
              new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
            )}`,
          )
          .reply(200, userServicesAndProfiles);

        mockHttpAdapterService
          .onPost(
            `${VulogPaths.userProfile(
              config.vulogConfig.fleetId,
              new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
            )}`,
          )
          .reply(200, {});

        mockHttpAdapterService
          .onPost(`${VulogPaths.auth()}`)
          .reply(200, testAuthResponse);

        mockUssApiService
          .onGet(
            `${
              config.httpServiceConfig.userSubscriptionUrl
            }/${ApiRouteUtils.buildApiRouteSegment(
              RootRouteSegment.Internal,
              ApiVersion.V1,
              ['users', userId],
            )}`,
          )
          .reply(200, { result: { id: userId, email } });

        await userRepo.insert(VulogUserEntity.from(vulogUser));
      });

      afterEach(async () => {
        mockHttpAdapterService.reset();

        mockUssApiService.reset();

        mockStsApiService.reset();

        await userRepo.clear();

        await reservationRepo.clear();

        await cacheService.clearAll();
      });

      describe.skip('should be able to get on-going reservation', () => {
        it('Check expectation', async () => {
          const sourceId = aUUID();

          const zoneId = aUUID();

          const mockCarStaticInfoDto = createTestVulogCarStaticInfoDto(
            sourceId,
            config.vulogConfig.fleetId.value,
          );

          const mockVulogJourneyDto = createTestVulogJourneyDto(
            false,
            true,
            mockCarStaticInfoDto.id,
            config.vulogConfig.fleetId.value,
          );

          const mockStationDto = createTestStationDto({
            zoneId: zoneId,
          });

          const reservation = await reservationService.saveOne(
            Reservation.from(
              new ReservationEntityBuilder()
                .makeBsgUserId(userId)
                .makeCarId(mockCarStaticInfoDto.id)
                .makeCarModel(mockCarStaticInfoDto.model.name as CarModelEnum)
                .makeStartStationId(mockStationDto.id)
                .makeVulogTripId(mockVulogJourneyDto.id)
                .makeStatus(ReservationStatus.Reserved)
                .makeReservedAt(DateTime.now().toJSDate())
                .makeExpiresAt(DateTime.now().plus({ minutes: 15 }).toJSDate())
                .build(),
            ),
          );

          // VulogCarService.getUserCurrentReservations
          mockHttpAdapterService
            .onGet(`${VulogPaths.currentJourneys()}`)
            .replyOnce(200, {
              journeys: [mockVulogJourneyDto.toRawJsonObject()],
            });

          const authorizedUserToken = JSON.stringify({
            id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
          });

          const response = await request(context.getHttpServer())
            .get(`${reservationUrl}`)
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          const updatedReservation = await reservationService.findOneById(
            new ReservationId(reservation.getId),
          );

          expect(updatedReservation.status).toBe(ReservationStatus.Reserved);

          const reservationInfo = new ReservationInfo({
            ...mockVulogJourneyDto.toReservationInfo(),
            entityId: new ReservationId(updatedReservation.getId),
            startStationId: updatedReservation.startStationId,
            carModel: updatedReservation.carModel,
            status: updatedReservation.status,
            local: updatedReservation.local,
            plateNumber: updatedReservation.plateNumber,
            bsgUserId: updatedReservation.bsgUserId,
          });

          expect(response.body.result).toEqual(
            JSON.parse(
              JSON.stringify(
                ReservationResponseDtoV1.fromReservationInfo(reservationInfo),
              ),
            ),
          );
        });
      });

      describe.skip('should return empty response when no on-going reservation is found', () => {
        it('Check expectation', async () => {
          // VulogCarService.getUserCurrentReservations
          mockHttpAdapterService
            .onGet(`${VulogPaths.currentJourneys()}`)
            .replyOnce(200, {
              journeys: [],
            });

          const authorizedUserToken = JSON.stringify({
            id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
          });

          const response = await request(context.getHttpServer())
            .get(`${reservationUrl}`)
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          expect(response.body.result).toEqual(null);
        });
      });
    },
    [
      {
        name: CustomerJwtGuard,
        value: mockCustomerJwtGuard(userId),
        isGuard: true,
      },
    ],
  ),
);
