import * as supertest from 'supertest';

import { HttpStatus, INestApplication } from '@nestjs/common';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { getRepositoryToken } from '@nestjs/typeorm';
import { RouteSegment } from 'src/common/constants/api-path';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { UpdateRentalFeedBackRequestDtoV1 } from 'src/controllers/dtos/rental/request/update-rental-feedback.v1.request.dto';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { AppBuilder } from 'test/helpers/app.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { Repository } from 'typeorm';

const testingApiRoute = ApiRouteUtils.buildApiRouteSegment(
  '' as any,
  '' as any,
  ['api/v1', 'reservation', RouteSegment.Reservation.UpdateRentalFeedBack],
);

describe(`[E2E] PUT: ${testingApiRoute}`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let reserveRepo: Repository<ReservationEntity>;

  function requestFor(
    reserveId: string,
    payload: UpdateRentalFeedBackRequestDtoV1,
  ): supertest.Test {
    const injectedUrl = testingApiRoute.replace(':reservationId', reserveId);

    const superTest = supertest(app.getHttpServer())
      .put(`${injectedUrl}`)
      .send(payload);
    return superTest;
  }

  beforeAll(async () => {
    app = await appBuilder
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(aUUID()), true)
      .buildAndStart();

    reserveRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );
  });

  afterAll(async () => {
    await reserveRepo.delete({});
    await app.close();
  });

  async function clean(): Promise<void> {
    await reserveRepo.delete({});
  }

  describe('When send a request and it is valid', () => {
    const reserveId = aUUID();
    let responseObject: supertest.Response;
    const feedback: UpdateRentalFeedBackRequestDtoV1 = {
      carCleanliness: 'noaice',
      rating: 'noaice',
    };

    beforeAll(async () => {
      await reserveRepo.save(
        new ReservationEntityBuilder().makeReservationId(reserveId).build(),
      );

      responseObject = await requestFor(reserveId, feedback);
    });

    afterAll(async () => {
      await clean();
    });

    it('Should return status ok', () => {
      expect(responseObject.status).toBe(HttpStatus.OK);
    });

    it.skip("Should update the reservation's feedback", async () => {
      expect(responseObject.body.result).toMatchObject(feedback);
    });
  });

  describe('When send a non exist reservation id', () => {
    const reserveId = aUUID();
    let responseObject: supertest.Response;
    const feedback: UpdateRentalFeedBackRequestDtoV1 = {
      carCleanliness: 'noaice',
      rating: 'noaice',
    };

    beforeAll(async () => {
      responseObject = await requestFor(reserveId, feedback);
    });

    afterAll(async () => {
      await clean();
    });

    it('Should return status Not found', () => {
      expect(responseObject.status).toBe(HttpStatus.NOT_FOUND);
    });

    it("Should update the reservation's feedback", async () => {
      expect(responseObject.body.message).toBe('Reservation not found');
    });
  });
});
