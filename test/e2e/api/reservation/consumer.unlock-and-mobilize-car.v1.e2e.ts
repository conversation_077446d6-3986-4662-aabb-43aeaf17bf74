import * as supertest from 'supertest';

import { HttpStatus, INestApplication } from '@nestjs/common';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { AppBuilder } from 'test/helpers/app.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { Repository } from 'typeorm';

import MockAdapter from 'axios-mock-adapter';
import {
  BsgUserId,
  VulogCarId,
  VulogJourneyOrTripId,
  VulogProfileId,
  VulogUserId,
} from 'src/common/tiny-types';
import { config } from 'src/config';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';

import { faker } from '@faker-js/faker';
import { DateTime } from 'luxon';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { MapUtils } from 'src/common/utils/map.util';
import { ConsumerLockAndImmobilizeResponseDtoV1 } from 'src/controllers/consumer/v1/car/dto/consumer.lock-and-immobilize.response.dto.v1';
import { CacheService } from 'src/logic/cache/cache.service';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { StationDto } from 'src/model/dtos/station.dto';
import {
  VulogFleetServiceDto,
  VulogFleetServiceType,
} from 'src/model/dtos/vulog-fleet-service/vulog-fleet-service.dto';
import { VulogJourneyDto } from 'src/model/dtos/vulog-journey/vulog-journey.dto';
import { VvgVehicleStatusDto } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { VulogAimaErrorResponseDtoBuilder } from 'test/helpers/builders/dtos/axios/vulog.aima.error.response.dto.builder';
import { VVGErrorResponseDtoBuilder } from 'test/helpers/builders/dtos/axios/vulog.vvg.error.response.dto.builder';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import { VulogJourneyDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-journey.dto.builder';
import { VvgVehicleStatusDtoBuilder } from 'test/helpers/builders/dtos/vulog/vvg/vvg-vehicle-status.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { CUSTOMER_JWT_MOCK_TOKEN } from 'test/helpers/customer-jwt-guard.mocker';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { VulogUserBuilder } from '../../../helpers/builders/vulog-user.builder';
import { UssApiMocker } from '../../../helpers/external-services/uss.api-mocker';
import { VulogApiMocker } from '../../../helpers/external-services/vulog.api-mocker';

const userId = aUUID();

const email = faker.internet.email();

const authorizedUserToken = JSON.stringify({
  id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
});

const carUrl = `/api/v1/${RentalServiceRootRouteSegment.Consumer}/${RouteSegment.Car.Index}/${RouteSegment.Car.Unlock}`;

// This is a long test
jest.setTimeout(1000000);

describe(`[E2E] POST: ${carUrl}`, () => {
  let app: INestApplication;

  let mockHttpAdapterService: MockAdapter;
  let mockUssApiService: MockAdapter;

  let cacheService: CacheService;

  let userRepo: Repository<VulogUserEntity>;
  let reservationRepo: Repository<ReservationEntity>;
  let carRepo: Repository<CarEntity>;
  let featureFlagService: FeatureFlagService;
  let spyOn__FeatureFlagService__startRentalAutoUnlocked: jest.SpyInstance;
  let spyOn__FeatureFlagService__shouldCancelReservationAfterBestEffort: jest.SpyInstance;

  const testAuthResponse = createTestVulogAuthResponse();
  const testVulogUser = VulogUserBuilder.allSync()
    .withBsgUserId(new BsgUserId(userId))
    .build();

  function requestFor(carId: string): supertest.Test {
    const injectedUrl = carUrl.replace(':carId', carId);

    const superTest = supertest(app.getHttpServer())
      .post(`${injectedUrl}`)
      .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
      .send();

    return superTest;
  }

  beforeAll(async () => {
    app = await new AppBuilder()
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(aUUID()), true)
      .buildAndStart();

    userRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );

    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );

    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );

    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));

    mockHttpAdapterService = new VulogApiMocker(app).mock();
    mockUssApiService = new UssApiMocker(app).mock();

    cacheService = app.get(CacheService);

    featureFlagService = app.get(FeatureFlagService);

    spyOn__FeatureFlagService__startRentalAutoUnlocked = jest.spyOn(
      featureFlagService,
      'startRentalAutoUnlocked',
    );

    spyOn__FeatureFlagService__shouldCancelReservationAfterBestEffort =
      jest.spyOn(featureFlagService, 'shouldCancelReservationAfterBestEffort');
  });

  afterAll(async () => {
    await cleanResources();

    await app.close();
  });

  async function mockCredential() {
    const userDto = createTestVulogUserDto();

    userDto.email = email;

    mockHttpAdapterService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, userDto);

    const userServicesAndProfiles = createTestVulogUserServicesDto();
    userServicesAndProfiles.userId = userDto.id;

    mockHttpAdapterService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(userDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockHttpAdapterService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockHttpAdapterService
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    mockHttpAdapterService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, testAuthResponse);

    await userRepo.insert(VulogUserEntity.from(testVulogUser));

    mockVulogCredential(app);
  }

  async function cleanResources(): Promise<void> {
    jest.clearAllMocks();

    mockHttpAdapterService.reset();
    mockUssApiService.reset();

    await userRepo.clear();
    await reservationRepo.clear();
    await cacheService.clearAll();
    await carRepo.delete({});
    await cacheService.clearAll();
  }

  describe('🔥 Flag `startRentalAutoUnlocked` is 🔴 OFF, unlock car successfully, then automatically start rental successfully', () => {
    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());
    const vvgVehicleStatusDto: VvgVehicleStatusDto =
      new VvgVehicleStatusDtoBuilder()
        .withId(carEntity.vulogId)
        .withDoorsLocked(true)
        .withImmobilizerOn(true)
        .build();
    const stationDto: StationDto = new StationDtoBuilder().build();
    const fleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withType(VulogFleetServiceType.FreeFloating)
        .withVehicles([carEntity.id])
        .withZones([stationDto.zoneId])
        .withId(config.vulogConfig.serviceId.value)
        .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(carEntity.vulogId)
      .makePlateNumber(carEntity.plate)
      .makeStatus(ReservationStatus.Converted)
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(carEntity.plate)
      .makeBsgUserId(userId)
      .build();
    const vulogJourneyDto: VulogJourneyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(carEntity.vulogId)
      .withServiceId(fleetServiceDto.id)
      .build();

    beforeAll(async () => {
      await mockCredential();

      spyOn__FeatureFlagService__startRentalAutoUnlocked.mockResolvedValueOnce(
        false,
      );

      await carRepo.insert(carEntity);

      await reservationRepo.insert(reservationEntity);

      // Mock cache VvgVehicleStatusDto
      await cacheService.set(
        `${config.redisConfig.singleTelemetryCarInfo.cacheKey}/${carEntity.vulogId}`,
        vvgVehicleStatusDto,
        { ttl: config.redisConfig.singleTelemetryCarInfo.ttl },
      );

      // Mock cache fleetService
      await cacheService.set(
        `${config.redisConfig.fleetServices.cacheKey}`,
        [fleetServiceDto],
        { ttl: config.redisConfig.fleetServices.ttl },
      );

      // Mock cache zoneStationMap
      await cacheService.set(
        `${config.redisConfig.stations.cacheKey}`,
        [stationDto],
        { ttl: config.redisConfig.stations.ttl },
      );
      await cacheService.set(
        `${config.redisConfig.zoneStationMap.cacheKey}`,
        MapUtils.build([stationDto], (station) => station.zoneId),
        { ttl: config.redisConfig.zoneStationMap.ttl },
      );

      // Mock VVG unlockAndMobilize
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.vvgUnlockAndMobilizeVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(() => {
          vvgVehicleStatusDto.status.locked = false;
          vvgVehicleStatusDto.status.immobilizerOn = false;

          return [200];
        });

      // Mock refetch vehicle status
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, vvgVehicleStatusDto);

      // Mock AiMA on-going journey
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, []);
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, null);

      // Mock AiMA start rental
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.startOrEndTrip(
            new VulogJourneyOrTripId(reservationEntity.vulogTripId),
          )}`,
        )
        .replyOnce(201, vulogJourneyDto);
    });

    it('check expectation', async () => {
      const response = await requestFor(carEntity.vulogId);

      expect(response.statusCode).toEqual(HttpStatus.CREATED);

      expect(response.body.result).toEqual({
        immobilized: false,
        locked: false,
      } as ConsumerLockAndImmobilizeResponseDtoV1);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('🔥 Flag `startRentalAutoUnlocked` is 🔴 OFF, unlock car failed, retry unlocking car again, unlock car successfully, then automatically start rental successfully', () => {
    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());
    const vvgVehicleStatusDto: VvgVehicleStatusDto =
      new VvgVehicleStatusDtoBuilder()
        .withId(carEntity.vulogId)
        .withDoorsLocked(true)
        .withImmobilizerOn(true)
        .build();
    const stationDto: StationDto = new StationDtoBuilder().build();
    const fleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withType(VulogFleetServiceType.FreeFloating)
        .withVehicles([carEntity.id])
        .withZones([stationDto.zoneId])
        .withId(config.vulogConfig.serviceId.value)
        .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(carEntity.vulogId)
      .makePlateNumber(carEntity.plate)
      .makeStatus(ReservationStatus.Converted)
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(carEntity.plate)
      .makeBsgUserId(userId)
      .build();
    const vulogJourneyDto: VulogJourneyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(carEntity.vulogId)
      .withServiceId(fleetServiceDto.id)
      .build();

    beforeAll(async () => {
      await mockCredential();

      spyOn__FeatureFlagService__startRentalAutoUnlocked.mockResolvedValueOnce(
        false,
      );

      await carRepo.insert(carEntity);

      await reservationRepo.insert(reservationEntity);

      // Mock cache VvgVehicleStatusDto
      await cacheService.set(
        `${config.redisConfig.singleTelemetryCarInfo.cacheKey}/${carEntity.vulogId}`,
        vvgVehicleStatusDto,
        { ttl: config.redisConfig.singleTelemetryCarInfo.ttl },
      );

      // Mock cache fleetService
      await cacheService.set(
        `${config.redisConfig.fleetServices.cacheKey}`,
        [fleetServiceDto],
        { ttl: config.redisConfig.fleetServices.ttl },
      );

      // Mock cache zoneStationMap
      await cacheService.set(
        `${config.redisConfig.stations.cacheKey}`,
        [stationDto],
        { ttl: config.redisConfig.stations.ttl },
      );
      await cacheService.set(
        `${config.redisConfig.zoneStationMap.cacheKey}`,
        MapUtils.build([stationDto], (station) => station.zoneId),
        { ttl: config.redisConfig.zoneStationMap.ttl },
      );

      // Mock VVG unlockAndMobilize failed
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.vvgUnlockAndMobilizeVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(
          HttpStatus.GONE,
          new VVGErrorResponseDtoBuilder()
            .withCodeStr('carConnectionProblem')
            .build(),
        );

      // Mock VVG wakeup ongoing
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      // Mock VVG ping success
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      // Mock VVG unlockAndMobilize success
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.vvgUnlockAndMobilizeVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(() => {
          vvgVehicleStatusDto.status.locked = false;
          vvgVehicleStatusDto.status.immobilizerOn = false;

          return [200];
        });

      // Mock refetch vehicle status
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, vvgVehicleStatusDto);

      // Mock AiMA on-going journey
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, []);
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, null);

      // Mock AiMA start rental
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.startOrEndTrip(
            new VulogJourneyOrTripId(reservationEntity.vulogTripId),
          )}`,
        )
        .replyOnce(201, vulogJourneyDto);
    });

    it('check expectation', async () => {
      const response = await requestFor(carEntity.vulogId);

      expect(response.statusCode).toEqual(HttpStatus.CREATED);

      expect(response.body.result).toEqual({
        immobilized: false,
        locked: false,
      } as ConsumerLockAndImmobilizeResponseDtoV1);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('🔥 Flag `startRentalAutoUnlocked` is 🔴 OFF, previous attempt was failed due to timeout issue, getting car info from VVG, car is unlocked and mobilized, then automatically start rental successfully', () => {
    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());
    const vvgVehicleStatusDto: VvgVehicleStatusDto =
      new VvgVehicleStatusDtoBuilder()
        .withId(carEntity.vulogId)
        .withDoorsLocked(false) // Unlocked
        .withImmobilizerOn(false) // Unlocked
        .build();
    const stationDto: StationDto = new StationDtoBuilder().build();
    const fleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withType(VulogFleetServiceType.FreeFloating)
        .withVehicles([carEntity.id])
        .withZones([stationDto.zoneId])
        .withId(config.vulogConfig.serviceId.value)
        .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(carEntity.vulogId)
      .makePlateNumber(carEntity.plate)
      .makeStatus(ReservationStatus.Converted)
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(carEntity.plate)
      .makeBsgUserId(userId)
      .build();
    const vulogJourneyDto: VulogJourneyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(carEntity.vulogId)
      .withServiceId(fleetServiceDto.id)
      .build();

    beforeAll(async () => {
      await mockCredential();

      spyOn__FeatureFlagService__startRentalAutoUnlocked.mockResolvedValueOnce(
        false,
      );

      await carRepo.insert(carEntity);

      await reservationRepo.insert(reservationEntity);

      // Mock cache VvgVehicleStatusDto
      await cacheService.set(
        `${config.redisConfig.singleTelemetryCarInfo.cacheKey}/${carEntity.vulogId}`,
        vvgVehicleStatusDto,
        { ttl: config.redisConfig.singleTelemetryCarInfo.ttl },
      );

      // Mock cache fleetService
      await cacheService.set(
        `${config.redisConfig.fleetServices.cacheKey}`,
        [fleetServiceDto],
        { ttl: config.redisConfig.fleetServices.ttl },
      );

      // Mock cache zoneStationMap
      await cacheService.set(
        `${config.redisConfig.stations.cacheKey}`,
        [stationDto],
        { ttl: config.redisConfig.stations.ttl },
      );
      await cacheService.set(
        `${config.redisConfig.zoneStationMap.cacheKey}`,
        MapUtils.build([stationDto], (station) => station.zoneId),
        { ttl: config.redisConfig.zoneStationMap.ttl },
      );

      // Mock AiMA on-going journey
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, []);
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, null);

      // Mock AiMA start rental
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.startOrEndTrip(
            new VulogJourneyOrTripId(reservationEntity.vulogTripId),
          )}`,
        )
        .replyOnce(201, vulogJourneyDto);
    });

    it('check expectation', async () => {
      const response = await requestFor(carEntity.vulogId);

      expect(response.statusCode).toEqual(HttpStatus.CREATED);

      expect(response.body.result).toEqual({
        immobilized: false,
        locked: false,
      } as ConsumerLockAndImmobilizeResponseDtoV1);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('🔥 Flag `startRentalAutoUnlocked` is 🔴 OFF, previous attempt was failed due to timeout issue, getting car info from VVG, car is unlocked and mobilized, then automatically start rental but failed, cancel reservation successfully', () => {
    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());
    const vvgVehicleStatusDto: VvgVehicleStatusDto =
      new VvgVehicleStatusDtoBuilder()
        .withId(carEntity.vulogId)
        .withDoorsLocked(false) // Unlocked
        .withImmobilizerOn(false) // Unlocked
        .build();
    const stationDto: StationDto = new StationDtoBuilder().build();
    const fleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withType(VulogFleetServiceType.FreeFloating)
        .withVehicles([carEntity.id])
        .withZones([stationDto.zoneId])
        .withId(config.vulogConfig.serviceId.value)
        .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(carEntity.vulogId)
      .makePlateNumber(carEntity.plate)
      .makeStatus(ReservationStatus.Converted)
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(carEntity.plate)
      .makeBsgUserId(userId)
      .build();

    beforeAll(async () => {
      await mockCredential();

      spyOn__FeatureFlagService__startRentalAutoUnlocked.mockResolvedValueOnce(
        false,
      );

      spyOn__FeatureFlagService__shouldCancelReservationAfterBestEffort.mockResolvedValueOnce(
        true,
      );

      await carRepo.insert(carEntity);

      await reservationRepo.insert(reservationEntity);

      // Mock cache VvgVehicleStatusDto
      await cacheService.set(
        `${config.redisConfig.singleTelemetryCarInfo.cacheKey}/${carEntity.vulogId}`,
        vvgVehicleStatusDto,
        { ttl: config.redisConfig.singleTelemetryCarInfo.ttl },
      );

      // Mock cache fleetService
      await cacheService.set(
        `${config.redisConfig.fleetServices.cacheKey}`,
        [fleetServiceDto],
        { ttl: config.redisConfig.fleetServices.ttl },
      );

      // Mock cache zoneStationMap
      await cacheService.set(
        `${config.redisConfig.stations.cacheKey}`,
        [stationDto],
        { ttl: config.redisConfig.stations.ttl },
      );
      await cacheService.set(
        `${config.redisConfig.zoneStationMap.cacheKey}`,
        MapUtils.build([stationDto], (station) => station.zoneId),
        { ttl: config.redisConfig.zoneStationMap.ttl },
      );

      // Mock AiMA on-going journey
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, []);
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, null);

      // Mock AiMA start rental
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.startOrEndTrip(
            new VulogJourneyOrTripId(reservationEntity.vulogTripId),
          )}`,
        )
        .replyOnce(
          400,
          new VulogAimaErrorResponseDtoBuilder()
            .withCode('sample_error_code')
            .build(),
        );

      // Mock AiMA trip termination
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.backOfficeExpert(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
          {
            cmd: 'Trip Termination',
          },
        )
        .replyOnce(200);
    });

    it('check expectation', async () => {
      const response = await requestFor(carEntity.vulogId);

      expect(response.statusCode).toEqual(HttpStatus.INTERNAL_SERVER_ERROR);

      expect(response.body.message).toEqual(
        'The rental was canceled due to technical error. Sorry for the inconvenience - Cause: Due to technical issues, your rental has been cancelled. Please note that your rental is not eligible for billing. Sorry for the inconvenience, you should try to book car again.',
      );

      const updatedReservationEntity: ReservationEntity =
        await reservationRepo.findOne({
          where: { id: reservationEntity.id },
        });

      expect(updatedReservationEntity.status).toEqual(
        ReservationStatus.Cancelled,
      );
      expect(updatedReservationEntity.canceledAt).toBeDefined();
      expect(updatedReservationEntity.deletedBy).toEqual(
        'The rental was canceled by CRS due to technical problems with Vulog',
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('💧 Flag `startRentalAutoUnlocked` is 🔴 OFF, unlock car failed, retry unlocking car again, unlock car successfully, then automatically start rental but failed, cancel reservation successfully', () => {
    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());
    const vvgVehicleStatusDto: VvgVehicleStatusDto =
      new VvgVehicleStatusDtoBuilder()
        .withId(carEntity.vulogId)
        .withDoorsLocked(true)
        .withImmobilizerOn(true)
        .build();
    const stationDto: StationDto = new StationDtoBuilder().build();
    const fleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withType(VulogFleetServiceType.FreeFloating)
        .withVehicles([carEntity.id])
        .withZones([stationDto.zoneId])
        .withId(config.vulogConfig.serviceId.value)
        .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(carEntity.vulogId)
      .makePlateNumber(carEntity.plate)
      .makeStatus(ReservationStatus.Converted)
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(carEntity.plate)
      .makeBsgUserId(userId)
      .build();

    beforeAll(async () => {
      await mockCredential();

      spyOn__FeatureFlagService__startRentalAutoUnlocked.mockResolvedValueOnce(
        false,
      );

      spyOn__FeatureFlagService__shouldCancelReservationAfterBestEffort.mockResolvedValueOnce(
        true,
      );

      await carRepo.insert(carEntity);

      await reservationRepo.insert(reservationEntity);

      // Mock cache VvgVehicleStatusDto
      await cacheService.set(
        `${config.redisConfig.singleTelemetryCarInfo.cacheKey}/${carEntity.vulogId}`,
        vvgVehicleStatusDto,
        { ttl: config.redisConfig.singleTelemetryCarInfo.ttl },
      );

      // Mock cache fleetService
      await cacheService.set(
        `${config.redisConfig.fleetServices.cacheKey}`,
        [fleetServiceDto],
        { ttl: config.redisConfig.fleetServices.ttl },
      );

      // Mock cache zoneStationMap
      await cacheService.set(
        `${config.redisConfig.stations.cacheKey}`,
        [stationDto],
        { ttl: config.redisConfig.stations.ttl },
      );
      await cacheService.set(
        `${config.redisConfig.zoneStationMap.cacheKey}`,
        MapUtils.build([stationDto], (station) => station.zoneId),
        { ttl: config.redisConfig.zoneStationMap.ttl },
      );

      // Mock VVG unlockAndMobilize failed
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.vvgUnlockAndMobilizeVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(
          HttpStatus.GONE,
          new VVGErrorResponseDtoBuilder()
            .withCodeStr('carConnectionProblem')
            .build(),
        );

      // Mock VVG wakeup ongoing
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      // Mock VVG ping success
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      // Mock VVG unlockAndMobilize success
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.vvgUnlockAndMobilizeVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(() => {
          vvgVehicleStatusDto.status.locked = false;
          vvgVehicleStatusDto.status.immobilizerOn = false;

          return [200];
        });

      // Mock refetch vehicle status
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, vvgVehicleStatusDto);

      // Mock AiMA on-going journey
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, []);
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, null);

      // Mock AiMA start rental
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.startOrEndTrip(
            new VulogJourneyOrTripId(reservationEntity.vulogTripId),
          )}`,
        )
        .replyOnce(
          400,
          new VulogAimaErrorResponseDtoBuilder()
            .withCode('sample_error_code')
            .build(),
        );

      // Mock AiMA trip termination
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.backOfficeExpert(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
          {
            cmd: 'Trip Termination',
          },
        )
        .replyOnce(200);
    });

    it('check expectation', async () => {
      const response = await requestFor(carEntity.vulogId);

      expect(response.statusCode).toEqual(HttpStatus.INTERNAL_SERVER_ERROR);

      expect(response.body.message).toEqual(
        'The rental was canceled due to technical error. Sorry for the inconvenience - Cause: Due to technical issues, your rental has been cancelled. Please note that your rental is not eligible for billing. Sorry for the inconvenience, you should try to book car again.',
      );

      const updatedReservationEntity: ReservationEntity =
        await reservationRepo.findOne({
          where: { id: reservationEntity.id },
        });

      expect(updatedReservationEntity.status).toEqual(
        ReservationStatus.Cancelled,
      );
      expect(updatedReservationEntity.canceledAt).toBeDefined();
      expect(updatedReservationEntity.deletedBy).toEqual(
        'The rental was canceled by CRS due to technical problems with Vulog',
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('🔥 Flag `startRentalAutoUnlocked` is 🟢 ON, start rental successfully, side effect auto unlocking car successfully', () => {
    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());
    const vvgVehicleStatusDto: VvgVehicleStatusDto =
      new VvgVehicleStatusDtoBuilder()
        .withId(carEntity.vulogId)
        .withDoorsLocked(true)
        .withImmobilizerOn(true)
        .build();
    const stationDto: StationDto = new StationDtoBuilder().build();
    const fleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withType(VulogFleetServiceType.FreeFloating)
        .withVehicles([carEntity.id])
        .withZones([stationDto.zoneId])
        .withId(config.vulogConfig.serviceId.value)
        .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(carEntity.vulogId)
      .makePlateNumber(carEntity.plate)
      .makeStatus(ReservationStatus.Converted)
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(carEntity.plate)
      .makeBsgUserId(userId)
      .build();
    const vulogJourneyDto: VulogJourneyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(carEntity.vulogId)
      .withServiceId(fleetServiceDto.id)
      .build();

    beforeAll(async () => {
      await mockCredential();

      spyOn__FeatureFlagService__startRentalAutoUnlocked.mockResolvedValueOnce(
        true,
      );

      await carRepo.insert(carEntity);

      await reservationRepo.insert(reservationEntity);

      // Mock cache VvgVehicleStatusDto
      await cacheService.set(
        `${config.redisConfig.singleTelemetryCarInfo.cacheKey}/${carEntity.vulogId}`,
        vvgVehicleStatusDto,
        { ttl: config.redisConfig.singleTelemetryCarInfo.ttl },
      );

      // Mock cache fleetService
      await cacheService.set(
        `${config.redisConfig.fleetServices.cacheKey}`,
        [fleetServiceDto],
        { ttl: config.redisConfig.fleetServices.ttl },
      );

      // Mock cache zoneStationMap
      await cacheService.set(
        `${config.redisConfig.stations.cacheKey}`,
        [stationDto],
        { ttl: config.redisConfig.stations.ttl },
      );
      await cacheService.set(
        `${config.redisConfig.zoneStationMap.cacheKey}`,
        MapUtils.build([stationDto], (station) => station.zoneId),
        { ttl: config.redisConfig.zoneStationMap.ttl },
      );

      // Mock AiMA on-going journey
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, []);
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, null);

      // Mock AiMA start rental
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.startOrEndTrip(
            new VulogJourneyOrTripId(reservationEntity.vulogTripId),
          )}`,
        )
        .replyOnce(201, vulogJourneyDto);
    });

    it('check expectation', async () => {
      const response = await requestFor(carEntity.vulogId);

      expect(response.statusCode).toEqual(HttpStatus.CREATED);

      expect(response.body.result).toEqual({
        immobilized: false,
        locked: false,
      } as ConsumerLockAndImmobilizeResponseDtoV1);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('🔥 Flag `startRentalAutoUnlocked` is 🟢 ON, start rental failed, retry starting rental again, start rental successfully', () => {
    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());
    const vvgVehicleStatusDto: VvgVehicleStatusDto =
      new VvgVehicleStatusDtoBuilder()
        .withId(carEntity.vulogId)
        .withDoorsLocked(true)
        .withImmobilizerOn(true)
        .build();
    const stationDto: StationDto = new StationDtoBuilder().build();
    const fleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withType(VulogFleetServiceType.FreeFloating)
        .withVehicles([carEntity.id])
        .withZones([stationDto.zoneId])
        .withId(config.vulogConfig.serviceId.value)
        .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(carEntity.vulogId)
      .makePlateNumber(carEntity.plate)
      .makeStatus(ReservationStatus.Converted)
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(carEntity.plate)
      .makeBsgUserId(userId)
      .build();
    const vulogJourneyDto: VulogJourneyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(carEntity.vulogId)
      .withServiceId(fleetServiceDto.id)
      .build();

    beforeAll(async () => {
      await mockCredential();

      spyOn__FeatureFlagService__startRentalAutoUnlocked.mockResolvedValueOnce(
        true,
      );

      await carRepo.insert(carEntity);

      await reservationRepo.insert(reservationEntity);

      // Mock cache VvgVehicleStatusDto
      await cacheService.set(
        `${config.redisConfig.singleTelemetryCarInfo.cacheKey}/${carEntity.vulogId}`,
        vvgVehicleStatusDto,
        { ttl: config.redisConfig.singleTelemetryCarInfo.ttl },
      );

      // Mock cache fleetService
      await cacheService.set(
        `${config.redisConfig.fleetServices.cacheKey}`,
        [fleetServiceDto],
        { ttl: config.redisConfig.fleetServices.ttl },
      );

      // Mock cache zoneStationMap
      await cacheService.set(
        `${config.redisConfig.stations.cacheKey}`,
        [stationDto],
        { ttl: config.redisConfig.stations.ttl },
      );
      await cacheService.set(
        `${config.redisConfig.zoneStationMap.cacheKey}`,
        MapUtils.build([stationDto], (station) => station.zoneId),
        { ttl: config.redisConfig.zoneStationMap.ttl },
      );

      // Mock AiMA on-going journey
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, []);
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, null);

      // Mock AiMA start rental failed
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.startOrEndTrip(
            new VulogJourneyOrTripId(reservationEntity.vulogTripId),
          )}`,
        )
        .replyOnce(
          400,
          new VulogAimaErrorResponseDtoBuilder()
            .withCode('carConnectionProblem')
            .build(),
        );

      // Mock VVG wakeup ongoing
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      // Mock VVG ping success
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      // Mock AiMA start rental failed
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.startOrEndTrip(
            new VulogJourneyOrTripId(reservationEntity.vulogTripId),
          )}`,
        )
        .replyOnce(200, vulogJourneyDto);
    });

    it('check expectation', async () => {
      const response = await requestFor(carEntity.vulogId);

      expect(response.statusCode).toEqual(HttpStatus.CREATED);

      expect(response.body.result).toEqual({
        immobilized: false,
        locked: false,
      } as ConsumerLockAndImmobilizeResponseDtoV1);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('💧 Flag `startRentalAutoUnlocked` is 🟢 ON, start rental failed, retry starting rental again, start rental failed, cancel reservation successfully', () => {
    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());
    const vvgVehicleStatusDto: VvgVehicleStatusDto =
      new VvgVehicleStatusDtoBuilder()
        .withId(carEntity.vulogId)
        .withDoorsLocked(true)
        .withImmobilizerOn(true)
        .build();
    const stationDto: StationDto = new StationDtoBuilder().build();
    const fleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withType(VulogFleetServiceType.FreeFloating)
        .withVehicles([carEntity.id])
        .withZones([stationDto.zoneId])
        .withId(config.vulogConfig.serviceId.value)
        .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(carEntity.vulogId)
      .makePlateNumber(carEntity.plate)
      .makeStatus(ReservationStatus.Converted)
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(carEntity.plate)
      .makeBsgUserId(userId)
      .build();

    beforeAll(async () => {
      await mockCredential();

      spyOn__FeatureFlagService__startRentalAutoUnlocked.mockResolvedValueOnce(
        true,
      );

      spyOn__FeatureFlagService__shouldCancelReservationAfterBestEffort.mockResolvedValueOnce(
        true,
      );

      await carRepo.insert(carEntity);

      await reservationRepo.insert(reservationEntity);

      // Mock cache VvgVehicleStatusDto
      await cacheService.set(
        `${config.redisConfig.singleTelemetryCarInfo.cacheKey}/${carEntity.vulogId}`,
        vvgVehicleStatusDto,
        { ttl: config.redisConfig.singleTelemetryCarInfo.ttl },
      );

      // Mock cache fleetService
      await cacheService.set(
        `${config.redisConfig.fleetServices.cacheKey}`,
        [fleetServiceDto],
        { ttl: config.redisConfig.fleetServices.ttl },
      );

      // Mock cache zoneStationMap
      await cacheService.set(
        `${config.redisConfig.stations.cacheKey}`,
        [stationDto],
        { ttl: config.redisConfig.stations.ttl },
      );
      await cacheService.set(
        `${config.redisConfig.zoneStationMap.cacheKey}`,
        MapUtils.build([stationDto], (station) => station.zoneId),
        { ttl: config.redisConfig.zoneStationMap.ttl },
      );

      // Mock AiMA on-going journey
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, []);
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, null);

      // Mock AiMA start rental failed
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.startOrEndTrip(
            new VulogJourneyOrTripId(reservationEntity.vulogTripId),
          )}`,
        )
        .replyOnce(
          400,
          new VulogAimaErrorResponseDtoBuilder()
            .withCode('carConnectionProblem')
            .build(),
        );

      // Mock VVG wakeup ongoing
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      // Mock VVG ping success
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      // Mock AiMA start rental failed
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.startOrEndTrip(
            new VulogJourneyOrTripId(reservationEntity.vulogTripId),
          )}`,
        )
        .replyOnce(400);
    });

    it('check expectation', async () => {
      const response = await requestFor(carEntity.vulogId);

      expect(response.statusCode).toEqual(HttpStatus.INTERNAL_SERVER_ERROR);

      expect(response.body.message).toEqual(
        'The rental was canceled due to technical error. Sorry for the inconvenience - Cause: Due to technical issues, your rental has been cancelled. Please note that your rental is not eligible for billing. Sorry for the inconvenience, you should try to book car again.',
      );

      const updatedReservationEntity: ReservationEntity =
        await reservationRepo.findOne({
          where: { id: reservationEntity.id },
        });

      expect(updatedReservationEntity.status).toEqual(
        ReservationStatus.Cancelled,
      );
      expect(updatedReservationEntity.canceledAt).toBeDefined();
      expect(updatedReservationEntity.deletedBy).toEqual(
        'The rental was canceled by CRS due to technical problems with Vulog',
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('💧 Car is not found', () => {
    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());

    beforeAll(async () => {
      await mockCredential();

      spyOn__FeatureFlagService__startRentalAutoUnlocked.mockResolvedValueOnce(
        false,
      );
    });

    it('check expectation', async () => {
      const response = await requestFor(carEntity.vulogId);

      expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);

      expect(response.body.message).toEqual(
        `An error occurred during the reservation flow - Cause: The car [${carEntity.vulogId}] is not found`,
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('💧 No BlueSG on-going rental', () => {
    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());

    beforeAll(async () => {
      await mockCredential();

      spyOn__FeatureFlagService__startRentalAutoUnlocked.mockResolvedValueOnce(
        false,
      );

      await carRepo.insert(carEntity);
    });

    it('check expectation', async () => {
      const response = await requestFor(carEntity.vulogId);

      expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);

      expect(response.body.message).toEqual(
        `The customer (BSG User Id: ${userId}) does not have on-going rental in BlueSG`,
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('💧 Target car is different from allocated car in on-going rental', () => {
    const targetCarEntity: CarEntity = CarEntity.from(new CarBuilder().build());
    const allocatedCarEntity: CarEntity = CarEntity.from(
      new CarBuilder().build(),
    );

    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(allocatedCarEntity.vulogId)
      .makePlateNumber(allocatedCarEntity.plate)
      .makeStatus(ReservationStatus.Converted)
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(allocatedCarEntity.plate)
      .makeBsgUserId(userId)
      .build();

    beforeAll(async () => {
      await mockCredential();

      spyOn__FeatureFlagService__startRentalAutoUnlocked.mockResolvedValueOnce(
        false,
      );

      await carRepo.insert(targetCarEntity);

      await reservationRepo.insert(reservationEntity);
    });

    it('check expectation', async () => {
      const response = await requestFor(targetCarEntity.vulogId);

      expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);

      expect(response.body.message).toEqual(
        `The target car (Vulog Car ID: ${targetCarEntity.vulogId}) is not the same as the car in user-current on-going rental (Vulog Car ID: ${allocatedCarEntity.vulogId})`,
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('💧 Failed to unlock and mobilize after multiple retries to VVG', () => {
    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());
    const vvgVehicleStatusDto: VvgVehicleStatusDto =
      new VvgVehicleStatusDtoBuilder()
        .withId(carEntity.vulogId)
        .withDoorsLocked(true)
        .withImmobilizerOn(true)
        .build();
    const stationDto: StationDto = new StationDtoBuilder().build();
    const fleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withType(VulogFleetServiceType.FreeFloating)
        .withVehicles([carEntity.id])
        .withZones([stationDto.zoneId])
        .withId(config.vulogConfig.serviceId.value)
        .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(carEntity.vulogId)
      .makePlateNumber(carEntity.plate)
      .makeStatus(ReservationStatus.Converted)
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(carEntity.plate)
      .makeBsgUserId(userId)
      .build();

    beforeAll(async () => {
      await mockCredential();

      spyOn__FeatureFlagService__startRentalAutoUnlocked.mockResolvedValueOnce(
        false,
      );

      await carRepo.insert(carEntity);

      await reservationRepo.insert(reservationEntity);

      // Mock cache VvgVehicleStatusDto
      await cacheService.set(
        `${config.redisConfig.singleTelemetryCarInfo.cacheKey}/${carEntity.vulogId}`,
        vvgVehicleStatusDto,
        { ttl: config.redisConfig.singleTelemetryCarInfo.ttl },
      );

      // Mock cache fleetService
      await cacheService.set(
        `${config.redisConfig.fleetServices.cacheKey}`,
        [fleetServiceDto],
        { ttl: config.redisConfig.fleetServices.ttl },
      );

      // Mock cache zoneStationMap
      await cacheService.set(
        `${config.redisConfig.stations.cacheKey}`,
        [stationDto],
        { ttl: config.redisConfig.stations.ttl },
      );
      await cacheService.set(
        `${config.redisConfig.zoneStationMap.cacheKey}`,
        MapUtils.build([stationDto], (station) => station.zoneId),
        { ttl: config.redisConfig.zoneStationMap.ttl },
      );

      // Mock VVG unlockAndMobilize failed
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.vvgUnlockAndMobilizeVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(
          HttpStatus.GONE,
          new VVGErrorResponseDtoBuilder()
            .withCodeStr('carConnectionProblem')
            .build(),
        );

      // Mock VVG wakeup ongoing
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      // Mock VVG ping failed
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(HttpStatus.GONE)
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(HttpStatus.GONE)
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(HttpStatus.GONE)
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(HttpStatus.GONE)
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(HttpStatus.GONE)
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(HttpStatus.GONE)
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(HttpStatus.GONE);
    });

    it('check expectation', async () => {
      const response = await requestFor(carEntity.vulogId);

      expect(response.statusCode).toEqual(HttpStatus.INTERNAL_SERVER_ERROR);

      expect(response.body.message).toEqual(
        `(1) Failed to unlock and mobilize the vehicle ${carEntity.vulogId}. Please retry again until successful response is returned.`,
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('💧 Unlock successfully but the latest state of the car does not reflect new changes yet', () => {
    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());
    const vvgVehicleStatusDto: VvgVehicleStatusDto =
      new VvgVehicleStatusDtoBuilder()
        .withId(carEntity.vulogId)
        .withDoorsLocked(true)
        .withImmobilizerOn(true)
        .build();
    const stationDto: StationDto = new StationDtoBuilder().build();
    const fleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withType(VulogFleetServiceType.FreeFloating)
        .withVehicles([carEntity.id])
        .withZones([stationDto.zoneId])
        .withId(config.vulogConfig.serviceId.value)
        .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(carEntity.vulogId)
      .makePlateNumber(carEntity.plate)
      .makeStatus(ReservationStatus.Converted)
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(carEntity.plate)
      .makeBsgUserId(userId)
      .build();

    beforeAll(async () => {
      await mockCredential();

      spyOn__FeatureFlagService__startRentalAutoUnlocked.mockResolvedValueOnce(
        false,
      );

      await carRepo.insert(carEntity);

      await reservationRepo.insert(reservationEntity);

      // Mock cache VvgVehicleStatusDto
      await cacheService.set(
        `${config.redisConfig.singleTelemetryCarInfo.cacheKey}/${carEntity.vulogId}`,
        vvgVehicleStatusDto,
        { ttl: config.redisConfig.singleTelemetryCarInfo.ttl },
      );

      // Mock cache fleetService
      await cacheService.set(
        `${config.redisConfig.fleetServices.cacheKey}`,
        [fleetServiceDto],
        { ttl: 10000 },
      );

      // Mock cache zoneStationMap
      await cacheService.set(
        `${config.redisConfig.stations.cacheKey}`,
        [stationDto],
        { ttl: config.redisConfig.stations.ttl },
      );
      await cacheService.set(
        `${config.redisConfig.zoneStationMap.cacheKey}`,
        MapUtils.build([stationDto], (station) => station.zoneId),
        { ttl: config.redisConfig.zoneStationMap.ttl },
      );

      // Mock VVG unlockAndMobilize
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.vvgUnlockAndMobilizeVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(() => {
          return [200];
        });

      // Mock refetch vehicle status
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, vvgVehicleStatusDto)
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, vvgVehicleStatusDto)
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, vvgVehicleStatusDto)
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, vvgVehicleStatusDto)
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, vvgVehicleStatusDto)
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, vvgVehicleStatusDto)
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .replyOnce(200, vvgVehicleStatusDto); // still locked and immobilized
    });

    it('check expectation', async () => {
      const response = await requestFor(carEntity.vulogId);

      expect(response.statusCode).toEqual(HttpStatus.INTERNAL_SERVER_ERROR);

      expect(response.body.message).toEqual(
        `(2) Failed to unlock and mobilize the vehicle ${carEntity.vulogId}. Please retry again until successful response is returned.`,
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });
});
