import * as supertest from 'supertest';

import { HttpStatus, INestApplication } from '@nestjs/common';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { faker } from '@faker-js/faker';
import { getRepositoryToken } from '@nestjs/typeorm';
import MockAdapter from 'axios-mock-adapter';
import { DateTime } from 'luxon';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { RootRouteSegment, RouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { config } from 'src/config';
import { ReservationResponseDtoV1 } from 'src/controllers/dtos/reservation.response.v1.dto';
import { BillingInternalApiService } from 'src/external/billing/apis/billing.internal.api.service';
import { BillingExternalService } from 'src/external/billing/billing.external.service';
import { CacheService } from 'src/logic/cache/cache.service';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogUserService } from 'src/logic/vulog/vulog-user.service';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { Reservation } from 'src/model/reservation.domain';
import { AppBuilder } from 'test/helpers/app.builder';
import { AttachRentalPackageRequestDtoV1Builder } from 'test/helpers/builders/dtos/attach-rental-package-request-dto.builder';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { createTestVulogUserEntity } from 'test/helpers/builders/entities/test-vulog-user-entity.builder';
import { RealtimeCarBuilder } from 'test/helpers/builders/realtime-car.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { Repository } from 'typeorm';
import { InternalApiService } from '~shared/http/internal-api.service';

const testingApiRoute = ApiRouteUtils.buildApiRouteSegment(
  '' as any,
  '' as any,
  ['api/v1', 'reservation', RouteSegment.Reservation.AttachRentalPackage],
);

const userId = aUUID();

const email = faker.internet.email();

describe(`[E2E] GET: ${testingApiRoute}`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let reserveRepo: Repository<ReservationEntity>;
  let billingInternalApiService: BillingInternalApiService;
  let mockHttpAdapterService: MockAdapter;
  let mockUssApiService: MockAdapter;
  let vulogUserService: VulogUserService;
  let vulogUserRepo: Repository<VulogUserEntity>;
  let cacheService: CacheService;
  let vulogCarClient: VulogCarService;

  const mockUserServicesAndProfiles = createTestVulogUserServicesDto();
  const mockAuthResponse = createTestVulogAuthResponse();
  const mockVulogUserEntity = createTestVulogUserEntity(
    userId,
    mockUserServicesAndProfiles.profiles[0].profileId,
  );

  function requestFor(
    reserveId: string,
    payload: {
      packageId: string;
      name: string;
      title: string;
      duration: number;
      price: number;
      minAllowedBatteryPercentage: number;
    },
  ): supertest.Test {
    const injectedUrl = testingApiRoute.replace(':reservationId', reserveId);

    const superTest = supertest(app.getHttpServer())
      .put(`${injectedUrl}`)
      .send(payload);
    return superTest;
  }

  const mockUssUser = new InternalUserDetailResponseDtoV1Builder()
    .withId(userId)
    .withEmail(email)
    .build();

  function disableCron() {
    process.env.CRON_EXPIRE_PRE_RESERVATIONS_ENABLED = 'false';
    process.env.CRON_EXPIRE_RESERVATIONS_ENABLED = 'false';
    process.env.CRON_FETCH_REAL_TIME_VULOG_VEHICLES_ENABLED = 'false';
    process.env.CRON_FETCH_VULOG_FLEET_SERVICES_ENABLED = 'false';
    process.env.CRON_GET_CAR_AVAILABILITY_ENABLED = 'false';
    process.env.CRON_POP_PRE_RESERVATIONS_ENABLED = 'false';
    process.env.CRON_FETCH_ALL_STATIONS_ENABLED = 'false';
  }

  async function settingUp() {
    disableCron();

    app = await appBuilder
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(aUUID()), true)
      .buildAndStart();

    reserveRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );

    vulogUserRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );

    billingInternalApiService = app.get<BillingExternalService>(
      BillingExternalService,
    ).internalApi;

    cacheService = app.get(CacheService);

    const httpAdapterService = app.get(HttpAdapterService);

    mockHttpAdapterService = new MockAdapter(
      httpAdapterService.httpService.axiosRef,
    );
    const internalApiService1: InternalApiService = app.get(
      config.httpServiceConfig.userSubscriptionName,
    );
    mockUssApiService = new MockAdapter(
      internalApiService1.httpService.axiosRef,
    );
    vulogUserService = app.get(VulogUserService);
    vulogCarClient = app.get(VulogCarService);

    const userDto = createTestVulogUserDto();

    userDto.id = userId;
    userDto.email = email;

    mockHttpAdapterService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, userDto);

    mockHttpAdapterService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(userDto.id),
        )}`,
      )
      .reply(200, mockUserServicesAndProfiles);

    mockHttpAdapterService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(mockUserServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, mockUserServicesAndProfiles);

    mockHttpAdapterService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, mockAuthResponse);

    mockUssApiService
      .onGet(
        `${
          config.httpServiceConfig.userSubscriptionUrl
        }${ApiRouteUtils.buildApiRouteSegment(
          RootRouteSegment.Internal,
          ApiVersion.V1,
          ['users', userId],
        )}`,
      )
      .reply(200, BaseResponseDto.success(mockUssUser));

    await vulogUserService.save(mockVulogUserEntity.toVulogUser());
  }

  async function destroy() {
    await reserveRepo.delete({});
    await vulogUserRepo.delete({});
    await cacheService.clearAll();
    jest.resetAllMocks();
    await app.close();
  }

  describe('When send a dynamic rental package and it is valid', () => {
    const reserveId = aUUID();
    let responseObject: supertest.Response;

    const rentalPackageParams = new AttachRentalPackageRequestDtoV1Builder()
      .withMinAllowedBatteryPercentage(60)
      .build();

    const startStationDto = new StationDtoBuilder().build();

    const vulogCarId = aUUID();

    const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withZones([startStationDto.zoneId])
      .withVehicles([vulogCarId])
      .build();

    beforeAll(async () => {
      await settingUp();

      jest
        .spyOn(billingInternalApiService, 'checkPackageAvailable')
        .mockResolvedValue(
          Promise.resolve({
            isAvailable: true,
            isDynamic: true,
          }),
        );

      // VulogCarService.getSpecificCarInRealtime
      jest
        .spyOn(vulogCarClient, 'getSpecificCarInRealtime')
        .mockResolvedValue(
          new RealtimeCarBuilder().withEnergyLevel(100).build(),
        );

      // VulogFleetService.getFleetServices
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .replyOnce(200, [mockVulogFleetServiceDto]);

      await reserveRepo.save(
        new ReservationEntityBuilder()
          .makeExpiresAt(DateTime.now().plus({ hours: 2 }).toJSDate())
          .makeStatus(ReservationStatus.Reserved)
          .makeReservationId(reserveId)
          .makeLocal(true)
          .makeCarId(vulogCarId)
          .build(),
      );

      responseObject = await requestFor(reserveId, rentalPackageParams);
    });

    it('Should return status ok', () => {
      expect(responseObject.status).toBe(HttpStatus.OK);
    });

    it("Should update the reservation's rental package info field", async () => {
      const expectedRes = await reserveRepo.findOneBy({ id: reserveId });

      expect(responseObject.body.result).toEqual(
        ReservationResponseDtoV1.fromReservation(
          Reservation.from(expectedRes),
          expectedRes.carModel,
        ),
      );

      expect({
        ...rentalPackageParams,
        isDynamic: true,
      }).toMatchObject(expectedRes.rentalPackageData);
    });

    afterAll(async () => {
      await destroy();
    });
  });

  describe('When send a normal rental package and it is valid', () => {
    const reserveId = aUUID();
    let responseObject: supertest.Response;
    const rentalPackage = new AttachRentalPackageRequestDtoV1Builder()
      .withMinAllowedBatteryPercentage(60)
      .build();

    const startStationDto = new StationDtoBuilder().build();

    const vulogCarId = aUUID();

    const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withZones([startStationDto.zoneId])
      .withVehicles([vulogCarId])
      .build();

    beforeAll(async () => {
      await settingUp();

      jest
        .spyOn(billingInternalApiService, 'checkPackageAvailable')
        .mockResolvedValue(
          Promise.resolve({
            isAvailable: true,
            isDynamic: false,
          }),
        );

      // VulogFleetService.getFleetServices
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .replyOnce(200, [mockVulogFleetServiceDto]);

      // VulogCarService.getSpecificCarInRealtime
      jest
        .spyOn(vulogCarClient, 'getSpecificCarInRealtime')
        .mockResolvedValue(
          new RealtimeCarBuilder().withEnergyLevel(100).build(),
        );

      await reserveRepo.save(
        new ReservationEntityBuilder()
          .makeExpiresAt(DateTime.now().plus({ hours: 2 }).toJSDate())
          .makeStatus(ReservationStatus.Reserved)
          .makeReservationId(reserveId)
          .makeLocal(true)
          .makeCarId(vulogCarId)
          .build(),
      );

      responseObject = await requestFor(reserveId, rentalPackage);
    });

    it('Should return status ok', () => {
      expect(responseObject.status).toBe(HttpStatus.OK);
    });

    it("Should update the reservation's rental package info field", async () => {
      const expectedRes = await reserveRepo.findOneBy({ id: reserveId });

      expect(responseObject.body.result).toEqual(
        ReservationResponseDtoV1.fromReservation(
          Reservation.from(expectedRes),
          expectedRes.carModel,
        ),
      );

      expect({
        ...rentalPackage,
        isDynamic: false,
      }).toMatchObject(expectedRes.rentalPackageData);
    });

    afterAll(async () => {
      await destroy();
    });
  });

  describe('When send a rental package and it is not valid', () => {
    const reserveId = aUUID();
    let responseObject: supertest.Response;
    const rentalPackage = new AttachRentalPackageRequestDtoV1Builder().build();

    beforeAll(async () => {
      await settingUp();

      jest
        .spyOn(billingInternalApiService, 'checkPackageAvailable')
        .mockResolvedValue(
          Promise.resolve({
            isAvailable: false,
            isDynamic: false,
          }),
        );

      await reserveRepo.save(
        new ReservationEntityBuilder()
          .makeExpiresAt(DateTime.now().plus({ hours: 2 }).toJSDate())
          .makeStatus(ReservationStatus.Reserved)
          .makeReservationId(reserveId)
          .makeLocal(true)
          .build(),
      );

      responseObject = await requestFor(reserveId, rentalPackage);
    });

    it('Should return status NOT FOUND', () => {
      expect(responseObject.status).toBe(HttpStatus.NOT_FOUND);
    });

    it('Should throw error with right message', () => {
      expect(responseObject.body.message).toEqual(
        'This package is wrong or not available!',
      );
    });

    afterAll(async () => {
      await destroy();
    });
  });

  describe('When send a not exist reservation id', () => {
    const reserveId = aUUID();
    let responseObject: supertest.Response;
    const rentalPackage = new AttachRentalPackageRequestDtoV1Builder().build();

    beforeAll(async () => {
      await settingUp();

      jest
        .spyOn(billingInternalApiService, 'checkPackageAvailable')
        .mockResolvedValue(
          Promise.resolve({
            isAvailable: true,
            isDynamic: true,
          }),
        );

      responseObject = await requestFor(reserveId, rentalPackage);
    });

    it('Should return status BAD_REQUEST', () => {
      expect(responseObject.status).toBe(HttpStatus.BAD_REQUEST);
    });

    it('Should throw error with right message', () => {
      expect(responseObject.body.message).toEqual(
        'An error occurred during the reservation flow - Cause: No reservation is found',
      );
    });

    afterAll(async () => {
      await destroy();
    });
  });

  // Blocking the mobile flow, at the moment
  describe.skip('Should throw error when reservation is non-local', () => {
    const reserveId = aUUID();
    let responseObject: supertest.Response;
    const rentalPackage = new AttachRentalPackageRequestDtoV1Builder().build();

    beforeAll(async () => {
      await settingUp();

      await reserveRepo.save(
        new ReservationEntityBuilder()
          .makeExpiresAt(DateTime.now().plus({ hours: 2 }).toJSDate())
          .makeReservationId(reserveId)
          .makeLocal(false)
          .build(),
      );

      responseObject = await requestFor(reserveId, rentalPackage);
    });

    it('Should return status bad request', () => {
      expect(responseObject.status).toBe(HttpStatus.BAD_REQUEST);
    });

    it('Should return the error message', async () => {
      expect(responseObject.body.message).toEqual(
        'An error occurred during the reservation flow - Cause: Your on-going reservation is non-local. Could not attach rental package',
      );
    });

    afterAll(async () => {
      await destroy();
    });
  });
});
