import * as supertest from 'supertest';

import { HttpStatus, INestApplication } from '@nestjs/common';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { AppBuilder } from 'test/helpers/app.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aString, aUUID } from 'test/helpers/random-data.helper';
import { Repository } from 'typeorm';

import MockAdapter from 'axios-mock-adapter';
import {
  BsgUserId,
  CarModel,
  VulogCarId,
  VulogJourneyOrTripId,
  VulogProfileId,
  VulogUserId,
} from 'src/common/tiny-types';
import { config } from 'src/config';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';

import { EventName } from '@bluesg-2/event-library';
import { faker } from '@faker-js/faker';
import { DateTime } from 'luxon';
import * as QueryString from 'qs';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { GRACE_PERIOD_ERROR } from 'src/common/constants/error-codes';
import { RESERVATION_ERROR_MESSAGE } from 'src/common/constants/messages';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ReservationResponseDtoV1 } from 'src/controllers/dtos/reservation.response.v1.dto';
import { ReservationRequestDto } from 'src/controllers/dtos/reservation/request/reservation.request.dto';
import {
  SubscriptionStatus,
  UserStatus,
} from 'src/external/user-subscription/constants';
import { InternalUserDetailResponseDtoV1 } from 'src/external/user-subscription/dtos/response/internal.user-detail.v1.response.dto';
import { AvailabilityService } from 'src/logic/availability.service';
import { CacheService } from 'src/logic/cache/cache.service';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { ReservationService } from 'src/logic/reservation.service';
import { ReservationCacheService } from 'src/logic/reservation/reservation.cache.service';
import { StationPaths } from 'src/logic/station/station-paths';
import {
  VulogRealTimeBookingStatus,
  VulogRealTimeVehicleStatus,
} from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { Reservation } from 'src/model/reservation.domain';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { VulogCarStaticInfoDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car-static-info.dto.builder';
import { VulogCarRealTimeDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import {
  VulogBookingBuilder,
  VulogJourneyDtoBuilder,
} from 'test/helpers/builders/dtos/vulog/vulog-journey.dto.builder';
import { CUSTOMER_JWT_MOCK_TOKEN } from 'test/helpers/customer-jwt-guard.mocker';
import { aMD5 } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';
import { EventBusService } from '~shared/event-bus/event-bus.service';
import { VulogUserBuilder } from '../../../helpers/builders/vulog-user.builder';
import { UssApiMocker } from '../../../helpers/external-services/uss.api-mocker';
import { VulogApiMocker } from '../../../helpers/external-services/vulog.api-mocker';

const userId = aUUID();

const email = faker.internet.email();

const authorizedUserToken = JSON.stringify({
  id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
});

const reservationUrl = '/api/v1/reservation/allocate/:stationId';

describe(`[E2E] POST: ${reservationUrl}`, () => {
  let app: INestApplication;

  let mockHttpAdapterService: MockAdapter;
  let mockUssApiService: MockAdapter;

  let cacheService: CacheService;
  let reservationService: ReservationService;
  let eventBusService: EventBusService;
  let reservationCacheService: ReservationCacheService;
  let availabilityService: AvailabilityService;

  let userRepo: Repository<VulogUserEntity>;
  let reservationRepo: Repository<ReservationEntity>;
  let carRepo: Repository<CarEntity>;
  let spyOn_EventBusService__nonBlockingEmit: jest.SpyInstance;
  let spyOn_AvailabilityService__getAvailableCarsForSpecificStation: jest.SpyInstance;

  const testAuthResponse = createTestVulogAuthResponse();
  const testVulogUser = VulogUserBuilder.allSync()
    .withBsgUserId(new BsgUserId(userId))
    .build();

  const carId: string = aUUID();
  const targetCarId: string = aUUID();

  function requestFor(
    stationId: string,
    payload?: ReservationRequestDto,
  ): supertest.Test {
    const injectedUrl = reservationUrl.replace(':stationId', stationId);

    const superTest = supertest(app.getHttpServer())
      .post(`${injectedUrl}`)
      .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
      .send(payload);

    return superTest;
  }

  beforeAll(async () => {
    app = await new AppBuilder()
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(aUUID()), true)
      .buildAndStart();

    userRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );

    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );

    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );

    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));

    eventBusService = app.get<EventBusService>(EventBusService);

    mockHttpAdapterService = new VulogApiMocker(app).mock();
    mockUssApiService = new UssApiMocker(app).mock();

    cacheService = app.get(CacheService);
    reservationService = app.get(ReservationService);
    reservationCacheService = app.get(ReservationCacheService);

    availabilityService = app.get<AvailabilityService>(AvailabilityService);

    spyOn_AvailabilityService__getAvailableCarsForSpecificStation = jest.spyOn(
      availabilityService,
      'getAvailableCarsForSpecificStation',
    );

    spyOn_EventBusService__nonBlockingEmit = jest.spyOn(
      eventBusService,
      'nonBlockingEmit',
    );
  });

  afterAll(async () => {
    await cleanResources();

    await app.close();
  });

  async function mockCredential() {
    const userDto = createTestVulogUserDto();

    userDto.email = email;

    mockHttpAdapterService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, userDto);

    const userServicesAndProfiles = createTestVulogUserServicesDto();
    userServicesAndProfiles.userId = userDto.id;

    mockHttpAdapterService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(userDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockHttpAdapterService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockHttpAdapterService
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    mockHttpAdapterService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, testAuthResponse);

    await userRepo.insert(VulogUserEntity.from(testVulogUser));

    mockVulogCredential(app);
  }

  async function cleanResources(): Promise<void> {
    jest.clearAllMocks();

    mockHttpAdapterService.reset();
    mockUssApiService.reset();

    await userRepo.clear();
    await reservationRepo.clear();
    await cacheService.clearAll();
    await carRepo.delete({});
  }

  describe('should be able to allocate car with random car model', () => {
    const zoneId = aMD5();

    const stationDto1 = new StationDtoBuilder().withZoneId(zoneId).build();
    const stationDto2 = new StationDtoBuilder().build();
    const stationDtos = [stationDto1, stationDto2];

    const carDto = {
      static: new VulogCarStaticInfoDtoBuilder().withId(carId).build(),
      realTime: new VulogCarRealTimeDtoBuilder()
        .withId(carId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
        .withBookingStatus(VulogRealTimeBookingStatus.Available)
        .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
        .withAutonomy(55)
        .build(),
    };

    const now = DateTime.now();
    const reservedAt = now.toJSDate();
    const expiresAt = now.plus({ minutes: 15 }).toJSDate();

    const journeyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(carId)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(reservedAt.toISOString())
          .withMaxTripStartDate(expiresAt.toISOString())
          .build(),
      )
      .build();

    const userInfo: InternalUserDetailResponseDtoV1 =
      new InternalUserDetailResponseDtoV1Builder()
        .withId(userId)
        .withUserStatus(UserStatus.VERIFIED)
        .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
        .build();

    beforeAll(async () => {
      await mockCredential();

      // Vulog Car Service - get real time car
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [carDto.realTime]);

      // StationService - Get all stations
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos.map((stn) => stn.toRawJsonObject()),
        });

      // VulogCarService.bookCar
      mockHttpAdapterService
        .onPost(`${VulogPaths.bookJourney(new VulogCarId(carDto.realTime.id))}`)
        .replyOnce(200, journeyDto);

      jest
        .spyOn(availabilityService, 'getAvailableCarsForSpecificStation')
        .mockResolvedValueOnce([
          {
            ...carDto.realTime.toRealtimeCar(),
            model: new CarModel(CarModelEnum.BlueCar),
          } as any,
        ]);

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(JSON.stringify(userInfo)),
        } as BaseResponseDto);

      await carRepo.save([CarEntity.from(carDto.static.toStaticCar().toCar())]);
    });

    it('Check expectation', async () => {
      const response = await requestFor(stationDto1.id);

      const reservation = await reservationService.findOneByTripId(
        new VulogJourneyOrTripId(journeyDto.id),
      );

      expect(reservation.userEmail).toBeDefined();
      expect(reservation.userFullName).toBeDefined();
      expect(reservation.userEmail).toEqual(userInfo.email);
      expect(reservation.userFullName).toEqual(
        `${userInfo.firstName} ${userInfo.lastName}`,
      );

      expect(response.body.result).toEqual(
        JSON.parse(
          JSON.stringify(
            ReservationResponseDtoV1.fromReservation(
              reservation,
              reservation.carModel,
            ),
          ),
        ),
      );

      expect(spyOn_EventBusService__nonBlockingEmit).toBeCalledTimes(1);
      const emittedEvent =
        spyOn_EventBusService__nonBlockingEmit.mock.calls[0][0];
      expect(emittedEvent.event).toBe(EventName.RentalReservationCreatedEvent);
      expect(emittedEvent.payload.id).toBe(reservation.getId);
      expect(emittedEvent.payload.carId).toBe(reservation.carId.value);
      expect(emittedEvent.payload.createdAt).toBeTruthy();
      expect(emittedEvent.payload.reservedAt).toBeTruthy();
      expect(emittedEvent.payload.status).toBe(ReservationStatus.Reserved);
      expect(emittedEvent.payload.userId).toBe(reservation.bsgUserId.value);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('should throw error when there is already on-going reservation for this station', () => {
    const zoneId = aMD5();

    const stationDto1 = new StationDtoBuilder().withZoneId(zoneId).build();

    const carDto = {
      static: new VulogCarStaticInfoDtoBuilder()
        .withModel(CarModelEnum.BlueCar)
        .withId(carId)
        .build(),
      realTime: new VulogCarRealTimeDtoBuilder()
        .withId(carId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
        .withBookingStatus(VulogRealTimeBookingStatus.Available)
        .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
        .withBattery(55)
        .build(),
    };

    const now = DateTime.now();
    const reservedAt = now.toJSDate();
    const expiresAt = now.plus({ minutes: 15 }).toJSDate();

    const journeyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(carId)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(reservedAt.toISOString())
          .withMaxTripStartDate(expiresAt.toISOString())
          .build(),
      )
      .build();

    const existingReservationEntity = new ReservationEntityBuilder()
      .makeBsgUserId(userId)
      .makeStartStationId(stationDto1.id)
      .makeStatus(ReservationStatus.Reserved)
      .makeExpiresAt(DateTime.now().plus({ hours: 1 }).toJSDate())
      .makeCarModel(carDto.static.model.name as CarModelEnum)
      .makeVulogTripId(journeyDto.id)
      .makeLocal(true)
      .build();

    beforeAll(async () => {
      await mockCredential();

      await reservationService.saveOne(
        Reservation.from(existingReservationEntity),
      );

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(
            JSON.stringify(
              new InternalUserDetailResponseDtoV1Builder()
                .withId(userId)
                .withUserStatus(UserStatus.VERIFIED)
                .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
                .build(),
            ),
          ),
        } as BaseResponseDto);
    });

    it('Check expectation', async () => {
      const response = await requestFor(stationDto1.id, {
        carModel: existingReservationEntity.carModel,
      });

      expect(response.body.message).toEqual(
        'An error occurred during the reservation flow - Cause: A car reservation already exists for this station',
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('should throw error when current user is not found in the system', () => {
    beforeAll(async () => {
      await mockCredential();

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: null,
        } as BaseResponseDto);
    });

    it('Check expectation', async () => {
      const response = await requestFor(aUUID(), {
        carModel: CarModelEnum.BlueCar,
        local: false,
      });

      expect(response.body.message).toEqual(
        `An error occurred during the reservation flow - Cause: BlueSG customer (ID: ${userId}) is not found`,
      );

      expect(response.statusCode).toEqual(HttpStatus.FORBIDDEN);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('should be able to mobile screen transition at Start Rental page', () => {
    const zoneId = aMD5();

    const stationDto1 = new StationDtoBuilder().withZoneId(zoneId).build();

    const carDto = {
      static: new VulogCarStaticInfoDtoBuilder()
        .withModel(CarModelEnum.BlueCar)
        .withId(carId)
        .build(),
      realTime: new VulogCarRealTimeDtoBuilder()
        .withId(carId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
        .withBookingStatus(VulogRealTimeBookingStatus.Available)
        .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
        .withBattery(55)
        .build(),
    };

    const now = DateTime.now();
    const reservedAt = now.toJSDate();
    const expiresAt = now.plus({ minutes: 15 }).toJSDate();

    const journeyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(carId)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(reservedAt.toISOString())
          .withMaxTripStartDate(expiresAt.toISOString())
          .build(),
      )
      .build();

    const userInfo: InternalUserDetailResponseDtoV1 =
      new InternalUserDetailResponseDtoV1Builder()
        .withId(userId)
        .withUserStatus(UserStatus.VERIFIED)
        .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
        .build();

    const existingReservationEntity = new ReservationEntityBuilder()
      .makeBsgUserId(userId)
      .makeStartStationId(stationDto1.id)
      .makeStatus(ReservationStatus.Reserved)
      .makeExpiresAt(DateTime.now().plus({ hours: 1 }).toJSDate())
      .makeCarModel(carDto.static.model.name as CarModelEnum)
      .makeLocal(false)
      .makeVulogTripId(journeyDto.id)
      .makeUserEmail(userInfo.email)
      .makeUserFullName(`${userInfo.firstName} ${userInfo.lastName}`)
      .build();

    beforeAll(async () => {
      await mockCredential();

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(JSON.stringify(userInfo)),
        } as BaseResponseDto);

      await reservationService.saveOne(
        Reservation.from(existingReservationEntity),
      );
    });

    it('Check expectation', async () => {
      const response = await requestFor(stationDto1.id, {
        carModel: existingReservationEntity.carModel,
        local: true,
      });

      const reservation = await reservationService.findOneByTripId(
        new VulogJourneyOrTripId(journeyDto.id),
      );

      expect(response.body.result).toEqual(
        JSON.parse(
          JSON.stringify(
            ReservationResponseDtoV1.fromReservation(
              reservation,
              reservation.carModel,
            ),
          ),
        ),
      );

      expect(reservation.local).toBe(true);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('should throw error when current user is banned', () => {
    beforeAll(async () => {
      await mockCredential();

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(
            JSON.stringify(
              new InternalUserDetailResponseDtoV1Builder()
                .withId(userId)
                .withUserStatus(UserStatus.BANNED) // Wrong status
                .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
                .build(),
            ),
          ),
        } as BaseResponseDto);
    });

    it('Check expectation', async () => {
      const response = await requestFor(aUUID(), {
        carModel: CarModelEnum.BlueCar,
        local: true,
      });

      expect(response.body.message).toEqual(
        `An error occurred during the reservation flow - Cause: You’ve been banned due to a breach of policy. Please contact us for more information.`,
      );

      expect(response.statusCode).toEqual(HttpStatus.FORBIDDEN);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('should throw error when current user is not an active user', () => {
    beforeAll(async () => {
      await mockCredential();

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(
            JSON.stringify(
              new InternalUserDetailResponseDtoV1Builder()
                .withId(userId)
                .withUserStatus(UserStatus.CREATED) // Wrong status
                .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
                .build(),
            ),
          ),
        } as BaseResponseDto);
    });

    it('Check expectation', async () => {
      const response = await requestFor(aUUID(), {
        carModel: CarModelEnum.BlueCar,
        local: true,
      });

      expect(response.body.message).toEqual(
        `An error occurred during the reservation flow - Cause: BlueSG customer must be verified customer to take part in reservation and rental flow`,
      );

      expect(response.statusCode).toEqual(HttpStatus.FORBIDDEN);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('should throw error when current user does not have active subscription', () => {
    beforeAll(async () => {
      await mockCredential();

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(
            JSON.stringify(
              new InternalUserDetailResponseDtoV1Builder()
                .withId(userId)
                .withUserStatus(UserStatus.VERIFIED)
                .withLatestSubscriptionStatus(SubscriptionStatus.EXPIRED) // Wrong status
                .build(),
            ),
          ),
        } as BaseResponseDto);
    });

    it('Check expectation', async () => {
      const response = await requestFor(aUUID(), {
        carModel: CarModelEnum.BlueCar,
        local: true,
      });

      expect(response.body.message).toEqual(
        `An error occurred during the reservation flow - Cause: BlueSG customer must have an active subscription to take part in reservation and rental flow`,
      );

      expect(response.statusCode).toEqual(HttpStatus.FORBIDDEN);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('should throw error when customer is in grace period', () => {
    const zoneId = aMD5();

    const stationDto1 = new StationDtoBuilder().withZoneId(zoneId).build();

    const carDto = {
      static: new VulogCarStaticInfoDtoBuilder()
        .withModel(CarModelEnum.BlueCar)
        .withId(carId)
        .build(),
      realTime: new VulogCarRealTimeDtoBuilder()
        .withId(carId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
        .withBookingStatus(VulogRealTimeBookingStatus.Available)
        .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
        .withBattery(55)
        .build(),
    };

    const now = DateTime.now();

    const existingReservationEntity = new ReservationEntityBuilder()
      .makeBsgUserId(userId)
      .makeStatus(ReservationStatus.Cancelled)
      .makeCarModel(carDto.static.model.name as CarModelEnum)
      .makeLocal(false)
      .makeAbuseReleasesAt(now.plus({ minutes: 10 }).toJSDate())
      .makeCanceledAt(now.minus({ minutes: 5 }).toJSDate())
      .build();

    const reservation = Reservation.from(existingReservationEntity);

    beforeAll(async () => {
      await mockCredential();

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(
            JSON.stringify(
              new InternalUserDetailResponseDtoV1Builder()
                .withId(userId)
                .withUserStatus(UserStatus.VERIFIED)
                .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
                .build(),
            ),
          ),
        } as BaseResponseDto);

      await reservationService.saveOne(
        Reservation.from(existingReservationEntity),
      );
    });

    it('Check expectation', async () => {
      const response = await requestFor(stationDto1.id);

      expect(response.body.errorCode).toEqual(GRACE_PERIOD_ERROR);

      expect(response.body.message).toEqual(
        `${RESERVATION_ERROR_MESSAGE} - Cause: You can't reserve now. After you cancel reservation or your reservation is expired, you must wait ${reservation.gracePeriodDuration} before making a new one.`,
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('should be able to allocate car with specified car model', () => {
    const zoneId = aMD5();

    const stationDto1 = new StationDtoBuilder().withZoneId(zoneId).build();
    const stationDto2 = new StationDtoBuilder().build();
    const stationDtos = [stationDto1, stationDto2];

    const specifiedCarModel = CarModelEnum.BlueCar;

    const carDto = {
      static: new VulogCarStaticInfoDtoBuilder()
        .withModel(specifiedCarModel)
        .withId(carId)
        .build(),
      realTime: new VulogCarRealTimeDtoBuilder()
        .withId(carId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
        .withBookingStatus(VulogRealTimeBookingStatus.Available)
        .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
        .withAutonomy(55)
        .build(),
    };

    const now = DateTime.now();
    const reservedAt = now.toJSDate();
    const expiresAt = now.plus({ minutes: 15 }).toJSDate();

    const journeyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(carId)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(reservedAt.toISOString())
          .withMaxTripStartDate(expiresAt.toISOString())
          .build(),
      )
      .build();

    const userInfo: InternalUserDetailResponseDtoV1 =
      new InternalUserDetailResponseDtoV1Builder()
        .withId(userId)
        .withUserStatus(UserStatus.VERIFIED)
        .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
        .build();

    beforeAll(async () => {
      await mockCredential();

      await carRepo.save([CarEntity.from(carDto.static.toStaticCar().toCar())]);

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(JSON.stringify(userInfo)),
        } as BaseResponseDto);

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(
            JSON.stringify(
              new InternalUserDetailResponseDtoV1Builder()
                .withId(userId)
                .withUserStatus(UserStatus.VERIFIED)
                .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
                .build(),
            ),
          ),
        } as BaseResponseDto);

      // Vulog Car Service - get real time car
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [carDto.realTime]);

      // VulogCarService - get all static cars
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .replyOnce(200, [carDto.static], { last: true });

      // StationService - Get all stations
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos.map((stn) => stn.toRawJsonObject()),
        });

      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 1, size: 200 })}`,
        )
        .replyOnce(200, []);

      // VulogCarService.bookCar
      mockHttpAdapterService
        .onPost(`${VulogPaths.bookJourney(new VulogCarId(carDto.realTime.id))}`)
        .replyOnce(201, journeyDto);

      spyOn_AvailabilityService__getAvailableCarsForSpecificStation.mockResolvedValueOnce(
        [
          {
            ...carDto.realTime.toRealtimeCar(),
            model: new CarModel(CarModelEnum.BlueCar),
          } as any,
        ],
      );
    });

    it('Check expectation', async () => {
      const response = await requestFor(stationDto1.id, {
        carModel: specifiedCarModel,
      });

      const reservation = await reservationService.findOneByTripId(
        new VulogJourneyOrTripId(journeyDto.id),
      );

      expect(reservation.userEmail).toBeDefined();
      expect(reservation.userFullName).toBeDefined();
      expect(reservation.userEmail).toEqual(userInfo.email);
      expect(reservation.userFullName).toEqual(
        `${userInfo.firstName} ${userInfo.lastName}`,
      );

      expect(response.body.result).toEqual(
        JSON.parse(
          JSON.stringify(
            ReservationResponseDtoV1.fromReservation(
              reservation,
              reservation.carModel,
            ),
          ),
        ),
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('should throw error when there is no available car for allocation', () => {
    const zoneId = aMD5();

    const stationDto1 = new StationDtoBuilder().withZoneId(zoneId).build();
    const stationDto2 = new StationDtoBuilder().build();
    const stationDtos = [stationDto1, stationDto2];

    const specifiedCarModel = CarModelEnum.BlueCar;

    beforeAll(async () => {
      await mockCredential();

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(
            JSON.stringify(
              new InternalUserDetailResponseDtoV1Builder()
                .withId(userId)
                .withUserStatus(UserStatus.VERIFIED)
                .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
                .build(),
            ),
          ),
        } as BaseResponseDto);

      // Vulog Car Service - get real time car
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, []);

      // VulogCarService - get all static cars
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .replyOnce(200, [], { last: true });

      // StationService - Get all stations
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos.map((stn) => stn.toRawJsonObject()),
        });

      spyOn_AvailabilityService__getAvailableCarsForSpecificStation.mockResolvedValueOnce(
        [],
      );
    });

    it('Check expectation', async () => {
      const response = await requestFor(stationDto1.id, {
        carModel: specifiedCarModel,
      });

      expect(response.body.message).toEqual(
        'An error occurred during the reservation flow - Cause: There is no available car for allocation',
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('should throw error when there is no available car matched provided car model', () => {
    const zoneId = aMD5();

    const stationDto1 = new StationDtoBuilder().withZoneId(zoneId).build();
    const stationDto2 = new StationDtoBuilder().build();
    const stationDtos = [stationDto1, stationDto2];

    const specifiedCarModel = CarModelEnum.BlueCar;

    const carDto = {
      static: new VulogCarStaticInfoDtoBuilder()
        .withModel(CarModelEnum.OpelCorsaE)
        .withId(carId)
        .build(),
      realTime: new VulogCarRealTimeDtoBuilder()
        .withId(carId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
        .withBookingStatus(VulogRealTimeBookingStatus.Available)
        .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
        .withAutonomy(55)
        .build(),
    };

    const now = DateTime.now();
    const reservedAt = now.toJSDate();
    const expiresAt = now.plus({ minutes: 15 }).toJSDate();

    const journeyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(carId)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(reservedAt.toISOString())
          .withMaxTripStartDate(expiresAt.toISOString())
          .build(),
      )
      .build();

    beforeAll(async () => {
      await mockCredential();

      await carRepo.save([CarEntity.from(carDto.static.toStaticCar().toCar())]);

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(
            JSON.stringify(
              new InternalUserDetailResponseDtoV1Builder()
                .withId(userId)
                .withUserStatus(UserStatus.VERIFIED)
                .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
                .build(),
            ),
          ),
        } as BaseResponseDto);

      // Vulog Car Service - get real time car
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [carDto.realTime]);

      // VulogCarService - get all static cars
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .replyOnce(200, [carDto.static], { last: true });

      // StationService - Get all stations
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos.map((stn) => stn.toRawJsonObject()),
        });

      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 1, size: 200 })}`,
        )
        .replyOnce(200, []);

      // VulogCarService.bookCar
      mockHttpAdapterService
        .onPost(`${VulogPaths.bookJourney(new VulogCarId(carDto.realTime.id))}`)
        .replyOnce(200, journeyDto);

      spyOn_AvailabilityService__getAvailableCarsForSpecificStation.mockResolvedValueOnce(
        [carDto.realTime.toRealtimeCar(new CarModel(CarModelEnum.OpelCorsaE))],
      );
    });

    it('Check expectation', async () => {
      const response = await requestFor(stationDto1.id, {
        carModel: specifiedCarModel,
      });

      expect(response.body.message).toEqual(
        'An error occurred during the reservation flow - Cause: There is no available car matched the provided car model',
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('when car availability is stale, booking targeted car is failed, then a reasonable error should be thrown', () => {
    const zoneId = aMD5();

    const stationDto1 = new StationDtoBuilder().withZoneId(zoneId).build();
    const stationDto2 = new StationDtoBuilder().build();
    const stationDtos = [stationDto1, stationDto2];

    const carModel = CarModelEnum.BlueCar;

    const carDto = {
      static: new VulogCarStaticInfoDtoBuilder()
        .withId(carId)
        .withModel(carModel)
        .build(),
      realTime: new VulogCarRealTimeDtoBuilder()
        .withId(carId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
        .withBookingStatus(VulogRealTimeBookingStatus.Available)
        .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
        .withAutonomy(55)
        .build(),
    };

    beforeAll(async () => {
      await mockCredential();

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(
            JSON.stringify(
              new InternalUserDetailResponseDtoV1Builder()
                .withId(userId)
                .withUserStatus(UserStatus.VERIFIED)
                .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
                .build(),
            ),
          ),
        } as BaseResponseDto);

      // Vulog Car Service - get real time car
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [carDto.realTime]);

      // StationService - Get all stations
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos.map((stn) => stn.toRawJsonObject()),
        });

      // VulogCarService.bookCar
      mockHttpAdapterService
        .onPost(`${VulogPaths.bookJourney(new VulogCarId(carDto.realTime.id))}`)
        .replyOnce(400);

      // Stale cache
      jest
        .spyOn(availabilityService, 'getAvailableCarsForSpecificStation')
        .mockResolvedValueOnce([
          carDto.realTime.toRealtimeCar(new CarModel(carModel)),
        ]);

      await carRepo.save([CarEntity.from(carDto.static.toStaticCar().toCar())]);
    });

    it('Check expectation', async () => {
      const response = await requestFor(stationDto1.id);

      expect(response.body.message).toEqual(
        'An error occurred during the reservation flow - Cause: Sorry that your booking request is failed. Please try to book again.',
      );

      expect(response.statusCode).toEqual(HttpStatus.SERVICE_UNAVAILABLE);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('should be able to allocate car although there is on-going reservation, but with the same `startStationId`, different `carModel`', () => {
    const zoneId = aMD5();

    const stationDto1 = new StationDtoBuilder().withZoneId(zoneId).build();
    const stationDto2 = new StationDtoBuilder().build();
    const stationDtos = [stationDto1, stationDto2];

    const specifiedCarModel = CarModelEnum.BlueCar;

    const carDto = {
      static: new VulogCarStaticInfoDtoBuilder()
        .withModel(specifiedCarModel)
        .withId(carId)
        .build(),
      realTime: new VulogCarRealTimeDtoBuilder()
        .withId(carId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
        .withBookingStatus(VulogRealTimeBookingStatus.Available)
        .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
        .withAutonomy(55)
        .build(),
    };

    const now = DateTime.now();
    const reservedAt = now.toJSDate();
    const expiresAt = now.plus({ minutes: 15 }).toJSDate();

    const newJourneyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(carId)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(reservedAt.toISOString())
          .withMaxTripStartDate(expiresAt.toISOString())
          .build(),
      )
      .build();

    const userInfo: InternalUserDetailResponseDtoV1 =
      new InternalUserDetailResponseDtoV1Builder()
        .withId(userId)
        .withUserStatus(UserStatus.VERIFIED)
        .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
        .build();

    const existingReservationEntity = new ReservationEntityBuilder()
      .makeBsgUserId(userId)
      .makeStartStationId(stationDto1.id)
      .makeStatus(ReservationStatus.Reserved)
      .makeCarModel(CarModelEnum.OpelCorsaE)
      .makeExpiresAt(DateTime.now().plus({ hours: 1 }).toJSDate())
      .makeVulogTripId(newJourneyDto.id)
      .makeLocal(false)
      .makeUserEmail(userInfo.email)
      .makeUserFullName(`${userInfo.firstName} ${userInfo.lastName}`)
      .build();

    beforeAll(async () => {
      await mockCredential();

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(JSON.stringify(userInfo)),
        } as BaseResponseDto);

      await reservationService.saveOne(
        Reservation.from(existingReservationEntity),
      );

      await carRepo.save([CarEntity.from(carDto.static.toStaticCar().toCar())]);

      // Vulog Car Service - get real time car
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .reply(200, [carDto.realTime]);

      // VulogCarService - get all static cars
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, [carDto.static], { last: true });

      // StationService - Get all stations
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos.map((stn) => stn.toRawJsonObject()),
        });

      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 1, size: 200 })}`,
        )
        .replyOnce(200, []);

      // VulogCarService.getUserCurrentReservations
      mockHttpAdapterService
        .onGet(`${VulogPaths.currentJourneys()}`)
        .replyOnce(200, {
          journeys: [newJourneyDto.toRawJsonObject()],
        });

      // VulogCarService.cancelCarReservationOrAllocation
      mockHttpAdapterService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(newJourneyDto.id),
          )}`,
        )
        .replyOnce(() => {
          existingReservationEntity.allocateAgain = false;

          reservationRepo.save(existingReservationEntity);

          return [200];
        });

      // VulogCarService.bookCar
      mockHttpAdapterService
        .onPost(`${VulogPaths.bookJourney(new VulogCarId(carDto.realTime.id))}`)
        .replyOnce(200, newJourneyDto);

      jest
        .spyOn(availabilityService, 'getAvailableCarsForSpecificStation')
        .mockResolvedValueOnce([
          carDto.realTime.toRealtimeCar(new CarModel(specifiedCarModel)),
        ]);
    });

    it('Check expectation', async () => {
      const response = await requestFor(stationDto1.id, {
        carModel: specifiedCarModel,
      });

      const reservationEntity = await reservationService.findOneByTripId(
        new VulogJourneyOrTripId(newJourneyDto.id),
      );

      expect(reservationEntity.allocateAgain).toBe(false);
      expect(reservationEntity.canceledAt).toBe(null);
      expect(reservationEntity.getId).toEqual(existingReservationEntity.id);
      expect(reservationEntity.reservedAt.toDateTime().toMillis()).toEqual(
        DateTime.fromJSDate(existingReservationEntity.reservedAt).toMillis(),
      );
      expect(reservationEntity.expiresAt.toDateTime().toMillis()).toEqual(
        DateTime.fromJSDate(existingReservationEntity.expiresAt).toMillis(),
      );

      const carSwitchingPerformed =
        await reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.retrieve(
          reservationEntity.bsgUserId,
        );

      expect(carSwitchingPerformed).toEqual(true);

      expect(response.body.result).toEqual(
        JSON.parse(
          JSON.stringify(
            ReservationResponseDtoV1.fromReservation(
              reservationEntity,
              reservationEntity.carModel,
            ),
          ),
        ),
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('should be able to allocate car although there is on-going reservation, but with the different `startStationId`, same `carModel`', () => {
    const zoneId = aMD5();

    const zoneId2 = aMD5();

    const stationDto1 = new StationDtoBuilder().withZoneId(zoneId).build();
    const stationDto2 = new StationDtoBuilder().withZoneId(zoneId2).build();
    const stationDtos = [stationDto1, stationDto2];

    const currentAllocatedCarModel = CarModelEnum.OpelCorsaE;
    const targetCarModel = currentAllocatedCarModel;

    const carDto = {
      static: new VulogCarStaticInfoDtoBuilder()
        .withModel(currentAllocatedCarModel)
        .withId(carId)
        .build(),
      targetStatic: new VulogCarStaticInfoDtoBuilder()
        .withModel(targetCarModel)
        .withId(targetCarId)
        .build(),
      realTime: new VulogCarRealTimeDtoBuilder()
        .withId(carId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
        .withBookingStatus(VulogRealTimeBookingStatus.Available)
        .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
        .withAutonomy(55)
        .build(),
      targetRealtime: new VulogCarRealTimeDtoBuilder()
        .withId(targetCarId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
        .withBookingStatus(VulogRealTimeBookingStatus.Available)
        .withZones([new VulogZone(zoneId2, 1, 'allowed', false)])
        .withAutonomy(55)
        .build(),
    };

    const now = DateTime.now();
    const reservedAt = now.toJSDate();
    const expiresAt = now.plus({ minutes: 15 }).toJSDate();

    const journeyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(carId)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(reservedAt.toISOString())
          .withMaxTripStartDate(expiresAt.toISOString())
          .build(),
      )
      .build();

    const newJourneyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(targetCarId)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(reservedAt.toISOString())
          .withMaxTripStartDate(expiresAt.toISOString())
          .build(),
      )
      .build();

    const userInfo: InternalUserDetailResponseDtoV1 =
      new InternalUserDetailResponseDtoV1Builder()
        .withId(userId)
        .withUserStatus(UserStatus.VERIFIED)
        .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
        .build();

    const existingReservationEntity = new ReservationEntityBuilder()
      .makeBsgUserId(userId)
      .makeStartStationId(stationDto1.id)
      .makeStatus(ReservationStatus.Reserved)
      .makeCarModel(currentAllocatedCarModel)
      .makeExpiresAt(DateTime.now().plus({ hours: 1 }).toJSDate())
      .makeVulogTripId(journeyDto.id)
      .makeLocal(false)
      .makeUserEmail(userInfo.email)
      .makeUserFullName(`${userInfo.firstName} ${userInfo.lastName}`)
      .build();

    beforeAll(async () => {
      await mockCredential();

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(JSON.stringify(userInfo)),
        } as BaseResponseDto);

      await reservationService.saveOne(
        Reservation.from(existingReservationEntity),
      );

      await carRepo.save([
        CarEntity.from(carDto.static.toStaticCar().toCar()),
        CarEntity.from(carDto.targetStatic.toStaticCar().toCar()),
      ]);

      // Vulog Car Service - get real time car
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .reply(200, [carDto.realTime, carDto.targetRealtime]);

      // VulogCarService - get all static cars
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, [carDto.static, carDto.targetStatic], { last: true });

      // StationService - Get all stations
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos.map((stn) => stn.toRawJsonObject()),
        });

      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 1, size: 200 })}`,
        )
        .replyOnce(200, []);

      // VulogCarService.getUserCurrentReservations
      mockHttpAdapterService
        .onGet(`${VulogPaths.currentJourneys()}`)
        .replyOnce(200, {
          journeys: [journeyDto.toRawJsonObject()],
        });

      // VulogCarService.cancelCarReservationOrAllocation
      mockHttpAdapterService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(journeyDto.id),
          )}`,
        )
        .replyOnce(() => {
          existingReservationEntity.allocateAgain = false;

          reservationRepo.save(existingReservationEntity);

          return [200];
        });

      // VulogCarService.bookCar
      mockHttpAdapterService
        .onPost(`${VulogPaths.bookJourney(new VulogCarId(carDto.realTime.id))}`)
        .replyOnce(200, newJourneyDto);

      jest
        .spyOn(availabilityService, 'getAvailableCarsForSpecificStation')
        .mockResolvedValueOnce([
          carDto.realTime.toRealtimeCar(new CarModel(CarModelEnum.OpelCorsaE)),
        ]);
    });

    it('Check expectation', async () => {
      const response = await requestFor(stationDto2.id, {
        carModel: targetCarModel,
      });

      const reservationEntity = await reservationService.findOneByTripId(
        new VulogJourneyOrTripId(newJourneyDto.id),
      );

      expect(reservationEntity.allocateAgain).toBe(false);
      expect(reservationEntity.canceledAt).toBe(null);
      expect(reservationEntity.getId).toEqual(existingReservationEntity.id);
      expect(reservationEntity.reservedAt.toDateTime().toMillis()).toEqual(
        DateTime.fromJSDate(existingReservationEntity.reservedAt).toMillis(),
      );
      expect(reservationEntity.expiresAt.toDateTime().toMillis()).toEqual(
        DateTime.fromJSDate(existingReservationEntity.expiresAt).toMillis(),
      );

      const carSwitchingPerformed =
        await reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.retrieve(
          reservationEntity.bsgUserId,
        );

      expect(carSwitchingPerformed).toEqual(true);

      expect(response.body.result).toEqual(
        JSON.parse(
          JSON.stringify(
            ReservationResponseDtoV1.fromReservation(
              reservationEntity,
              reservationEntity.carModel,
            ),
          ),
        ),
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('when car availability is stale, switching to targeted car is failed, should book previous car, return reasonable error', () => {
    const zoneId = aMD5();
    const targetZoneId = aMD5();

    const stationDto1 = new StationDtoBuilder().withZoneId(zoneId).build();
    const stationDto2 = new StationDtoBuilder()
      .withZoneId(targetZoneId)
      .build();
    const stationDtos = [stationDto1, stationDto2];

    const specifiedCarModel = CarModelEnum.BlueCar;

    const carPlate = aString();
    const targetCarPlate = aString();

    const carDto = {
      static: new VulogCarStaticInfoDtoBuilder()
        .withModel(specifiedCarModel)
        .withId(carId)
        .withPlate(carPlate)
        .build(),
      realTime: new VulogCarRealTimeDtoBuilder()
        .withId(carId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
        .withBookingStatus(VulogRealTimeBookingStatus.Booked)
        .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
        .withAutonomy(55)
        .withPlate(carPlate)
        .build(),
    };

    const targetCarDto = {
      static: new VulogCarStaticInfoDtoBuilder()
        .withModel(specifiedCarModel)
        .withId(targetCarId)
        .withPlate(targetCarPlate)
        .build(),
      realTime: new VulogCarRealTimeDtoBuilder()
        .withId(targetCarId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
        .withBookingStatus(VulogRealTimeBookingStatus.Available)
        .withZones([new VulogZone(targetZoneId, 1, 'allowed', false)])
        .withAutonomy(55)
        .withPlate(targetCarPlate)
        .build(),
    };

    const now = DateTime.now();
    const reservedAt = now.toJSDate();
    const expiresAt = now.plus({ minutes: 15 }).toJSDate();

    const currentJourneyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(carId)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(reservedAt.toISOString())
          .withMaxTripStartDate(expiresAt.toISOString())
          .build(),
      )
      .build();

    const userInfo: InternalUserDetailResponseDtoV1 =
      new InternalUserDetailResponseDtoV1Builder()
        .withId(userId)
        .withUserStatus(UserStatus.VERIFIED)
        .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
        .build();

    const existingReservationEntity = new ReservationEntityBuilder()
      .makeBsgUserId(userId)
      .makeStartStationId(stationDto1.id)
      .makeStatus(ReservationStatus.Reserved)
      .makeCarModel(specifiedCarModel)
      .makeExpiresAt(DateTime.now().plus({ hours: 1 }).toJSDate())
      .makeVulogTripId(currentJourneyDto.id)
      .makeCarId(carId)
      .makePlateNumber(carDto.static.plate)
      .makeLocal(false)
      .makeUserEmail(userInfo.email)
      .makeUserFullName(`${userInfo.firstName} ${userInfo.lastName}`)
      .build();

    beforeAll(async () => {
      await mockCredential();

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(JSON.stringify(userInfo)),
        } as BaseResponseDto);

      await reservationService.saveOne(
        Reservation.from(existingReservationEntity),
      );

      await carRepo.save([
        CarEntity.from(carDto.static.toStaticCar().toCar()),
        CarEntity.from(targetCarDto.static.toStaticCar().toCar()),
      ]);

      // Vulog Car Service - get real time car
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .reply(200, [carDto.realTime, targetCarDto.realTime]);

      // VulogCarService - get all static cars
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, [carDto.static, targetCarDto.static], { last: true });

      // StationService - Get all stations
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos.map((stn) => stn.toRawJsonObject()),
        });

      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 1, size: 200 })}`,
        )
        .replyOnce(200, []);

      // VulogCarService.getUserCurrentReservations
      mockHttpAdapterService
        .onGet(`${VulogPaths.currentJourneys()}`)
        .replyOnce(200, {
          journeys: [currentJourneyDto.toRawJsonObject()],
        });

      // VulogCarService.cancelCarReservationOrAllocation
      mockHttpAdapterService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(currentJourneyDto.id),
          )}`,
        )
        .replyOnce(() => {
          existingReservationEntity.allocateAgain = false;

          reservationRepo.save(existingReservationEntity);

          return [200];
        });

      // VulogCarService.bookCar (switching attempt)
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.bookJourney(new VulogCarId(targetCarDto.realTime.id))}`,
        )
        .replyOnce(400);

      // VulogCarService.bookCar (re-book previous car)
      mockHttpAdapterService
        .onPost(`${VulogPaths.bookJourney(new VulogCarId(carDto.realTime.id))}`)
        .replyOnce(200, currentJourneyDto);

      // Stale cache
      jest
        .spyOn(availabilityService, 'getAvailableCarsForSpecificStation')
        .mockResolvedValueOnce([
          targetCarDto.realTime.toRealtimeCar(
            new CarModel(CarModelEnum.BlueCar),
          ),
        ]);
    });

    it('Check expectation', async () => {
      const response = await requestFor(stationDto2.id, {
        carModel: specifiedCarModel,
      });

      const reservation = await reservationService.findOneByTripId(
        new VulogJourneyOrTripId(currentJourneyDto.id),
      );

      expect(response.body.message).toEqual(
        `An error occurred during the reservation flow - Cause: Sorry that your attempt to switch from car ${existingReservationEntity.plateNumber} to car ${targetCarDto.static.plate} is failed. Please choose another car to switch.`,
      );

      expect(response.statusCode).toEqual(HttpStatus.SERVICE_UNAVAILABLE);

      expect(reservation.allocateAgain).toBe(false);
      expect(reservation.canceledAt).toBe(null);
      expect(reservation.getId).toEqual(existingReservationEntity.id);
      expect(reservation.reservedAt.toDateTime().toMillis()).toEqual(
        DateTime.fromJSDate(existingReservationEntity.reservedAt).toMillis(),
      );
      expect(reservation.expiresAt.toDateTime().toMillis()).toEqual(
        DateTime.fromJSDate(existingReservationEntity.expiresAt).toMillis(),
      );
      expect(reservation.carId.value).toEqual(carId);
      expect(reservation.plateNumber.value).toEqual(carDto.static.plate);
      expect(reservation.carModel).toEqual(carDto.static.model.name);
      expect(reservation.startStationId.value).toEqual(stationDto1.id);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('when car availability is stale, switching to targeted car is failed, then booking previous car is failed, should cancel current reservation due to technical issue, and return reasonable error', () => {
    const zoneId = aMD5();
    const targetZoneId = aMD5();

    const stationDto1 = new StationDtoBuilder().withZoneId(zoneId).build();
    const stationDto2 = new StationDtoBuilder()
      .withZoneId(targetZoneId)
      .build();
    const stationDtos = [stationDto1, stationDto2];

    const specifiedCarModel = CarModelEnum.BlueCar;

    const carPlate = aString();
    const targetCarPlate = aString();

    const carDto = {
      static: new VulogCarStaticInfoDtoBuilder()
        .withModel(specifiedCarModel)
        .withId(carId)
        .withPlate(carPlate)
        .build(),
      realTime: new VulogCarRealTimeDtoBuilder()
        .withId(carId)
        .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
        .withAutonomy(55)
        .withPlate(carPlate)
        .withBookingStatus(VulogRealTimeBookingStatus.Booked)
        .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
        .build(),
    };

    const targetCarDto = {
      static: new VulogCarStaticInfoDtoBuilder()
        .withModel(specifiedCarModel)
        .withId(targetCarId)
        .withPlate(targetCarPlate)
        .build(),
      realTime: new VulogCarRealTimeDtoBuilder()
        .withId(targetCarId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
        .withBookingStatus(VulogRealTimeBookingStatus.Available)
        .withZones([new VulogZone(targetZoneId, 1, 'allowed', false)])
        .withAutonomy(55)
        .withPlate(targetCarPlate)
        .build(),
    };

    const now = DateTime.now();
    const reservedAt = now.toJSDate();
    const expiresAt = now.plus({ minutes: 15 }).toJSDate();

    const currentJourneyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(carId)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(reservedAt.toISOString())
          .withMaxTripStartDate(expiresAt.toISOString())
          .build(),
      )
      .build();

    const userInfo: InternalUserDetailResponseDtoV1 =
      new InternalUserDetailResponseDtoV1Builder()
        .withId(userId)
        .withUserStatus(UserStatus.VERIFIED)
        .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
        .build();

    const existingReservationEntity = new ReservationEntityBuilder()
      .makeBsgUserId(userId)
      .makeStartStationId(stationDto1.id)
      .makeStatus(ReservationStatus.Reserved)
      .makeCarModel(specifiedCarModel)
      .makeExpiresAt(DateTime.now().plus({ hours: 1 }).toJSDate())
      .makeVulogTripId(currentJourneyDto.id)
      .makeCarId(carId)
      .makePlateNumber(carDto.static.plate)
      .makeLocal(false)
      .makeUserEmail(userInfo.email)
      .makeUserFullName(`${userInfo.firstName} ${userInfo.lastName}`)
      .build();

    beforeAll(async () => {
      await mockCredential();

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(JSON.stringify(userInfo)),
        } as BaseResponseDto);

      await reservationService.saveOne(
        Reservation.from(existingReservationEntity),
      );

      await carRepo.save([
        CarEntity.from(carDto.static.toStaticCar().toCar()),
        CarEntity.from(targetCarDto.static.toStaticCar().toCar()),
      ]);

      // Vulog Car Service - get real time car
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .reply(200, [carDto.realTime, targetCarDto.realTime]);

      // VulogCarService - get all static cars
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, [carDto.static, targetCarDto.static], { last: true });

      // StationService - Get all stations
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos.map((stn) => stn.toRawJsonObject()),
        });

      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 1, size: 200 })}`,
        )
        .replyOnce(200, []);

      // VulogCarService.getUserCurrentReservations
      mockHttpAdapterService
        .onGet(`${VulogPaths.currentJourneys()}`)
        .replyOnce(200, {
          journeys: [currentJourneyDto.toRawJsonObject()],
        });

      // VulogCarService.cancelCarReservationOrAllocation
      mockHttpAdapterService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(currentJourneyDto.id),
          )}`,
        )
        .replyOnce(() => {
          existingReservationEntity.allocateAgain = false;

          reservationRepo.save(existingReservationEntity);

          return [200];
        });

      // VulogCarService.bookCar (switching attempt)
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.bookJourney(new VulogCarId(targetCarDto.realTime.id))}`,
        )
        .replyOnce(400);

      // VulogCarService.bookCar (re-book previous car)
      mockHttpAdapterService
        .onPost(`${VulogPaths.bookJourney(new VulogCarId(carDto.realTime.id))}`)
        .replyOnce(400);

      // Stale cache
      jest
        .spyOn(availabilityService, 'getAvailableCarsForSpecificStation')
        .mockResolvedValueOnce([
          targetCarDto.realTime.toRealtimeCar(
            new CarModel(CarModelEnum.BlueCar),
          ),
        ]);
    });

    it('Check expectation', async () => {
      const response = await requestFor(stationDto2.id, {
        carModel: specifiedCarModel,
      });

      expect(response.body.message).toEqual(
        'The rental was canceled due to technical error. Sorry for the inconvenience - Cause: Your reservation was canceled due to technical problems. Please try to book a new car again.',
      );

      expect(response.statusCode).toEqual(HttpStatus.INTERNAL_SERVER_ERROR);

      const reservation = await reservationService.findOneByTripId(
        new VulogJourneyOrTripId(currentJourneyDto.id),
      );

      expect(reservation.allocateAgain).toBe(false);
      expect(reservation.canceledAt).toBeDefined();
      expect(reservation.getId).toEqual(existingReservationEntity.id);
      expect(reservation.carId.value).toEqual(carId);
      expect(reservation.plateNumber.value).toEqual(carDto.static.plate);
      expect(reservation.carModel).toEqual(carDto.static.model.name);
      expect(reservation.startStationId.value).toEqual(stationDto1.id);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('when car availability is stale, car availability includes customer-allocated car (same station ID), customer tries to switch car at same station, should throw an error', () => {
    const zoneId = aMD5();

    const stationDto1 = new StationDtoBuilder().withZoneId(zoneId).build();
    const stationDtos = [stationDto1];

    const specifiedCarModel = CarModelEnum.BlueCar;

    const carPlate = aString();
    const targetCarPlate = aString();

    const carDto = {
      static: new VulogCarStaticInfoDtoBuilder()
        .withModel(specifiedCarModel)
        .withId(carId)
        .withPlate(carPlate)
        .build(),
      realTime: new VulogCarRealTimeDtoBuilder()
        .withId(carId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
        .withBookingStatus(VulogRealTimeBookingStatus.Booked)
        .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
        .withAutonomy(55)
        .withPlate(carPlate)
        .build(),
    };

    const dummyCarDto = {
      static: new VulogCarStaticInfoDtoBuilder()
        .withModel(specifiedCarModel)
        .withId(targetCarId)
        .withPlate(targetCarPlate)
        .build(),
      realTime: new VulogCarRealTimeDtoBuilder()
        .withId(targetCarId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
        .withBookingStatus(VulogRealTimeBookingStatus.Available)
        .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
        .withAutonomy(55)
        .withPlate(targetCarPlate)
        .build(),
    };

    const now = DateTime.now();
    const reservedAt = now.toJSDate();
    const expiresAt = now.plus({ minutes: 15 }).toJSDate();

    const currentJourneyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(carId)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(reservedAt.toISOString())
          .withMaxTripStartDate(expiresAt.toISOString())
          .build(),
      )
      .build();

    const userInfo: InternalUserDetailResponseDtoV1 =
      new InternalUserDetailResponseDtoV1Builder()
        .withId(userId)
        .withUserStatus(UserStatus.VERIFIED)
        .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
        .build();

    const existingReservationEntity = new ReservationEntityBuilder()
      .makeBsgUserId(userId)
      .makeStartStationId(stationDto1.id)
      .makeStatus(ReservationStatus.Reserved)
      .makeCarModel(specifiedCarModel)
      .makeExpiresAt(DateTime.now().plus({ hours: 1 }).toJSDate())
      .makeVulogTripId(currentJourneyDto.id)
      .makeCarId(carId)
      .makePlateNumber(carDto.static.plate)
      .makeLocal(false)
      .makeUserEmail(userInfo.email)
      .makeUserFullName(`${userInfo.firstName} ${userInfo.lastName}`)
      .build();

    beforeAll(async () => {
      await mockCredential();

      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .replyOnce(200, {
          success: true,
          result: JSON.parse(JSON.stringify(userInfo)),
        } as BaseResponseDto);

      await reservationService.saveOne(
        Reservation.from(existingReservationEntity),
      );

      await carRepo.save([
        CarEntity.from(carDto.static.toStaticCar().toCar()),
        CarEntity.from(dummyCarDto.static.toStaticCar().toCar()),
      ]);

      // Vulog Car Service - get real time car
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .reply(200, [carDto.realTime, dummyCarDto.realTime]);

      // VulogCarService - get all static cars
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, [carDto.static, dummyCarDto.static], { last: true });

      // StationService - Get all stations
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos.map((stn) => stn.toRawJsonObject()),
        });

      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 1, size: 200 })}`,
        )
        .replyOnce(200, []);

      // VulogCarService.getUserCurrentReservations
      mockHttpAdapterService
        .onGet(`${VulogPaths.currentJourneys()}`)
        .replyOnce(200, {
          journeys: [currentJourneyDto.toRawJsonObject()],
        });

      // VulogCarService.cancelCarReservationOrAllocation
      mockHttpAdapterService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(currentJourneyDto.id),
          )}`,
        )
        .replyOnce(() => {
          existingReservationEntity.allocateAgain = false;

          reservationRepo.save(existingReservationEntity);

          return [200];
        });

      // Stale cache
      jest
        .spyOn(availabilityService, 'getAvailableCarsForSpecificStation')
        .mockResolvedValueOnce([
          carDto.realTime.toRealtimeCar(new CarModel(CarModelEnum.BlueCar)),
        ]);
    });

    it('Check expectation', async () => {
      const response = await requestFor(stationDto1.id, {
        carModel: specifiedCarModel,
      });

      expect(response.body.message).toEqual(
        'An error occurred during the reservation flow - Cause: There is no available car for allocation (stale car availability)',
      );

      expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });
});
