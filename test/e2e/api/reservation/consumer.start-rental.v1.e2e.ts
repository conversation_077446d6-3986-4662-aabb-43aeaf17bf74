import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { EventName } from '@bluesg-2/event-library';
import { getRepositoryToken } from '@nestjs/typeorm';
import { HttpStatusCode } from 'axios';
import MockAdapter from 'axios-mock-adapter';
import { DateTime } from 'luxon';
import * as QueryString from 'qs';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { BsgUserId, VulogProfileId } from 'src/common/tiny-types';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { config } from 'src/config';
import { CacheService } from 'src/logic/cache/cache.service';
import { ReservationService } from 'src/logic/reservation.service';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogUserService } from 'src/logic/vulog/vulog-user.service';
import {
  VulogRealTimeBookingStatus,
  VulogRealTimeVehicleStatus,
} from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import {
  VulogZone,
  VulogZoneType,
} from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { Reservation } from 'src/model/reservation.domain';
import supertest, * as request from 'supertest';
import { originalDateTime_Now } from 'test/constants';
import { InternalPricingPolicyResponseDtoV1Builder } from 'test/helpers/builders/dtos/internal.pricing-policy.v1.response.dto.builder';
import { createTestVulogCarStaticInfoDto } from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { VulogCarRealTimeDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import {
  CUSTOMER_JWT_MOCK_TOKEN,
  mockCustomerJwtGuard,
} from 'test/helpers/customer-jwt-guard.mocker';
import { BillingApiMocker } from 'test/helpers/external-services/billing.api-mocker';
import {
  aBoolean,
  anEmail,
  anEnumValue,
  aNumber,
  aUUID,
} from 'test/helpers/random-data.helper';
import { ApiTestContext, testApi } from 'test/helpers/test-api.util';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';
import { Repository } from 'typeorm';
import { EventBusService } from '~shared/event-bus/event-bus.service';
import { CarService } from '../../../../src/logic/car/car.service';
import { CarEntity } from '../../../../src/logic/car/entity/car.entity';
import { StationPaths } from '../../../../src/logic/station/station-paths';
import { CarBuilder } from '../../../helpers/builders/car.builder';
import { StationDtoBuilder } from '../../../helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogAuthResponse } from '../../../helpers/builders/dtos/test-vulog-auth-response.builder';
import { InternalUserDetailResponseDtoV1Builder } from '../../../helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import {
  VulogBookingBuilder,
  VulogJourneyDtoBuilder,
} from '../../../helpers/builders/dtos/vulog/vulog-journey.dto.builder';
import { VulogUserBuilder } from '../../../helpers/builders/vulog-user.builder';
import { StationApiMocker } from '../../../helpers/external-services/station.api-mocker';
import { UssApiMocker } from '../../../helpers/external-services/uss.api-mocker';
import { VulogApiMocker } from '../../../helpers/external-services/vulog.api-mocker';

const userId = aUUID();

const email = anEmail();

const authorizedUserToken = JSON.stringify({
  id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
});

const dtNowFunc = originalDateTime_Now;

const carModel = anEnumValue(CarModelEnum);

const rentalRate = aNumber();

describe(
  'POST /api/v1/reservations/:reservation-id/start-rental',
  testApi(
    (context: ApiTestContext) => {
      let response: request.Response;

      let externalApiMock: MockAdapter;
      let ussApiMock: MockAdapter;
      let mockStsApiService: MockAdapter;
      let mockBsApiService: MockAdapter;

      let vulogUserService: VulogUserService;
      let vulogUserRepository: Repository<VulogUserEntity>;
      let reservationRepository: Repository<ReservationEntity>;
      let reservationService: ReservationService;
      let cacheService: CacheService;
      let carService: CarService;
      let carRepo: Repository<CarEntity>;
      let eventBusService: EventBusService;

      const spyOnDateTime_Now = jest.spyOn(DateTime, 'now');
      const fleetId = config.vulogConfig.fleetId;
      const serviceId = config.vulogConfig.serviceId;

      const vulogUser = new VulogUserBuilder()
        .withBsgUserId(new BsgUserId(userId))
        .build();

      async function startRental(
        reservation: Reservation,
      ): Promise<supertest.Response> {
        return request(context.getHttpServer())
          .post(`/api/v1/reservation/${reservation.getId}/start-rental`)
          .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken);
      }

      async function cleanDb() {
        await vulogUserRepository.delete({});
        await reservationRepository.delete({});
        await cacheService.clearAll();
        await carRepo.delete({});
      }

      function resetAllMocks() {
        externalApiMock.reset();
        ussApiMock.reset();
        mockStsApiService.reset();
        mockBsApiService.reset();
      }

      const mockUssUser = new InternalUserDetailResponseDtoV1Builder()
        .withId(userId)
        .withEmail(email)
        .build();

      beforeEach(async () => {
        ussApiMock = new UssApiMocker(context.app).mock();
        mockStsApiService = new StationApiMocker(context.app).mock();
        mockBsApiService = new BillingApiMocker(context.app).mock();

        externalApiMock = new VulogApiMocker(context.app).mock();

        vulogUserRepository = context.app.get<Repository<VulogUserEntity>>(
          getRepositoryToken(VulogUserEntity),
        );
        reservationRepository = context.app.get<Repository<ReservationEntity>>(
          getRepositoryToken(ReservationEntity),
        );
        reservationService = context.app.get(ReservationService);
        vulogUserService = context.app.get(VulogUserService);
        cacheService = context.app.get(CacheService);
        carRepo = context.app.get(getRepositoryToken(CarEntity));
        carService = context.app.get(CarService);

        const mockAuthResponse = createTestVulogAuthResponse();

        const userServicesAndProfiles = createTestVulogUserServicesDto();
        userServicesAndProfiles.userId = vulogUser.id.value;

        /**
         * Doing Vulog authentication mock
         */
        externalApiMock
          .onGet(
            `${VulogPaths.userServicesAndProfiles(
              config.vulogConfig.fleetId,
              vulogUser.id,
            )}`,
          )
          .reply(200, userServicesAndProfiles);

        externalApiMock
          .onPost(
            `${VulogPaths.registerUserProfileForService(
              config.vulogConfig.fleetId,
              new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
            )}`,
          )
          .reply(200, userServicesAndProfiles);

        externalApiMock
          .onPost(
            `${VulogPaths.userProfile(
              config.vulogConfig.fleetId,
              new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
            )}`,
          )
          .reply(200, {});

        externalApiMock
          .onPost(`${VulogPaths.auth()}`)
          .reply(200, mockAuthResponse);

        ussApiMock
          .onGet(
            `${
              config.httpServiceConfig.userSubscriptionUrl
            }${ApiRouteUtils.buildApiRouteSegment(
              RootRouteSegment.Internal,
              ApiVersion.V1,
              ['users', userId],
            )}`,
          )
          .reply(200, BaseResponseDto.success(mockUssUser));

        await vulogUserService.save(vulogUser);

        mockVulogCredential(context.app);

        spyOnDateTime_Now.mockImplementation(dtNowFunc);

        eventBusService = context.app.get(EventBusService);
        eventBusService.nonBlockingEmit = jest.fn();
      });

      afterEach(async () => {
        await cleanDb();

        resetAllMocks();
      });

      afterAll(async () => {
        spyOnDateTime_Now.mockRestore();
      });

      const mockPricingPolicyResponseDto =
        new InternalPricingPolicyResponseDtoV1Builder()
          .withCarModel(carModel)
          .withSubscriptionPlanId(
            mockUssUser.currentSubscription.subscriptionPlanId,
          )
          .withRentalRate(rentalRate)
          .build();

      describe('should start a rental properly', () => {
        let reservation: Reservation;

        const carId = aUUID();

        const stationDto = new StationDtoBuilder().build();

        const now = DateTime.now();

        const vulogRealtimeCarDto = new VulogCarRealTimeDtoBuilder()
          .withId(carId)
          .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
          .withBookingStatus(VulogRealTimeBookingStatus.Available)
          .withFleetId(fleetId.value)
          .withServiceId(serviceId.value)
          .withZones([
            new VulogZone(
              stationDto.zoneId,
              aNumber(),
              VulogZoneType.Allowed,
              aBoolean(),
            ),
          ])
          .withAutonomy(55)
          .build();

        beforeEach(async () => {
          reservation = await reservationService.saveOne(
            Reservation.from(
              new ReservationEntityBuilder()
                .makeBsgUserId(vulogUser.bsgUserId.value)
                .makeCarId(carId)
                .makeCarModel(carModel)
                .makeStartStationId(stationDto.id)
                .makeStatus(ReservationStatus.Reserved)
                .makeExpiresAt(DateTime.now().plus({ hours: 1 }).toJSDate())
                .build(),
            ),
          );

          const vulogStaticCarDto = createTestVulogCarStaticInfoDto(
            carId,
            fleetId.value,
            reservation.carModel,
          );

          const reservedAt = now.toJSDate();
          const expiresAt = now.plus({ minutes: 15 }).toJSDate();

          const mockRentalJourney = new VulogJourneyDtoBuilder()
            .asRental()
            .withId(reservation.vulogTripId.value)
            .withVehicleId(carId)
            .withServiceId(serviceId.value)
            .withBooking(
              new VulogBookingBuilder()
                .withBookingDate(reservedAt.toISOString())
                .withMaxTripStartDate(expiresAt.toISOString())
                .build(),
            )
            .build();

          const car = await carService.save(
            new CarBuilder()
              .withVulogId(reservation.carId)
              .withModel(reservation.carModel)
              .build(),
          );

          const customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
            .withVehicles([car.vulogId.value])
            .withZones(
              vulogRealtimeCarDto.zones.map((vulogZone) => vulogZone.zoneId),
            )
            .build();

          // Listing out fleet services from Vulog
          externalApiMock
            .onGet(
              `${VulogPaths.backOfficeFleetServices(
                config.vulogConfig.fleetId,
              )}`,
            )
            .reply(200, [customerFleetServiceDto]);

          // BillingExternalService.getPricingPolicy
          mockBsApiService
            .onGet(new RegExp('pricing-policy'))
            .reply(
              200,
              BaseResponseDto.success([mockPricingPolicyResponseDto]),
            );

          // VulogCarService.startRental
          externalApiMock
            .onPost(`${VulogPaths.startOrEndTrip(reservation.vulogTripId)}`)
            .replyOnce(200, mockRentalJourney.toRawJsonObject());

          // VulogCarService.getStaticCar
          externalApiMock
            .onGet(
              `${VulogPaths.backOfficeListingAllStaticVehicles(
                fleetId,
              )}?${QueryString.stringify({ page: 0, size: 200 })}`,
            )
            .replyOnce(200, [vulogStaticCarDto], { last: true });

          // StationExternalService.getStationDetail
          mockStsApiService
            .onGet(`/api/v1/stations/${stationDto.id}`)
            .replyOnce(
              200,
              BaseResponseDto.success(stationDto.toRawJsonObject()),
            );

          // getCarsCounterForStation - AvailabilityService.getAllAvailableCars
          externalApiMock
            .onGet(`${VulogPaths.backOfficeVehicles(fleetId)}`)
            .replyOnce(200, [vulogRealtimeCarDto]);

          // getCarsCounterForStation - VulogCarService.getAllStaticCars
          externalApiMock
            .onGet(
              `${VulogPaths.backOfficeListingAllStaticVehicles(
                fleetId,
              )}?${QueryString.stringify({ page: 0, pageSize: 200 })}`,
            )
            .replyOnce(
              200,
              [
                createTestVulogCarStaticInfoDto(
                  carId,
                  fleetId.value,
                  reservation.carModel,
                ),
              ],
              { last: true },
            );

          // StationService.getAllStations
          externalApiMock
            .onGet(`${StationPaths.stations()}?page=1&pageSize=200`)
            .replyOnce(
              200,
              BaseResponseDto.success([stationDto.toRawJsonObject()]),
            );

          externalApiMock
            .onGet(`${StationPaths.stations()}?page=2&pageSize=200`)
            .replyOnce(200, BaseResponseDto.success([]));

          // VulogCarService.getAllStaticCars in AvailabilityService.mapCarsToStations
          externalApiMock
            .onGet(
              `${VulogPaths.backOfficeListAllVehicles(
                fleetId,
              )}?${QueryString.stringify({ page: 0, size: 200 })}`,
            )
            .replyOnce(200, [vulogStaticCarDto], { last: true });

          response = await startRental(reservation);
        });

        it('should return an HTTP status CREATED', () => {
          expect(response.status).toBe(HttpStatusCode.Created);
        });

        it('should return the reservation details', async () => {
          expect(response.body.result).toStrictEqual({
            id: reservation.getId,
            startStationId: stationDto.id,
            carCategory: reservation.carModel,
            carId: reservation.carId.value,
            local: reservation.local,
            stationAvailability: {
              id: stationDto.id,
              carsCounter: {},
              slots: null,
              pricingInformation: null,
            },
          });
        });

        it('should emit RENTAL_STARTED event', () => {
          expect(eventBusService.nonBlockingEmit as jest.Mock).toBeCalledTimes(
            2,
          );
          const emittedEvent = (eventBusService.nonBlockingEmit as jest.Mock)
            .mock.calls[1][0];
          expect(emittedEvent.event).toBe(EventName.RentalStartedEvent);
          expect(emittedEvent.payload.id).toBe(reservation.getId);
          expect(emittedEvent.payload.carId).toBe(reservation.carId.value);
          expect(emittedEvent.payload.createdAt).toBeTruthy();
          expect(emittedEvent.payload.reservedAt).toBeTruthy();
          expect(emittedEvent.payload.startedAt).toBeTruthy();
          expect(emittedEvent.payload.startStationId).toBe(stationDto.id);
          expect(emittedEvent.payload.status).toBe(ReservationStatus.Converted);
          expect(emittedEvent.payload.userId).toBe(reservation.bsgUserId.value);
          // legacy fields
          expect(emittedEvent.payload.stationId).toBe(stationDto.id);
          expect(emittedEvent.payload.carMetadata.id).toBeTruthy();
          expect(emittedEvent.payload.carMetadata.model).toBe(
            reservation.carModel,
          );
          expect(emittedEvent.payload.carMetadata.vulogId).toBe(
            reservation.carId.value,
          );
        });
      });

      describe('should throw error when no reservation found in database', () => {
        beforeEach(async () => {
          response = await startRental(
            Reservation.from(new ReservationEntityBuilder().build()),
          );
        });

        it('should return an HTTP status BAD REQUEST', () => {
          expect(response.status).toBe(HttpStatusCode.BadRequest);
        });

        it('should return the error response', async () => {
          expect(response.body.message).toEqual(
            'An error occurred during the reservation flow - Cause: No reservation found in database',
          );
        });
      });

      describe.each([
        {
          isExpired: true,
        },
        {
          isCancelled: true,
        },
      ])(
        'An error occurred during the  - Cause: Client-specified reservation is not valid',
        (scenarioData) => {
          let reservation: Reservation;

          const carId = aUUID();

          const stationDto = new StationDtoBuilder().build();

          beforeEach(async () => {
            reservation = await reservationService.saveOne(
              Reservation.from(
                new ReservationEntityBuilder()
                  .makeBsgUserId(vulogUser.bsgUserId.value)
                  .makeCarId(carId)
                  .makeStartStationId(stationDto.id)
                  .makeStatus(
                    !scenarioData.isCancelled
                      ? ReservationStatus.Expired
                      : ReservationStatus.Cancelled,
                  )
                  .makeExpiresAt(
                    !scenarioData.isExpired
                      ? DateTime.now().plus({ hours: 1 }).toJSDate()
                      : DateTime.now().minus({ hours: 42 }).toJSDate(),
                  )
                  .makeCanceledAt(
                    !scenarioData.isCancelled
                      ? null
                      : DateTime.now().minus({ hours: 1 }).toJSDate(),
                  )
                  .build(),
              ),
            );

            response = await startRental(reservation);
          });

          it('check expectation', () => {
            expect(response.body.message).toEqual(
              'An error occurred during the reservation flow - Cause: Client-specified reservation is not valid',
            );
          });
        },
      );
    },
    [
      {
        name: CustomerJwtGuard,
        value: mockCustomerJwtGuard(userId),
        isGuard: true,
      },
    ],
  ),
);
