import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import MockAdapter from 'axios-mock-adapter';
import {
  RentalPackageId,
  VulogProfileId,
  VulogUserId,
} from 'src/common/tiny-types';
import { config } from 'src/config';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import * as request from 'supertest';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { ApiTestContext, testApi } from 'test/helpers/test-api.util';
import { Repository } from 'typeorm';

import { faker } from '@faker-js/faker';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DateTime } from 'luxon';
import * as QueryString from 'qs';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { AvailableRentalPackagesForReservationResponseDtoV1 } from 'src/controllers/dtos/reservation/response/available-rental-packages-for-reservation.response.v1.dto';
import { BILLING_SERVICE_DI_TOKEN } from 'src/external/constants';
import { CacheService } from 'src/logic/cache/cache.service';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { ReservationService } from 'src/logic/reservation.service';
import { VulogRealTimeVehicleStatus } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { RentalPackage } from 'src/model/rental-package';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { Reservation } from 'src/model/reservation.domain';
import { InternalApiService } from 'src/shared/http/internal-api.service';
import { AttachRentalPackageRequestDtoV1Builder } from 'test/helpers/builders/dtos/attach-rental-package-request-dto.builder';
import { InternalGetRentalPackageV1ResponseDtoBuilder } from 'test/helpers/builders/dtos/bs/internal.get-rental-packges.v1.response.dto.builder';
import { createTestVulogCarStaticInfoDto } from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestStationDto } from 'test/helpers/builders/dtos/test-station-dto.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { VulogCarRealTimeDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import {
  CUSTOMER_JWT_MOCK_TOKEN,
  mockCustomerJwtGuard,
} from 'test/helpers/customer-jwt-guard.mocker';
import { aMD5, anUsername, aUUID } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';

const userId = aUUID();

const email = faker.internet.email();

const authorizedUserToken = JSON.stringify({
  id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
});

describe(
  'GET /api/v1/reservation/:reservationId/available-rental-packages',
  testApi(
    (context: ApiTestContext) => {
      let mockHttpAdapterService: MockAdapter;
      let mockUssApiService: MockAdapter;
      let mockStsApiService: MockAdapter;
      let mockBsApiService: MockAdapter;

      let cacheService: CacheService;
      let reservationService: ReservationService;
      let carRepo: Repository<CarEntity>;

      const reservationUrl =
        '/api/v1/reservation/:reservationId/available-rental-packages';

      const testAuthResponse = createTestVulogAuthResponse();

      let reservationRepo: Repository<ReservationEntity>;

      beforeEach(async () => {
        const httpAdapterService = context.app.get(HttpAdapterService);
        mockHttpAdapterService = new MockAdapter(
          httpAdapterService.httpService.axiosRef,
        );

        const internalApiServiceUss: InternalApiService = context.app.get(
          config.httpServiceConfig.userSubscriptionName,
        );
        mockUssApiService = new MockAdapter(
          internalApiServiceUss.httpService.axiosRef,
        );

        const internalApiServiceSts: InternalApiService = context.app.get(
          config.httpServiceConfig.stationName,
        );
        mockStsApiService = new MockAdapter(
          internalApiServiceSts.httpService.axiosRef,
        );

        const internalApiServiceBs: InternalApiService = context.app.get(
          BILLING_SERVICE_DI_TOKEN,
        );
        mockBsApiService = new MockAdapter(
          internalApiServiceBs.httpService.axiosRef,
        );

        cacheService = context.app.get(CacheService);

        carRepo = context.app.get<Repository<CarEntity>>(
          getRepositoryToken(CarEntity),
        );

        reservationRepo = context.app.get<Repository<ReservationEntity>>(
          getRepositoryToken(ReservationEntity),
        );

        reservationService = context.app.get(ReservationService);

        const userDto = createTestVulogUserDto();

        userDto.id = userId;
        userDto.email = email;

        mockHttpAdapterService
          .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
          .reply(200, userDto);

        const userServicesAndProfiles = createTestVulogUserServicesDto();

        mockHttpAdapterService
          .onGet(
            `${VulogPaths.userServicesAndProfiles(
              config.vulogConfig.fleetId,
              new VulogUserId(userDto.id),
            )}`,
          )
          .reply(200, userServicesAndProfiles);

        mockHttpAdapterService
          .onPost(
            `${VulogPaths.registerUserProfileForService(
              config.vulogConfig.fleetId,
              new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
            )}`,
          )
          .reply(200, userServicesAndProfiles);

        mockHttpAdapterService
          .onPost(`${VulogPaths.auth()}`)
          .reply(200, testAuthResponse);

        mockUssApiService
          .onGet(
            `${
              config.httpServiceConfig.userSubscriptionUrl
            }/${ApiRouteUtils.buildApiRouteSegment(
              RootRouteSegment.Internal,
              ApiVersion.V1,
              ['users', userId],
            )}`,
          )
          .reply(200, { result: { id: userId, email } });

        mockVulogCredential(context.app);
      });

      afterEach(async () => {
        jest.clearAllMocks();

        mockHttpAdapterService.reset();

        mockUssApiService.reset();

        mockStsApiService.reset();

        mockBsApiService.reset();

        await reservationRepo.clear();

        await cacheService.clearAll();

        await carRepo.delete({});
      });

      describe('`reservationId` is not UUID', () => {
        it('Check expectation', async () => {
          const response = await request(context.getHttpServer())
            .get(`${reservationUrl.replace(':reservationId', anUsername())}`)
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          expect(response.body.message).toEqual([
            'reservationId must be a UUID',
          ]);
        });
      });

      describe('client-specified reservation is not found', () => {
        it('Check expectation', async () => {
          const response = await request(context.getHttpServer())
            .get(`${reservationUrl.replace(':reservationId', aUUID())}`)
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          expect(response.body.message).toEqual(
            'An error occurred during the reservation flow - Cause: No reservation is found',
          );
        });
      });

      describe.each([
        {
          isExpired: true,
        },
        {
          isCancelled: true,
        },
      ])(
        'current reservation is either expired or canceled',
        (scenarioData) => {
          it('Check expectation', async () => {
            const allocatedCarId = aUUID();

            const stationDto = createTestStationDto({ zoneId: aMD5() });

            const carStaticDto = createTestVulogCarStaticInfoDto(
              allocatedCarId,
              config.vulogConfig.fleetId.value,
            );

            const vulogTripId = aMD5();

            const ownReservation = await reservationService.saveOne(
              Reservation.from(
                new ReservationEntityBuilder()
                  .makeBsgUserId(userId)
                  .makeCarId(allocatedCarId)
                  .makeCarModel(carStaticDto.model.name as CarModelEnum)
                  .makeReservedAt(
                    scenarioData.isExpired
                      ? DateTime.now().minus({ minutes: 30 }).toJSDate()
                      : DateTime.now().toJSDate(),
                  )
                  .makeExpiresAt(
                    scenarioData.isExpired
                      ? DateTime.now().minus({ minutes: 15 }).toJSDate()
                      : DateTime.now().plus({ minutes: 15 }).toJSDate(),
                  )
                  .makeStatus(
                    scenarioData.isCancelled
                      ? ReservationStatus.Cancelled
                      : ReservationStatus.Expired,
                  )
                  .makeVulogTripId(vulogTripId)
                  .makeStartStationId(stationDto.id)
                  .makeLocal(true)
                  .build(),
              ),
            );

            await reservationService.saveOne(ownReservation);

            const response = await request(context.getHttpServer())
              .get(
                `${reservationUrl.replace(
                  ':reservationId',
                  ownReservation.getId,
                )}`,
              )
              .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
              .send();

            expect(response.body.message).toEqual(
              'An error occurred during the reservation flow - Cause: Client-specified reservation is not valid',
            );
          });
        },
      );

      describe('should be able to get available rental packages for reservation', () => {
        it('Check expectation', async () => {
          const rentalPackageData = new AttachRentalPackageRequestDtoV1Builder()
            .withMinAllowedBatteryPercentage(40)
            .build();

          const availableRentalPackages = [
            new InternalGetRentalPackageV1ResponseDtoBuilder().build(),
            new InternalGetRentalPackageV1ResponseDtoBuilder().build(),
          ];

          const allocatedCarId = aUUID();

          const fleetId = config.vulogConfig.fleetId.value;

          const stationDto = createTestStationDto({ zoneId: aMD5() });

          const carStaticDto = createTestVulogCarStaticInfoDto(
            allocatedCarId,
            fleetId,
          );

          const vulogTripId = aMD5();

          const staticDtos = [carStaticDto];

          await carRepo.save(
            staticDtos.map((it) => CarEntity.from(it.toStaticCar().toCar())),
          );

          const ownReservation = await reservationService.saveOne(
            Reservation.from(
              new ReservationEntityBuilder()
                .makeBsgUserId(userId)
                .makeCarId(allocatedCarId)
                .makePlateNumber(carStaticDto.plate)
                .makeCarModel(carStaticDto.model.name as CarModelEnum)
                .makeReservedAt(DateTime.now().toJSDate())
                .makeExpiresAt(DateTime.now().plus({ minutes: 15 }).toJSDate())
                .makeStatus(ReservationStatus.Reserved)
                .makeVulogTripId(vulogTripId)
                .makeStartStationId(stationDto.id)
                .makeRentalPackageData(rentalPackageData)
                .makeLocal(true)
                .build(),
            ),
          );

          const currentAllocatedRealtimeCar = new VulogCarRealTimeDtoBuilder()
            .withId(allocatedCarId)
            .withPlate(carStaticDto.plate)
            .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
            .withFleetId(config.vulogConfig.fleetId.value)
            .withServiceId(config.vulogConfig.serviceId.value)
            .withZones([new VulogZone(stationDto.zoneId, 1, 'allowed', true)])
            .withStartZones([
              new VulogZone(stationDto.zoneId, 1, 'allowed', true),
            ])
            .withVulogTripId(vulogTripId)
            .withAutonomy(60)
            .build();

          const carRealtimeDtos = [currentAllocatedRealtimeCar];

          await reservationService.saveOne(ownReservation);

          const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
            .withId(config.vulogConfig.serviceId.value)
            .withZones([stationDto.zoneId])
            .withVehicles(carRealtimeDtos.map((rltCar) => rltCar.id))
            .build();

          // VulogFleetService.getFleetServices
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeFleetServices(
                config.vulogConfig.fleetId,
              )}`,
            )
            .replyOnce(200, [mockVulogFleetServiceDto]);

          // BillingExternalService.getAvailableRentalPackages
          mockBsApiService
            .onGet(
              `${ApiRouteUtils.buildApiRouteSegment(
                RootRouteSegment.Internal,
                ApiVersion.V1,
                ['rental-package', 'car-model', ownReservation.carModel],
              )}`,
            )
            .replyOnce(200, BaseResponseDto.success(availableRentalPackages));

          // VulogCarService - get realtime cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`,
            )
            .replyOnce(
              200,
              carRealtimeDtos.map((car) => car.toRawJsonObject()),
            );

          // VulogCarService - get all static cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeListingAllStaticVehicles(
                config.vulogConfig.fleetId,
              )}?${QueryString.stringify({ page: 0, size: 200 })}`,
            )
            .replyOnce(200, staticDtos);

          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeListingAllStaticVehicles(
                config.vulogConfig.fleetId,
              )}?${QueryString.stringify({ page: 1, size: 200 })}`,
            )
            .replyOnce(200, []);

          const response = await request(context.getHttpServer())
            .get(
              `${reservationUrl.replace(
                ':reservationId',
                ownReservation.getId,
              )}`,
            )
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          expect(response.body.result).toEqual(
            JSON.parse(
              JSON.stringify(
                AvailableRentalPackagesForReservationResponseDtoV1.from(
                  new RentalPackage({
                    packageId: new RentalPackageId(rentalPackageData.packageId),
                    hrid: rentalPackageData.hrid,
                    name: rentalPackageData.name,
                    duration: rentalPackageData.duration,
                    price: rentalPackageData.price,
                    minAllowedBatteryPercentage:
                      rentalPackageData.minAllowedBatteryPercentage,
                  }),
                  availableRentalPackages.map((rentalPackage) =>
                    rentalPackage.toRentalPackage(),
                  ),
                  ownReservation,
                  currentAllocatedRealtimeCar.toRealtimeCar(),
                ),
              ),
            ),
          );
        });
      });

      describe('should be able to get available rental packages for reservation (but no current package-attached reservation)', () => {
        it('Check expectation', async () => {
          const availableRentalPackages = [
            new InternalGetRentalPackageV1ResponseDtoBuilder().build(),
            new InternalGetRentalPackageV1ResponseDtoBuilder().build(),
          ];

          const fleetId = config.vulogConfig.fleetId.value;

          const allocatedCarId = aUUID();

          const stationDto = createTestStationDto({ zoneId: aMD5() });

          const carStaticDto = createTestVulogCarStaticInfoDto(
            allocatedCarId,
            fleetId,
          );

          const vulogTripId = aMD5();

          const staticDtos = [carStaticDto];

          await carRepo.save(
            staticDtos.map((it) => CarEntity.from(it.toStaticCar().toCar())),
          );

          const ownReservation = await reservationService.saveOne(
            Reservation.from(
              new ReservationEntityBuilder()
                .makeBsgUserId(userId)
                .makeCarId(allocatedCarId)
                .makePlateNumber(carStaticDto.plate)
                .makeCarModel(carStaticDto.model.name as CarModelEnum)
                .makeReservedAt(DateTime.now().toJSDate())
                .makeExpiresAt(DateTime.now().plus({ minutes: 15 }).toJSDate())
                .makeStatus(ReservationStatus.Reserved)
                .makeVulogTripId(vulogTripId)
                .makeStartStationId(stationDto.id)
                .makeLocal(true)
                .build(),
            ),
          );

          const currentAllocatedRealtimeCar = new VulogCarRealTimeDtoBuilder()
            .withId(allocatedCarId)
            .withPlate(carStaticDto.plate)
            .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
            .withFleetId(fleetId)
            .withServiceId(config.vulogConfig.serviceId.value)
            .withZones([new VulogZone(stationDto.zoneId, 1, 'allowed', true)])
            .withStartZones([
              new VulogZone(stationDto.zoneId, 1, 'allowed', true),
            ])
            .withVulogTripId(vulogTripId)
            .withAutonomy(60)
            .build();

          const carRealtimeDtos = [currentAllocatedRealtimeCar];

          await reservationService.saveOne(ownReservation);

          const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
            .withId(config.vulogConfig.serviceId.value)
            .withZones([stationDto.zoneId])
            .withVehicles(carRealtimeDtos.map((rltCar) => rltCar.id))
            .build();

          // VulogFleetService.getFleetServices
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeFleetServices(
                config.vulogConfig.fleetId,
              )}`,
            )
            .replyOnce(200, [mockVulogFleetServiceDto]);

          // BillingExternalService.getAvailableRentalPackages
          mockBsApiService
            .onGet(
              `${ApiRouteUtils.buildApiRouteSegment(
                RootRouteSegment.Internal,
                ApiVersion.V1,
                ['rental-package', 'car-model', ownReservation.carModel],
              )}`,
            )
            .replyOnce(200, BaseResponseDto.success(availableRentalPackages));

          // VulogCarService - get realtime cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`,
            )
            .replyOnce(
              200,
              carRealtimeDtos.map((car) => car.toRawJsonObject()),
            );

          // VulogCarService - get all static cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeListingAllStaticVehicles(
                config.vulogConfig.fleetId,
              )}?${QueryString.stringify({ page: 0, size: 200 })}`,
            )
            .replyOnce(200, staticDtos);

          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeListingAllStaticVehicles(
                config.vulogConfig.fleetId,
              )}?${QueryString.stringify({ page: 1, size: 200 })}`,
            )
            .replyOnce(200, []);

          const response = await request(context.getHttpServer())
            .get(
              `${reservationUrl.replace(
                ':reservationId',
                ownReservation.getId,
              )}`,
            )
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          expect(response.body.result).toEqual(
            JSON.parse(
              JSON.stringify(
                AvailableRentalPackagesForReservationResponseDtoV1.from(
                  null,
                  availableRentalPackages.map((rentalPackage) =>
                    rentalPackage.toRentalPackage(),
                  ),
                  ownReservation,
                  currentAllocatedRealtimeCar.toRealtimeCar(),
                ),
              ),
            ),
          );
        });
      });

      describe.skip('should throw error because current on-going reservation is non-local', () => {
        it('Check expectation', async () => {
          const allocatedCarId = aUUID();

          const stationDto = createTestStationDto({ zoneId: aMD5() });

          const carStaticDto = createTestVulogCarStaticInfoDto(
            allocatedCarId,
            config.vulogConfig.fleetId.value,
          );

          const vulogTripId = aMD5();

          const ownReservation = await reservationService.saveOne(
            Reservation.from(
              new ReservationEntityBuilder()
                .makeBsgUserId(userId)
                .makeCarId(allocatedCarId)
                .makeCarModel(carStaticDto.model.name as CarModelEnum)
                .makeReservedAt(DateTime.now().toJSDate())
                .makeExpiresAt(DateTime.now().plus({ minutes: 15 }).toJSDate())
                .makeStatus(ReservationStatus.Reserved)
                .makeVulogTripId(vulogTripId)
                .makeStartStationId(stationDto.id)
                .makeLocal(false)
                .build(),
            ),
          );

          await reservationService.saveOne(ownReservation);

          const response = await request(context.getHttpServer())
            .get(
              `${reservationUrl.replace(
                ':reservationId',
                ownReservation.getId,
              )}`,
            )
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          expect(response.body.message).toEqual(
            'An error occurred during the reservation flow - Cause: Your on-going reservation is non-local. Could not get available rental packages',
          );
        });
      });

      describe('no available rental packages for reservation', () => {
        it('Check expectation', async () => {
          const rentalPackageData =
            new AttachRentalPackageRequestDtoV1Builder().build();

          const availableRentalPackages = [];

          const allocatedCarId = aUUID();

          const fleetId = config.vulogConfig.fleetId.value;

          const stationDto = createTestStationDto({ zoneId: aMD5() });

          const carStaticDto = createTestVulogCarStaticInfoDto(
            allocatedCarId,
            fleetId,
          );

          const vulogTripId = aMD5();

          const staticDtos = [carStaticDto];

          await carRepo.save(
            staticDtos.map((it) => CarEntity.from(it.toStaticCar().toCar())),
          );

          const ownReservation = await reservationService.saveOne(
            Reservation.from(
              new ReservationEntityBuilder()
                .makeBsgUserId(userId)
                .makeCarId(allocatedCarId)
                .makePlateNumber(carStaticDto.plate)
                .makeCarModel(carStaticDto.model.name as CarModelEnum)
                .makeReservedAt(DateTime.now().toJSDate())
                .makeExpiresAt(DateTime.now().plus({ minutes: 15 }).toJSDate())
                .makeStatus(ReservationStatus.Reserved)
                .makeVulogTripId(vulogTripId)
                .makeStartStationId(stationDto.id)
                .makeRentalPackageData(rentalPackageData)
                .makeLocal(true)
                .build(),
            ),
          );

          const currentAllocatedRealtimeCar = new VulogCarRealTimeDtoBuilder()
            .withId(allocatedCarId)
            .withPlate(carStaticDto.plate)
            .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
            .withFleetId(fleetId)
            .withServiceId(config.vulogConfig.serviceId.value)
            .withZones([new VulogZone(stationDto.zoneId, 1, 'allowed', true)])
            .withStartZones([
              new VulogZone(stationDto.zoneId, 1, 'allowed', true),
            ])
            .withVulogTripId(vulogTripId)
            .withAutonomy(60)
            .build();

          const carRealtimeDtos = [currentAllocatedRealtimeCar];

          await reservationService.saveOne(ownReservation);

          const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
            .withId(config.vulogConfig.serviceId.value)
            .withZones([stationDto.zoneId])
            .withVehicles(carRealtimeDtos.map((rltCar) => rltCar.id))
            .build();

          // VulogFleetService.getFleetServices
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeFleetServices(
                config.vulogConfig.fleetId,
              )}`,
            )
            .replyOnce(200, [mockVulogFleetServiceDto]);

          // BillingExternalService.getAvailableRentalPackages
          mockBsApiService
            .onGet(
              `${ApiRouteUtils.buildApiRouteSegment(
                RootRouteSegment.Internal,
                ApiVersion.V1,
                ['rental-package', 'car-model', ownReservation.carModel],
              )}`,
            )
            .replyOnce(200, BaseResponseDto.success(availableRentalPackages));

          // VulogCarService - get realtime cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`,
            )
            .replyOnce(
              200,
              carRealtimeDtos.map((car) => car.toRawJsonObject()),
            );

          // VulogCarService - get all static cars
          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeListingAllStaticVehicles(
                config.vulogConfig.fleetId,
              )}?${QueryString.stringify({ page: 0, size: 200 })}`,
            )
            .replyOnce(200, staticDtos);

          mockHttpAdapterService
            .onGet(
              `${VulogPaths.backOfficeListingAllStaticVehicles(
                config.vulogConfig.fleetId,
              )}?${QueryString.stringify({ page: 1, size: 200 })}`,
            )
            .replyOnce(200, []);

          const response = await request(context.getHttpServer())
            .get(
              `${reservationUrl.replace(
                ':reservationId',
                ownReservation.getId,
              )}`,
            )
            .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
            .send();

          expect(response.body.result).toEqual(
            JSON.parse(
              JSON.stringify(
                AvailableRentalPackagesForReservationResponseDtoV1.from(
                  new RentalPackage({
                    packageId: new RentalPackageId(rentalPackageData.packageId),
                    hrid: rentalPackageData.hrid,
                    name: rentalPackageData.name,
                    duration: rentalPackageData.duration,
                    price: rentalPackageData.price,
                    minAllowedBatteryPercentage:
                      rentalPackageData.minAllowedBatteryPercentage,
                  }),
                  availableRentalPackages.map((rentalPackage) =>
                    rentalPackage.toRentalPackage(),
                  ),
                  ownReservation,
                  currentAllocatedRealtimeCar.toRealtimeCar(),
                ),
              ),
            ),
          );
        });
      });
    },
    [
      {
        name: CustomerJwtGuard,
        value: mockCustomerJwtGuard(userId),
        isGuard: true,
      },
    ],
  ),
);
