import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { HttpStatus, INestApplication } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DateTime } from 'luxon';
import { RouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-version';
import { RESERVATION_ERROR } from 'src/common/constants/error-codes';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import * as supertest from 'supertest';
import { AppBuilder } from 'test/helpers/app.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { Repository } from 'typeorm';

const testingApiRoute = ApiRouteUtils.buildApiRouteSegment(
  'api' as any,
  ApiVersion.V1,
  ['reservation', RouteSegment.Reservation.DetachRentalPackage],
);

describe(`[E2E] DELETE: ${testingApiRoute}`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let reservationRepository: Repository<ReservationEntity>;

  function requestFor(reserveId: string): supertest.Test {
    const injectedUrl = testingApiRoute.replace(':reservationId', reserveId);

    const superTest = supertest(app.getHttpServer()).delete(`${injectedUrl}`);

    return superTest;
  }

  beforeAll(async () => {
    app = await appBuilder
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(aUUID()), true)
      .buildAndStart();

    reservationRepository = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );
  });

  afterAll(async () => {
    await reservationRepository.delete({});
    await app.close();
  });

  async function clean(): Promise<void> {
    await reservationRepository.delete({});
    jest.clearAllMocks();
  }

  describe('Delete rental package data from reservation', () => {
    const reservationId = aUUID();
    let response: supertest.Response;

    beforeAll(async () => {
      await reservationRepository.save(
        new ReservationEntityBuilder()
          .makeExpiresAt(DateTime.now().plus({ hours: 2 }).toJSDate())
          .makeStatus(ReservationStatus.Reserved)
          .makeReservationId(reservationId)
          .build(),
      );

      response = await requestFor(reservationId);
    });

    afterAll(async () => {
      await clean();
    });

    it('Should return status ok', () => {
      expect(response.status).toBe(HttpStatus.OK);
    });

    it('Should delete rental package data for the provided reservation', async () => {
      const expectedRes = await reservationRepository.findOneBy({
        id: reservationId,
      });

      expect(expectedRes.rentalPackageData).toBeNull();
    });
  });

  describe('Reservation not found', () => {
    const reservationId = aUUID();
    let response: supertest.Response;

    beforeAll(async () => {
      await reservationRepository.save(
        new ReservationEntityBuilder()
          .makeExpiresAt(DateTime.now().plus({ hours: 2 }).toJSDate())
          .makeStatus(ReservationStatus.Reserved)
          .makeReservationId(reservationId)
          .build(),
      );

      response = await requestFor(aUUID()); // invalid
    });

    afterAll(async () => {
      await clean();
    });

    it(`Should return status with error code: RESERVATION_ERROR`, () => {
      expect(response.body.errorCode).toBe(RESERVATION_ERROR);
    });

    it(`Should return a message for RESERVATION_ERROR`, () => {
      expect(response.body.message).toBe(
        `An error occurred during the reservation flow - Cause: No reservation is found`,
      );
    });
  });

  describe('Reservation with invalid status', () => {
    const reservationId = aUUID();
    let response: supertest.Response;

    beforeAll(async () => {
      await reservationRepository.save(
        new ReservationEntityBuilder()
          .makeExpiresAt(DateTime.now().plus({ hours: 2 }).toJSDate())
          .makeStatus(ReservationStatus.Expired) // invalid
          .makeReservationId(reservationId)
          .build(),
      );

      response = await requestFor(reservationId);
    });

    afterAll(async () => {
      await clean();
    });

    it(`Should return status with error code: RESERVATION_ERROR`, () => {
      expect(response.body.errorCode).toBe(RESERVATION_ERROR);
    });

    it(`Should return a message for RESERVATION_ERROR`, () => {
      expect(response.body.message).toBe(
        `An error occurred during the reservation flow - Cause: Client-specified reservation is not valid`,
      );
    });
  });
});
