import MockAdapter from 'axios-mock-adapter';
import 'reflect-metadata';
import { config } from 'src/config';
import { CarAvailabilityResponseDto } from 'src/controllers/dtos/car-availability.response.dto';
import { StationPaths } from 'src/logic/station/station-paths';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import {
  VulogRealTimeBookingStatus,
  VulogRealTimeVehicleStatus,
} from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import {
  VulogZone,
  VulogZoneType,
} from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import * as request from 'supertest';
import { createTestVulogCarStaticInfoDto } from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { ApiTestContext, testApi } from 'test/helpers/test-api.util';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { faker } from '@faker-js/faker';
import { getRepositoryToken } from '@nestjs/typeorm';
import * as QueryString from 'qs';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { CacheService } from 'src/logic/cache/cache.service';
import { CarModelName } from 'src/logic/car-availability';
import { VulogAuthService } from 'src/logic/vulog/vulog-auth.service';
import { VulogFleetService } from 'src/logic/vulog/vulog-fleet-service.service';
import { FleetService } from 'src/model/fleet-service';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { VulogCarRealTimeDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { createTestVulogUserEntity } from 'test/helpers/builders/entities/test-vulog-user-entity.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { VulogApiMocker } from 'test/helpers/external-services/vulog.api-mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { Repository } from 'typeorm';
import { CarEntity } from '../../../../src/logic/car/entity/car.entity';

const userId = aUUID();

const email = faker.internet.email();

describe(
  '/api/v1/availability',
  testApi(
    (context: ApiTestContext) => {
      let response: request.Response;

      let mockHttpAdapterService: MockAdapter;

      const testVulogUserEntity = createTestVulogUserEntity();

      const availabilityUrl = '/api/v1/availability';

      const testAuthResponse = createTestVulogAuthResponse();

      let userRepo: Repository<VulogUserEntity>;

      let carRepo: Repository<CarEntity>;

      let cacheService: CacheService;

      beforeAll(async () => {
        mockHttpAdapterService = new VulogApiMocker(context.app).mock();
        userRepo = context.app.get<Repository<VulogUserEntity>>(
          getRepositoryToken(VulogUserEntity),
        );
        carRepo = context.app.get<Repository<CarEntity>>(
          getRepositoryToken(CarEntity),
        );

        cacheService = context.app.get(CacheService);

        await cacheService.clearAll();
      });

      beforeEach(async () => {
        const userDto = createTestVulogUserDto();

        userDto.id = userId;
        userDto.email = email;

        mockHttpAdapterService
          .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
          .reply(200, userDto);

        mockHttpAdapterService
          .onPost(`${VulogPaths.auth()}`)
          .reply(200, testAuthResponse);

        const vulogAuthService: VulogAuthService =
          context.app.get(VulogAuthService);

        jest.spyOn(vulogAuthService, 'setupAuthConfig').mockResolvedValue({
          headers: {
            Authorization: `Bearer hehe`,
            'x-api-key': 'hehe',
          },
        });

        await userRepo.insert(testVulogUserEntity);
      });

      afterEach(async () => {
        mockHttpAdapterService.reset();

        await cacheService.clearAll();
        await carRepo.delete({});
        await userRepo.delete({});
      });

      async function makeRequest() {
        return request(context.getHttpServer()).get(availabilityUrl).send();
      }

      it('should return a list of stations with the number of available cars for each station', async () => {
        const mockStation1 = new StationDtoBuilder().build();
        const mockStation2 = new StationDtoBuilder().build();

        const mockStations = [mockStation1, mockStation2];

        const mockStation1ZoneId = mockStation1.toStation().zoneId;
        const mockStation2ZoneId = mockStation2.toStation().zoneId;

        const mockVulogAvailableCar1 = new VulogCarRealTimeDtoBuilder().build();
        const mockVulogCarStaticInfo1 = createTestVulogCarStaticInfoDto();
        mockVulogAvailableCar1.zones = [
          new VulogZone(mockStation1ZoneId, 1, VulogZoneType.Allowed, false),
        ];
        mockVulogAvailableCar1.vehicle_status =
          VulogRealTimeVehicleStatus.Available;
        mockVulogAvailableCar1.booking_status =
          VulogRealTimeBookingStatus.Available;
        mockVulogAvailableCar1.autonomy = 55;
        mockVulogCarStaticInfo1.id = mockVulogAvailableCar1.id;
        mockVulogCarStaticInfo1.model.name = CarModelEnum.BlueCar;

        const mockVulogAvailableCar2 = new VulogCarRealTimeDtoBuilder().build();
        const mockVulogCarStaticInfo2 = createTestVulogCarStaticInfoDto();
        mockVulogAvailableCar2.zones = [
          new VulogZone(mockStation1ZoneId, 1, VulogZoneType.Allowed, false),
        ];
        mockVulogAvailableCar2.vehicle_status =
          VulogRealTimeVehicleStatus.Available;
        mockVulogAvailableCar2.booking_status =
          VulogRealTimeBookingStatus.Available;
        mockVulogAvailableCar2.autonomy = 55;
        mockVulogCarStaticInfo2.id = mockVulogAvailableCar2.id;
        mockVulogCarStaticInfo2.model.name = CarModelEnum.BlueCar;

        const mockVulogAvailableCar3 = new VulogCarRealTimeDtoBuilder().build();
        const mockVulogCarStaticInfo3 = createTestVulogCarStaticInfoDto();
        mockVulogAvailableCar3.zones = [
          new VulogZone(mockStation2ZoneId, 1, VulogZoneType.Allowed, false),
        ];
        mockVulogAvailableCar3.vehicle_status =
          VulogRealTimeVehicleStatus.Available;
        mockVulogAvailableCar3.booking_status =
          VulogRealTimeBookingStatus.Available;
        mockVulogAvailableCar3.autonomy = 55;
        mockVulogCarStaticInfo3.id = mockVulogAvailableCar3.id;
        mockVulogCarStaticInfo3.model.name = CarModelEnum.OpelCorsaE;

        const mockVulogAvailableCars = [
          mockVulogAvailableCar1,
          mockVulogAvailableCar2,
          mockVulogAvailableCar3,
        ];
        const mockVulogCarStaticInfos = [
          mockVulogCarStaticInfo1,
          mockVulogCarStaticInfo2,
          mockVulogCarStaticInfo3,
        ];

        const cars = await carRepo.save(
          mockVulogCarStaticInfos.map((c) =>
            CarEntity.from(c.toStaticCar().toCar()),
          ),
        );

        const vulogBackOfficeVehicles = VulogPaths.backOfficeVehicles(
          config.vulogConfig.fleetId,
        );

        // Vulog Car Service - get real time car
        mockHttpAdapterService.onGet(`${vulogBackOfficeVehicles}`).replyOnce(
          200,
          mockVulogAvailableCars.map((dto) => dto.toRawJsonObject()),
        );

        // StationService - Get all stations
        mockHttpAdapterService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 1,
              pageSize: 200,
            })}`,
          )
          .replyOnce(200, {
            result: mockStations.map((stn) => stn.toRawJsonObject()),
          });

        mockHttpAdapterService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 2,
              pageSize: 200,
            })}`,
          )
          .replyOnce(200, {
            result: [], // Breaking loop
          });

        // NOTE: mock fleet service
        const vulogFleetService: VulogFleetService =
          context.app.get(VulogFleetService);

        jest
          .spyOn(vulogFleetService, 'getCustomerUsableFleetService')
          .mockResolvedValue(
            Object.assign(new FleetService(), {
              vehicleIds: cars.map((car) => car.vulogId),
            }),
          );

        response = await makeRequest();

        const expectedCarAvailabilityResponse: CarAvailabilityResponseDto = {
          stations: {
            [mockStation1.toStation().id.value]: {
              station: mockStation1.toStation(),
              carAvailability: { [CarModelName.BlueCar]: 2 },
            },
            [mockStation2.toStation().id.value]: {
              station: mockStation2.toStation(),
              carAvailability: { [CarModelName.CorsaE]: 1 },
            },
          },
        };

        expect(response.body.result).toEqual(
          JSON.parse(JSON.stringify(expectedCarAvailabilityResponse)),
        );
      });

      it('should return a list of stations with the number of available cars for each station (excluding battery-low car)', async () => {
        const mockStation1 = new StationDtoBuilder().build();
        const mockStation2 = new StationDtoBuilder().build();
        const mockStations = [mockStation1, mockStation2];
        const mockStation1ZoneId = mockStation1.toStation().zoneId;
        const mockStation2ZoneId = mockStation2.toStation().zoneId;

        const mockVulogAvailableCar1 = new VulogCarRealTimeDtoBuilder().build();
        const mockVulogCarStaticInfo1 = createTestVulogCarStaticInfoDto();
        mockVulogAvailableCar1.zones = [
          new VulogZone(mockStation1ZoneId, 1, VulogZoneType.Allowed, false),
        ];
        mockVulogAvailableCar1.vehicle_status =
          VulogRealTimeVehicleStatus.Available;
        mockVulogAvailableCar1.booking_status =
          VulogRealTimeBookingStatus.Available;
        mockVulogAvailableCar1.autonomy = 20; // Low battery
        mockVulogCarStaticInfo1.id = mockVulogAvailableCar1.id;
        mockVulogCarStaticInfo1.model.name = CarModelName.BlueCar;

        const mockVulogAvailableCar2 = new VulogCarRealTimeDtoBuilder().build();
        const mockVulogCarStaticInfo2 = createTestVulogCarStaticInfoDto();
        mockVulogAvailableCar2.zones = [
          new VulogZone(mockStation1ZoneId, 1, VulogZoneType.Allowed, false),
        ];
        mockVulogAvailableCar2.vehicle_status =
          VulogRealTimeVehicleStatus.Available;
        mockVulogAvailableCar2.booking_status =
          VulogRealTimeBookingStatus.Available;
        mockVulogAvailableCar2.autonomy = 55;
        mockVulogCarStaticInfo2.id = mockVulogAvailableCar2.id;
        mockVulogCarStaticInfo2.model.name = CarModelName.BlueCar;

        const mockVulogAvailableCar3 = new VulogCarRealTimeDtoBuilder().build();
        const mockVulogCarStaticInfo3 = createTestVulogCarStaticInfoDto();
        mockVulogAvailableCar3.zones = [
          new VulogZone(mockStation2ZoneId, 1, VulogZoneType.Allowed, false),
        ];
        mockVulogAvailableCar3.vehicle_status =
          VulogRealTimeVehicleStatus.Available;
        mockVulogAvailableCar3.booking_status =
          VulogRealTimeBookingStatus.Available;
        mockVulogAvailableCar3.autonomy = 55;
        mockVulogCarStaticInfo3.id = mockVulogAvailableCar3.id;
        mockVulogCarStaticInfo3.model.name = CarModelName.CorsaE;

        const mockVulogAvailableCars = [
          mockVulogAvailableCar1,
          mockVulogAvailableCar2,
          mockVulogAvailableCar3,
        ];
        const mockVulogCarStaticInfos = [
          mockVulogCarStaticInfo1,
          mockVulogCarStaticInfo2,
          mockVulogCarStaticInfo3,
        ];

        const cars = await carRepo.save(
          mockVulogCarStaticInfos.map((c) =>
            CarEntity.from(c.toStaticCar().toCar()),
          ),
        );

        // NOTE: mock fleet service
        const vulogFleetService: VulogFleetService =
          context.app.get(VulogFleetService);

        jest
          .spyOn(vulogFleetService, 'getCustomerUsableFleetService')
          .mockResolvedValue(
            Object.assign(new FleetService(), {
              vehicleIds: cars.map((car) => car.vulogId),
            }),
          );

        const vulogBackOfficeVehicles = VulogPaths.backOfficeVehicles(
          config.vulogConfig.fleetId,
        );

        // Vulog Car Service - get real time car
        mockHttpAdapterService.onGet(`${vulogBackOfficeVehicles}`).replyOnce(
          200,
          mockVulogAvailableCars.map((dto) => dto.toRawJsonObject()),
        );

        // StationService - Get all stations
        mockHttpAdapterService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 1,
              pageSize: 200,
            })}`,
          )
          .replyOnce(200, {
            result: mockStations.map((stn) => stn.toRawJsonObject()),
          });

        mockHttpAdapterService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 2,
              pageSize: 200,
            })}`,
          )
          .replyOnce(200, {
            result: [], // Breaking loop
          });

        response = await makeRequest();

        const expectedCarAvailabilityResponse: CarAvailabilityResponseDto = {
          stations: {
            [mockStation1.toStation().id.value]: {
              station: mockStation1.toStation(),
              carAvailability: { [CarModelName.BlueCar]: 1 },
            },
            [mockStation2.toStation().id.value]: {
              station: mockStation2.toStation(),
              carAvailability: { [CarModelName.CorsaE]: 1 },
            },
          },
        };

        expect(response.body.result).toEqual(
          JSON.parse(JSON.stringify(expectedCarAvailabilityResponse)),
        );
      });

      it('should return a list of stations with availability if there are no car available for any station', async () => {
        const mockStation1 = new StationDtoBuilder().build();
        const mockStation2 = new StationDtoBuilder().build();
        const mockStations = [mockStation1, mockStation2];

        const unavailableCars = [aUUID(), aUUID()].map((carId) => ({
          static: createTestVulogCarStaticInfoDto(carId),
          realTime: new VulogCarRealTimeDtoBuilder()
            .withId(carId)
            .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
            .withBookingStatus(VulogRealTimeBookingStatus.Available)
            .build(),
        }));

        // Vulog Car Service - get real time car
        mockHttpAdapterService
          .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
          .reply(
            200,
            unavailableCars.map((c) => c.realTime),
          );

        // VulogCarService - get all static cars
        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeListingAllStaticVehicles(
              config.vulogConfig.fleetId,
            )}?${QueryString.stringify({ page: 0, size: 200 })}`,
          )
          .reply(
            200,
            unavailableCars.map((c) => c.static),
            { last: true },
          );

        // StationService - Get all stations
        mockHttpAdapterService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 1,
              pageSize: 200,
            })}`,
          )
          .reply(200, {
            result: mockStations.map((stn) => stn.toRawJsonObject()),
          });

        // Vulog Car Service - getStaticInformationOfAllCars
        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeListAllVehicles(
              config.vulogConfig.fleetId,
            )}?${QueryString.stringify({
              page: 0,
              size: 200,
            })}`,
          )
          .reply(
            200,
            unavailableCars.map((c) => c.static),
            { last: true },
          );

        response = await makeRequest();

        const expectedCarAvailabilityResponse: CarAvailabilityResponseDto = {
          stations: {
            [mockStation1.toStation().id.value]: {
              station: mockStation1.toStation(),
              carAvailability: {},
            },
            [mockStation2.toStation().id.value]: {
              station: mockStation2.toStation(),
              carAvailability: {},
            },
          },
        };

        expect(response.body.result).toEqual(
          JSON.parse(JSON.stringify(expectedCarAvailabilityResponse)),
        );
      });

      it('should return a empty object when no station is found', async () => {
        const unavailableCars = [aUUID(), aUUID()].map((carId) => ({
          static: createTestVulogCarStaticInfoDto(carId),
          realTime: new VulogCarRealTimeDtoBuilder()
            .withId(carId)
            .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
            .withBookingStatus(VulogRealTimeBookingStatus.Available)
            .withAutonomy(55)
            .build(),
        }));

        // Vulog Car Service - get real time car
        mockHttpAdapterService
          .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
          .reply(
            200,
            unavailableCars.map((c) => c.realTime),
          );

        // VulogCarService - get all static cars
        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeListingAllStaticVehicles(
              config.vulogConfig.fleetId,
            )}?${QueryString.stringify({ page: 0, size: 200 })}`,
          )
          .reply(
            200,
            unavailableCars.map((c) => c.static),
            { last: true },
          );

        // StationService - Get all stations
        mockHttpAdapterService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 1,
              pageSize: 200,
            })}`,
          )
          .reply(200, {
            result: [],
          });

        response = await makeRequest();

        expect(response.body.result).toEqual({ stations: {} });
      });
    },
    [
      {
        name: CustomerJwtGuard,
        value: mockCustomerJwtGuard(userId),
        isGuard: true,
      },
    ],
  ),
);
