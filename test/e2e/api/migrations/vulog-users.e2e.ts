import { HttpStatus } from '@nestjs/common';
import <PERSON>ck<PERSON>dapter from 'axios-mock-adapter';
import * as supertest from 'supertest';
import { config } from '../../../../src/config';
import { VulogUserRepository } from '../../../../src/model/repositories/vulog-user.repository';
import { VulogUserStatus } from '../../../../src/model/vulog-user';
import { InternalApiService } from '../../../../src/shared/http/internal-api.service';
import { InternalUserDetailResponseDtoV1Builder } from '../../../helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { ApiTestContext, testApi } from '../../../helpers/test-api.util';

describe(
  'POST /api/v1/internal/migrations/vulog-users',
  testApi((context: ApiTestContext) => {
    let mockUss: MockAdapter;
    let response: supertest.Response;

    let vulogUserRepository: VulogUserRepository;

    const users = [
      new InternalUserDetailResponseDtoV1Builder().build(),
      new InternalUserDetailResponseDtoV1Builder().build(),
      new InternalUserDetailResponseDtoV1Builder().build(),
    ];

    async function request(): Promise<supertest.Response> {
      return supertest(context.getHttpServer()).post(
        '/api/v1/internal/migrations/vulog-users?batch_size=2',
      );
    }

    beforeAll(() => {
      vulogUserRepository = context.app.get(VulogUserRepository);
      const internalApiService: InternalApiService = context.app.get(
        config.httpServiceConfig.userSubscriptionName,
      );
      mockUss = new MockAdapter(internalApiService.httpService.axiosRef, {
        onNoMatch: 'throwException',
      });
    });

    beforeEach(async () => {
      mockUss.onGet('/internal/api/v1/users?page=1&pageSize=2').reply(200, {
        success: true,
        result: [users[0], users[1]],
        pagination: { page: 1, pageSize: 2, totalPages: 2 },
      });
      mockUss.onGet('/internal/api/v1/users?page=2&pageSize=2').reply(200, {
        success: true,
        result: [users[2]],
        pagination: { page: 2, pageSize: 2, totalPages: 2 },
      });

      response = await request();
    });

    afterEach(async () => {
      await vulogUserRepository.clearAll();
    });

    it('should return a HTTP Status CREATED', () => {
      expect(response.status).toBe(HttpStatus.CREATED);
    });

    it('should have created all the users from USS', async () => {
      const vUsers = await vulogUserRepository.find({});

      expect(vUsers.length).toBe(users.length);

      vUsers.forEach((vu) => {
        const u = users.find((u) => u.id === vu.bsgUserId);

        expect(vu).toMatchObject({
          id: expect.any(String),
          email: `vulog-${u.email}`,
          bsgUserId: u.id,
          status: VulogUserStatus.CREATING,
          password: expect.any(String),
          createdAt: expect.any(Date),
          profileId: null,
          isRegistered: false,
          updatedAt: expect.any(Date),
        });
      });
    });
  }),
);
