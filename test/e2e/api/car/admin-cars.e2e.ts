import MockAdapter from 'axios-mock-adapter';
import * as jwt from 'jsonwebtoken';
import { CarModel, VulogCarId } from 'src/common/tiny-types';
import { config } from 'src/config';
import { CarAdminInfoResponseDto } from 'src/controllers/dtos/car-admin-info.response.dto';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import * as request from 'supertest';
import { createTestVulogCarStaticInfoDto } from 'test/helpers/builders/dtos/test-car-dto.builder';
import { ApiTestContext, testApi } from 'test/helpers/test-api.util';

import { faker } from '@faker-js/faker';
import { HttpStatus } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import * as qs from 'querystring';
import { CacheService } from 'src/logic/cache/cache.service';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { VulogAuthService } from 'src/logic/vulog/vulog-auth.service';
import { VulogFleetService } from 'src/logic/vulog/vulog-fleet-service.service';
import { FleetService } from 'src/model/fleet-service';
import { VulogCarRealTimeDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { Repository } from 'typeorm';

describe(
  '/api/v1/admin/cars',
  testApi((context: ApiTestContext) => {
    let response: request.Response;

    let httpService: HttpAdapterService;

    let mockAdapter: MockAdapter;

    let token: string;

    let cacheService: CacheService;
    let vulogFleetService: VulogFleetService;

    let carRepo: Repository<CarEntity>;

    beforeAll(() => {
      token = jwt.sign(
        {
          userId: faker.string.uuid(),
          username: 'username',
          email: faker.internet.email(),
          name: faker.person.fullName(),
          roles: [
            {
              id: faker.string.uuid(),
              roleName: 'Admin',
              roleDescription: '',
              active: true,
            },
          ],
          permissions: [
            {
              id: faker.string.uuid(),
              resourceName: 'cars',
              action: 'all',
              attributes: '*',
              active: true,
            },
          ],
          lastChangePasswordTime: Date.now(),
          iat: Date.now() / 1000,
          exp: Date.now() / 1000 + 3600,
        },
        config.jwtConfig.adminSecret,
      );
    });

    beforeEach(() => {
      httpService = context.app.get(HttpAdapterService);

      cacheService = context.app.get(CacheService);
      vulogFleetService = context.app.get(VulogFleetService);

      carRepo = context.app.get(getRepositoryToken(CarEntity));

      const vulogAuthService: VulogAuthService =
        context.app.get(VulogAuthService);

      jest.spyOn(vulogAuthService, 'setupAuthConfig').mockResolvedValueOnce({
        headers: {
          Authorization: `Bearer hehe`,
          'x-api-key': 'hehe',
        },
      });

      mockAdapter = new MockAdapter(httpService.httpService.axiosRef);

      mockAdapter.onPost(`${VulogPaths.auth()}`).reply(200, {
        access_token: 'vulog-token',
        refresh_token: 'vulog-refresh-token',
      });
    });

    afterEach(async () => {
      mockAdapter.reset();

      cacheService.clearAll();

      await carRepo.delete({});
    });

    it('should get admin information about a specific car', async () => {
      const id = faker.string.numeric(5);

      const mockRealTimeCarDto = new VulogCarRealTimeDtoBuilder()
        .withId(id)
        .withAutonomy(55)
        .build();

      const mockCarStaticInfoDto = createTestVulogCarStaticInfoDto(id);

      await carRepo.save([
        CarEntity.from(mockCarStaticInfoDto.toStaticCar().toCar()),
      ]);

      jest
        .spyOn(vulogFleetService, 'getCustomerUsableFleetService')
        .mockResolvedValueOnce(
          Object.assign(new FleetService(), {
            vehicleIds: [id],
          }),
        );

      mockAdapter
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [mockRealTimeCarDto.toRawJsonObject()]);

      mockAdapter
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${qs.stringify({ page: 0, size: 200 })}`,
        )
        .replyOnce(200, [mockCarStaticInfoDto.toRawJsonObject()]);

      mockAdapter
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${qs.stringify({ page: 1, size: 200 })}`, // Breaking loop call
        )
        .replyOnce(200, []);

      response = await request(context.getHttpServer())
        .get(`/api/v1/admin/cars/${mockRealTimeCarDto.id}`)
        .set('Authorization', `Bearer ${token}`);

      expect(response.body.result).toEqual(
        CarAdminInfoResponseDto.from(
          mockRealTimeCarDto.toRealtimeCar(
            new CarModel(mockCarStaticInfoDto.model.name),
          ),
        ),
      );
    });

    it.skip.each([
      [HttpStatus.OK, false],
      [HttpStatus.ACCEPTED, true],
    ])(
      'should ping a car successfully',
      async (statusCode: HttpStatus, expectedIsCarAwake: boolean) => {
        const mockCarId = new VulogCarId(faker.string.uuid());
        mockAdapter
          .onPost(
            `${VulogPaths.vvgWakeUpVehicle(
              config.vulogConfig.fleetId,
              mockCarId,
            )}`,
          )
          .replyOnce(statusCode);

        response = await request(context.getHttpServer())
          .post(`/api/v1/admin/cars/${mockCarId.value}/ping`)
          .set('Authorization', `Bearer ${token}`);

        expect(response.body.result.isAwake).toEqual(expectedIsCarAwake);
      },
    );

    it.skip('should be able to get the first page of the list of all cars', async () => {
      const id = faker.string.numeric(5);

      const mockRealTimeCarDto = new VulogCarRealTimeDtoBuilder()
        .withId(id)
        .withAutonomy(55)
        .build();

      const mockCarStaticInfoDto = createTestVulogCarStaticInfoDto(id);

      await carRepo.save([
        CarEntity.from(mockCarStaticInfoDto.toStaticCar().toCar()),
      ]);

      jest
        .spyOn(vulogFleetService, 'getCustomerUsableFleetService')
        .mockResolvedValueOnce(
          Object.assign(new FleetService(), {
            vehicleIds: [id],
          }),
        );

      mockAdapter
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [mockRealTimeCarDto.toRawJsonObject()]);

      mockAdapter
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${qs.stringify({ page: 0, size: 200 })}`,
        )
        .replyOnce(200, [mockCarStaticInfoDto.toRawJsonObject()], {
          last: true,
        });

      response = await request(context.getHttpServer())
        .get(`/api/v1/admin/cars`)
        .set('Authorization', `Bearer ${token}`);

      expect(response.body.result).toEqual([
        JSON.parse(
          JSON.stringify(
            CarAdminInfoResponseDto.from(
              mockRealTimeCarDto.toRealtimeCar(
                new CarModel(mockCarStaticInfoDto.model.name),
              ),
            ),
          ),
        ),
      ]);
    });

    it.skip('should be able to lock a car', async () => {
      const mockCarId = new VulogCarId(faker.string.uuid());
      mockAdapter
        .onPost(
          `${VulogPaths.vvgLockVehicle(config.vulogConfig.fleetId, mockCarId)}`,
        )
        .replyOnce(200);

      response = await request(context.getHttpServer())
        .post(`/api/v1/admin/cars/${mockCarId.value}/lock`)
        .set('Authorization', `Bearer ${token}`);

      expect(response.body.success).toEqual(true);
    });

    it.skip('should be able to unlock a car', async () => {
      const mockCarId = new VulogCarId(faker.string.uuid());
      mockAdapter
        .onPost(
          `${VulogPaths.vvgUnlockVehicle(
            config.vulogConfig.fleetId,
            mockCarId,
          )}`,
        )
        .replyOnce(200);

      response = await request(context.getHttpServer())
        .post(`/api/v1/admin/cars/${mockCarId.value}/unlock`)
        .set('Authorization', `Bearer ${token}`);

      expect(response.body.success).toEqual(true);
    });

    it.skip('should be able to immobilize a car', async () => {
      const mockCarId = new VulogCarId(faker.string.uuid());
      mockAdapter
        .onPost(
          `${VulogPaths.vvgImmobilizeVehicle(
            config.vulogConfig.fleetId,
            mockCarId,
          )}`,
        )
        .replyOnce(200);

      response = await request(context.getHttpServer())
        .post(`/api/v1/admin/cars/${mockCarId.value}/immobilize`)
        .set('Authorization', `Bearer ${token}`);

      expect(response.body.success).toEqual(true);
    });

    it.skip('should be able to mobilize a car', async () => {
      const mockCarId = new VulogCarId(faker.string.uuid());
      mockAdapter
        .onPost(
          `${VulogPaths.vvgMobilizeVehicle(
            config.vulogConfig.fleetId,
            mockCarId,
          )}`,
        )
        .replyOnce(200);

      response = await request(context.getHttpServer())
        .post(`/api/v1/admin/cars/${mockCarId.value}/mobilize`)
        .set('Authorization', `Bearer ${token}`);

      expect(response.body.success).toEqual(true);
    });

    it.skip.each([
      [HttpStatus.OK, true],
      [HttpStatus.GONE, false],
    ])(
      'should be able to put a car in standby successfully',
      async (statusCode: number, expectedIsCarAwake: boolean) => {
        const mockCarId = new VulogCarId(faker.string.uuid());
        mockAdapter
          .onPost(
            `${VulogPaths.vvgStandbyVehicle(
              config.vulogConfig.fleetId,
              mockCarId,
            )}`,
          )
          .replyOnce(statusCode);

        response = await request(context.getHttpServer())
          .post(`/api/v1/admin/cars/${mockCarId.value}/standby`)
          .set('Authorization', `Bearer ${token}`);

        expect(response.body.result.isAwake).toEqual(expectedIsCarAwake);
      },
    );
  }),
);
