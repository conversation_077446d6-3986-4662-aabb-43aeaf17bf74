import MockAdapter from 'axios-mock-adapter';
import { config } from 'src/config';
import { RentalListResponseDto } from 'src/controllers/dtos/rental-list.response.dto';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import * as request from 'supertest';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogTripDto } from 'test/helpers/builders/dtos/test-vulog-trip-dto.builder';
import { createTestVulogUserEntity } from 'test/helpers/builders/entities/test-vulog-user-entity.builder';
import { ApiTestContext, testApi } from 'test/helpers/test-api.util';
import { Repository } from 'typeorm';

import { getRepositoryToken } from '@nestjs/typeorm';

import {
  JwtGuard,
  PasswordNotChangedGuard,
  RolesGuard,
} from '@bluesg-2/bo-auth-guard';
import { faker } from '@faker-js/faker';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { VulogAuthService } from 'src/logic/vulog/vulog-auth.service';
import { InternalApiService } from 'src/shared/http/internal-api.service';
import {
  ADMIN_JWT_MOCK_TOKEN,
  mockAdminJwtGuard,
} from 'test/helpers/admin-jwt-guard.mocker';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { mockPasswordNotChangedGuard } from 'test/helpers/password-not-changed-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { mockRolesGuard } from 'test/helpers/roles-guard.mocker';

const userId = aUUID();

const email = faker.internet.email();

const authorizedUserToken = JSON.stringify({
  id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
});

describe(
  '/api/v1/admin/rental/all-finished',
  testApi(
    (context: ApiTestContext) => {
      let response: request.Response;
      let mockHttpAdapterService: MockAdapter;
      let mockUssApiService: MockAdapter;
      const rentalUrl = '/api/v1/admin/rental/all-finished';

      const testAuthResponse = createTestVulogAuthResponse();

      let userRepo: Repository<VulogUserEntity>;

      beforeEach(async () => {
        const httpAdapterService = context.app.get(HttpAdapterService);
        mockHttpAdapterService = new MockAdapter(
          httpAdapterService.httpService.axiosRef,
        );

        const internalApiService1: InternalApiService = context.app.get(
          config.httpServiceConfig.userSubscriptionName,
        );
        mockUssApiService = new MockAdapter(
          internalApiService1.httpService.axiosRef,
        );
        const vulogAuthService: VulogAuthService =
          context.app.get(VulogAuthService);

        const userDto = createTestVulogUserDto();

        userDto.id = userId;
        userDto.email = email;

        jest.spyOn(vulogAuthService, 'setupAuthConfig').mockResolvedValueOnce({
          headers: {
            Authorization: `Bearer hehe`,
            'x-api-key': 'hehe',
          },
        });

        mockHttpAdapterService
          .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
          .reply(200, userDto);

        const userServicesAndProfiles = createTestVulogUserServicesDto();

        mockHttpAdapterService
          .onGet(
            `${VulogPaths.userServicesAndProfiles(
              config.vulogConfig.fleetId,
              new VulogUserId(userDto.id),
            )}`,
          )
          .reply(200, userServicesAndProfiles);

        mockHttpAdapterService
          .onPost(
            `${VulogPaths.registerUserProfileForService(
              config.vulogConfig.fleetId,
              new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
            )}`,
          )
          .reply(200, userServicesAndProfiles);

        mockHttpAdapterService
          .onPost(`${VulogPaths.auth()}`)
          .reply(200, testAuthResponse);

        mockUssApiService
          .onGet(
            `${
              config.httpServiceConfig.userSubscriptionUrl
            }${ApiRouteUtils.buildApiRouteSegment(
              RootRouteSegment.Internal,
              ApiVersion.V1,
              ['users', userId],
            )}`,
          )
          .reply(200, BaseResponseDto.success({ id: userId, email }));

        const testVulogUserEntity = createTestVulogUserEntity(
          userId,
          userServicesAndProfiles.profiles[0].profileId,
        );

        await userRepo.insert(testVulogUserEntity);
      });

      afterEach(async () => {
        mockHttpAdapterService.reset();

        await userRepo.clear();
      });

      beforeAll(async () => {
        userRepo = context.app.get<Repository<VulogUserEntity>>(
          getRepositoryToken(VulogUserEntity),
        );
      });

      it('should be able to get all finished rentals', async () => {
        const mockRentalTrip1 = createTestVulogTripDto(true, false);
        const mockRentalTrip2 = createTestVulogTripDto(true, false);
        const mockRentalTrip3 = createTestVulogTripDto(true, false);

        mockHttpAdapterService
          .onGet(
            `${VulogPaths.finishedTrips(
              config.vulogConfig.fleetId,
            ).addQueryParams({
              page: '0',
              size: '200',
            })}`,
          )
          .replyOnce(200, [mockRentalTrip1, mockRentalTrip2], {
            first: true,
            last: false,
          });

        mockHttpAdapterService
          .onGet(
            `${VulogPaths.finishedTrips(
              config.vulogConfig.fleetId,
            ).addQueryParams({
              page: '1',
              size: '200',
            })}`,
          )
          .replyOnce(200, [mockRentalTrip3], {
            first: false,
            last: true,
          });

        response = await request(context.getHttpServer())
          .get(`${rentalUrl}`)
          .set(ADMIN_JWT_MOCK_TOKEN, authorizedUserToken);

        const expectedResponseDto = RentalListResponseDto.from(
          [mockRentalTrip1, mockRentalTrip2, mockRentalTrip3].map((trip) =>
            trip.toRentalInfo(),
          ),
        );

        expect(response.body.result).toEqual(expectedResponseDto);
      });
    },
    [
      {
        name: JwtGuard,
        value: mockAdminJwtGuard(userId),
        isGuard: true,
      },
      {
        name: PasswordNotChangedGuard,
        value: mockPasswordNotChangedGuard(),
        isGuard: true,
      },
      {
        name: RolesGuard,
        value: mockRolesGuard(),
        isGuard: true,
      },
    ],
  ),
);
