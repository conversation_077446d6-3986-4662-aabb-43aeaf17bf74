import MockAdapter from 'axios-mock-adapter';
import { config } from 'src/config';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import * as request from 'supertest';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogJourneyDto } from 'test/helpers/builders/dtos/test-vulog-journey-dto.builder';
import { createTestVulogUserEntity } from 'test/helpers/builders/entities/test-vulog-user-entity.builder';
import { ApiTestContext, testApi } from 'test/helpers/test-api.util';
import { Repository } from 'typeorm';

import { getRepositoryToken } from '@nestjs/typeorm';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { faker } from '@faker-js/faker';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import {
  RentalPackageId,
  ReservationId,
  VulogCarId,
  VulogProfileId,
  VulogUserId,
} from 'src/common/tiny-types';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { bigNumberize } from 'src/common/utils/big-decimal.util';
import { OnGoingRentalResponseDtoV1 } from 'src/controllers/dtos/rental/response/on-going-rental.response.v1.dto';
import { BILLING_SERVICE_DI_TOKEN } from 'src/external/constants';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { VulogUserService } from 'src/logic/vulog/vulog-user.service';
import { VulogRealTimeVehicleStatus } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { OnGoingRental } from 'src/model/on-going-rental';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { InternalApiService } from 'src/shared/http/internal-api.service';
import { AttachRentalPackageRequestDtoV1Builder } from 'test/helpers/builders/dtos/attach-rental-package-request-dto.builder';
import { createTestVulogCarStaticInfoDto } from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import {
  CUSTOMER_JWT_MOCK_TOKEN,
  mockCustomerJwtGuard,
} from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';

import { DateTime } from 'luxon';

import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { CacheService } from 'src/logic/cache/cache.service';
import { VulogAuthService } from 'src/logic/vulog/vulog-auth.service';
import { RentalPackage } from 'src/model/rental-package';
import { CarRepository } from 'src/model/repositories/car.repository';
import { Reservation } from 'src/model/reservation.domain';
import { originalDateTime_Now } from 'test/constants';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { VulogCarRealTimeDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import { CarService } from '../../../../src/logic/car/car.service';
import { CarRealtimeMetadataBuilder } from '../../../helpers/builders/car-realtime.metadata.builder';
import { CarBuilder } from '../../../helpers/builders/car.builder';
import { StationDtoBuilder } from '../../../helpers/builders/dtos/station/station.dto.builder';

const userId = aUUID();

const email = faker.internet.email();

const authorizedUserToken = JSON.stringify({
  id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
});

const dtNowFunc = originalDateTime_Now;

describe(
  '/api/v1/rental',
  testApi(
    (context: ApiTestContext) => {
      let response: request.Response;

      const spyOnDateTime_Now = jest.spyOn(DateTime, 'now');

      let mockHttpAdapterService: MockAdapter;
      let mockUssApiService: MockAdapter;
      let mockBsApiService: MockAdapter;
      let vulogUserService: VulogUserService;
      let carService: CarService;
      let carRepository: CarRepository;
      let vulogUserRepository: Repository<VulogUserEntity>;

      let reservationRepository: Repository<ReservationEntity>;

      let cacheService: CacheService;

      const rentalUrl = '/api/v1/rental';

      const mockUserServicesAndProfiles = createTestVulogUserServicesDto();

      const mockAuthResponse = createTestVulogAuthResponse();
      const mockVulogUserEntity = createTestVulogUserEntity(
        userId,
        mockUserServicesAndProfiles.profiles[0].profileId,
      );

      const mockUssUser = new InternalUserDetailResponseDtoV1Builder()
        .withId(userId)
        .withEmail(email)
        .build();

      beforeEach(async () => {
        const httpAdapterService = context.app.get(HttpAdapterService);
        mockHttpAdapterService = new MockAdapter(
          httpAdapterService.httpService.axiosRef,
        );

        const internalApiService1: InternalApiService = context.app.get(
          config.httpServiceConfig.userSubscriptionName,
        );
        mockUssApiService = new MockAdapter(
          internalApiService1.httpService.axiosRef,
        );

        const internalApiService3: InternalApiService = context.app.get(
          BILLING_SERVICE_DI_TOKEN,
        );
        mockBsApiService = new MockAdapter(
          internalApiService3.httpService.axiosRef,
        );

        vulogUserRepository = context.app.get<Repository<VulogUserEntity>>(
          getRepositoryToken(VulogUserEntity),
        );

        reservationRepository = context.app.get<Repository<ReservationEntity>>(
          getRepositoryToken(ReservationEntity),
        );

        vulogUserService = context.app.get(VulogUserService);
        carService = context.app.get(CarService);
        cacheService = context.app.get(CacheService);

        carRepository = context.app.get(CarRepository);

        const vulogAuthService: VulogAuthService =
          context.app.get(VulogAuthService);

        jest.spyOn(vulogAuthService, 'setupAuthConfig').mockResolvedValue({
          headers: {
            Authorization: `Bearer hehe`,
            'x-api-key': 'hehe',
          },
        });

        const userDto = createTestVulogUserDto();

        userDto.id = userId;
        userDto.email = email;

        mockHttpAdapterService
          .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
          .reply(200, userDto);

        mockHttpAdapterService
          .onGet(
            `${VulogPaths.userServicesAndProfiles(
              config.vulogConfig.fleetId,
              new VulogUserId(userDto.id),
            )}`,
          )
          .reply(200, mockUserServicesAndProfiles);

        mockHttpAdapterService
          .onPost(
            `${VulogPaths.registerUserProfileForService(
              config.vulogConfig.fleetId,
              new VulogProfileId(
                mockUserServicesAndProfiles.profiles[0].profileId,
              ),
            )}`,
          )
          .reply(200, mockUserServicesAndProfiles);

        mockHttpAdapterService
          .onPost(`${VulogPaths.auth()}`)
          .reply(200, mockAuthResponse);

        mockUssApiService
          .onGet(
            `${
              config.httpServiceConfig.userSubscriptionUrl
            }${ApiRouteUtils.buildApiRouteSegment(
              RootRouteSegment.Internal,
              ApiVersion.V1,
              ['users', userId],
            )}`,
          )
          .reply(200, BaseResponseDto.success(mockUssUser));

        await vulogUserService.save(mockVulogUserEntity.toVulogUser());

        spyOnDateTime_Now.mockImplementation(dtNowFunc);
      });

      afterEach(async () => {
        mockHttpAdapterService.reset();

        await vulogUserRepository.delete({});

        await reservationRepository.delete({});

        await carRepository.clearAll();

        mockUssApiService.reset();

        mockBsApiService.reset();

        await cacheService.clearAll();
      });

      afterAll(async () => {
        spyOnDateTime_Now.mockRestore();
      });

      it('should be able to get details of an ongoing rental for a user (normal rental)', async () => {
        const now = DateTime.now();

        const dtNow = DateTime.now().plus({ hours: 2 });

        spyOnDateTime_Now.mockReturnValue(dtNow);

        const mockRentalJourney = createTestVulogJourneyDto(true, false);

        const mockStartStationDto = new StationDtoBuilder().build();

        const mockCurrentStationDto = new StationDtoBuilder().build();

        const reservationEntity = await reservationRepository.save(
          new ReservationEntityBuilder()
            .makeCarId(mockRentalJourney.vehicleId)
            .makeVulogTripId(mockRentalJourney.id)
            .makeBsgUserId(userId)
            .makeStatus(ReservationStatus.Converted)
            .makeStartedAt(now.minus({ hours: 3 }).toJSDate())
            .makeEndedAt(null)
            .makeExpiresAt(now.plus({ hours: 3 }).toJSDate())
            .makeStartStationId(mockStartStationDto.id)
            .build(),
        );

        const car = await carService.save(
          new CarBuilder()
            .withVulogId(new VulogCarId(mockRentalJourney.vehicleId))
            .build(),
        );

        const mockCarStaticInfoDto = createTestVulogCarStaticInfoDto(
          car.vulogId.value,
          config.vulogConfig.fleetId.value,
          car.model,
          car.vin,
        );

        const mockVulogCarRealtimeDto = new VulogCarRealTimeDtoBuilder()
          .withId(mockCarStaticInfoDto.id)
          .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
          .withFleetId(config.vulogConfig.fleetId.value)
          .withServiceId(config.vulogConfig.serviceId.value)
          .withZones([
            new VulogZone(mockCurrentStationDto.zoneId, 1, 'allowed', false),
          ])
          .withStartZones([
            new VulogZone(mockStartStationDto.zoneId, 1, 'allowed', false),
          ])
          .withVin(mockCarStaticInfoDto.vin)
          .withProfileId(mockUserServicesAndProfiles.profiles[0].profileId)
          .withStartDate(reservationEntity.startedAt.toISOString())
          .withAutonomy(55)
          .build();

        const onGoingRental = mockVulogCarRealtimeDto.toRentalInfo(new Map());

        const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
          .withZones([mockCurrentStationDto.zoneId, mockStartStationDto.zoneId])
          .withVehicles([mockCarStaticInfoDto.id])
          .build();

        // VulogCarService.getSpecificCarInRealtime
        mockHttpAdapterService
          .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
          .replyOnce(200, [mockVulogCarRealtimeDto]);

        // StationService.getAllStations
        mockHttpAdapterService
          .onGet(
            `${
              config.httpServiceConfig.stationUrl
            }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
              'stations?page=1&pageSize=200',
            ])}`,
          )
          .replyOnce(
            200,
            BaseResponseDto.success([
              mockStartStationDto,
              mockCurrentStationDto,
            ]),
          );

        // StationService.getAllStations
        mockHttpAdapterService
          .onGet(
            `${
              config.httpServiceConfig.stationUrl
            }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
              'stations?page=2&pageSize=200',
            ])})}`,
          )
          .replyOnce(200, BaseResponseDto.success([]));

        // VulogFleetService.getFleetServices
        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
          )
          .replyOnce(200, [mockVulogFleetServiceDto]);

        response = await request(context.getHttpServer())
          .get(`${rentalUrl}/on-going`)
          .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken);

        const reservation = Reservation.from(reservationEntity);

        const expectedResponseDto = OnGoingRentalResponseDtoV1.from(
          new OnGoingRental({
            id: new ReservationId(reservationEntity.id),
            vulogTripId: onGoingRental.sourceId,
            startDate: VulogDate.fromJsDate(reservationEntity.startedAt),
            endDate: reservationEntity.endedAt
              ? VulogDate.fromJsDate(reservationEntity.endedAt)
              : undefined,
            duration: reservation.getCurrentDuration(),
            billedDuration: reservation.getBilledDuration(),
            distance: onGoingRental.distance.value,
            price: null,
            startStation: mockStartStationDto.toStation(),
            endStation: null,
            currentStation: mockCurrentStationDto.toStation(),
            car: car,
            rentalPackage: undefined,
            hasStartedTripInVulog: onGoingRental.hasTripStarted,
            realtimeInfo: new CarRealtimeMetadataBuilder()
              .withBattery(mockVulogCarRealtimeDto.autonomy)
              .withIsDoorLocked(mockVulogCarRealtimeDto.isDoorLocked)
              .build(),
          }),
        );

        expect(response.body.result).toEqual(
          JSON.parse(JSON.stringify(expectedResponseDto)),
        );
      });

      it('should be able to get details of an ongoing rental for a user (rental package)', async () => {
        const now = DateTime.now();

        const dtNow = DateTime.now().plus({ hours: 2 });

        spyOnDateTime_Now.mockReturnValue(dtNow);

        const mockRentalJourney = createTestVulogJourneyDto(true, false);

        const mockStartStationDto = new StationDtoBuilder().build();

        const mockCurrentStationDto = new StationDtoBuilder().build();

        const rentalPackageData =
          new AttachRentalPackageRequestDtoV1Builder().build();

        const car = await carService.save(
          new CarBuilder()
            .withVulogId(new VulogCarId(mockRentalJourney.vehicleId))
            .build(),
        );
        const mockCarStaticInfoDto = createTestVulogCarStaticInfoDto(
          mockRentalJourney.vehicleId,
          config.vulogConfig.fleetId.value,
          car.model,
          car.vin,
        );

        const reservationEntity = await reservationRepository.save(
          new ReservationEntityBuilder()
            .makeCarId(mockRentalJourney.vehicleId)
            .makeStartedAt(now.minus({ hours: 3 }).toJSDate())
            .makeVulogTripId(mockRentalJourney.id)
            .makeRentalPackageData(rentalPackageData)
            .makeStatus(ReservationStatus.Converted)
            .makeEndedAt(null)
            .makeBsgUserId(userId)
            .makeExpiresAt(now.plus({ hours: 3 }).toJSDate())
            .makeStartStationId(mockStartStationDto.id)
            .build(),
        );

        const mockVulogCarRealtimeDto = new VulogCarRealTimeDtoBuilder()
          .withId(mockCarStaticInfoDto.id)
          .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
          .withFleetId(config.vulogConfig.fleetId.value)
          .withServiceId(config.vulogConfig.serviceId.value)
          .withZones([
            new VulogZone(mockCurrentStationDto.zoneId, 1, 'allowed', false),
          ])
          .withStartZones([
            new VulogZone(mockStartStationDto.zoneId, 1, 'allowed', false),
          ])
          .withVin(mockCarStaticInfoDto.vin)
          .withProfileId(mockUserServicesAndProfiles.profiles[0].profileId)
          .withStartDate(reservationEntity.startedAt.toISOString())
          .withAutonomy(55)
          .build();

        const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
          .withZones([mockCurrentStationDto.zoneId, mockStartStationDto.zoneId])
          .withVehicles([mockCarStaticInfoDto.id])
          .build();

        // VulogCarService.getSpecificCarInRealtime
        mockHttpAdapterService
          .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
          .replyOnce(200, [mockVulogCarRealtimeDto]);

        // StationService.getAllStations
        mockHttpAdapterService
          .onGet(
            `${
              config.httpServiceConfig.stationUrl
            }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
              'stations?page=1&pageSize=200',
            ])}`,
          )
          .replyOnce(
            200,
            BaseResponseDto.success([
              mockStartStationDto,
              mockCurrentStationDto,
            ]),
          );

        // StationService.getAllStations
        mockHttpAdapterService
          .onGet(
            `${
              config.httpServiceConfig.stationUrl
            }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
              'stations?page=2&pageSize=200',
            ])})}`,
          )
          .replyOnce(200, BaseResponseDto.success([]));

        // VulogFleetService.getFleetServices
        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
          )
          .replyOnce(200, [mockVulogFleetServiceDto]);

        response = await request(context.getHttpServer())
          .get(`${rentalUrl}/on-going`)
          .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken);

        const onGoingRental = mockVulogCarRealtimeDto.toRentalInfo(new Map());

        const rentalPackage = new RentalPackage({
          hrid: rentalPackageData.hrid,
          packageId: new RentalPackageId(rentalPackageData.packageId),
          duration: rentalPackageData.duration,
          name: rentalPackageData.name,
          price: rentalPackageData.price,
          minAllowedBatteryPercentage:
            rentalPackageData.minAllowedBatteryPercentage,
        });

        const reservation = Reservation.from(reservationEntity);

        const expectedResponseDto = OnGoingRentalResponseDtoV1.from(
          new OnGoingRental({
            id: new ReservationId(reservation.getId),
            vulogTripId: onGoingRental.sourceId,
            startDate: VulogDate.fromJsDate(reservationEntity.startedAt),
            endDate: reservationEntity.endedAt
              ? VulogDate.fromJsDate(reservationEntity.endedAt)
              : undefined,
            duration: reservation.getCurrentDuration(),
            billedDuration: reservation.getBilledDuration(),
            distance: bigNumberize(onGoingRental.distance.value).toNumber(),
            price: null,
            startStation: mockStartStationDto.toStation(),
            endStation: null,
            currentStation: mockCurrentStationDto.toStation(),
            car: car,
            hasStartedTripInVulog: onGoingRental.hasTripStarted,
            rentalPackage: rentalPackage,
            realtimeInfo: new CarRealtimeMetadataBuilder()
              .withBattery(mockVulogCarRealtimeDto.autonomy)
              .withIsDoorLocked(mockVulogCarRealtimeDto.isDoorLocked)
              .build(),
          }),
        );

        expect(response.body.result).toEqual(
          JSON.parse(JSON.stringify(expectedResponseDto)),
        );
      });

      it.skip('should be able to get details of an ongoing rental for a user (on-going BlueSG rental but in Vulog, it is still a Vulog booking)', async () => {
        const now = DateTime.now();

        const dtNow = DateTime.now().plus({ hours: 2 });

        spyOnDateTime_Now.mockReturnValue(dtNow);

        const mockRentalJourney = createTestVulogJourneyDto(true, false);

        const mockStartStationDto = new StationDtoBuilder().build();

        const reservationEntity = await reservationRepository.save(
          new ReservationEntityBuilder()
            .makeCarId(mockRentalJourney.vehicleId)
            .makeVulogTripId(mockRentalJourney.id)
            .makeBsgUserId(userId)
            .makeStatus(ReservationStatus.Converted)
            .makeStartedAt(now.minus({ hours: 3 }).toJSDate())
            .makeEndedAt(null)
            .makeExpiresAt(now.plus({ hours: 3 }).toJSDate())
            .makeStartStationId(mockStartStationDto.id)
            .build(),
        );

        const car = await carService.save(
          new CarBuilder()
            .withVulogId(new VulogCarId(mockRentalJourney.vehicleId))
            .build(),
        );

        const mockCarStaticInfoDto = createTestVulogCarStaticInfoDto(
          car.vulogId.value,
          config.vulogConfig.fleetId.value,
          car.model,
          car.vin,
        );

        const mockVulogCarRealtimeDto = new VulogCarRealTimeDtoBuilder()
          .withId(mockCarStaticInfoDto.id)
          .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
          .withFleetId(config.vulogConfig.fleetId.value)
          .withServiceId(config.vulogConfig.serviceId.value)
          .withZones([new VulogZone(aUUID(), 1, 'allowed', false)])
          .withStartZones([
            new VulogZone(mockStartStationDto.zoneId, 1, 'allowed', false),
          ])
          .withVin(mockCarStaticInfoDto.vin)
          .withProfileId(mockUserServicesAndProfiles.profiles[0].profileId)
          .withAutonomy(55)
          .build();

        const onGoingRental = mockVulogCarRealtimeDto.toRentalInfo(new Map());

        const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
          .withZones([mockStartStationDto.zoneId])
          .withVehicles([mockCarStaticInfoDto.id])
          .build();

        // VulogCarService.getSpecificCarInRealtime
        mockHttpAdapterService
          .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
          .replyOnce(200, [mockVulogCarRealtimeDto]);

        // StationService.getAllStations
        mockHttpAdapterService
          .onGet(
            `${
              config.httpServiceConfig.stationUrl
            }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
              'stations?page=1&pageSize=200',
            ])}`,
          )
          .replyOnce(200, BaseResponseDto.success([mockStartStationDto]));

        // StationService.getAllStations
        mockHttpAdapterService
          .onGet(
            `${
              config.httpServiceConfig.stationUrl
            }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
              'stations?page=2&pageSize=200',
            ])})}`,
          )
          .replyOnce(200, BaseResponseDto.success([]));

        // VulogFleetService.getFleetServices
        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
          )
          .replyOnce(200, [mockVulogFleetServiceDto]);

        response = await request(context.getHttpServer())
          .get(`${rentalUrl}/on-going`)
          .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken);

        const reservation = Reservation.from(reservationEntity);

        const expectedResponseDto = OnGoingRentalResponseDtoV1.from(
          new OnGoingRental({
            id: new ReservationId(reservationEntity.id),
            vulogTripId: onGoingRental.sourceId,
            startDate: VulogDate.fromJsDate(reservationEntity.startedAt),
            endDate: reservationEntity.endedAt
              ? VulogDate.fromJsDate(reservationEntity.endedAt)
              : undefined,
            duration: reservation.getCurrentDuration(),
            billedDuration: reservation.getBilledDuration(),
            distance: onGoingRental.distance.value,
            price: null,
            startStation: mockStartStationDto.toStation(),
            endStation: null,
            currentStation: mockStartStationDto.toStation(), // Same with start station as the vehicle is not moving yet
            car: car,
            rentalPackage: undefined,
            hasStartedTripInVulog: onGoingRental.hasTripStarted,
            realtimeInfo: new CarRealtimeMetadataBuilder()
              .withBattery(mockVulogCarRealtimeDto.autonomy)
              .withIsDoorLocked(mockVulogCarRealtimeDto.isDoorLocked)
              .build(),
          }),
        );

        expect(response.body.result).toEqual(
          JSON.parse(JSON.stringify(expectedResponseDto)),
        );
      });
    },
    [
      {
        name: CustomerJwtGuard,
        value: mockCustomerJwtGuard(userId),
        isGuard: true,
      },
    ],
  ),
);
