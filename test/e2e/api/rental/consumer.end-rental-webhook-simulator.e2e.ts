import MockAdapter from 'axios-mock-adapter';
import { config } from 'src/config';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import * as request from 'supertest';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserEntity } from 'test/helpers/builders/entities/test-vulog-user-entity.builder';
import { ApiTestContext, testApi } from 'test/helpers/test-api.util';
import { Repository } from 'typeorm';

import { getRepositoryToken } from '@nestjs/typeorm';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { faker } from '@faker-js/faker';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { CORRELATION_ID_HEADER } from 'src/common/constants/http-headers';
import { VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { BILLING_SERVICE_DI_TOKEN } from 'src/external/constants';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { VulogUserService } from 'src/logic/vulog/vulog-user.service';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { InternalApiService } from 'src/shared/http/internal-api.service';
import { TestEndRentalEventDtoBuilder } from 'test/helpers/builders/dtos/test-end-rental-event-dto.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import {
  CUSTOMER_JWT_MOCK_TOKEN,
  mockCustomerJwtGuard,
} from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';

import { DateTime } from 'luxon';
import { CacheService } from 'src/logic/cache/cache.service';
import { originalDateTime_Now } from 'test/constants';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';

const userId = aUUID();

const email = faker.internet.email();

const authorizedUserToken = JSON.stringify({
  id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
});

const dtNowFunc = originalDateTime_Now;

describe(
  '/api/v1/rental',
  testApi(
    (context: ApiTestContext) => {
      let response: request.Response;

      const spyOnDateTime_Now = jest.spyOn(DateTime, 'now');

      let mockHttpAdapterService: MockAdapter;
      let mockUssApiService: MockAdapter;
      let mockStsApiService: MockAdapter;
      let mockBsApiService: MockAdapter;
      let vulogUserService: VulogUserService;

      const rentalUrl = '/api/v1/rental';

      const mockUserServicesAndProfiles = createTestVulogUserServicesDto();

      const mockAuthResponse = createTestVulogAuthResponse();
      const mockVulogUserEntity = createTestVulogUserEntity(
        userId,
        mockUserServicesAndProfiles.profiles[0].profileId,
      );

      const mockUssUser = new InternalUserDetailResponseDtoV1Builder()
        .withId(userId)
        .withEmail(email)
        .build();

      let vulogUserRepository: Repository<VulogUserEntity>;

      let reservationRepository: Repository<ReservationEntity>;

      let cacheService: CacheService;

      beforeEach(async () => {
        const httpAdapterService = context.app.get(HttpAdapterService);
        mockHttpAdapterService = new MockAdapter(
          httpAdapterService.httpService.axiosRef,
        );

        const internalApiService1: InternalApiService = context.app.get(
          config.httpServiceConfig.userSubscriptionName,
        );
        mockUssApiService = new MockAdapter(
          internalApiService1.httpService.axiosRef,
        );

        const internalApiService2: InternalApiService = context.app.get(
          config.httpServiceConfig.stationName,
        );
        mockStsApiService = new MockAdapter(
          internalApiService2.httpService.axiosRef,
        );

        const internalApiService3: InternalApiService = context.app.get(
          BILLING_SERVICE_DI_TOKEN,
        );
        mockBsApiService = new MockAdapter(
          internalApiService3.httpService.axiosRef,
        );

        vulogUserRepository = context.app.get<Repository<VulogUserEntity>>(
          getRepositoryToken(VulogUserEntity),
        );

        reservationRepository = context.app.get<Repository<ReservationEntity>>(
          getRepositoryToken(ReservationEntity),
        );

        vulogUserService = context.app.get(VulogUserService);

        cacheService = context.app.get(CacheService);

        const userDto = createTestVulogUserDto();

        userDto.id = userId;
        userDto.email = email;

        mockHttpAdapterService
          .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
          .reply(200, userDto);

        mockHttpAdapterService
          .onGet(
            `${VulogPaths.userServicesAndProfiles(
              config.vulogConfig.fleetId,
              new VulogUserId(userDto.id),
            )}`,
          )
          .reply(200, mockUserServicesAndProfiles);

        mockHttpAdapterService
          .onPost(
            `${VulogPaths.registerUserProfileForService(
              config.vulogConfig.fleetId,
              new VulogProfileId(
                mockUserServicesAndProfiles.profiles[0].profileId,
              ),
            )}`,
          )
          .reply(200, mockUserServicesAndProfiles);

        mockHttpAdapterService
          .onPost(`${VulogPaths.auth()}`)
          .reply(200, mockAuthResponse);

        mockUssApiService
          .onGet(
            `${
              config.httpServiceConfig.userSubscriptionUrl
            }${ApiRouteUtils.buildApiRouteSegment(
              RootRouteSegment.Internal,
              ApiVersion.V1,
              ['users', userId],
            )}`,
          )
          .reply(200, BaseResponseDto.success(mockUssUser));

        await vulogUserService.save(mockVulogUserEntity.toVulogUser());

        spyOnDateTime_Now.mockImplementation(dtNowFunc);
      });

      afterEach(async () => {
        mockHttpAdapterService.reset();

        await vulogUserRepository.delete({});

        await reservationRepository.delete({});

        mockUssApiService.reset();

        mockStsApiService.reset();

        mockBsApiService.reset();

        await cacheService.clearAll();
      });

      afterAll(async () => {
        spyOnDateTime_Now.mockRestore();
      });

      it('should be able to end a rental for simulating data received from VULOG', async () => {
        const correlationId = aUUID();
        const testEndRentalRequestDto = new TestEndRentalEventDtoBuilder()
          .withDurationOfTheTrip(204)
          .build();

        response = await request(context.getHttpServer())
          .post(`${rentalUrl}/end/simulate`)
          .set(CORRELATION_ID_HEADER, correlationId)
          .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
          .send(testEndRentalRequestDto);

        expect(response.body.success).toBe(true);
        expect(response.body.result).toBeUndefined();
      });
    },
    [
      {
        name: CustomerJwtGuard,
        value: mockCustomerJwtGuard(userId),
        isGuard: true,
      },
    ],
  ),
);
