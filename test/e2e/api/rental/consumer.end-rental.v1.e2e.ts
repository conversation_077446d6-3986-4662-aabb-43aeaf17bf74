import MockAdapter from 'axios-mock-adapter';
import { config } from 'src/config';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import * as request from 'supertest';
import { createTestVulogJourneyDto } from 'test/helpers/builders/dtos/test-vulog-journey-dto.builder';
import { ApiTestContext, testApi } from 'test/helpers/test-api.util';
import { Repository } from 'typeorm';

import { getRepositoryToken } from '@nestjs/typeorm';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { faker } from '@faker-js/faker';
import * as QueryString from 'qs';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { VulogCarId, VulogJourneyOrTripId } from 'src/common/tiny-types';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { VulogRealTimeVehicleStatus } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { InternalApiService } from 'src/shared/http/internal-api.service';
import { createTestVulogCarStaticInfoDto } from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import {
  CUSTOMER_JWT_MOCK_TOKEN,
  mockCustomerJwtGuard,
} from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';

import { EventName } from '@bluesg-2/event-library';
import { DateTime } from 'luxon';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { CacheService } from 'src/logic/cache/cache.service';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { StationDto } from 'src/model/dtos/station.dto';
import { VulogFleetServiceDto } from 'src/model/dtos/vulog-fleet-service/vulog-fleet-service.dto';
import {
  VulogVehicleStatus,
  VvgVehicleStatusDto,
} from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { originalDateTime_Now } from 'test/constants';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { VulogCarRealTimeDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { EventBusService } from '~shared/event-bus/event-bus.service';

const userId = aUUID();

const email = faker.internet.email();

const authorizedUserToken = JSON.stringify({
  id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
});

const dtNowFunc = originalDateTime_Now;

describe(
  '/api/v1/rental',
  testApi(
    (context: ApiTestContext) => {
      let response: request.Response;

      const spyOnDateTime_Now = jest.spyOn(DateTime, 'now');

      let mockHttpAdapterService: MockAdapter;
      let mockUssApiService: MockAdapter;
      let mockStsApiService: MockAdapter;

      const rentalUrl = '/api/v1/rental';

      const mockUserServicesAndProfiles = createTestVulogUserServicesDto();

      const mockUssUser = new InternalUserDetailResponseDtoV1Builder()
        .withId(userId)
        .withEmail(email)
        .build();

      let reservationRepository: Repository<ReservationEntity>;

      let carRepository: Repository<CarEntity>;

      let cacheService: CacheService;

      let eventBusService: EventBusService;

      let reservation: ReservationEntity;

      let mockStartStationDto: StationDto;

      let mockEndStationDto: StationDto;

      async function setTestCarRental(withVulogTrip: boolean) {
        const dtNow = DateTime.now().plus({ hours: 1.5 });

        spyOnDateTime_Now.mockReturnValue(dtNow);

        const startStationId = aUUID();

        const startZoneId = aUUID();

        const endStationId = aUUID();

        const endZoneId = aUUID();

        const mockRentalJourney = createTestVulogJourneyDto(true, false);

        mockStartStationDto = new StationDtoBuilder()
          .withId(startStationId)
          .withZoneId(startZoneId)
          .build();

        mockEndStationDto = new StationDtoBuilder()
          .withId(endStationId)
          .withZoneId(endZoneId)
          .build();

        const mockCarStaticInfoDto = createTestVulogCarStaticInfoDto(
          mockRentalJourney.vehicleId,
          config.vulogConfig.fleetId.value,
        );

        // withVulogTrip specifies whether there's a vulog trip with this or not
        // this covers the case where rental is started on bsg, but is still a booking on vulog
        const mockVulogCarRealtimeDto = new VulogCarRealTimeDtoBuilder()
          .withId(mockCarStaticInfoDto.id)
          .withVehicleStatus(
            withVulogTrip
              ? VulogRealTimeVehicleStatus.InUse
              : VulogRealTimeVehicleStatus.Available,
          )
          .withFleetId(config.vulogConfig.fleetId.value)
          .withServiceId(config.vulogConfig.serviceId.value)
          .withZones([new VulogZone(startZoneId, 1, 'allowed', false)])
          .withStartZones([new VulogZone(startZoneId, 1, 'allowed', false)])
          .withVin(mockCarStaticInfoDto.vin)
          .withProfileId(mockUserServicesAndProfiles.profiles[0].profileId)
          .withStartDate(withVulogTrip ? DateTime.now().toISO() : null)
          .withIsCharging(true)
          .withIsDoorClosed(true)
          .withIsDoorLocked(true)
          .withEngineOn(false)
          .build();

        expect(mockVulogCarRealtimeDto.isRental()).toBe(withVulogTrip);

        // VulogCarService.getSpecificOnGoingRental
        // VulogCarService.fetchRealtimeVehicles
        mockHttpAdapterService
          .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
          .reply(200, [mockVulogCarRealtimeDto]);

        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeVehicles(
              config.vulogConfig.fleetId,
              new VulogCarId(mockRentalJourney.vehicleId),
            )}`,
          )
          .reply(200, mockVulogCarRealtimeDto);

        // VulogCarService.getUserCurrentRentals
        mockHttpAdapterService
          .onGet(`${VulogPaths.currentJourneys()}`)
          .reply(200, { journeys: [mockRentalJourney.toRawJsonObject()] });

        // ReservationService.getOneByCarIdAndTripId
        // ReservationService.getOwnOnGoingRental
        reservation = await reservationRepository.save(
          new ReservationEntityBuilder()
            .makeStatus(ReservationStatus.Converted)
            .makeEndedAt(null)
            .makeBsgUserId(userId)
            .makeCarId(mockRentalJourney.vehicleId)
            .makeVulogTripId(mockRentalJourney.id)
            .makeStartStationId(startStationId)
            .build(),
        );

        // VulogFleetService.getCustomerUsableFleetService
        const mockCustomerUsableFleetService = new VulogFleetServiceDto();
        mockCustomerUsableFleetService.id = config.vulogConfig.serviceId.value;

        await cacheService.set(
          config.redisConfig.fleetServices.cacheKey,
          [mockCustomerUsableFleetService],
          {
            ttl: config.redisConfig.fleetServices.ttl,
          },
        );

        // CarService.getOneByVulogId
        await carRepository.save(
          CarEntity.from(
            new CarBuilder()
              .withVulogId(new VulogCarId(mockRentalJourney.vehicleId))
              .withVin(mockCarStaticInfoDto.vin)
              .build(),
          ),
        );

        // VulogCarService.getAllStaticCars
        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeListingAllStaticVehicles(
              config.vulogConfig.fleetId,
            )}?${QueryString.stringify({ page: 0, size: 200 })}`,
          )
          .reply(200, [mockCarStaticInfoDto]);

        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeListingAllStaticVehicles(
              config.vulogConfig.fleetId,
            )}?${QueryString.stringify({ page: 1, size: 200 })}`,
          )
          .reply(200, []);

        // StationExternalService.getStation
        mockHttpAdapterService
          .onGet(
            `${
              config.httpServiceConfig.stationUrl
            }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
              'stations?page=1&pageSize=200',
            ])}`,
          )
          .reply(
            200,
            BaseResponseDto.success([mockStartStationDto, mockEndStationDto]),
          );

        mockHttpAdapterService
          .onDelete(
            `${VulogPaths.startOrEndTrip(
              new VulogJourneyOrTripId(mockRentalJourney.id),
            )}`,
          )
          .reply(200);

        // Get ended station
        mockStsApiService
          .onGet(
            `${
              config.httpServiceConfig.stationUrl
            }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
              'stations',
            ])}?${QueryString.stringify({
              zoneIds: [endZoneId].join(','),
            })}`,
          )
          .reply(200, BaseResponseDto.success([mockEndStationDto]));

        // VulogTelemetryService.getCarStatusSessionCommand
        const vvgVehicleStatus = new VvgVehicleStatusDto();
        vvgVehicleStatus.id = mockRentalJourney.vehicleId;
        vvgVehicleStatus.zones = {
          current: [new VulogZone(startZoneId, 1, 'allowed', false)],
        };

        const vulogVehicleStatus = new VulogVehicleStatus();
        vulogVehicleStatus.doorsAndWindowsClosed = true;
        vulogVehicleStatus.locked = true;
        vulogVehicleStatus.immobilizerOn = true;
        vulogVehicleStatus.engineOn = false;
        vulogVehicleStatus.charging = true;
        vulogVehicleStatus.plugged = true;

        vvgVehicleStatus.status = vulogVehicleStatus;

        mockHttpAdapterService
          .onGet(
            `${VulogPaths.vvgVehicle(
              config.vulogConfig.fleetId,
              new VulogCarId(mockRentalJourney.vehicleId),
            )}`,
          )
          .reply(200, vvgVehicleStatus);
        return mockCarStaticInfoDto;
      }

      beforeEach(async () => {
        const httpAdapterService = context.app.get(HttpAdapterService);
        mockHttpAdapterService = new MockAdapter(
          httpAdapterService.httpService.axiosRef,
        );

        const internalApiService1: InternalApiService = context.app.get(
          config.httpServiceConfig.userSubscriptionName,
        );
        mockUssApiService = new MockAdapter(
          internalApiService1.httpService.axiosRef,
        );

        const internalApiService2: InternalApiService = context.app.get(
          config.httpServiceConfig.stationName,
        );
        mockStsApiService = new MockAdapter(
          internalApiService2.httpService.axiosRef,
        );

        reservationRepository = context.app.get<Repository<ReservationEntity>>(
          getRepositoryToken(ReservationEntity),
        );

        carRepository = context.app.get<Repository<CarEntity>>(
          getRepositoryToken(CarEntity),
        );

        cacheService = context.app.get(CacheService);

        mockUssApiService
          .onGet(
            `${
              config.httpServiceConfig.userSubscriptionUrl
            }${ApiRouteUtils.buildApiRouteSegment(
              RootRouteSegment.Internal,
              ApiVersion.V1,
              ['users', userId],
            )}`,
          )
          .reply(200, BaseResponseDto.success(mockUssUser));

        spyOnDateTime_Now.mockImplementation(dtNowFunc);

        await cacheService.set('testUsername:vulog-access-token', 'foobar');

        eventBusService = context.app.get(EventBusService);
        eventBusService.nonBlockingEmit = jest.fn();
      });

      afterEach(async () => {
        mockHttpAdapterService.reset();

        await reservationRepository.delete({});

        await carRepository.delete({});

        mockUssApiService.reset();

        mockStsApiService.reset();

        await cacheService.clearAll();
      });

      afterAll(async () => {
        spyOnDateTime_Now.mockRestore();
      });

      it('should be able to end a rental with vulog trip', async () => {
        const mockCarStaticInfoDto = await setTestCarRental(true);

        // verify that we have an on-going rental
        response = await request(context.getHttpServer())
          .get(`${rentalUrl}/on-going`)
          .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken);

        expect(response.statusCode).toBe(200);
        expect(response.body.result.car.vin).toBe(mockCarStaticInfoDto.vin);

        // end that rental
        response = await request(context.getHttpServer())
          .delete(`${rentalUrl}`)
          .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken);

        expect(response.statusCode).toBe(200);
        expect(response.body.result.hasEnded).toBe(true);

        verifyRentalEndedEvent();
      });

      function verifyRentalEndedEvent() {
        // verify event, start & end stations will be the same
        expect(eventBusService.nonBlockingEmit as jest.Mock).toBeCalledTimes(1);
        const emittedEvent = (eventBusService.nonBlockingEmit as jest.Mock).mock
          .calls[0][0];
        expect(emittedEvent.event).toBe(EventName.RentalEndedEvent);
        expect(emittedEvent.payload.id).toBe(reservation.id);
        expect(emittedEvent.payload.carId).toBe(reservation.carId);
        expect(emittedEvent.payload.createdAt).toBeTruthy();
        expect(emittedEvent.payload.reservedAt).toBeTruthy();
        expect(emittedEvent.payload.startedAt).toBeTruthy();
        expect(emittedEvent.payload.startStationId).toBe(
          mockStartStationDto.id,
        );
        expect(emittedEvent.payload.endedAt).toBeTruthy();
        expect(emittedEvent.payload.endStationId).toBe(mockStartStationDto.id);
        expect(emittedEvent.payload.status).toBe(ReservationStatus.Ended);
        expect(emittedEvent.payload.userId).toBe(reservation.bsgUserId);
        // legacy fields
        expect(emittedEvent.payload.tripInfo.id).toBe(reservation.id);
        expect(emittedEvent.payload.carInfo.id).toBe(reservation.carId);
        expect(emittedEvent.payload.userInfo.id).toBe(reservation.bsgUserId);
        expect(emittedEvent.payload.subscriptionInfo).toBeTruthy();
        expect(emittedEvent.payload.startStationInfo.id).toBe(
          mockStartStationDto.id,
        );
        expect(emittedEvent.payload.endStationInfo.id).toBe(
          mockStartStationDto.id,
        );
        expect(emittedEvent.payload.rentalPackageInfo).toBeNull();
      }

      it('should be able to end a rental without vulog trip', async () => {
        const mockCarStaticInfoDto = await setTestCarRental(false);

        // verify that we have an on-going rental
        response = await request(context.getHttpServer())
          .get(`${rentalUrl}/on-going`)
          .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken);

        expect(response.statusCode).toBe(200);
        expect(response.body.result.car.vin).toBe(mockCarStaticInfoDto.vin);

        // end that rental
        response = await request(context.getHttpServer())
          .delete(`${rentalUrl}`)
          .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken);

        expect(response.statusCode).toBe(200);
        expect(response.body.result.hasEnded).toBe(true);

        verifyRentalEndedEvent();
      });

      it('should throw error when there is no on-going rental', async () => {
        response = await request(context.getHttpServer())
          .delete(`${rentalUrl}`)
          .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken);

        expect(response.body.message).toEqual('No on-going rental is found');
      });
    },
    [
      {
        name: CustomerJwtGuard,
        value: mockCustomerJwtGuard(userId),
        isGuard: true,
      },
    ],
  ),
);
