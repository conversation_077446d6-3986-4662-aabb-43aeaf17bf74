import * as supertest from 'supertest';

import { HttpStatus, INestApplication } from '@nestjs/common';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { AppBuilder } from 'test/helpers/app.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aMD5, aUUID } from 'test/helpers/random-data.helper';
import { Repository } from 'typeorm';

import MockAdapter from 'axios-mock-adapter';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';

import { DateTime } from 'luxon';

import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { ApiVersion } from 'src/common/constants/api-versions';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { config } from 'src/config';
import { EndRentalWithQRRequestDtoV1 } from 'src/controllers/dtos/rental/request/end-rental-with-qr.request.dto.v1';
import {
  InternalParkingLotResponseDtoV1,
  ParkingLotStatus,
} from 'src/external/station/dtos/response/internal.parking-lot.v1.response.dto';
import { InternalStationResponseDtoV1 } from 'src/external/station/dtos/response/internal.station-response.v1.response.dto';
import { CacheService } from 'src/logic/cache/cache.service';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogRealTimeVehicleStatus } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { InternalStationResponseDtoV1Builder } from 'test/helpers/builders/dtos/internal-station.response.v1.dto.builder';
import { InternalParkingLotResponseDtoV1Builder } from 'test/helpers/builders/dtos/sts/internal.parking-lot.response.v1.dto.builder';
import { createTestVulogCarStaticInfoDto } from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { VulogCarRealTimeDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import { VulogUserDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-user.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { CUSTOMER_JWT_MOCK_TOKEN } from 'test/helpers/customer-jwt-guard.mocker';

import * as QueryString from 'qs';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { EndRentalConditionId } from 'src/common/constants/ending-rental-conditions';
import { STATION_DISABLED_MESSAGE } from 'src/common/constants/messages';
import { VulogCarId, VulogZoneId } from 'src/common/tiny-types';
import { EndRentalResponseV1Dto } from 'src/controllers/dtos/rental/response/end-rental.response.v1.dto';
import { StationStatus } from 'src/external/station/dtos/station-status';
import {
  SubscriptionStatus,
  UserStatus,
} from 'src/external/user-subscription/constants';
import { RentalService } from 'src/logic/rental.service';
import { VvgVehicleStatusDto } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { OnGoingRental } from 'src/model/on-going-rental';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { VulogCarStaticInfoDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car-static-info.dto.builder';
import { VvgVehicleStatusDtoBuilder } from 'test/helpers/builders/dtos/vulog/vvg/vvg-vehicle-status.dto.builder';
import { StationApiMocker } from 'test/helpers/external-services/station.api-mocker';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { UssApiMocker } from '../../../helpers/external-services/uss.api-mocker';
import { VulogApiMocker } from '../../../helpers/external-services/vulog.api-mocker';

const userId = aUUID();

const authorizedUserToken = JSON.stringify({
  id: userId, // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
});

const rentalPrefixUrl = '/api/v1/rental/:reservationId/end';

describe(`[E2E] POST: ${rentalPrefixUrl}`, () => {
  let app: INestApplication;

  let mockAdapter_VulogApiService: MockAdapter;
  let mockAdapter_UssApiService: MockAdapter;
  let mockAdapter_StsApiService: MockAdapter;

  let cacheService: CacheService;

  let userRepo: Repository<VulogUserEntity>;
  let reservationRepo: Repository<ReservationEntity>;
  let carRepo: Repository<CarEntity>;
  let rentalService: RentalService;
  let featureFlagService: FeatureFlagService;

  function requestFor(
    reservationId: string,
    dto: EndRentalWithQRRequestDtoV1,
  ): supertest.Test {
    const superTest = supertest(app.getHttpServer())
      .post(rentalPrefixUrl.replace(':reservationId', reservationId))
      .set(CUSTOMER_JWT_MOCK_TOKEN, authorizedUserToken)
      .send(dto);

    return superTest;
  }

  const vulogUserDto = new VulogUserDtoBuilder().withId(userId).build();
  const vulogUserProfileDto = createTestVulogUserServicesDto();

  beforeAll(async () => {
    app = await new AppBuilder()
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(aUUID()), true)
      .buildAndStart();

    userRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );

    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );

    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );

    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));

    const vulogApiMocker = new VulogApiMocker(app);

    mockAdapter_VulogApiService = vulogApiMocker.mock();
    mockAdapter_UssApiService = new UssApiMocker(app).mock();
    mockAdapter_StsApiService = new StationApiMocker(app).mock();

    cacheService = app.get(CacheService);

    rentalService = app.get(RentalService);

    featureFlagService = app.get(FeatureFlagService);

    // Make Vulog Credentials Ready
    await vulogApiMocker.makeVulogCredentialsReady(
      vulogUserDto,
      vulogUserProfileDto,
    );
  });

  afterAll(async () => {
    await cleanResources(true);
    await app.close();
  });

  async function cleanResources(truncateUsers = false): Promise<void> {
    jest.clearAllMocks();

    mockAdapter_VulogApiService.reset();
    mockAdapter_UssApiService.reset();

    if (truncateUsers) await userRepo.clear();
    await reservationRepo.clear();
    await cacheService.clearAll();
    await carRepo.delete({});
  }

  describe('Should be able to end rental by QR Code successfully', () => {
    const mock_qrCode =
      'https://qrcode-chargingservices.totalenergies.sg/qrcode?emi3=SG*TCB*E001241';

    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());

    const mock_internalStationResponseDtoV1: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder().build();

    const mock_placeholder_internalStationResponseDtoV1_A: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder().build();

    const mock_placeholder_internalStationResponseDtoV1_B: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder().build();

    const mock_internalParkingLotResponseDtoV1: InternalParkingLotResponseDtoV1 =
      new InternalParkingLotResponseDtoV1Builder()
        .buildStatus(ParkingLotStatus.AVAILABLE)
        .buildStation(mock_internalStationResponseDtoV1)
        .buildIsAvailable(true)
        .getResult();

    const mock_CarStaticInfoDto = createTestVulogCarStaticInfoDto(
      carEntity.vulogId,
      config.vulogConfig.fleetId.value,
      carEntity.model,
      carEntity.vin,
    );

    const mock_VulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withZones([
        mock_internalStationResponseDtoV1.zoneId,
        mock_placeholder_internalStationResponseDtoV1_A.zoneId,
        mock_placeholder_internalStationResponseDtoV1_B.zoneId,
      ])
      .withVehicles([mock_CarStaticInfoDto.id])
      .build();

    const vulogTripId: string = aMD5();

    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(carEntity.vulogId)
      .makePlateNumber(carEntity.plate)
      .makeStatus(ReservationStatus.Converted) // On-going rental
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(carEntity.plate)
      .makeBsgUserId(userId)
      .makeVulogTripId(vulogTripId)
      .makeStartStationId(mock_internalStationResponseDtoV1.id)
      .makeValidPricingPolicy()
      .build();

    const mock_vulogCarRealtimeDto = new VulogCarRealTimeDtoBuilder()
      .withId(mock_CarStaticInfoDto.id)
      .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
      .withFleetId(config.vulogConfig.fleetId.value)
      .withServiceId(config.vulogConfig.serviceId.value)
      .withZones([
        new VulogZone(
          mock_internalStationResponseDtoV1.zoneId,
          1,
          'allowed',
          false,
        ),
        new VulogZone(
          mock_placeholder_internalStationResponseDtoV1_A.zoneId,
          1,
          'allowed',
          false,
        ),
        new VulogZone(
          mock_placeholder_internalStationResponseDtoV1_B.zoneId,
          1,
          'allowed',
          false,
        ),
      ])
      .withStartZones([
        new VulogZone(
          mock_internalStationResponseDtoV1.zoneId,
          1,
          'allowed',
          false,
        ),
      ])
      .withVin(mock_CarStaticInfoDto.vin)
      .withProfileId(vulogUserProfileDto.profiles[0].profileId)
      .withStartDate(reservationEntity.startedAt.toISOString())
      .withAutonomy(55)
      .build();

    const mock_vvgVehicleStatusDto: VvgVehicleStatusDto =
      new VvgVehicleStatusDtoBuilder()
        .withId(carEntity.vulogId)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true) // For the car is locked, the immobilizer must be on
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withImmobilizerOn(true) // For the immobilizer is on, the car must be locked
        .build();

    let apiResponse: supertest.Response;

    let spyInstance_RentalService__getCurrentRentalDetail;

    // Map static car DTOs
    const mock_vulogStaticCarDtos = [
      new VulogCarStaticInfoDtoBuilder()
        .withId(carEntity.vulogId)
        .withModel(carEntity.model)
        .withPlate(carEntity.plate)
        .withServiceId(mock_VulogFleetServiceDto.id)
        .withVin(carEntity.vin)
        .build(),
    ];

    beforeAll(async () => {
      jest
        .spyOn(featureFlagService, 'enforceQRStationSameAsEndingStation')
        .mockResolvedValue(true);

      spyInstance_RentalService__getCurrentRentalDetail = jest.spyOn(
        rentalService,
        'getCurrentRentalDetail',
      );

      await carRepo.insert(carEntity);

      await reservationRepo.insert(reservationEntity);

      // VulogCarService.getSpecificCarInRealtime
      mockAdapter_VulogApiService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [mock_vulogCarRealtimeDto]);

      // StationService.getAllStations
      mockAdapter_VulogApiService
        .onGet(
          `${
            config.httpServiceConfig.stationUrl
          }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'stations?page=1&pageSize=200',
          ])}`,
        )
        .replyOnce(
          200,
          BaseResponseDto.success([
            mock_placeholder_internalStationResponseDtoV1_A,
            mock_placeholder_internalStationResponseDtoV1_B,
            mock_internalStationResponseDtoV1,
          ]),
        );

      // StationService.getAllStations
      mockAdapter_VulogApiService
        .onGet(
          `${
            config.httpServiceConfig.stationUrl
          }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'stations?page=2&pageSize=200',
          ])})}`,
        )
        .replyOnce(200, BaseResponseDto.success([]));

      // VulogFleetService.getFleetServices
      mockAdapter_VulogApiService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .replyOnce(200, [mock_VulogFleetServiceDto]);

      // StationExternalService.internalApi.getParkingLotByQR
      mockAdapter_StsApiService
        .onPost(
          `${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'parking-lots',
            'parking-lots',
            'qr',
          ])}`,
        )
        .reply(
          200,
          BaseResponseDto.success(mock_internalParkingLotResponseDtoV1),
        );

      // VulogTelemetryService.getCarStatusSessionCommand
      mockAdapter_VulogApiService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .reply(200, mock_vvgVehicleStatusDto);

      // 💥 Below this line is to mock the logic that is previously defined in local end trip report handler, now it is moved to RentalService
      // VulogFleetService.getFleetServices

      // Stick station with car. Already covered in another test, so mock Service directly
      mockAdapter_VulogApiService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
            new VulogZoneId(mock_internalStationResponseDtoV1.zoneId),
          )}`,
        )
        .replyOnce(200);

      // Listing out static cars from Vulog
      mockAdapter_VulogApiService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, mock_vulogStaticCarDtos, { last: true });

      // Get user details from USS
      mockAdapter_UssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .reply(200, {
          success: true,
          result: JSON.parse(
            JSON.stringify(
              new InternalUserDetailResponseDtoV1Builder()
                .withId(userId)
                .withUserStatus(UserStatus.VERIFIED)
                .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
                .build(),
            ),
          ),
        } as BaseResponseDto);

      // StationService.getAllStations
      mockAdapter_VulogApiService
        .onGet(
          `${
            config.httpServiceConfig.stationUrl
          }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'stations?page=1&pageSize=200',
          ])}`,
        )
        .replyOnce(
          200,
          BaseResponseDto.success([
            mock_placeholder_internalStationResponseDtoV1_A,
            mock_placeholder_internalStationResponseDtoV1_B,
            mock_internalStationResponseDtoV1,
          ]),
        );

      // StationService.getAllStations
      mockAdapter_VulogApiService
        .onGet(
          `${
            config.httpServiceConfig.stationUrl
          }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'stations?page=2&pageSize=200',
          ])})}`,
        )
        .replyOnce(200, BaseResponseDto.success([]));

      apiResponse = await requestFor(reservationEntity.id, {
        qrCode: mock_qrCode,
      });
    });

    it('All ending rental conditions are satisfied (hasEnded = true)', async () => {
      expect(apiResponse.status).toEqual(HttpStatus.CREATED);

      const resultBody = apiResponse.body.result;

      expect(resultBody.hasEnded).toEqual(true);
    });

    it('Current Station (later chosen as Ending Station) should be the same as QR station', async () => {
      const mockResult =
        spyInstance_RentalService__getCurrentRentalDetail.mock.results[0];

      const resultValue: OnGoingRental = await mockResult.value;

      expect(resultValue.currentStation.id.value).toEqual(
        mock_internalStationResponseDtoV1.id,
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('Should be able to end rental by QR Code successfully when car lock check is disabled', () => {
    const mock_qrCode =
      'https://qrcode-chargingservices.totalenergies.sg/qrcode?emi3=SG*TCB*E001241';

    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());

    const mock_internalStationResponseDtoV1: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder().build();

    const mock_placeholder_internalStationResponseDtoV1_A: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder().build();

    const mock_placeholder_internalStationResponseDtoV1_B: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder().build();

    const mock_internalParkingLotResponseDtoV1: InternalParkingLotResponseDtoV1 =
      new InternalParkingLotResponseDtoV1Builder()
        .buildStatus(ParkingLotStatus.AVAILABLE)
        .buildStation(mock_internalStationResponseDtoV1)
        .buildIsAvailable(true)
        .getResult();

    const mock_CarStaticInfoDto = createTestVulogCarStaticInfoDto(
      carEntity.vulogId,
      config.vulogConfig.fleetId.value,
      carEntity.model,
      carEntity.vin,
    );

    const mock_VulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withZones([
        mock_internalStationResponseDtoV1.zoneId,
        mock_placeholder_internalStationResponseDtoV1_A.zoneId,
        mock_placeholder_internalStationResponseDtoV1_B.zoneId,
      ])
      .withVehicles([mock_CarStaticInfoDto.id])
      .build();

    const vulogTripId: string = aMD5();

    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(carEntity.vulogId)
      .makePlateNumber(carEntity.plate)
      .makeStatus(ReservationStatus.Converted) // On-going rental
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(carEntity.plate)
      .makeBsgUserId(userId)
      .makeVulogTripId(vulogTripId)
      .makeStartStationId(mock_internalStationResponseDtoV1.id)
      .makeValidPricingPolicy()
      .build();

    const mock_vulogCarRealtimeDto = new VulogCarRealTimeDtoBuilder()
      .withId(mock_CarStaticInfoDto.id)
      .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
      .withFleetId(config.vulogConfig.fleetId.value)
      .withServiceId(config.vulogConfig.serviceId.value)
      .withZones([
        new VulogZone(
          mock_internalStationResponseDtoV1.zoneId,
          1,
          'allowed',
          false,
        ),
        new VulogZone(
          mock_placeholder_internalStationResponseDtoV1_A.zoneId,
          1,
          'allowed',
          false,
        ),
        new VulogZone(
          mock_placeholder_internalStationResponseDtoV1_B.zoneId,
          1,
          'allowed',
          false,
        ),
      ])
      .withStartZones([
        new VulogZone(
          mock_internalStationResponseDtoV1.zoneId,
          1,
          'allowed',
          false,
        ),
      ])
      .withVin(mock_CarStaticInfoDto.vin)
      .withProfileId(vulogUserProfileDto.profiles[0].profileId)
      .withStartDate(reservationEntity.startedAt.toISOString())
      .withAutonomy(55)
      .build();

    const mock_vvgVehicleStatusDto: VvgVehicleStatusDto =
      new VvgVehicleStatusDtoBuilder()
        .withId(carEntity.vulogId)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(false) // For the car is locked, the immobilizer must be on
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withImmobilizerOn(false) // For the immobilizer is on, the car must be locked
        .build();

    // Map static car DTOs
    const mock_vulogStaticCarDtos = [
      new VulogCarStaticInfoDtoBuilder()
        .withId(carEntity.vulogId)
        .withModel(carEntity.model)
        .withPlate(carEntity.plate)
        .withServiceId(mock_VulogFleetServiceDto.id)
        .withVin(carEntity.vin)
        .build(),
    ];

    let apiResponse: supertest.Response;

    let spyInstance_RentalService__getCurrentRentalDetail;

    beforeAll(async () => {
      jest
        .spyOn(featureFlagService, 'enforceQRStationSameAsEndingStation')
        .mockResolvedValue(true);

      jest
        .spyOn(featureFlagService, 'isRequireLockCarWhenEndRental')
        .mockResolvedValue(false);

      spyInstance_RentalService__getCurrentRentalDetail = jest.spyOn(
        rentalService,
        'getCurrentRentalDetail',
      );

      await carRepo.insert(carEntity);

      await reservationRepo.insert(reservationEntity);

      // VulogCarService.getSpecificCarInRealtime
      mockAdapter_VulogApiService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [mock_vulogCarRealtimeDto]);

      // StationService.getAllStations
      mockAdapter_VulogApiService
        .onGet(
          `${
            config.httpServiceConfig.stationUrl
          }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'stations?page=1&pageSize=200',
          ])}`,
        )
        .replyOnce(
          200,
          BaseResponseDto.success([
            mock_placeholder_internalStationResponseDtoV1_A,
            mock_placeholder_internalStationResponseDtoV1_B,
            mock_internalStationResponseDtoV1,
          ]),
        );

      // StationService.getAllStations
      mockAdapter_VulogApiService
        .onGet(
          `${
            config.httpServiceConfig.stationUrl
          }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'stations?page=2&pageSize=200',
          ])})}`,
        )
        .replyOnce(200, BaseResponseDto.success([]));

      // VulogFleetService.getFleetServices
      mockAdapter_VulogApiService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .replyOnce(200, [mock_VulogFleetServiceDto]);

      // StationExternalService.internalApi.getParkingLotByQR
      mockAdapter_StsApiService
        .onPost(
          `${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'parking-lots',
            'parking-lots',
            'qr',
          ])}`,
        )
        .reply(
          200,
          BaseResponseDto.success(mock_internalParkingLotResponseDtoV1),
        );

      // VulogTelemetryService.getCarStatusSessionCommand
      mockAdapter_VulogApiService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .reply(200, mock_vvgVehicleStatusDto);

      // 💥 Below this line is to mock the logic that is previously defined in local end trip report handler, now it is moved to RentalService
      // VulogFleetService.getFleetServices

      // Stick station with car. Already covered in another test, so mock Service directly
      mockAdapter_VulogApiService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
            new VulogZoneId(mock_internalStationResponseDtoV1.zoneId),
          )}`,
        )
        .replyOnce(200);

      // Listing out static cars from Vulog
      mockAdapter_VulogApiService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, mock_vulogStaticCarDtos, { last: true });

      // Get user details from USS
      mockAdapter_UssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .reply(200, {
          success: true,
          result: JSON.parse(
            JSON.stringify(
              new InternalUserDetailResponseDtoV1Builder()
                .withId(userId)
                .withUserStatus(UserStatus.VERIFIED)
                .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
                .build(),
            ),
          ),
        } as BaseResponseDto);

      // StationService.getAllStations
      mockAdapter_VulogApiService
        .onGet(
          `${
            config.httpServiceConfig.stationUrl
          }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'stations?page=1&pageSize=200',
          ])}`,
        )
        .replyOnce(
          200,
          BaseResponseDto.success([
            mock_placeholder_internalStationResponseDtoV1_A,
            mock_placeholder_internalStationResponseDtoV1_B,
            mock_internalStationResponseDtoV1,
          ]),
        );

      // StationService.getAllStations
      mockAdapter_VulogApiService
        .onGet(
          `${
            config.httpServiceConfig.stationUrl
          }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'stations?page=2&pageSize=200',
          ])})}`,
        )
        .replyOnce(200, BaseResponseDto.success([]));

      apiResponse = await requestFor(reservationEntity.id, {
        qrCode: mock_qrCode,
      });
    });

    it('All ending rental conditions are satisfied (hasEnded = true)', async () => {
      expect(apiResponse.status).toEqual(HttpStatus.CREATED);

      const resultBody = apiResponse.body.result;

      expect(resultBody.hasEnded).toEqual(true);
    });

    it('Current Station (later chosen as Ending Station) should be the same as QR station', async () => {
      const mockResult =
        spyInstance_RentalService__getCurrentRentalDetail.mock.results[0];

      const resultValue: OnGoingRental = await mockResult.value;

      expect(resultValue.currentStation.id.value).toEqual(
        mock_internalStationResponseDtoV1.id,
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('Should throw an error when QR code is missing', () => {
    const mock_qrCode = null;

    let apiResponse: supertest.Response;

    beforeAll(async () => {
      apiResponse = await requestFor(aUUID(), {
        qrCode: mock_qrCode,
      });
    });

    it('QR code is missing in the request payload', async () => {
      expect(apiResponse.status).toEqual(HttpStatus.BAD_REQUEST);

      expect(apiResponse.body.message).toEqual([
        'qrCode should not be empty',
        'qrCode must be a string',
      ]);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('Should throw an error when on-going rental is not found', () => {
    const mock_qrCode =
      'https://qrcode-chargingservices.totalenergies.sg/qrcode?emi3=SG*TCB*E001241';

    const mock_reservationId = aUUID();

    let apiResponse: supertest.Response;

    beforeAll(async () => {
      apiResponse = await requestFor(mock_reservationId, {
        qrCode: mock_qrCode,
      });
    });

    it('On-going rental is not found', async () => {
      expect(apiResponse.status).toEqual(HttpStatus.NOT_FOUND);

      expect(apiResponse.body.message).toEqual(
        'No ongoing rental found for the user',
      );
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('Should indicate that QR code is broken in ending rental conditions', () => {
    const mock_qrCode = 'https://broken.com';

    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());

    const mock_internalStationResponseDtoV1: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder().build();

    const mock_placeholder_internalStationResponseDtoV1_A: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder().build();

    const mock_placeholder_internalStationResponseDtoV1_B: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder().build();

    const mock_internalParkingLotResponseDtoV1: InternalParkingLotResponseDtoV1 =
      new InternalParkingLotResponseDtoV1Builder()
        .buildStatus(ParkingLotStatus.AVAILABLE)
        .buildStation(mock_internalStationResponseDtoV1)
        .getResult();

    const mock_CarStaticInfoDto = createTestVulogCarStaticInfoDto(
      carEntity.vulogId,
      config.vulogConfig.fleetId.value,
      carEntity.model,
      carEntity.vin,
    );

    const mock_VulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withZones([
        mock_internalStationResponseDtoV1.zoneId,
        mock_placeholder_internalStationResponseDtoV1_A.zoneId,
        mock_placeholder_internalStationResponseDtoV1_B.zoneId,
      ])
      .withVehicles([mock_CarStaticInfoDto.id])
      .build();

    const vulogTripId: string = aMD5();

    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(carEntity.vulogId)
      .makePlateNumber(carEntity.plate)
      .makeStatus(ReservationStatus.Converted) // On-going rental
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(carEntity.plate)
      .makeBsgUserId(userId)
      .makeVulogTripId(vulogTripId)
      .makeStartStationId(mock_internalStationResponseDtoV1.id)
      .build();

    const mock_vulogCarRealtimeDto = new VulogCarRealTimeDtoBuilder()
      .withId(mock_CarStaticInfoDto.id)
      .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
      .withFleetId(config.vulogConfig.fleetId.value)
      .withServiceId(config.vulogConfig.serviceId.value)
      .withZones([
        new VulogZone(
          mock_internalStationResponseDtoV1.zoneId,
          1,
          'allowed',
          false,
        ),
        new VulogZone(
          mock_placeholder_internalStationResponseDtoV1_A.zoneId,
          1,
          'allowed',
          false,
        ),
        new VulogZone(
          mock_placeholder_internalStationResponseDtoV1_B.zoneId,
          1,
          'allowed',
          false,
        ),
      ])
      .withStartZones([
        new VulogZone(
          mock_internalStationResponseDtoV1.zoneId,
          1,
          'allowed',
          false,
        ),
      ])
      .withVin(mock_CarStaticInfoDto.vin)
      .withProfileId(vulogUserProfileDto.profiles[0].profileId)
      .withStartDate(reservationEntity.startedAt.toISOString())
      .withAutonomy(55)
      .build();

    const mock_vvgVehicleStatusDto: VvgVehicleStatusDto =
      new VvgVehicleStatusDtoBuilder()
        .withId(carEntity.vulogId)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withImmobilizerOn(true)
        .build();

    let apiResponse: supertest.Response;

    beforeAll(async () => {
      await carRepo.insert(carEntity);

      await reservationRepo.insert(reservationEntity);

      // VulogCarService.getSpecificCarInRealtime
      mockAdapter_VulogApiService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [mock_vulogCarRealtimeDto]);

      // StationService.getAllStations
      mockAdapter_VulogApiService
        .onGet(
          `${
            config.httpServiceConfig.stationUrl
          }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'stations?page=1&pageSize=200',
          ])}`,
        )
        .replyOnce(
          200,
          BaseResponseDto.success([
            mock_placeholder_internalStationResponseDtoV1_A,
            mock_placeholder_internalStationResponseDtoV1_B,
            mock_internalStationResponseDtoV1,
          ]),
        );

      // StationService.getAllStations
      mockAdapter_VulogApiService
        .onGet(
          `${
            config.httpServiceConfig.stationUrl
          }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'stations?page=2&pageSize=200',
          ])})}`,
        )
        .replyOnce(200, BaseResponseDto.success([]));

      // VulogFleetService.getFleetServices
      mockAdapter_VulogApiService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .replyOnce(200, [mock_VulogFleetServiceDto]);

      // StationExternalService.internalApi.getParkingLotByQR
      mockAdapter_StsApiService
        .onPost(
          `${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'parking-lots',
            'parking-lots',
            'qr',
          ])}`,
        )
        .reply(
          200,
          BaseResponseDto.success(mock_internalParkingLotResponseDtoV1),
        );

      // VulogTelemetryService.getCarStatusSessionCommand
      mockAdapter_VulogApiService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .reply(200, mock_vvgVehicleStatusDto);

      apiResponse = await requestFor(reservationEntity.id, {
        qrCode: mock_qrCode,
      });
    });

    it('QR code is broken', async () => {
      expect(apiResponse.status).toEqual(HttpStatus.CREATED);

      const resultBody: EndRentalResponseV1Dto = apiResponse.body.result;

      expect(resultBody.hasEnded).toEqual(false);

      expect(
        resultBody.qrConditions.find(
          (qrCond) => qrCond.id === EndRentalConditionId.VALID_QR_FORMAT,
        ).fulfilled,
      ).toEqual(false);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('Should indicate that QR station is not in the current zones of the car in ending rental conditions', () => {
    const mock_qrCode =
      'https://qrcode-chargingservices.totalenergies.sg/qrcode?emi3=SG*TCB*E001241';

    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());

    const mock_internalStationResponseDtoV1: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder().build();

    const mock_placeholder_internalStationResponseDtoV1_A: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder().build();

    const mock_placeholder_internalStationResponseDtoV1_B: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder().build();

    const mock_internalParkingLotResponseDtoV1: InternalParkingLotResponseDtoV1 =
      new InternalParkingLotResponseDtoV1Builder()
        .buildStatus(ParkingLotStatus.AVAILABLE)
        .buildStation(mock_internalStationResponseDtoV1)
        .getResult();

    const mock_CarStaticInfoDto = createTestVulogCarStaticInfoDto(
      carEntity.vulogId,
      config.vulogConfig.fleetId.value,
      carEntity.model,
      carEntity.vin,
    );

    const mock_VulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withZones([
        mock_internalStationResponseDtoV1.zoneId,
        mock_placeholder_internalStationResponseDtoV1_A.zoneId,
        mock_placeholder_internalStationResponseDtoV1_B.zoneId,
      ])
      .withVehicles([mock_CarStaticInfoDto.id])
      .build();

    const vulogTripId: string = aMD5();

    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(carEntity.vulogId)
      .makePlateNumber(carEntity.plate)
      .makeStatus(ReservationStatus.Converted) // On-going rental
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(carEntity.plate)
      .makeBsgUserId(userId)
      .makeVulogTripId(vulogTripId)
      .makeStartStationId(mock_internalStationResponseDtoV1.id)
      .build();

    const mock_vulogCarRealtimeDto = new VulogCarRealTimeDtoBuilder()
      .withId(mock_CarStaticInfoDto.id)
      .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
      .withFleetId(config.vulogConfig.fleetId.value)
      .withServiceId(config.vulogConfig.serviceId.value)
      .withZones([
        new VulogZone(
          mock_placeholder_internalStationResponseDtoV1_A.zoneId,
          1,
          'allowed',
          false,
        ),
        new VulogZone(
          mock_placeholder_internalStationResponseDtoV1_B.zoneId,
          1,
          'allowed',
          false,
        ),
      ])
      .withStartZones([
        new VulogZone(
          mock_internalStationResponseDtoV1.zoneId,
          1,
          'allowed',
          false,
        ),
      ])
      .withVin(mock_CarStaticInfoDto.vin)
      .withProfileId(vulogUserProfileDto.profiles[0].profileId)
      .withStartDate(reservationEntity.startedAt.toISOString())
      .withAutonomy(55)
      .build();

    const mock_vvgVehicleStatusDto: VvgVehicleStatusDto =
      new VvgVehicleStatusDtoBuilder()
        .withId(carEntity.vulogId)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withImmobilizerOn(true)
        .build();

    let apiResponse: supertest.Response;

    beforeAll(async () => {
      await carRepo.insert(carEntity);

      await reservationRepo.insert(reservationEntity);

      // VulogCarService.getSpecificCarInRealtime
      mockAdapter_VulogApiService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [mock_vulogCarRealtimeDto]);

      // StationService.getAllStations
      mockAdapter_VulogApiService
        .onGet(
          `${
            config.httpServiceConfig.stationUrl
          }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'stations?page=1&pageSize=200',
          ])}`,
        )
        .replyOnce(
          200,
          BaseResponseDto.success([
            mock_placeholder_internalStationResponseDtoV1_A,
            mock_placeholder_internalStationResponseDtoV1_B,
            mock_internalStationResponseDtoV1,
          ]),
        );

      // StationService.getAllStations
      mockAdapter_VulogApiService
        .onGet(
          `${
            config.httpServiceConfig.stationUrl
          }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'stations?page=2&pageSize=200',
          ])})}`,
        )
        .replyOnce(200, BaseResponseDto.success([]));

      // VulogFleetService.getFleetServices
      mockAdapter_VulogApiService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .replyOnce(200, [mock_VulogFleetServiceDto]);

      // StationExternalService.internalApi.getParkingLotByQR
      mockAdapter_StsApiService
        .onPost(
          `${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'parking-lots',
            'parking-lots',
            'qr',
          ])}`,
        )
        .reply(
          200,
          BaseResponseDto.success(mock_internalParkingLotResponseDtoV1),
        );

      // VulogTelemetryService.getCarStatusSessionCommand
      mockAdapter_VulogApiService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .reply(200, mock_vvgVehicleStatusDto);

      apiResponse = await requestFor(reservationEntity.id, {
        qrCode: mock_qrCode,
      });
    });

    it('QR code is broken', async () => {
      expect(apiResponse.status).toEqual(HttpStatus.CREATED);

      const resultBody: EndRentalResponseV1Dto = apiResponse.body.result;

      expect(resultBody.hasEnded).toEqual(false);

      expect(
        resultBody.qrConditions.find(
          (qrCond) =>
            qrCond.id === EndRentalConditionId.QR_IN_VALID_STATION_ZONE,
        ).fulfilled,
      ).toEqual(false);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('Should indicate that chargerless parking availability is not sufficient in ending rental conditions', () => {
    const mock_qrCode = 'BSG-111'; // Chargerless QR code format

    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());

    const mock_internalStationResponseDtoV1: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder()
        .withParkingSpacesAvailable(0)
        .build();

    const mock_placeholder_internalStationResponseDtoV1_A: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder().build();

    const mock_placeholder_internalStationResponseDtoV1_B: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder().build();

    const mock_internalParkingLotResponseDtoV1: InternalParkingLotResponseDtoV1 =
      new InternalParkingLotResponseDtoV1Builder()
        .buildStatus(ParkingLotStatus.AVAILABLE)
        .buildStation(mock_internalStationResponseDtoV1)
        .getResult();

    const mock_CarStaticInfoDto = createTestVulogCarStaticInfoDto(
      carEntity.vulogId,
      config.vulogConfig.fleetId.value,
      carEntity.model,
      carEntity.vin,
    );

    const mock_VulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withZones([
        mock_internalStationResponseDtoV1.zoneId,
        mock_placeholder_internalStationResponseDtoV1_A.zoneId,
        mock_placeholder_internalStationResponseDtoV1_B.zoneId,
      ])
      .withVehicles([mock_CarStaticInfoDto.id])
      .build();

    const vulogTripId: string = aMD5();

    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(carEntity.vulogId)
      .makePlateNumber(carEntity.plate)
      .makeStatus(ReservationStatus.Converted) // On-going rental
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(carEntity.plate)
      .makeBsgUserId(userId)
      .makeVulogTripId(vulogTripId)
      .makeStartStationId(mock_internalStationResponseDtoV1.id)
      .build();

    const mock_vulogCarRealtimeDto = new VulogCarRealTimeDtoBuilder()
      .withId(mock_CarStaticInfoDto.id)
      .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
      .withFleetId(config.vulogConfig.fleetId.value)
      .withServiceId(config.vulogConfig.serviceId.value)
      .withZones([
        new VulogZone(
          mock_internalStationResponseDtoV1.zoneId,
          1,
          'allowed',
          false,
        ),
        new VulogZone(
          mock_placeholder_internalStationResponseDtoV1_A.zoneId,
          1,
          'allowed',
          false,
        ),
        new VulogZone(
          mock_placeholder_internalStationResponseDtoV1_B.zoneId,
          1,
          'allowed',
          false,
        ),
      ])
      .withStartZones([
        new VulogZone(
          mock_internalStationResponseDtoV1.zoneId,
          1,
          'allowed',
          false,
        ),
      ])
      .withVin(mock_CarStaticInfoDto.vin)
      .withProfileId(vulogUserProfileDto.profiles[0].profileId)
      .withStartDate(reservationEntity.startedAt.toISOString())
      .withAutonomy(55)
      .build();

    const mock_vvgVehicleStatusDto: VvgVehicleStatusDto =
      new VvgVehicleStatusDtoBuilder()
        .withId(carEntity.vulogId)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withImmobilizerOn(true)
        .build();

    let apiResponse: supertest.Response;

    beforeAll(async () => {
      await carRepo.insert(carEntity);

      await reservationRepo.insert(reservationEntity);

      jest
        .spyOn(featureFlagService, 'isChargelessEnabled')
        .mockResolvedValue(true);

      // VulogCarService.getSpecificCarInRealtime
      mockAdapter_VulogApiService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [mock_vulogCarRealtimeDto]);

      // StationService.getAllStations
      mockAdapter_VulogApiService
        .onGet(
          `${
            config.httpServiceConfig.stationUrl
          }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'stations?page=1&pageSize=200',
          ])}`,
        )
        .replyOnce(
          200,
          BaseResponseDto.success([
            mock_placeholder_internalStationResponseDtoV1_A,
            mock_placeholder_internalStationResponseDtoV1_B,
            mock_internalStationResponseDtoV1,
          ]),
        );

      // StationService.getAllStations
      mockAdapter_VulogApiService
        .onGet(
          `${
            config.httpServiceConfig.stationUrl
          }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'stations?page=2&pageSize=200',
          ])})}`,
        )
        .replyOnce(200, BaseResponseDto.success([]));

      // VulogFleetService.getFleetServices
      mockAdapter_VulogApiService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .replyOnce(200, [mock_VulogFleetServiceDto]);

      // StationExternalService.internalApi.getParkingLotByQR
      mockAdapter_StsApiService
        .onPost(
          `${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'parking-lots',
            'parking-lots',
            'qr',
          ])}`,
        )
        .reply(
          200,
          BaseResponseDto.success(mock_internalParkingLotResponseDtoV1),
        );

      // VulogTelemetryService.getCarStatusSessionCommand
      mockAdapter_VulogApiService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(carEntity.vulogId),
          )}`,
        )
        .reply(200, mock_vvgVehicleStatusDto);

      apiResponse = await requestFor(reservationEntity.id, {
        qrCode: mock_qrCode,
      });
    });

    it('Parking availability is not sufficient', async () => {
      expect(apiResponse.status).toEqual(HttpStatus.CREATED);

      const resultBody: EndRentalResponseV1Dto = apiResponse.body.result;

      expect(resultBody.hasEnded).toEqual(false);

      expect(
        resultBody.qrConditions.find(
          (qrCond) =>
            qrCond.id === EndRentalConditionId.PARKING_HAS_AVAILABLE_SPACE,
        ).fulfilled,
      ).toEqual(false);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });

  describe('Should throw an error when QR station is found but disabled', () => {
    const mock_qrCode =
      'https://qrcode-chargingservices.totalenergies.sg/qrcode?emi3=SG*TCB*E001241';

    const mock_internalStationResponseDtoV1: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder()
        .withStatus(StationStatus.Closed) // Closed
        .build();

    const mock_internalParkingLotResponseDtoV1: InternalParkingLotResponseDtoV1 =
      new InternalParkingLotResponseDtoV1Builder()
        .buildStatus(ParkingLotStatus.AVAILABLE)
        .buildStation(mock_internalStationResponseDtoV1)
        .getResult();

    let apiResponse: supertest.Response;

    beforeAll(async () => {
      // StationService.getAllStations
      mockAdapter_VulogApiService
        .onGet(
          `${
            config.httpServiceConfig.stationUrl
          }${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'stations?page=2&pageSize=200',
          ])})}`,
        )
        .replyOnce(200, BaseResponseDto.success([]));

      // StationExternalService.internalApi.getParkingLotByQR
      mockAdapter_StsApiService
        .onPost(
          `${ApiRouteUtils.buildApiRouteSegment('api' as any, ApiVersion.V1, [
            'parking-lots',
            'parking-lots',
            'qr',
          ])}`,
        )
        .reply(
          200,
          BaseResponseDto.success(mock_internalParkingLotResponseDtoV1),
        );

      apiResponse = await requestFor(aUUID(), {
        qrCode: mock_qrCode,
      });
    });

    it('On-going rental is not found', async () => {
      expect(apiResponse.status).toEqual(HttpStatus.UNPROCESSABLE_ENTITY);

      expect(apiResponse.body.message).toEqual(STATION_DISABLED_MESSAGE);
    });

    afterAll(async () => {
      await cleanResources();
    });
  });
});
