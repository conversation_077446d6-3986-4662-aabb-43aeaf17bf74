import { AuthService } from '@bluesg-2/customer-authentication';
import { AUTHORIZATION_HEADER } from '@bluesg-2/customer-authentication/dist/guards/customer-jwt.guard';
import { HttpStatus } from '@nestjs/common';
import * as supertest from 'supertest';
import { ReservationService } from '../../../../src/logic/reservation.service';
import { ReservationBuilder } from '../../../helpers/builders/reservation.builder';
import { aUUID } from '../../../helpers/random-data.helper';
import { ApiTestContext, testApi } from '../../../helpers/test-api.util';

describe(
  'POST /api/v1/reservation/:id/end-rental',
  testApi((context: ApiTestContext) => {
    let reservationService: ReservationService;
    let auth: AuthService;
    let response: supertest.Response;

    const userId = aUUID();

    beforeAll(async () => {
      auth = context.app.get(AuthService);
      reservationService = context.app.get(ReservationService);
    });

    function request(id: string): supertest.Test {
      return supertest(context.app.getHttpServer())
        .post(`/api/v1/reservation/${id}/end-rental`)
        .set(
          AUTHORIZATION_HEADER,
          'Bearer ' + auth.accessToken(aUUID(), userId).token,
        )
        .send();
    }

    describe('when the reservation is not found', () => {
      const reservation = new ReservationBuilder().build();

      beforeEach(async () => {
        response = await request(reservation.getId);
      });

      it('should return 404 Not found', () => {
        expect(response.status).toBe(HttpStatus.NOT_FOUND);
      });
      it('should return an error message', () => {
        expect(response.body.message).toBe(`Reservation not found`);
      });
    });

    describe('when the reservation is not owned by the current user', () => {
      const reservation = new ReservationBuilder().build();

      beforeEach(async () => {
        await reservationService.saveOne(reservation);

        response = await request(reservation.getId);
      });

      it('should return 403 Forbidden', () => {
        expect(response.status).toBe(HttpStatus.FORBIDDEN);
      });
      it('should return an error message', () => {
        expect(response.body.message).toBe(
          'You do not have access to this resource',
        );
      });
    });

    describe('when the car is not ready', () => {
      describe.each([
        {
          label: 'the engine is still running',
          condition: 'engine-turned-off',
        },
        {
          label: 'one door is unlocked',
          condition: 'doors-locked',
        },
        {
          label: 'the car is unplugged',
          condition: 'car-plugged',
        },
      ])('when %s', ({ label, condition }) => {
        it.todo('should return 200 OK');
        it.todo('should return success: false');
        it.todo('should return a condition $condition invalid');
      });
    });

    describe.skip('when Vulog has an error', () => {});

    describe.skip('when all conditions are valid', () => {
      it.todo('should return 200 OK');
      it.each([
        {
          condition: 'engine-turned-off',
        },
        {
          condition: 'doors-locked',
        },
        {
          condition: 'car-plugged',
        },
      ])('should return the condition %s valid', ({ condition }) => {});
      it.todo('should have sent a event rental-ended');
    });
  }),
);
