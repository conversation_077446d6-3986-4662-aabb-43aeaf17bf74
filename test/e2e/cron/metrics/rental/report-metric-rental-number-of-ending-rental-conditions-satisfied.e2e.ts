import { INestApplication, Module } from '@nestjs/common';

import { MonitoringModule } from '@bluesg-2/monitoring';
import { faker } from '@faker-js/faker';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmHealthIndicator } from '@nestjs/terminus';
import { getRepositoryToken } from '@nestjs/typeorm';
import MockAdapter from 'axios-mock-adapter';
import * as _ from 'lodash';
import { ClsModule } from 'nestjs-cls';
import * as QueryString from 'qs';
import { CacheConfigModule } from 'src/cache-config.module';
import {
  carInServiceZoneCond,
  testingEndRentalConditions,
} from 'src/common/constants/ending-rental-conditions';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { getEnvFilePath } from 'src/common/utils/get-env-file-path.util';
import { config } from 'src/config';
import { ReportMetricRentalNumberOfEndingRentalConditionsSatisfiedCronModule } from 'src/cron/jobs/report-metric/rental/report-metric-rental-number-of-ending-rental-conditions-satisfied/report-metric-rental-number-of-ending-rental-conditions-satisfied.cron.module';
import { ReportMetricRentalNumberOfEndingRentalConditionsSatisfiedCronService } from 'src/cron/jobs/report-metric/rental/report-metric-rental-number-of-ending-rental-conditions-satisfied/report-metric-rental-number-of-ending-rental-conditions-satisfied.cron.service';
import { DatabaseModule } from 'src/database/database.module';
import { CacheService } from 'src/logic/cache/cache.service';
import { Car } from 'src/logic/car/domain/car';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { LogicModule } from 'src/logic/logic.module';
import { StationPaths } from 'src/logic/station/station-paths';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { StationDto } from 'src/model/dtos/station.dto';
import {
  VulogZone,
  VulogZoneType,
} from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { VvgVehicleStatusDto } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { PricingPolicy } from 'src/model/pricing-policy';
import { RentalPackage } from 'src/model/rental-package';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { AppBuilder } from 'test/helpers/app.builder';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { VulogZoneBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import { VvgVehicleStatusDtoBuilder } from 'test/helpers/builders/dtos/vulog/vvg/vvg-vehicle-status.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { createTestVulogUserEntity } from 'test/helpers/builders/entities/test-vulog-user-entity.builder';
import { PricingPolicyBuilder } from 'test/helpers/builders/pricing-policy.builder';
import { RentalPackageBuilder } from 'test/helpers/builders/rental-package.builder';
import { UssApiMocker } from 'test/helpers/external-services/uss.api-mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';
import { Repository } from 'typeorm';
import { EventBusModule } from '~shared/event-bus/event-bus.module';
import { FeatureFlagModule } from '~shared/feature-flag/feature-flag.module';
import { HttpLoggerModule } from '~shared/http-logger/http-logger.module';
import { LocalEventEmitterModule } from '~shared/local-event-emitter/local-event-emitter.module';
import {
  NewRelicMetricCarModel,
  NewRelicMetricRentalType,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { NewRelicMetricCreationRequest } from '~shared/newrelic/metric/types/metric-creation-request.newrelic.type';
import { NewRelicModule } from '~shared/newrelic/newrelic.module';
import { NoRateLimitHttpAdapterService } from '~shared/no-rate-limit-http/no-rate-limit-http-adapter.service';
import * as AppNestConfig from '../../../../../src/config/app.nestjs.config';

const email = faker.internet.email();

const intactEndingRentalConditions = _.cloneDeep(testingEndRentalConditions);

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [...Object.values(AppNestConfig)],
      envFilePath: getEnvFilePath(),
    }),
    ClsModule.forRoot({
      global: true,
    }),
    MonitoringModule.registerAsync({
      imports: [LogicModule],
      useFactory: async (db: TypeOrmHealthIndicator, cache: CacheService) => ({
        health: {
          dependencies: [
            () => db.pingCheck('database'),
            () => cache.checkIfHealthy(),
          ],
        },
      }),
      inject: [TypeOrmHealthIndicator, CacheService],
    }),
    DatabaseModule,
    CacheConfigModule,
    LogicModule,
    HttpLoggerModule,
    LocalEventEmitterModule,
    EventBusModule,
    FeatureFlagModule,
    ReportMetricRentalNumberOfEndingRentalConditionsSatisfiedCronModule, // Required
    NewRelicModule,
  ],
})
export class SpecificModuleToTest {}

describe(`[E2E] Report metric "rental.numberOfEndingRentalConditionsSatisfied"`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let carRepo: Repository<CarEntity>;
  let vulogUserRepo: Repository<VulogUserEntity>;
  let reservationRepo: Repository<ReservationEntity>;

  let cacheService: CacheService;
  let mockOutgoingService: MockAdapter;
  let mockNonRateLimitOutgoingService: MockAdapter;
  let mockUssApiService: MockAdapter;
  let cronService: ReportMetricRentalNumberOfEndingRentalConditionsSatisfiedCronService;

  const mockAuthResponse = createTestVulogAuthResponse();

  const bsgUserId = aUUID();

  const vulogUserDto = createTestVulogUserDto();
  const vulogUserEntity = createTestVulogUserEntity(bsgUserId);

  beforeAll(async () => {
    app = await appBuilder.buildAndStart(SpecificModuleToTest);

    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));
    vulogUserRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );
    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );
    cacheService = app.get<CacheService>(CacheService);
    const httpApdaterService = app.get<HttpAdapterService>(HttpAdapterService);
    mockOutgoingService = new MockAdapter(
      httpApdaterService.httpService.axiosRef,
      { onNoMatch: 'throwException' },
    );
    const noRateLimitedHttpAdapterService =
      app.get<NoRateLimitHttpAdapterService>('NO_RATE_LIMIT_SERVICE');
    mockNonRateLimitOutgoingService = new MockAdapter(
      noRateLimitedHttpAdapterService.httpService.axiosRef,
      { onNoMatch: 'throwException' },
    );

    mockUssApiService = new UssApiMocker(app).mock();

    cronService =
      app.get<ReportMetricRentalNumberOfEndingRentalConditionsSatisfiedCronService>(
        ReportMetricRentalNumberOfEndingRentalConditionsSatisfiedCronService,
      );

    vulogUserDto.email = email;

    mockOutgoingService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, vulogUserDto);

    const userServicesAndProfiles = createTestVulogUserServicesDto();
    userServicesAndProfiles.userId = vulogUserDto.id;

    /**
     * Doing Vulog authentication mock
     */
    mockOutgoingService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(vulogUserDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    mockOutgoingService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, mockAuthResponse);

    mockVulogCredential(app);
  });

  afterAll(async () => {
    await cleanUpResources();

    await app.close();

    mockOutgoingService.reset();
    mockUssApiService.reset();
  });

  async function cleanUpResources(): Promise<void> {
    await carRepo.delete({});
    await cacheService.clearAll();
    await vulogUserRepo.delete({});
    await reservationRepo.delete({});

    testingEndRentalConditions.splice(
      0,
      testingEndRentalConditions.length,
      ...intactEndingRentalConditions,
    );

    jest.clearAllMocks();
  }

  describe('🔥 Report metric successfully: all dimensions are covered', () => {
    let success: boolean;

    let sentCommon;
    let sentMetricSummary;
    let sentMetric__Bluecar_Subscription;
    let sentMetric__Bluecar_RentalPackage;
    let sentMetric__CorsaE_Subscription;
    let sentMetric__CorsaE_RentalPackage;

    let expectedCommon;
    let expectedMetricSummary;
    let expectedMetric__Bluecar_Subscription;
    let expectedMetric__Bluecar_RentalPackage;
    let expectedMetric__CorsaE_Subscription;
    let expectedMetric__CorsaE_RentalPackage;

    const cars: Car[] = [
      new CarBuilder().withModel(CarModelEnum.BlueCar).build(), // Passed, Pricing policy
      new CarBuilder().withModel(CarModelEnum.BlueCar).build(), // Passed, Rental package
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(), // Passed, Pricing policy
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(), // Passed, Rental package, dynamic,

      new CarBuilder().build(), // 5th Failed, car not in zone
      new CarBuilder().build(), // 6th Failed, doors and windows not closed
      new CarBuilder().build(), // 7th Failed, doors not locked
      new CarBuilder().build(), // 8th Failed, engine is on
      new CarBuilder().build(), // 9th Failed, car is unplugged
    ];

    const pricingPolicies: PricingPolicy[] = [
      new PricingPolicyBuilder().withRentalRate(0.26).build(), // For car model: Bluecar
      new PricingPolicyBuilder().withRentalRate(0.42).build(), // For car model: Opel Corsa-e
    ];

    const rentalPackages: RentalPackage[] = [
      new RentalPackageBuilder()
        .withDuration(3)
        .withMinAllowedBatteryPercentage(24) // Bluecar
        .withPrice(20)
        .build(),
      new RentalPackageBuilder()
        .withDuration(3)
        .withMinAllowedBatteryPercentage(30) // Corsa-e
        .withPrice(24)
        .withIsDynamic(true)
        .build(),
    ];

    const vulogUsers: VulogUserEntity[] = [
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
    ];

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];
    const stationDtos: StationDto[] = [
      new StationDtoBuilder().withZoneId(vulogZones[0].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[1].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[2].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[3].zoneId).build(),
    ];

    const reservationEntities: ReservationEntity[] = [
      new ReservationEntityBuilder()
        .makeCarId(cars[0].vulogId.value)
        .makeStartStationId(stationDtos[0].id)
        .makeBsgUserId(vulogUsers[0].bsgUserId)
        .makeCarModel(cars[0].model)
        .makeStatus(ReservationStatus.Converted)
        .makePricingPolicy(pricingPolicies[0])
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[1].vulogId.value)
        .makeStartStationId(stationDtos[1].id)
        .makeBsgUserId(vulogUsers[1].bsgUserId)
        .makeCarModel(cars[1].model)
        .makeStatus(ReservationStatus.Converted)
        .makeRentalPackageData(rentalPackages[0].toJson())
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[2].vulogId.value)
        .makeStartStationId(stationDtos[2].id)
        .makeBsgUserId(vulogUsers[2].bsgUserId)
        .makeCarModel(cars[2].model)
        .makeStatus(ReservationStatus.Converted)
        .makePricingPolicy(pricingPolicies[1])
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[3].vulogId.value)
        .makeStartStationId(stationDtos[3].id)
        .makeBsgUserId(vulogUsers[3].bsgUserId)
        .makeCarModel(cars[3].model)
        .makeStatus(ReservationStatus.Converted)
        .makeRentalPackageData(rentalPackages[1].toJson())
        .makeEndedAt(null)
        .build(),
      // Failed
      new ReservationEntityBuilder()
        .makeCarId(cars[4].vulogId.value)
        .makeStartStationId(_.sample(stationDtos).id)
        .makeBsgUserId(vulogUsers[4].bsgUserId)
        .makeCarModel(cars[4].model)
        .makeStatus(ReservationStatus.Converted)
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[5].vulogId.value)
        .makeStartStationId(_.sample(stationDtos).id)
        .makeBsgUserId(vulogUsers[5].bsgUserId)
        .makeCarModel(cars[5].model)
        .makeStatus(ReservationStatus.Converted)
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[6].vulogId.value)
        .makeStartStationId(_.sample(stationDtos).id)
        .makeBsgUserId(vulogUsers[6].bsgUserId)
        .makeCarModel(cars[6].model)
        .makeStatus(ReservationStatus.Converted)
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[7].vulogId.value)
        .makeStartStationId(_.sample(stationDtos).id)
        .makeBsgUserId(vulogUsers[7].bsgUserId)
        .makeCarModel(cars[7].model)
        .makeStatus(ReservationStatus.Converted)
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[8].vulogId.value)
        .makeStartStationId(_.sample(stationDtos).id)
        .makeBsgUserId(vulogUsers[8].bsgUserId)
        .makeCarModel(cars[8].model)
        .makeStatus(ReservationStatus.Converted)
        .makeEndedAt(null)
        .build(),
    ];

    const customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withId(config.vulogConfig.serviceId.value)
      .withVehicles(cars.map((c) => c.vulogId.value))
      .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
      .build();

    const vvgCarStatusDtos: VvgVehicleStatusDto[] = [
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[0].vulogId.value)
        .withVin(cars[0].vin)
        .withUserId(vulogUsers[0].vulogUserId)
        .withImmobilizerOn(true)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[1].vulogId.value)
        .withVin(cars[1].vin)
        .withUserId(vulogUsers[1].vulogUserId)
        .withImmobilizerOn(true)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[2].vulogId.value)
        .withVin(cars[2].vin)
        .withUserId(vulogUsers[2].vulogUserId)
        .withImmobilizerOn(true)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[3].vulogId.value)
        .withVin(cars[3].vin)
        .withUserId(vulogUsers[3].vulogUserId)
        .withImmobilizerOn(true)
        .build(),
      // Failed
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(false) // Failed
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[4].vulogId.value)
        .withVin(cars[4].vin)
        .withUserId(vulogUsers[4].vulogUserId)
        .withImmobilizerOn(true)
        .withIsCharging(false)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(false) // Failed
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[5].vulogId.value)
        .withVin(cars[5].vin)
        .withUserId(vulogUsers[5].vulogUserId)
        .withImmobilizerOn(true)
        .withIsCharging(false)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(true) // Failed
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[6].vulogId.value)
        .withVin(cars[6].vin)
        .withUserId(vulogUsers[6].vulogUserId)
        .withImmobilizerOn(true)
        .withIsCharging(false)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(false)
        .withCablePlugged(false) // Failed
        .withZone(_.sample(vulogZones))
        .withId(cars[7].vulogId.value)
        .withVin(cars[7].vin)
        .withUserId(vulogUsers[7].vulogUserId)
        .withImmobilizerOn(true)
        .withIsCharging(false)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(null) // Failed
        .withId(cars[8].vulogId.value)
        .withVin(cars[8].vin)
        .withUserId(vulogUsers[8].vulogUserId)
        .withImmobilizerOn(true)
        .withIsCharging(false)
        .build(),
    ];

    beforeAll(async () => {
      // Save user into DB
      await vulogUserRepo.save(vulogUserEntity);

      // Save cars into DB
      await Promise.all(cars.map((car) => carRepo.save(CarEntity.from(car))));

      // Save reservations into DB
      await Promise.all(
        reservationEntities.map((rsrv) => reservationRepo.save(rsrv)),
      );

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      // Listing out car status from VVG
      mockOutgoingService
        .onGet(`${VulogPaths.vvgVehicles(config.vulogConfig.fleetId)}`)
        .reply(200, vvgCarStatusDtos);

      // Send metrics to NewRelic
      mockNonRateLimitOutgoingService
        .onPost(`https://metric-api.newrelic.com/metric/v1`)
        .replyOnce(201);

      success =
        await cronService.reportMetricNumberOfEndingRentalConditionsSatisfied();

      const { common, metrics }: NewRelicMetricCreationRequest = JSON.parse(
        mockNonRateLimitOutgoingService.history['post'][0].data,
      )[0];

      sentCommon = common;

      sentMetricSummary = metrics.find((m) => m.attributes['summary']).value;
      sentMetric__Bluecar_RentalPackage = metrics.find(
        (m) =>
          m.attributes['carModel'] === NewRelicMetricCarModel.Bluecar &&
          m.attributes['rentalType'] === NewRelicMetricRentalType.RentalPackage,
      ).value;
      sentMetric__Bluecar_Subscription = metrics.find(
        (m) =>
          m.attributes['carModel'] === NewRelicMetricCarModel.Bluecar &&
          m.attributes['rentalType'] === NewRelicMetricRentalType.Subscription,
      ).value;
      sentMetric__CorsaE_RentalPackage = metrics.find(
        (m) =>
          m.attributes['carModel'] === NewRelicMetricCarModel.CorsaE &&
          m.attributes['rentalType'] === NewRelicMetricRentalType.RentalPackage,
      ).value;
      sentMetric__CorsaE_Subscription = metrics.find(
        (m) =>
          m.attributes['carModel'] === NewRelicMetricCarModel.CorsaE &&
          m.attributes['rentalType'] === NewRelicMetricRentalType.Subscription,
      ).value;

      expectedCommon = { attributes: { env: process.env.NODE_ENV } };

      expectedMetricSummary = 4;
      expectedMetric__Bluecar_RentalPackage = 1;
      expectedMetric__Bluecar_Subscription = 1;
      expectedMetric__CorsaE_RentalPackage = 1;
      expectedMetric__CorsaE_Subscription = 1;
    });

    it('Run successfully', () => {
      expect(success).toEqual(true);
    });

    it('Common attributes correct', () => {
      expect(sentCommon).toEqual(expectedCommon);
    });

    it('Summary metric is correct', () => {
      expect(sentMetricSummary).toEqual(expectedMetricSummary);
    });

    it('Combination of Dims [bluecar, rental_package] is correct', () => {
      expect(sentMetric__Bluecar_RentalPackage).toEqual(
        expectedMetric__Bluecar_RentalPackage,
      );
    });

    it('Combination of Dims [bluecar, subscription] is correct', () => {
      expect(sentMetric__Bluecar_Subscription).toEqual(
        expectedMetric__Bluecar_Subscription,
      );
    });

    it('Combination of Dims [corsa_e, rental_package] is correct', () => {
      expect(sentMetric__CorsaE_RentalPackage).toEqual(
        expectedMetric__CorsaE_RentalPackage,
      );
    });

    it('Combination of Dims [corsa_e, subscription] is correct', () => {
      expect(sentMetric__CorsaE_Subscription).toEqual(
        expectedMetric__CorsaE_Subscription,
      );
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('💧 Report metric failed: number of ending rental conditions is changed but code implementation is stale', () => {
    let success: boolean;

    const cars: Car[] = [
      new CarBuilder().withModel(CarModelEnum.BlueCar).build(), // Passed, Pricing policy
      new CarBuilder().withModel(CarModelEnum.BlueCar).build(), // Passed, Rental package
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(), // Passed, Pricing policy
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(), // Passed, Rental package, dynamic,

      new CarBuilder().build(), // 5th Failed, car not in zone
      new CarBuilder().build(), // 6th Failed, doors and windows not closed
      new CarBuilder().build(), // 7th Failed, doors not locked
      new CarBuilder().build(), // 8th Failed, engine is on
      new CarBuilder().build(), // 9th Failed, car is unplugged
    ];

    const pricingPolicies: PricingPolicy[] = [
      new PricingPolicyBuilder().withRentalRate(0.26).build(), // For car model: Bluecar
      new PricingPolicyBuilder().withRentalRate(0.42).build(), // For car model: Opel Corsa-e
    ];

    const rentalPackages: RentalPackage[] = [
      new RentalPackageBuilder()
        .withDuration(3)
        .withMinAllowedBatteryPercentage(24) // Bluecar
        .withPrice(20)
        .build(),
      new RentalPackageBuilder()
        .withDuration(3)
        .withMinAllowedBatteryPercentage(30) // Corsa-e
        .withPrice(24)
        .withIsDynamic(true)
        .build(),
    ];

    const vulogUsers: VulogUserEntity[] = [
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
    ];

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];
    const stationDtos: StationDto[] = [
      new StationDtoBuilder().withZoneId(vulogZones[0].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[1].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[2].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[3].zoneId).build(),
    ];

    const reservationEntities: ReservationEntity[] = [
      new ReservationEntityBuilder()
        .makeCarId(cars[0].vulogId.value)
        .makeStartStationId(stationDtos[0].id)
        .makeBsgUserId(vulogUsers[0].bsgUserId)
        .makeCarModel(cars[0].model)
        .makeStatus(ReservationStatus.Converted)
        .makePricingPolicy(pricingPolicies[0])
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[1].vulogId.value)
        .makeStartStationId(stationDtos[1].id)
        .makeBsgUserId(vulogUsers[1].bsgUserId)
        .makeCarModel(cars[1].model)
        .makeStatus(ReservationStatus.Converted)
        .makeRentalPackageData(rentalPackages[0].toJson())
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[2].vulogId.value)
        .makeStartStationId(stationDtos[2].id)
        .makeBsgUserId(vulogUsers[2].bsgUserId)
        .makeCarModel(cars[2].model)
        .makeStatus(ReservationStatus.Converted)
        .makePricingPolicy(pricingPolicies[1])
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[3].vulogId.value)
        .makeStartStationId(stationDtos[3].id)
        .makeBsgUserId(vulogUsers[3].bsgUserId)
        .makeCarModel(cars[3].model)
        .makeStatus(ReservationStatus.Converted)
        .makeRentalPackageData(rentalPackages[1].toJson())
        .makeEndedAt(null)
        .build(),
      // Failed
      new ReservationEntityBuilder()
        .makeCarId(cars[4].vulogId.value)
        .makeStartStationId(_.sample(stationDtos).id)
        .makeBsgUserId(vulogUsers[4].bsgUserId)
        .makeCarModel(cars[4].model)
        .makeStatus(ReservationStatus.Converted)
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[5].vulogId.value)
        .makeStartStationId(_.sample(stationDtos).id)
        .makeBsgUserId(vulogUsers[5].bsgUserId)
        .makeCarModel(cars[5].model)
        .makeStatus(ReservationStatus.Converted)
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[6].vulogId.value)
        .makeStartStationId(_.sample(stationDtos).id)
        .makeBsgUserId(vulogUsers[6].bsgUserId)
        .makeCarModel(cars[6].model)
        .makeStatus(ReservationStatus.Converted)
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[7].vulogId.value)
        .makeStartStationId(_.sample(stationDtos).id)
        .makeBsgUserId(vulogUsers[7].bsgUserId)
        .makeCarModel(cars[7].model)
        .makeStatus(ReservationStatus.Converted)
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[8].vulogId.value)
        .makeStartStationId(_.sample(stationDtos).id)
        .makeBsgUserId(vulogUsers[8].bsgUserId)
        .makeCarModel(cars[8].model)
        .makeStatus(ReservationStatus.Converted)
        .makeEndedAt(null)
        .build(),
    ];

    const vvgCarStatusDtos: VvgVehicleStatusDto[] = [
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[0].vulogId.value)
        .withVin(cars[0].vin)
        .withUserId(vulogUsers[0].vulogUserId)
        .withImmobilizerOn(true)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[1].vulogId.value)
        .withVin(cars[1].vin)
        .withUserId(vulogUsers[1].vulogUserId)
        .withImmobilizerOn(true)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[2].vulogId.value)
        .withVin(cars[2].vin)
        .withUserId(vulogUsers[2].vulogUserId)
        .withImmobilizerOn(true)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[3].vulogId.value)
        .withVin(cars[3].vin)
        .withUserId(vulogUsers[3].vulogUserId)
        .withImmobilizerOn(true)
        .build(),
      // Failed
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(false) // Failed
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[4].vulogId.value)
        .withVin(cars[4].vin)
        .withUserId(vulogUsers[4].vulogUserId)
        .withImmobilizerOn(true)
        .withIsCharging(false)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(false) // Failed
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[5].vulogId.value)
        .withVin(cars[5].vin)
        .withUserId(vulogUsers[5].vulogUserId)
        .withImmobilizerOn(true)
        .withIsCharging(false)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(true) // Failed
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[6].vulogId.value)
        .withVin(cars[6].vin)
        .withUserId(vulogUsers[6].vulogUserId)
        .withImmobilizerOn(true)
        .withIsCharging(false)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(false)
        .withCablePlugged(false) // Failed
        .withZone(_.sample(vulogZones))
        .withId(cars[7].vulogId.value)
        .withVin(cars[7].vin)
        .withUserId(vulogUsers[7].vulogUserId)
        .withImmobilizerOn(true)
        .withIsCharging(false)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(null) // Failed
        .withId(cars[8].vulogId.value)
        .withVin(cars[8].vin)
        .withUserId(vulogUsers[8].vulogUserId)
        .withImmobilizerOn(true)
        .withIsCharging(false)
        .build(),
    ];

    const customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withId(config.vulogConfig.serviceId.value)
      .withVehicles(cars.map((c) => c.vulogId.value))
      .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
      .build();

    beforeAll(async () => {
      testingEndRentalConditions.push(carInServiceZoneCond);

      // Save user into DB
      await vulogUserRepo.save(vulogUserEntity);

      // Save cars into DB
      await Promise.all(cars.map((car) => carRepo.save(CarEntity.from(car))));

      // Save reservations into DB
      await Promise.all(
        reservationEntities.map((rsrv) => reservationRepo.save(rsrv)),
      );

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      // Listing out car status from VVG
      mockOutgoingService
        .onGet(`${VulogPaths.vvgVehicles(config.vulogConfig.fleetId)}`)
        .reply(200, vvgCarStatusDtos);

      success =
        await cronService.reportMetricNumberOfEndingRentalConditionsSatisfied();
    });

    it('Run successfully', () => {
      expect(success).toEqual(false);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('💧 Report metric failed: failed to send metrics to NewRelic', () => {
    let success: boolean;

    const cars: Car[] = [
      new CarBuilder().withModel(CarModelEnum.BlueCar).build(), // Passed, Pricing policy
      new CarBuilder().withModel(CarModelEnum.BlueCar).build(), // Passed, Rental package
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(), // Passed, Pricing policy
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(), // Passed, Rental package, dynamic,

      new CarBuilder().build(), // 5th Failed, car not in zone
      new CarBuilder().build(), // 6th Failed, doors and windows not closed
      new CarBuilder().build(), // 7th Failed, doors not locked
      new CarBuilder().build(), // 8th Failed, engine is on
      new CarBuilder().build(), // 9th Failed, car is unplugged
    ];

    const pricingPolicies: PricingPolicy[] = [
      new PricingPolicyBuilder().withRentalRate(0.26).build(), // For car model: Bluecar
      new PricingPolicyBuilder().withRentalRate(0.42).build(), // For car model: Opel Corsa-e
    ];

    const rentalPackages: RentalPackage[] = [
      new RentalPackageBuilder()
        .withDuration(3)
        .withMinAllowedBatteryPercentage(24) // Bluecar
        .withPrice(20)
        .build(),
      new RentalPackageBuilder()
        .withDuration(3)
        .withMinAllowedBatteryPercentage(30) // Corsa-e
        .withPrice(24)
        .withIsDynamic(true)
        .build(),
    ];

    const vulogUsers: VulogUserEntity[] = [
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
      createTestVulogUserEntity(),
    ];

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];
    const stationDtos: StationDto[] = [
      new StationDtoBuilder().withZoneId(vulogZones[0].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[1].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[2].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[3].zoneId).build(),
    ];

    const reservationEntities: ReservationEntity[] = [
      new ReservationEntityBuilder()
        .makeCarId(cars[0].vulogId.value)
        .makeStartStationId(stationDtos[0].id)
        .makeBsgUserId(vulogUsers[0].bsgUserId)
        .makeCarModel(cars[0].model)
        .makeStatus(ReservationStatus.Converted)
        .makePricingPolicy(pricingPolicies[0])
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[1].vulogId.value)
        .makeStartStationId(stationDtos[1].id)
        .makeBsgUserId(vulogUsers[1].bsgUserId)
        .makeCarModel(cars[1].model)
        .makeStatus(ReservationStatus.Converted)
        .makeRentalPackageData(rentalPackages[0].toJson())
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[2].vulogId.value)
        .makeStartStationId(stationDtos[2].id)
        .makeBsgUserId(vulogUsers[2].bsgUserId)
        .makeCarModel(cars[2].model)
        .makeStatus(ReservationStatus.Converted)
        .makePricingPolicy(pricingPolicies[1])
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[3].vulogId.value)
        .makeStartStationId(stationDtos[3].id)
        .makeBsgUserId(vulogUsers[3].bsgUserId)
        .makeCarModel(cars[3].model)
        .makeStatus(ReservationStatus.Converted)
        .makeRentalPackageData(rentalPackages[1].toJson())
        .makeEndedAt(null)
        .build(),
      // Failed
      new ReservationEntityBuilder()
        .makeCarId(cars[4].vulogId.value)
        .makeStartStationId(_.sample(stationDtos).id)
        .makeBsgUserId(vulogUsers[4].bsgUserId)
        .makeCarModel(cars[4].model)
        .makeStatus(ReservationStatus.Converted)
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[5].vulogId.value)
        .makeStartStationId(_.sample(stationDtos).id)
        .makeBsgUserId(vulogUsers[5].bsgUserId)
        .makeCarModel(cars[5].model)
        .makeStatus(ReservationStatus.Converted)
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[6].vulogId.value)
        .makeStartStationId(_.sample(stationDtos).id)
        .makeBsgUserId(vulogUsers[6].bsgUserId)
        .makeCarModel(cars[6].model)
        .makeStatus(ReservationStatus.Converted)
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[7].vulogId.value)
        .makeStartStationId(_.sample(stationDtos).id)
        .makeBsgUserId(vulogUsers[7].bsgUserId)
        .makeCarModel(cars[7].model)
        .makeStatus(ReservationStatus.Converted)
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[8].vulogId.value)
        .makeStartStationId(_.sample(stationDtos).id)
        .makeBsgUserId(vulogUsers[8].bsgUserId)
        .makeCarModel(cars[8].model)
        .makeStatus(ReservationStatus.Converted)
        .makeEndedAt(null)
        .build(),
    ];

    const vvgCarStatusDtos: VvgVehicleStatusDto[] = [
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[0].vulogId.value)
        .withVin(cars[0].vin)
        .withUserId(vulogUsers[0].vulogUserId)
        .withImmobilizerOn(true)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[1].vulogId.value)
        .withVin(cars[1].vin)
        .withUserId(vulogUsers[1].vulogUserId)
        .withImmobilizerOn(true)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[2].vulogId.value)
        .withVin(cars[2].vin)
        .withUserId(vulogUsers[2].vulogUserId)
        .withImmobilizerOn(true)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[3].vulogId.value)
        .withVin(cars[3].vin)
        .withUserId(vulogUsers[3].vulogUserId)
        .withImmobilizerOn(true)
        .build(),
      // Failed
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(false) // Failed
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[4].vulogId.value)
        .withVin(cars[4].vin)
        .withUserId(vulogUsers[4].vulogUserId)
        .withImmobilizerOn(true)
        .withIsCharging(false)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(false) // Failed
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[5].vulogId.value)
        .withVin(cars[5].vin)
        .withUserId(vulogUsers[5].vulogUserId)
        .withImmobilizerOn(true)
        .withIsCharging(false)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(true) // Failed
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(_.sample(vulogZones))
        .withId(cars[6].vulogId.value)
        .withVin(cars[6].vin)
        .withUserId(vulogUsers[6].vulogUserId)
        .withImmobilizerOn(true)
        .withIsCharging(false)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(false)
        .withCablePlugged(false) // Failed
        .withZone(_.sample(vulogZones))
        .withId(cars[7].vulogId.value)
        .withVin(cars[7].vin)
        .withUserId(vulogUsers[7].vulogUserId)
        .withImmobilizerOn(true)
        .withIsCharging(false)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withZone(null) // Failed
        .withId(cars[8].vulogId.value)
        .withVin(cars[8].vin)
        .withUserId(vulogUsers[8].vulogUserId)
        .withImmobilizerOn(true)
        .withIsCharging(false)
        .build(),
    ];

    const customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withId(config.vulogConfig.serviceId.value)
      .withVehicles(cars.map((c) => c.vulogId.value))
      .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
      .build();

    beforeAll(async () => {
      // Save user into DB
      await vulogUserRepo.save(vulogUserEntity);

      // Save cars into DB
      await Promise.all(cars.map((car) => carRepo.save(CarEntity.from(car))));

      // Save reservations into DB
      await Promise.all(
        reservationEntities.map((rsrv) => reservationRepo.save(rsrv)),
      );

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      // Listing out car status from VVG
      mockOutgoingService
        .onGet(`${VulogPaths.vvgVehicles(config.vulogConfig.fleetId)}`)
        .reply(200, vvgCarStatusDtos);

      // Send metrics to NewRelic
      mockNonRateLimitOutgoingService
        .onPost(`https://metric-api.newrelic.com/metric/v1`)
        .replyOnce(400);

      success =
        await cronService.reportMetricNumberOfEndingRentalConditionsSatisfied();
    });

    it('Run successfully', () => {
      expect(success).toEqual(false);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });
});
