import { INestApplication, Module } from '@nestjs/common';

import { MonitoringModule } from '@bluesg-2/monitoring';
import { faker } from '@faker-js/faker';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmHealthIndicator } from '@nestjs/terminus';
import { getRepositoryToken } from '@nestjs/typeorm';
import MockAdapter from 'axios-mock-adapter';
import { ClsModule } from 'nestjs-cls';
import { CacheConfigModule } from 'src/cache-config.module';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { getEnvFilePath } from 'src/common/utils/get-env-file-path.util';
import { config } from 'src/config';
import { ReportMetricRentalNumberOfBlueSGStartedButVulogBookingCronModule } from 'src/cron/jobs/report-metric/rental/report-metric-rental-number-of-bluesg-started-but-vulog-booking/report-metric-rental-number-of-bluesg-started-but-vulog-booking.cron.module';
import { ReportMetricRentalNumberOfBlueSGStartedButVulogBookingCronService } from 'src/cron/jobs/report-metric/rental/report-metric-rental-number-of-bluesg-started-but-vulog-booking/report-metric-rental-number-of-bluesg-started-but-vulog-booking.cron.service';
import { DatabaseModule } from 'src/database/database.module';
import { CacheService } from 'src/logic/cache/cache.service';
import { Car } from 'src/logic/car/domain/car';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { LogicModule } from 'src/logic/logic.module';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { StationDto } from 'src/model/dtos/station.dto';
import {
  VulogCarRealTimeDto,
  VulogRealTimeBookingStatus,
  VulogRealTimeVehicleStatus,
} from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogFleetServiceDto } from 'src/model/dtos/vulog-fleet-service/vulog-fleet-service.dto';
import {
  VulogZone,
  VulogZoneType,
} from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { PricingPolicy } from 'src/model/pricing-policy';
import { RentalPackage } from 'src/model/rental-package';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { AppBuilder } from 'test/helpers/app.builder';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import {
  VulogCarRealTimeDtoBuilder,
  VulogZoneBuilder,
} from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { createTestVulogUserEntity } from 'test/helpers/builders/entities/test-vulog-user-entity.builder';
import { PricingPolicyBuilder } from 'test/helpers/builders/pricing-policy.builder';
import { RentalPackageBuilder } from 'test/helpers/builders/rental-package.builder';
import { UssApiMocker } from 'test/helpers/external-services/uss.api-mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';
import { Repository } from 'typeorm';
import { EventBusModule } from '~shared/event-bus/event-bus.module';
import { FeatureFlagModule } from '~shared/feature-flag/feature-flag.module';
import { HttpLoggerModule } from '~shared/http-logger/http-logger.module';
import { LocalEventEmitterModule } from '~shared/local-event-emitter/local-event-emitter.module';
import {
  NewRelicMetricCarModel,
  NewRelicMetricRentalType,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { NewRelicMetricCreationRequest } from '~shared/newrelic/metric/types/metric-creation-request.newrelic.type';
import { NewRelicModule } from '~shared/newrelic/newrelic.module';
import { NoRateLimitHttpAdapterService } from '~shared/no-rate-limit-http/no-rate-limit-http-adapter.service';
import * as AppNestConfig from '../../../../../src/config/app.nestjs.config';

const email = faker.internet.email();

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [...Object.values(AppNestConfig)],
      envFilePath: getEnvFilePath(),
    }),
    ClsModule.forRoot({
      global: true,
    }),
    MonitoringModule.registerAsync({
      imports: [LogicModule],
      useFactory: async (db: TypeOrmHealthIndicator, cache: CacheService) => ({
        health: {
          dependencies: [
            () => db.pingCheck('database'),
            () => cache.checkIfHealthy(),
          ],
        },
      }),
      inject: [TypeOrmHealthIndicator, CacheService],
    }),
    DatabaseModule,
    CacheConfigModule,
    LogicModule,
    HttpLoggerModule,
    LocalEventEmitterModule,
    EventBusModule,
    FeatureFlagModule,
    ReportMetricRentalNumberOfBlueSGStartedButVulogBookingCronModule, // Required
    NewRelicModule,
  ],
})
export class SpecificModuleToTest {}

describe(`[E2E] Report metric "rental.numberOfBlueSGStartedButVulogBooking"`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let carRepo: Repository<CarEntity>;
  let vulogUserRepo: Repository<VulogUserEntity>;
  let reservationRepo: Repository<ReservationEntity>;

  let cacheService: CacheService;
  let mockOutgoingService: MockAdapter;
  let mockNonRateLimitOutgoingService: MockAdapter;
  let mockUssApiService: MockAdapter;
  let cronService: ReportMetricRentalNumberOfBlueSGStartedButVulogBookingCronService;

  const mockAuthResponse = createTestVulogAuthResponse();

  const bsgUserId = aUUID();

  const vulogUserDto = createTestVulogUserDto();
  const vulogUserEntity = createTestVulogUserEntity(bsgUserId);

  beforeAll(async () => {
    app = await appBuilder.buildAndStart(SpecificModuleToTest);

    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));
    vulogUserRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );
    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );
    cacheService = app.get<CacheService>(CacheService);
    const httpApdaterService = app.get<HttpAdapterService>(HttpAdapterService);
    mockOutgoingService = new MockAdapter(
      httpApdaterService.httpService.axiosRef,
      { onNoMatch: 'throwException' },
    );
    const noRateLimitedHttpAdapterService =
      app.get<NoRateLimitHttpAdapterService>('NO_RATE_LIMIT_SERVICE');
    mockNonRateLimitOutgoingService = new MockAdapter(
      noRateLimitedHttpAdapterService.httpService.axiosRef,
      { onNoMatch: 'throwException' },
    );

    mockUssApiService = new UssApiMocker(app).mock();

    cronService =
      app.get<ReportMetricRentalNumberOfBlueSGStartedButVulogBookingCronService>(
        ReportMetricRentalNumberOfBlueSGStartedButVulogBookingCronService,
      );

    vulogUserDto.email = email;

    mockOutgoingService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, vulogUserDto);

    const userServicesAndProfiles = createTestVulogUserServicesDto();
    userServicesAndProfiles.userId = vulogUserDto.id;

    /**
     * Doing Vulog authentication mock
     */
    mockOutgoingService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(vulogUserDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    mockOutgoingService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, mockAuthResponse);

    mockVulogCredential(app);
  });

  afterAll(async () => {
    await cleanUpResources();

    await app.close();

    mockOutgoingService.reset();
    mockUssApiService.reset();
  });

  async function cleanUpResources(): Promise<void> {
    await carRepo.delete({});
    await cacheService.clearAll();
    await vulogUserRepo.delete({});
    await reservationRepo.delete({});

    jest.clearAllMocks();
  }

  describe('🔥 Report metric successfully: all dimensions are covered', () => {
    let success: boolean;

    let sentCommon;
    let sentMetricSummary;
    let sentMetric__Bluecar_Subscription;
    let sentMetric__Bluecar_RentalPackage;
    let sentMetric__CorsaE_Subscription;
    let sentMetric__CorsaE_RentalPackage;

    let expectedCommon;
    let expectedMetricSummary;
    let expectedMetric__Bluecar_Subscription;
    let expectedMetric__Bluecar_RentalPackage;
    let expectedMetric__CorsaE_Subscription;
    let expectedMetric__CorsaE_RentalPackage;

    const cars: Car[] = [
      new CarBuilder().withModel(CarModelEnum.BlueCar).build(), // Pricing policy
      new CarBuilder().withModel(CarModelEnum.BlueCar).build(), // Rental package
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(), // Pricing policy
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(), // Rental package, dynamic
    ];

    const pricingPolicies: PricingPolicy[] = [
      new PricingPolicyBuilder().withRentalRate(0.26).build(), // For car model: Bluecar
      new PricingPolicyBuilder().withRentalRate(0.42).build(), // For car model: Opel Corsa-e
    ];

    const rentalPackages: RentalPackage[] = [
      new RentalPackageBuilder()
        .withDuration(3)
        .withMinAllowedBatteryPercentage(24) // Bluecar
        .withPrice(20)
        .build(),
      new RentalPackageBuilder()
        .withDuration(3)
        .withMinAllowedBatteryPercentage(30) // Corsa-e
        .withPrice(24)
        .withIsDynamic(true)
        .build(),
    ];

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];
    const stationDtos: StationDto[] = [
      new StationDtoBuilder().withZoneId(vulogZones[0].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[1].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[2].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[3].zoneId).build(),
    ];

    const reservationEntities: ReservationEntity[] = [
      new ReservationEntityBuilder()
        .makeCarId(cars[0].id.value)
        .makeStartStationId(stationDtos[0].id)
        .makeBsgUserId(bsgUserId)
        .makeCarModel(CarModelEnum.BlueCar)
        .makeStatus(ReservationStatus.Converted)
        .makePricingPolicy(pricingPolicies[0])
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[1].id.value)
        .makeStartStationId(stationDtos[1].id)
        .makeBsgUserId(bsgUserId)
        .makeCarModel(CarModelEnum.BlueCar)
        .makeStatus(ReservationStatus.Converted)
        .makeRentalPackageData(rentalPackages[0].toJson())
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[2].id.value)
        .makeStartStationId(stationDtos[2].id)
        .makeBsgUserId(bsgUserId)
        .makeCarModel(CarModelEnum.OpelCorsaE)
        .makeStatus(ReservationStatus.Converted)
        .makePricingPolicy(pricingPolicies[1])
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[3].id.value)
        .makeStartStationId(stationDtos[3].id)
        .makeBsgUserId(bsgUserId)
        .makeCarModel(CarModelEnum.OpelCorsaE)
        .makeStatus(ReservationStatus.Converted)
        .makeRentalPackageData(rentalPackages[1].toJson())
        .makeEndedAt(null)
        .build(),
    ];

    const customerFleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withId(config.vulogConfig.serviceId.value)
        .withVehicles(cars.map((car) => car.vulogId.value))
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

    const vulogRealtimeCarDtos: VulogCarRealTimeDto[] = [
      new VulogCarRealTimeDtoBuilder()
        .withAutonomy(100)
        .withIsCharging(true)
        .withBattery(100)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withServiceId(customerFleetServiceDto.id)
        .withServiceType(customerFleetServiceDto.type)
        .withZones([vulogZones[0]])
        .withId(cars[0].vulogId.value)
        .withVin(cars[0].vin)
        .withPlate(cars[0].plate.value)
        .withVulogTripId(reservationEntities[0].vulogTripId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
        .withBookingStatus(VulogRealTimeBookingStatus.Booked)
        .withUserId(vulogUserEntity.id)
        .withStartDate(null)
        .build(),
      new VulogCarRealTimeDtoBuilder()
        .withAutonomy(100)
        .withIsCharging(true)
        .withBattery(100)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withServiceId(customerFleetServiceDto.id)
        .withServiceType(customerFleetServiceDto.type)
        .withZones([vulogZones[1]])
        .withId(cars[1].vulogId.value)
        .withVin(cars[1].vin)
        .withPlate(cars[1].plate.value)
        .withVulogTripId(reservationEntities[1].vulogTripId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
        .withBookingStatus(VulogRealTimeBookingStatus.Booked)
        .withUserId(vulogUserEntity.id)
        .withStartDate(null)
        .build(),
      new VulogCarRealTimeDtoBuilder()
        .withAutonomy(100)
        .withIsCharging(true)
        .withBattery(100)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withServiceId(customerFleetServiceDto.id)
        .withServiceType(customerFleetServiceDto.type)
        .withZones([vulogZones[2]])
        .withId(cars[2].vulogId.value)
        .withVin(cars[2].vin)
        .withPlate(cars[2].plate.value)
        .withVulogTripId(reservationEntities[2].vulogTripId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
        .withBookingStatus(VulogRealTimeBookingStatus.Booked)
        .withUserId(vulogUserEntity.id)
        .withStartDate(null)
        .build(),
      new VulogCarRealTimeDtoBuilder()
        .withAutonomy(100)
        .withIsCharging(true)
        .withBattery(100)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withServiceId(customerFleetServiceDto.id)
        .withServiceType(customerFleetServiceDto.type)
        .withZones([vulogZones[3]])
        .withId(cars[3].vulogId.value)
        .withVin(cars[3].vin)
        .withPlate(cars[3].plate.value)
        .withVulogTripId(reservationEntities[3].vulogTripId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
        .withBookingStatus(VulogRealTimeBookingStatus.Booked)
        .withUserId(vulogUserEntity.id)
        .withStartDate(null)
        .build(),
    ];

    beforeAll(async () => {
      // Save user into DB
      await vulogUserRepo.save(vulogUserEntity);

      // Save cars into DB
      await Promise.all(cars.map((car) => carRepo.save(CarEntity.from(car))));

      // Save reservations into DB
      await Promise.all(
        reservationEntities.map((rsrv) => reservationRepo.save(rsrv)),
      );

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out real-time cars from Vulog
      mockOutgoingService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, vulogRealtimeCarDtos);

      // Send metrics to NewRelic
      mockNonRateLimitOutgoingService
        .onPost(`https://metric-api.newrelic.com/metric/v1`)
        .replyOnce(201);

      success =
        await cronService.reportMetricRentalNumberOfBlueSGStartedButVulogBooking();

      const { common, metrics }: NewRelicMetricCreationRequest = JSON.parse(
        mockNonRateLimitOutgoingService.history['post'][0].data,
      )[0];

      sentCommon = common;

      sentMetricSummary = metrics.find((m) => m.attributes['summary']).value;
      sentMetric__Bluecar_RentalPackage = metrics.find(
        (m) =>
          m.attributes['carModel'] === NewRelicMetricCarModel.Bluecar &&
          m.attributes['rentalType'] === NewRelicMetricRentalType.RentalPackage,
      ).value;
      sentMetric__Bluecar_Subscription = metrics.find(
        (m) =>
          m.attributes['carModel'] === NewRelicMetricCarModel.Bluecar &&
          m.attributes['rentalType'] === NewRelicMetricRentalType.Subscription,
      ).value;
      sentMetric__CorsaE_RentalPackage = metrics.find(
        (m) =>
          m.attributes['carModel'] === NewRelicMetricCarModel.CorsaE &&
          m.attributes['rentalType'] === NewRelicMetricRentalType.RentalPackage,
      ).value;
      sentMetric__CorsaE_Subscription = metrics.find(
        (m) =>
          m.attributes['carModel'] === NewRelicMetricCarModel.CorsaE &&
          m.attributes['rentalType'] === NewRelicMetricRentalType.Subscription,
      ).value;

      expectedCommon = { attributes: { env: process.env.NODE_ENV } };

      expectedMetricSummary = 4;
      expectedMetric__Bluecar_RentalPackage = 1;
      expectedMetric__Bluecar_Subscription = 1;
      expectedMetric__CorsaE_RentalPackage = 1;
      expectedMetric__CorsaE_Subscription = 1;
    });

    it('Run successfully', () => {
      expect(success).toEqual(true);
    });

    it('Common attributes correct', () => {
      expect(sentCommon).toEqual(expectedCommon);
    });

    it('Summary metric is correct', () => {
      expect(sentMetricSummary).toEqual(expectedMetricSummary);
    });

    it('Combination of Dims [bluecar, rental_package] is correct', () => {
      expect(sentMetric__Bluecar_RentalPackage).toEqual(
        expectedMetric__Bluecar_RentalPackage,
      );
    });

    it('Combination of Dims [bluecar, subscription] is correct', () => {
      expect(sentMetric__Bluecar_Subscription).toEqual(
        expectedMetric__Bluecar_Subscription,
      );
    });

    it('Combination of Dims [corsa_e, rental_package] is correct', () => {
      expect(sentMetric__CorsaE_RentalPackage).toEqual(
        expectedMetric__CorsaE_RentalPackage,
      );
    });

    it('Combination of Dims [corsa_e, subscription] is correct', () => {
      expect(sentMetric__CorsaE_Subscription).toEqual(
        expectedMetric__CorsaE_Subscription,
      );
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('💧 Report metric failed: failed to send metrics to NewRelic', () => {
    let success: boolean;
    const cars: Car[] = [
      new CarBuilder().withModel(CarModelEnum.BlueCar).build(), // Pricing policy
      new CarBuilder().withModel(CarModelEnum.BlueCar).build(), // Rental package
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(), // Pricing policy
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(), // Rental package, dynamic
    ];

    const pricingPolicies: PricingPolicy[] = [
      new PricingPolicyBuilder().withRentalRate(0.26).build(), // For car model: Bluecar
      new PricingPolicyBuilder().withRentalRate(0.42).build(), // For car model: Opel Corsa-e
    ];

    const rentalPackages: RentalPackage[] = [
      new RentalPackageBuilder()
        .withDuration(3)
        .withMinAllowedBatteryPercentage(24) // Bluecar
        .withPrice(20)
        .build(),
      new RentalPackageBuilder()
        .withDuration(3)
        .withMinAllowedBatteryPercentage(30) // Corsa-e
        .withPrice(24)
        .withIsDynamic(true)
        .build(),
    ];

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];
    const stationDtos: StationDto[] = [
      new StationDtoBuilder().withZoneId(vulogZones[0].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[1].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[2].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[3].zoneId).build(),
    ];

    const reservationEntities: ReservationEntity[] = [
      new ReservationEntityBuilder()
        .makeCarId(cars[0].id.value)
        .makeStartStationId(stationDtos[0].id)
        .makeBsgUserId(bsgUserId)
        .makeCarModel(CarModelEnum.BlueCar)
        .makeStatus(ReservationStatus.Converted)
        .makePricingPolicy(pricingPolicies[0])
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[1].id.value)
        .makeStartStationId(stationDtos[1].id)
        .makeBsgUserId(bsgUserId)
        .makeCarModel(CarModelEnum.BlueCar)
        .makeStatus(ReservationStatus.Converted)
        .makeRentalPackageData(rentalPackages[0].toJson())
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[2].id.value)
        .makeStartStationId(stationDtos[2].id)
        .makeBsgUserId(bsgUserId)
        .makeCarModel(CarModelEnum.OpelCorsaE)
        .makeStatus(ReservationStatus.Converted)
        .makePricingPolicy(pricingPolicies[1])
        .makeEndedAt(null)
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[3].id.value)
        .makeStartStationId(stationDtos[3].id)
        .makeBsgUserId(bsgUserId)
        .makeCarModel(CarModelEnum.OpelCorsaE)
        .makeStatus(ReservationStatus.Converted)
        .makeRentalPackageData(rentalPackages[1].toJson())
        .makeEndedAt(null)
        .build(),
    ];

    const customerFleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withId(config.vulogConfig.serviceId.value)
        .withVehicles(cars.map((car) => car.vulogId.value))
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

    const vulogRealtimeCarDtos: VulogCarRealTimeDto[] = [
      new VulogCarRealTimeDtoBuilder()
        .withAutonomy(100)
        .withIsCharging(true)
        .withBattery(100)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withServiceId(customerFleetServiceDto.id)
        .withServiceType(customerFleetServiceDto.type)
        .withZones([vulogZones[0]])
        .withId(cars[0].vulogId.value)
        .withVin(cars[0].vin)
        .withPlate(cars[0].plate.value)
        .withVulogTripId(reservationEntities[0].vulogTripId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
        .withBookingStatus(VulogRealTimeBookingStatus.Booked)
        .withUserId(vulogUserEntity.id)
        .withStartDate(null)
        .build(),
      new VulogCarRealTimeDtoBuilder()
        .withAutonomy(100)
        .withIsCharging(true)
        .withBattery(100)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withServiceId(customerFleetServiceDto.id)
        .withServiceType(customerFleetServiceDto.type)
        .withZones([vulogZones[1]])
        .withId(cars[1].vulogId.value)
        .withVin(cars[1].vin)
        .withPlate(cars[1].plate.value)
        .withVulogTripId(reservationEntities[1].vulogTripId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
        .withBookingStatus(VulogRealTimeBookingStatus.Booked)
        .withUserId(vulogUserEntity.id)
        .withStartDate(null)
        .build(),
      new VulogCarRealTimeDtoBuilder()
        .withAutonomy(100)
        .withIsCharging(true)
        .withBattery(100)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withServiceId(customerFleetServiceDto.id)
        .withServiceType(customerFleetServiceDto.type)
        .withZones([vulogZones[2]])
        .withId(cars[2].vulogId.value)
        .withVin(cars[2].vin)
        .withPlate(cars[2].plate.value)
        .withVulogTripId(reservationEntities[2].vulogTripId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
        .withBookingStatus(VulogRealTimeBookingStatus.Booked)
        .withUserId(vulogUserEntity.id)
        .withStartDate(null)
        .build(),
      new VulogCarRealTimeDtoBuilder()
        .withAutonomy(100)
        .withIsCharging(true)
        .withBattery(100)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withServiceId(customerFleetServiceDto.id)
        .withServiceType(customerFleetServiceDto.type)
        .withZones([vulogZones[3]])
        .withId(cars[3].vulogId.value)
        .withVin(cars[3].vin)
        .withPlate(cars[3].plate.value)
        .withVulogTripId(reservationEntities[3].vulogTripId)
        .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
        .withBookingStatus(VulogRealTimeBookingStatus.Booked)
        .withUserId(vulogUserEntity.id)
        .withStartDate(null)
        .build(),
    ];

    beforeAll(async () => {
      // Save user into DB
      await vulogUserRepo.save(vulogUserEntity);

      // Save cars into DB
      await Promise.all(cars.map((car) => carRepo.save(CarEntity.from(car))));

      // Save reservations into DB
      await Promise.all(
        reservationEntities.map((rsrv) => reservationRepo.save(rsrv)),
      );

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out real-time cars from Vulog
      mockOutgoingService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, vulogRealtimeCarDtos);

      // Send metrics to NewRelic
      mockNonRateLimitOutgoingService
        .onPost(`https://metric-api.newrelic.com/metric/v1`)
        .replyOnce(400);

      success =
        await cronService.reportMetricRentalNumberOfBlueSGStartedButVulogBooking();
    });

    it('Run successfully', () => {
      expect(success).toEqual(false);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });
});
