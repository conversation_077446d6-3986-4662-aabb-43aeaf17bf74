import { INestApplication, Module } from '@nestjs/common';

import { MonitoringModule } from '@bluesg-2/monitoring';
import { faker } from '@faker-js/faker';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmHealthIndicator } from '@nestjs/terminus';
import { getRepositoryToken } from '@nestjs/typeorm';
import MockAdapter from 'axios-mock-adapter';
import { DateTime } from 'luxon';
import { ClsModule } from 'nestjs-cls';
import { CacheConfigModule } from 'src/cache-config.module';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { getEnvFilePath } from 'src/common/utils/get-env-file-path.util';
import { config } from 'src/config';
import { ReportMetricReservationNumberOfOngoingCronModule } from 'src/cron/jobs/report-metric-reservation-number-of-ongoing/report-metric-reservation-number-of-ongoing.cron.module';
import { ReportMetricReservationNumberOfOngoingCronService } from 'src/cron/jobs/report-metric-reservation-number-of-ongoing/report-metric-reservation-number-of-ongoing.cron.service';
import { DatabaseModule } from 'src/database/database.module';
import { CacheService } from 'src/logic/cache/cache.service';
import { Car } from 'src/logic/car/domain/car';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { LogicModule } from 'src/logic/logic.module';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { StationDto } from 'src/model/dtos/station.dto';
import {
  VulogZone,
  VulogZoneType,
} from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { AppBuilder } from 'test/helpers/app.builder';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { VulogZoneBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { createTestVulogUserEntity } from 'test/helpers/builders/entities/test-vulog-user-entity.builder';
import { UssApiMocker } from 'test/helpers/external-services/uss.api-mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';
import { Repository } from 'typeorm';
import { EventBusModule } from '~shared/event-bus/event-bus.module';
import { FeatureFlagModule } from '~shared/feature-flag/feature-flag.module';
import { HttpLoggerModule } from '~shared/http-logger/http-logger.module';
import { LocalEventEmitterModule } from '~shared/local-event-emitter/local-event-emitter.module';
import { NewRelicMetricCarModel } from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { NewRelicMetricCreationRequest } from '~shared/newrelic/metric/types/metric-creation-request.newrelic.type';
import { NewRelicModule } from '~shared/newrelic/newrelic.module';
import { NoRateLimitHttpAdapterService } from '~shared/no-rate-limit-http/no-rate-limit-http-adapter.service';
import * as AppNestConfig from '../../../../../src/config/app.nestjs.config';

const email = faker.internet.email();

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [...Object.values(AppNestConfig)],
      envFilePath: getEnvFilePath(),
    }),
    ClsModule.forRoot({
      global: true,
    }),
    MonitoringModule.registerAsync({
      imports: [LogicModule],
      useFactory: async (db: TypeOrmHealthIndicator, cache: CacheService) => ({
        health: {
          dependencies: [
            () => db.pingCheck('database'),
            () => cache.checkIfHealthy(),
          ],
        },
      }),
      inject: [TypeOrmHealthIndicator, CacheService],
    }),
    DatabaseModule,
    CacheConfigModule,
    LogicModule,
    HttpLoggerModule,
    LocalEventEmitterModule,
    EventBusModule,
    FeatureFlagModule,
    ReportMetricReservationNumberOfOngoingCronModule, // Required
    NewRelicModule,
  ],
})
export class SpecificModuleToTest {}

describe(`[E2E] Report metric "reservation.numberOfOnGoing"`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let carRepo: Repository<CarEntity>;
  let vulogUserRepo: Repository<VulogUserEntity>;
  let reservationRepo: Repository<ReservationEntity>;

  let cacheService: CacheService;
  let mockOutgoingService: MockAdapter;
  let mockNonRateLimitOutgoingService: MockAdapter;
  let mockUssApiService: MockAdapter;
  let cronService: ReportMetricReservationNumberOfOngoingCronService;

  const mockAuthResponse = createTestVulogAuthResponse();

  const bsgUserId = aUUID();

  const vulogUserDto = createTestVulogUserDto();
  const vulogUserEntity = createTestVulogUserEntity(bsgUserId);

  beforeAll(async () => {
    app = await appBuilder.buildAndStart(SpecificModuleToTest);

    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));
    vulogUserRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );
    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );
    cacheService = app.get<CacheService>(CacheService);
    const httpApdaterService = app.get<HttpAdapterService>(HttpAdapterService);
    mockOutgoingService = new MockAdapter(
      httpApdaterService.httpService.axiosRef,
      { onNoMatch: 'throwException' },
    );
    const noRateLimitedHttpAdapterService =
      app.get<NoRateLimitHttpAdapterService>('NO_RATE_LIMIT_SERVICE');
    mockNonRateLimitOutgoingService = new MockAdapter(
      noRateLimitedHttpAdapterService.httpService.axiosRef,
      { onNoMatch: 'throwException' },
    );

    mockUssApiService = new UssApiMocker(app).mock();

    cronService = app.get<ReportMetricReservationNumberOfOngoingCronService>(
      ReportMetricReservationNumberOfOngoingCronService,
    );

    vulogUserDto.email = email;

    mockOutgoingService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, vulogUserDto);

    const userServicesAndProfiles = createTestVulogUserServicesDto();
    userServicesAndProfiles.userId = vulogUserDto.id;

    /**
     * Doing Vulog authentication mock
     */
    mockOutgoingService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(vulogUserDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    mockOutgoingService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, mockAuthResponse);

    mockVulogCredential(app);
  });

  afterAll(async () => {
    await cleanUpResources();

    await app.close();

    mockOutgoingService.reset();
    mockUssApiService.reset();
  });

  async function cleanUpResources(): Promise<void> {
    await carRepo.delete({});
    await cacheService.clearAll();
    await vulogUserRepo.delete({});
    await reservationRepo.delete({});

    jest.clearAllMocks();
  }

  describe('🔥 Report metric successfully: all dimensions are covered', () => {
    let success: boolean;

    let sentCommon;
    let sentMetricSummary;
    let sentMetric__Bluecar;
    let sentMetric__CorsaE;

    let expectedCommon;
    let expectedMetricSummary;
    let expectedMetric__Bluecar;
    let expectedMetric__CorsaE;

    const cars: Car[] = [
      new CarBuilder().withModel(CarModelEnum.BlueCar).build(),
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(),
    ];

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];
    const stationDtos: StationDto[] = [
      new StationDtoBuilder().withZoneId(vulogZones[0].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[1].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[2].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[3].zoneId).build(),
    ];

    const reservationEntities: ReservationEntity[] = [
      new ReservationEntityBuilder()
        .makeCarId(cars[0].id.value)
        .makeStartStationId(stationDtos[0].id)
        .makeBsgUserId(bsgUserId)
        .makeCarModel(CarModelEnum.BlueCar)
        .makeStatus(ReservationStatus.Reserved)
        .makeReservedAt(DateTime.now().toJSDate())
        .makeExpiresAt(DateTime.now().plus({ minutes: 30 }).toJSDate())
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[1].id.value)
        .makeStartStationId(stationDtos[2].id)
        .makeBsgUserId(bsgUserId)
        .makeCarModel(CarModelEnum.OpelCorsaE)
        .makeStatus(ReservationStatus.Reserved)
        .makeReservedAt(DateTime.now().toJSDate())
        .makeExpiresAt(DateTime.now().plus({ minutes: 30 }).toJSDate())
        .build(),
    ];

    beforeAll(async () => {
      // Save user into DB
      await vulogUserRepo.save(vulogUserEntity);

      // Save cars into DB
      await Promise.all(cars.map((car) => carRepo.save(CarEntity.from(car))));

      // Save reservations into DB
      await Promise.all(
        reservationEntities.map((rsrv) => reservationRepo.save(rsrv)),
      );

      // Send metrics to NewRelic
      mockNonRateLimitOutgoingService
        .onPost(`https://metric-api.newrelic.com/metric/v1`)
        .replyOnce(201);

      success = await cronService.reportMetricReservationNumberOfOngoing();

      const { common, metrics }: NewRelicMetricCreationRequest = JSON.parse(
        mockNonRateLimitOutgoingService.history['post'][0].data,
      )[0];

      sentCommon = common;

      sentMetricSummary = metrics.find((m) => m.attributes['summary']).value;
      sentMetric__Bluecar = metrics.find(
        (m) => m.attributes['carModel'] === NewRelicMetricCarModel.Bluecar,
      ).value;
      sentMetric__CorsaE = metrics.find(
        (m) => m.attributes['carModel'] === NewRelicMetricCarModel.CorsaE,
      ).value;

      expectedCommon = { attributes: { env: process.env.NODE_ENV } };

      expectedMetricSummary = 2;
      expectedMetric__Bluecar = 1;
      expectedMetric__CorsaE = 1;
    });

    it('Run successfully', () => {
      expect(success).toEqual(true);
    });

    it('Common attributes correct', () => {
      expect(sentCommon).toEqual(expectedCommon);
    });

    it('Summary metric is correct', () => {
      expect(sentMetricSummary).toEqual(expectedMetricSummary);
    });

    it('Combination of Dims [bluecar] is correct', () => {
      expect(sentMetric__Bluecar).toEqual(expectedMetric__Bluecar);
    });

    it('Combination of Dims [corsa_e] is correct', () => {
      expect(sentMetric__CorsaE).toEqual(expectedMetric__CorsaE);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('💧 Report metric failed: failed to send metrics to NewRelic', () => {
    let success: boolean;

    const cars: Car[] = [
      new CarBuilder().withModel(CarModelEnum.BlueCar).build(),
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(),
    ];

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];
    const stationDtos: StationDto[] = [
      new StationDtoBuilder().withZoneId(vulogZones[0].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[1].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[2].zoneId).build(),
      new StationDtoBuilder().withZoneId(vulogZones[3].zoneId).build(),
    ];

    const reservationEntities: ReservationEntity[] = [
      new ReservationEntityBuilder()
        .makeCarId(cars[0].id.value)
        .makeStartStationId(stationDtos[0].id)
        .makeBsgUserId(bsgUserId)
        .makeCarModel(CarModelEnum.BlueCar)
        .makeStatus(ReservationStatus.Reserved)
        .makeReservedAt(DateTime.now().toJSDate())
        .makeExpiresAt(DateTime.now().plus({ minutes: 30 }).toJSDate())
        .build(),
      new ReservationEntityBuilder()
        .makeCarId(cars[1].id.value)
        .makeStartStationId(stationDtos[2].id)
        .makeBsgUserId(bsgUserId)
        .makeCarModel(CarModelEnum.OpelCorsaE)
        .makeStatus(ReservationStatus.Reserved)
        .makeReservedAt(DateTime.now().toJSDate())
        .makeExpiresAt(DateTime.now().plus({ minutes: 30 }).toJSDate())
        .build(),
    ];

    beforeAll(async () => {
      // Save user into DB
      await vulogUserRepo.save(vulogUserEntity);

      // Save cars into DB
      await Promise.all(cars.map((car) => carRepo.save(CarEntity.from(car))));

      // Save reservations into DB
      await Promise.all(
        reservationEntities.map((rsrv) => reservationRepo.save(rsrv)),
      );

      // Send metrics to NewRelic
      mockNonRateLimitOutgoingService
        .onPost(`https://metric-api.newrelic.com/metric/v1`)
        .replyOnce(400);

      success = await cronService.reportMetricReservationNumberOfOngoing();
    });

    it('Run successfully', () => {
      expect(success).toEqual(false);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });
});
