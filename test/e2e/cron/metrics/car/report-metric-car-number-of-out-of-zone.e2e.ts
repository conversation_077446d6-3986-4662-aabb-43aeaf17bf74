import { INestApplication, Module } from '@nestjs/common';

import { MonitoringModule } from '@bluesg-2/monitoring';
import { faker } from '@faker-js/faker';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmHealthIndicator } from '@nestjs/terminus';
import { getRepositoryToken } from '@nestjs/typeorm';
import MockAdapter from 'axios-mock-adapter';
import * as _ from 'lodash';
import { ClsModule } from 'nestjs-cls';
import * as QueryString from 'qs';
import { CacheConfigModule } from 'src/cache-config.module';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { getEnvFilePath } from 'src/common/utils/get-env-file-path.util';
import { config } from 'src/config';
import { ReportMetricCarNumberOfOutOfZoneCronModule } from 'src/cron/jobs/report-metric/car/report-metric-car-number-of-out-of-zone/report-metric-car-number-of-out-of-zone.cron.module';
import { ReportMetricCarNumberOfOutOfZoneCronService } from 'src/cron/jobs/report-metric/car/report-metric-car-number-of-out-of-zone/report-metric-car-number-of-out-of-zone.cron.service';
import { DatabaseModule } from 'src/database/database.module';
import { CacheService } from 'src/logic/cache/cache.service';
import { Car } from 'src/logic/car/domain/car';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { LogicModule } from 'src/logic/logic.module';
import { StationPaths } from 'src/logic/station/station-paths';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { StationDto } from 'src/model/dtos/station.dto';
import { VulogCarRealTimeDto } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogCarStaticInfoDto } from 'src/model/dtos/vulog-car-static-info/vulog-car-static-info.dto';
import {
  VulogFleetServiceDto,
  VulogFleetServiceType,
} from 'src/model/dtos/vulog-fleet-service/vulog-fleet-service.dto';
import {
  VulogZone,
  VulogZoneType,
} from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { VvgVehicleStatusDto } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { AppBuilder } from 'test/helpers/app.builder';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { VulogCarStaticInfoDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car-static-info.dto.builder';
import {
  VulogCarRealTimeDtoBuilder,
  VulogZoneBuilder,
} from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import { UssApiMocker } from 'test/helpers/external-services/uss.api-mocker';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';
import { Repository } from 'typeorm';
import { EventBusModule } from '~shared/event-bus/event-bus.module';
import { FeatureFlagModule } from '~shared/feature-flag/feature-flag.module';
import { HttpLoggerModule } from '~shared/http-logger/http-logger.module';
import { LocalEventEmitterModule } from '~shared/local-event-emitter/local-event-emitter.module';
import {
  NewRelicMetricCarModel,
  NewRelicMetricService,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';
import { NewRelicMetricCreationRequest } from '~shared/newrelic/metric/types/metric-creation-request.newrelic.type';
import { NewRelicModule } from '~shared/newrelic/newrelic.module';
import { NoRateLimitHttpAdapterService } from '~shared/no-rate-limit-http/no-rate-limit-http-adapter.service';
import * as AppNestConfig from '../../../../../src/config/app.nestjs.config';

const email = faker.internet.email();

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [...Object.values(AppNestConfig)],
      envFilePath: getEnvFilePath(),
    }),
    ClsModule.forRoot({
      global: true,
    }),
    MonitoringModule.registerAsync({
      imports: [LogicModule],
      useFactory: async (db: TypeOrmHealthIndicator, cache: CacheService) => ({
        health: {
          dependencies: [
            () => db.pingCheck('database'),
            () => cache.checkIfHealthy(),
          ],
        },
      }),
      inject: [TypeOrmHealthIndicator, CacheService],
    }),
    DatabaseModule,
    CacheConfigModule,
    LogicModule,
    HttpLoggerModule,
    LocalEventEmitterModule,
    EventBusModule,
    FeatureFlagModule,
    ReportMetricCarNumberOfOutOfZoneCronModule, // Required
    NewRelicModule,
  ],
})
export class SpecificModuleToTest {}

describe(`[E2E] Report metric "car.numberOfOutOfZone"`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let carRepo: Repository<CarEntity>;

  let cacheService: CacheService;
  let mockOutgoingService: MockAdapter;
  let mockNonRateLimitOutgoingService: MockAdapter;
  let mockUssApiService: MockAdapter;
  let reportMetricCarNumberOfOutOfZoneCronService: ReportMetricCarNumberOfOutOfZoneCronService;

  const mockAuthResponse = createTestVulogAuthResponse();

  beforeAll(async () => {
    app = await appBuilder.buildAndStart(SpecificModuleToTest);

    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));
    cacheService = app.get<CacheService>(CacheService);
    const httpApdaterService = app.get<HttpAdapterService>(HttpAdapterService);
    mockOutgoingService = new MockAdapter(
      httpApdaterService.httpService.axiosRef,
      { onNoMatch: 'throwException' },
    );
    const noRateLimitedHttpAdapterService =
      app.get<NoRateLimitHttpAdapterService>('NO_RATE_LIMIT_SERVICE');
    mockNonRateLimitOutgoingService = new MockAdapter(
      noRateLimitedHttpAdapterService.httpService.axiosRef,
      { onNoMatch: 'throwException' },
    );

    mockUssApiService = new UssApiMocker(app).mock();

    reportMetricCarNumberOfOutOfZoneCronService =
      app.get<ReportMetricCarNumberOfOutOfZoneCronService>(
        ReportMetricCarNumberOfOutOfZoneCronService,
      );

    const userDto = createTestVulogUserDto();

    userDto.email = email;

    mockOutgoingService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, userDto);

    const userServicesAndProfiles = createTestVulogUserServicesDto();
    userServicesAndProfiles.userId = userDto.id;

    /**
     * Doing Vulog authentication mock
     */
    mockOutgoingService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(userDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    mockOutgoingService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, mockAuthResponse);

    mockVulogCredential(app);
  });

  afterAll(async () => {
    await cleanUpResources();

    await app.close();

    mockOutgoingService.reset();
    mockUssApiService.reset();
  });

  async function cleanUpResources(): Promise<void> {
    await carRepo.delete({});
    await cacheService.clearAll();

    jest.clearAllMocks();
  }

  describe('🔥 Report metric successfully: all dimensions are covered', () => {
    let success: boolean;

    let sentCommon;
    let sentMetricSummary;
    let sentMetric__CustomerTrip_Bluecar;
    let sentMetric__ServiceTrip_CorsaE;
    let sentMetric__OutOfService_CorsaE;
    let sentMetric__OutOfService_NoCarModel;

    let expectedCommon;
    let expectedMetricSummary;
    let expectedMetric__CustomerTrip_Bluecar;
    let expectedMetric__ServiceTrip_CorsaE;
    let expectedMetric__OutOfService_CorsaE;
    let expectedMetric__OutOfService_NoCarModel;

    const cars: Car[] = [
      new CarBuilder().withModel(CarModelEnum.BlueCar).build(),
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(),
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(),
      new CarBuilder().withModel(null).build(),
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(), // 5th element should be in zone
    ];

    const vulogCarRealTimeDtos: VulogCarRealTimeDto[] = [];

    const vvgCarStatusDtos: VvgVehicleStatusDto[] = [];

    let customerFleetServiceDto: VulogFleetServiceDto;
    let serviceTripFleetServiceDto: VulogFleetServiceDto;

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];

    const nonCustomerVulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [];

    const stationDtos: StationDto[] = [];

    beforeAll(async () => {
      // Save cars into DB
      await Promise.all(cars.map((car) => carRepo.save(CarEntity.from(car))));

      // Build Vulog Fleet Service DTO
      customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withId(config.vulogConfig.serviceId.value)
        .withVehicles([cars[0].vulogId.value, cars[3].vulogId.value])
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();
      serviceTripFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withType(VulogFleetServiceType.ServiceTrip)
        .withVehicles([cars[1].vulogId.value])
        .withZones(nonCustomerVulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

      // Map realtime car DTOs
      vulogCarRealTimeDtos.push(
        new VulogCarRealTimeDtoBuilder()
          .withAutonomy(100)
          .withBattery(100)
          .withIsCharging(false)
          .withFleetId(config.vulogConfig.fleetId.value)
          .withServiceId(customerFleetServiceDto.id)
          .withServiceType(customerFleetServiceDto.type)
          .withZones([]) // Out Of Station
          .withId(cars[0].vulogId.value)
          .withVin(cars[0].vin)
          .withPlate(cars[0].plate.value)
          .build(),
      );
      vulogCarRealTimeDtos.push(
        new VulogCarRealTimeDtoBuilder()
          .withAutonomy(100)
          .withIsCharging(false)
          .withBattery(100)
          .withFleetId(config.vulogConfig.fleetId.value)
          .withServiceId(serviceTripFleetServiceDto.id)
          .withServiceType(serviceTripFleetServiceDto.type)
          .withZones([]) // Out Of Station
          .withId(cars[1].vulogId.value)
          .withVin(cars[1].vin)
          .withPlate(cars[1].plate.value)
          .build(),
      );
      vulogCarRealTimeDtos.push(
        new VulogCarRealTimeDtoBuilder()
          .withAutonomy(100)
          .withIsCharging(false)
          .withBattery(100)
          .withFleetId(config.vulogConfig.fleetId.value)
          .withServiceId(null) // Out of Service
          .withServiceType(null) // Out of Service
          .withZones([]) // Out Of Station
          .withId(cars[2].vulogId.value)
          .withVin(cars[2].vin)
          .withPlate(cars[2].plate.value)
          .build(),
      );
      vulogCarRealTimeDtos.push(
        new VulogCarRealTimeDtoBuilder()
          .withAutonomy(100)
          .withIsCharging(false)
          .withBattery(100)
          .withFleetId(config.vulogConfig.fleetId.value)
          .withServiceId(null) // Out of Service
          .withServiceType(null) // Out of Service
          .withZones([]) // Out Of Station
          .withId(cars[3].vulogId.value)
          .withVin(cars[3].vin)
          .withPlate(cars[3].plate.value)
          .build(),
      );
      vulogCarRealTimeDtos.push(
        new VulogCarRealTimeDtoBuilder()
          .withAutonomy(100)
          .withIsCharging(false)
          .withBattery(100)
          .withFleetId(config.vulogConfig.fleetId.value)
          .withServiceId(serviceTripFleetServiceDto.id)
          .withServiceType(serviceTripFleetServiceDto.type)
          .withZones([_.sample(vulogZones)])
          .withId(cars[4].vulogId.value)
          .withVin(cars[4].vin)
          .withPlate(cars[4].plate.value)
          .build(),
      );

      // Map static car DTOs
      vulogStaticCarDtos.push(
        new VulogCarStaticInfoDtoBuilder()
          .withId(cars[0].vulogId.value)
          .withModel(cars[0].model)
          .withPlate(cars[0].plate.value)
          .withServiceId(customerFleetServiceDto.id)
          .withVin(cars[0].vin)
          .build(),
      );
      vulogStaticCarDtos.push(
        new VulogCarStaticInfoDtoBuilder()
          .withId(cars[1].vulogId.value)
          .withModel(cars[1].model)
          .withPlate(cars[1].plate.value)
          .withServiceId(serviceTripFleetServiceDto.id)
          .withVin(cars[1].vin)
          .build(),
      );
      vulogStaticCarDtos.push(
        new VulogCarStaticInfoDtoBuilder()
          .withId(cars[2].vulogId.value)
          .withModel(cars[2].model)
          .withPlate(cars[2].plate.value)
          .withServiceId(null)
          .withVin(cars[2].vin)
          .build(),
      );
      vulogStaticCarDtos.push(
        new VulogCarStaticInfoDtoBuilder()
          .withId(cars[3].vulogId.value)
          .withModel(null) // No Car Model
          .withPlate(cars[3].plate.value)
          .withServiceId(null)
          .withVin(cars[3].vin)
          .build(),
      );
      vulogStaticCarDtos.push(
        new VulogCarStaticInfoDtoBuilder()
          .withId(cars[4].vulogId.value)
          .withModel(cars[4].model)
          .withPlate(cars[4].plate.value)
          .withServiceId(serviceTripFleetServiceDto.id)
          .withVin(cars[4].vin)
          .build(),
      );

      // Map vulog zone DTOs
      vulogZones.forEach((vgZone) => {
        stationDtos.push(
          new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
        );
      });

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto, serviceTripFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      // Listing out real-time cars from Vulog
      mockOutgoingService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, vulogCarRealTimeDtos);

      // Listing out car status from VVG
      mockOutgoingService
        .onGet(`${VulogPaths.vvgVehicles(config.vulogConfig.fleetId)}`)
        .reply(200, vvgCarStatusDtos);

      // Send metrics to NewRelic
      mockNonRateLimitOutgoingService
        .onPost(`https://metric-api.newrelic.com/metric/v1`)
        .replyOnce(201);

      success =
        await reportMetricCarNumberOfOutOfZoneCronService.reportMetricCarNumberOfOutOfZone();

      const { common, metrics }: NewRelicMetricCreationRequest = JSON.parse(
        mockNonRateLimitOutgoingService.history['post'][0].data,
      )[0];

      sentCommon = common;

      sentMetricSummary = metrics.find((m) => m.attributes['summary']).value;
      sentMetric__CustomerTrip_Bluecar = metrics.find(
        (m) =>
          m.attributes['serviceType'] === NewRelicMetricService.CustomerTrip &&
          m.attributes['carModel'] === NewRelicMetricCarModel.Bluecar,
      ).value;
      sentMetric__OutOfService_CorsaE = metrics.find(
        (m) =>
          m.attributes['serviceType'] === NewRelicMetricService.OutOfService &&
          m.attributes['carModel'] === NewRelicMetricCarModel.CorsaE,
      ).value;
      sentMetric__OutOfService_NoCarModel = metrics.find(
        (m) =>
          m.attributes['serviceType'] === NewRelicMetricService.OutOfService &&
          m.attributes['carModel'] === NewRelicMetricCarModel.NoCarModel,
      ).value;
      sentMetric__ServiceTrip_CorsaE = metrics.find(
        (m) =>
          m.attributes['serviceType'] === NewRelicMetricService.ServiceTrip &&
          m.attributes['carModel'] === NewRelicMetricCarModel.CorsaE,
      ).value;

      expectedCommon = { attributes: { env: process.env.NODE_ENV } };

      expectedMetricSummary = 4;

      expectedMetric__CustomerTrip_Bluecar = 1;
      expectedMetric__ServiceTrip_CorsaE = 1;
      expectedMetric__OutOfService_CorsaE = 1;
      expectedMetric__OutOfService_NoCarModel = 1;
    });

    it('Run successfully', () => {
      expect(success).toEqual(true);
    });

    it('Common attributes correct', () => {
      expect(sentCommon).toEqual(expectedCommon);
    });

    it('Summary metric is correct', () => {
      expect(sentMetricSummary).toEqual(expectedMetricSummary);
    });

    it('Combination of Dims [customer_trip, bluecar] is correct', () => {
      expect(sentMetric__CustomerTrip_Bluecar).toEqual(
        expectedMetric__CustomerTrip_Bluecar,
      );
    });

    it('Combination of Dims [OUT_OF_SERVICE, corsa_e] is correct', () => {
      expect(sentMetric__OutOfService_CorsaE).toEqual(
        expectedMetric__OutOfService_CorsaE,
      );
    });

    it('Combination of Dims [OUT_OF_SERVICE, NO_CAR_MODEL] is correct', () => {
      expect(sentMetric__OutOfService_NoCarModel).toEqual(
        expectedMetric__OutOfService_NoCarModel,
      );
    });

    it('Combination of Dims [service_trip, corsa_e] is correct', () => {
      expect(sentMetric__ServiceTrip_CorsaE).toEqual(
        expectedMetric__ServiceTrip_CorsaE,
      );
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('💧 Report metric failed: failed to send metrics to NewRelic', () => {
    let success: boolean;

    const cars: Car[] = [
      new CarBuilder().withModel(CarModelEnum.BlueCar).build(),
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(),
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(),
      new CarBuilder().withModel(null).build(),
      new CarBuilder().withModel(CarModelEnum.OpelCorsaE).build(), // 5th element should be in zone
    ];

    const vulogCarRealTimeDtos: VulogCarRealTimeDto[] = [];

    const vvgCarStatusDtos: VvgVehicleStatusDto[] = [];

    let customerFleetServiceDto: VulogFleetServiceDto;
    let serviceTripFleetServiceDto: VulogFleetServiceDto;

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];

    const nonCustomerVulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [];

    const stationDtos: StationDto[] = [];

    beforeAll(async () => {
      // Save cars into DB
      await Promise.all(cars.map((car) => carRepo.save(CarEntity.from(car))));

      // Build Vulog Fleet Service DTO
      customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withId(config.vulogConfig.serviceId.value)
        .withVehicles([cars[0].vulogId.value, cars[3].vulogId.value])
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();
      serviceTripFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withType(VulogFleetServiceType.ServiceTrip)
        .withVehicles([cars[1].vulogId.value])
        .withZones(nonCustomerVulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

      // Map realtime car DTOs
      vulogCarRealTimeDtos.push(
        new VulogCarRealTimeDtoBuilder()
          .withAutonomy(100)
          .withBattery(100)
          .withIsCharging(false)
          .withFleetId(config.vulogConfig.fleetId.value)
          .withServiceId(customerFleetServiceDto.id)
          .withServiceType(customerFleetServiceDto.type)
          .withZones([]) // Out Of Station
          .withId(cars[0].vulogId.value)
          .withVin(cars[0].vin)
          .withPlate(cars[0].plate.value)
          .build(),
      );
      vulogCarRealTimeDtos.push(
        new VulogCarRealTimeDtoBuilder()
          .withAutonomy(100)
          .withIsCharging(false)
          .withBattery(100)
          .withFleetId(config.vulogConfig.fleetId.value)
          .withServiceId(serviceTripFleetServiceDto.id)
          .withServiceType(serviceTripFleetServiceDto.type)
          .withZones([]) // Out Of Station
          .withId(cars[1].vulogId.value)
          .withVin(cars[1].vin)
          .withPlate(cars[1].plate.value)
          .build(),
      );
      vulogCarRealTimeDtos.push(
        new VulogCarRealTimeDtoBuilder()
          .withAutonomy(100)
          .withIsCharging(false)
          .withBattery(100)
          .withFleetId(config.vulogConfig.fleetId.value)
          .withServiceId(null) // Out of Service
          .withServiceType(null) // Out of Service
          .withZones([]) // Out Of Station
          .withId(cars[2].vulogId.value)
          .withVin(cars[2].vin)
          .withPlate(cars[2].plate.value)
          .build(),
      );
      vulogCarRealTimeDtos.push(
        new VulogCarRealTimeDtoBuilder()
          .withAutonomy(100)
          .withIsCharging(false)
          .withBattery(100)
          .withFleetId(config.vulogConfig.fleetId.value)
          .withServiceId(null) // Out of Service
          .withServiceType(null) // Out of Service
          .withZones([]) // Out Of Station
          .withId(cars[3].vulogId.value)
          .withVin(cars[3].vin)
          .withPlate(cars[3].plate.value)
          .build(),
      );
      vulogCarRealTimeDtos.push(
        new VulogCarRealTimeDtoBuilder()
          .withAutonomy(100)
          .withIsCharging(false)
          .withBattery(100)
          .withFleetId(config.vulogConfig.fleetId.value)
          .withServiceId(serviceTripFleetServiceDto.id)
          .withServiceType(serviceTripFleetServiceDto.type)
          .withZones([_.sample(vulogZones)])
          .withId(cars[4].vulogId.value)
          .withVin(cars[4].vin)
          .withPlate(cars[4].plate.value)
          .build(),
      );

      // Map static car DTOs
      vulogStaticCarDtos.push(
        new VulogCarStaticInfoDtoBuilder()
          .withId(cars[0].vulogId.value)
          .withModel(cars[0].model)
          .withPlate(cars[0].plate.value)
          .withServiceId(customerFleetServiceDto.id)
          .withVin(cars[0].vin)
          .build(),
      );
      vulogStaticCarDtos.push(
        new VulogCarStaticInfoDtoBuilder()
          .withId(cars[1].vulogId.value)
          .withModel(cars[1].model)
          .withPlate(cars[1].plate.value)
          .withServiceId(serviceTripFleetServiceDto.id)
          .withVin(cars[1].vin)
          .build(),
      );
      vulogStaticCarDtos.push(
        new VulogCarStaticInfoDtoBuilder()
          .withId(cars[2].vulogId.value)
          .withModel(cars[2].model)
          .withPlate(cars[2].plate.value)
          .withServiceId(null)
          .withVin(cars[2].vin)
          .build(),
      );
      vulogStaticCarDtos.push(
        new VulogCarStaticInfoDtoBuilder()
          .withId(cars[3].vulogId.value)
          .withModel(null) // No Car Model
          .withPlate(cars[3].plate.value)
          .withServiceId(null)
          .withVin(cars[3].vin)
          .build(),
      );
      vulogStaticCarDtos.push(
        new VulogCarStaticInfoDtoBuilder()
          .withId(cars[4].vulogId.value)
          .withModel(cars[4].model)
          .withPlate(cars[4].plate.value)
          .withServiceId(serviceTripFleetServiceDto.id)
          .withVin(cars[4].vin)
          .build(),
      );

      // Map vulog zone DTOs
      vulogZones.forEach((vgZone) => {
        stationDtos.push(
          new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
        );
      });

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto, serviceTripFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      // Listing out real-time cars from Vulog
      mockOutgoingService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, vulogCarRealTimeDtos);

      // Listing out car status from VVG
      mockOutgoingService
        .onGet(`${VulogPaths.vvgVehicles(config.vulogConfig.fleetId)}`)
        .reply(200, vvgCarStatusDtos);

      // Send metrics to NewRelic
      mockNonRateLimitOutgoingService
        .onPost(`https://metric-api.newrelic.com/metric/v1`)
        .replyOnce(400);

      success =
        await reportMetricCarNumberOfOutOfZoneCronService.reportMetricCarNumberOfOutOfZone();
    });

    it('Run successfully', () => {
      expect(success).toEqual(false);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });
});
