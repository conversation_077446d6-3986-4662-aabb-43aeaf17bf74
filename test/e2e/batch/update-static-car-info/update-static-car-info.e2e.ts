import { INestApplication } from '@nestjs/common';

import { faker } from '@faker-js/faker';
import { getRepositoryToken } from '@nestjs/typeorm';
import MockAdapter from 'axios-mock-adapter';
import * as QueryString from 'qs';
import { UpdateCarsAppModule } from 'src/batch/update-cars/update-cars.app.module';
import { UpdateCarsBatchService } from 'src/batch/update-cars/update-cars.batch.service';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import {
  CarId,
  CarPlateNumber,
  VulogProfileId,
  VulogUserId,
} from 'src/common/tiny-types';
import { config } from 'src/config';
import { CacheService } from 'src/logic/cache/cache.service';
import { CarService } from 'src/logic/car/car.service';
import { Car } from 'src/logic/car/domain/car';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogCarStaticInfoDto } from 'src/model/dtos/vulog-car-static-info/vulog-car-static-info.dto';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { AppBuilder } from 'test/helpers/app.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { VulogCarStaticInfoDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car-static-info.dto.builder';
import { UssApiMocker } from 'test/helpers/external-services/uss.api-mocker';
import { aString, aUUID } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';
import { Repository } from 'typeorm';

const email = faker.internet.email();

describe(`[E2E] Update static car info`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let vulogUserRepo: Repository<VulogUserEntity>;
  let cacheService: CacheService;
  let mockOutgoingService: MockAdapter;
  let mockUssApiService: MockAdapter;
  let carService: CarService;
  let carRepo: Repository<CarEntity>;
  let updateCarsBatchService: UpdateCarsBatchService;

  const mockAuthResponse = createTestVulogAuthResponse();

  beforeAll(async () => {
    app = await appBuilder.buildAndStart(UpdateCarsAppModule);

    vulogUserRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );
    cacheService = app.get<CacheService>(CacheService);
    const httpApdaterService = app.get<HttpAdapterService>(HttpAdapterService);
    mockOutgoingService = new MockAdapter(
      httpApdaterService.httpService.axiosRef,
      { onNoMatch: 'throwException' },
    );
    updateCarsBatchService = await app.get(UpdateCarsBatchService);
    carRepo = await app.get(getRepositoryToken(CarEntity));
    carService = await app.get(CarService);
  });

  function mockAuth() {
    mockUssApiService = new UssApiMocker(app).mock();

    const userDto = createTestVulogUserDto();

    userDto.email = email;

    mockOutgoingService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, userDto);

    const userServicesAndProfiles = createTestVulogUserServicesDto();
    userServicesAndProfiles.userId = userDto.id;

    /**
     * Doing Vulog authentication mock
     */
    mockOutgoingService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(userDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    mockOutgoingService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, mockAuthResponse);
  }

  afterAll(async () => {
    await cleanUpResources();

    await app.close();

    mockOutgoingService.reset();
    mockUssApiService.reset();
  });

  async function cleanUpResources(): Promise<void> {
    await cacheService.clearAll();
    await vulogUserRepo.delete({});
    await carRepo.delete({});

    mockOutgoingService.reset();

    jest.resetAllMocks();
  }

  describe('🔥 Batch job run successfully - All Vulog cars are new (not existing in database)', () => {
    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [
      new VulogCarStaticInfoDtoBuilder().build(),
      new VulogCarStaticInfoDtoBuilder().build(),
      new VulogCarStaticInfoDtoBuilder().build(),
    ];
    const cars: Car[] = vulogStaticCarDtos.map((vulogStaticCarDto) => {
      return vulogStaticCarDto.toStaticCar().toCar(null, { isPlugged: null });
    });

    const expected_Result = {
      numberOfVulogCars: vulogStaticCarDtos.length,
      numberOfBlueSGCars: cars.length,
      numberOfBlueSGCarsUpdated: 0,
      numberOfBlueSGCarsCreated: cars.length,
      numberOfVulogCarsIgnored: 0,
      numberOfBlueSGCarsCreatedToReplaceOldOnes: 0,
    };

    beforeAll(async () => {
      mockVulogCredential(app);

      mockAuth();

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });
    });

    it('Test verification', async () => {
      const result = await updateCarsBatchService.updateStaticCarInformation();

      // Check general result
      expect(result).toEqual(expected_Result);

      const resultCars: Car[] = await carService.getAll();

      cars.forEach((car) => {
        delete car.id;
      });

      resultCars.forEach((car) => {
        delete car.id;
      });

      // Check actual result
      expect(
        resultCars.sort((a, b) => a.plate.value.localeCompare(b.plate.value)),
      ).toEqual(
        cars.sort((a, b) => a.plate.value.localeCompare(b.plate.value)),
      );
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('🔥 Batch job run successfully - Upsert all cars', () => {
    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [
      new VulogCarStaticInfoDtoBuilder().withArchived(false).build(),
      new VulogCarStaticInfoDtoBuilder().withArchived(false).build(),
      new VulogCarStaticInfoDtoBuilder().withArchived(false).build(),
    ];
    const cars: Car[] = vulogStaticCarDtos.map((vulogStaticCarDto) => {
      return vulogStaticCarDto
        .toStaticCar()
        .toCar(new CarId(aUUID()), { isPlugged: null });
    });
    vulogStaticCarDtos.forEach((car) => {
      car.archived = true; // Info to upsert
    });

    const expected_Result = {
      numberOfVulogCars: vulogStaticCarDtos.length,
      numberOfBlueSGCars: cars.length,
      numberOfBlueSGCarsUpdated: vulogStaticCarDtos.length,
      numberOfBlueSGCarsCreated: 0,
      numberOfVulogCarsIgnored: 0,
      numberOfBlueSGCarsCreatedToReplaceOldOnes: 0,
    };

    beforeAll(async () => {
      mockVulogCredential(app);

      mockAuth();

      await carService.saveAll(cars);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });
    });

    it('Test verification', async () => {
      const result = await updateCarsBatchService.updateStaticCarInformation();

      // Check general result
      expect(result).toEqual(expected_Result);

      const resultCars: Car[] = await carService.getAll();

      expect(resultCars.every((car) => car.isActive)).toEqual(false);

      resultCars.forEach((car) => {
        delete car.isActive;
      });

      cars.forEach((car) => {
        delete car.isActive;
      });

      // Check actual result
      expect(
        resultCars.sort((a, b) => a.plate.value.localeCompare(b.plate.value)),
      ).toEqual(
        cars.sort((a, b) => a.plate.value.localeCompare(b.plate.value)),
      );
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('💦 Batch job run failed - A Vulog car has different model and same (VIN or plate) with existing car, but different Vulog Car ID', () => {
    const existingVulogCarStaticDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withArchived(false)
        .withModel(CarModelEnum.BlueCar)
        .build();
    const problematicVulogCarStaticDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withVin(existingVulogCarStaticDto.vin)
        .withPlate(existingVulogCarStaticDto.plate)
        .withModel(CarModelEnum.OpelCorsaE) // Wrong here
        .withArchived(false)
        .build();

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [
      existingVulogCarStaticDto,
      problematicVulogCarStaticDto,
    ];
    const cars: Car[] = [
      existingVulogCarStaticDto
        .toStaticCar()
        .toCar(new CarId(aUUID()), { isPlugged: null }),
    ];

    const expected_Result = {
      numberOfVulogCars: vulogStaticCarDtos.length,
      numberOfBlueSGCars: cars.length,
      numberOfBlueSGCarsUpdated: 1,
      numberOfBlueSGCarsCreated: 0,
      numberOfVulogCarsIgnored: 1,
      numberOfBlueSGCarsCreatedToReplaceOldOnes: 0,
    };

    beforeAll(async () => {
      mockVulogCredential(app);

      mockAuth();

      await carService.saveAll(cars);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });
    });

    it('Test verification', async () => {
      const result = await updateCarsBatchService.updateStaticCarInformation();

      // Check general result
      expect(result).toEqual(expected_Result);

      const resultCars: Car[] = await carService.getAll();

      expect(resultCars.length).toEqual(1);

      expect(resultCars[0]).toEqual(cars[0]);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('💦 Batch job run failed - A Vulog car has same model and (VIN or plate) with existing car, but different Vulog Car ID, and existing car is inactive or is_active = null or Vulog static car is archived', () => {
    const existingVulogCarStaticDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withArchived(false)
        .withModel(CarModelEnum.BlueCar)
        .build();
    const problematicVulogCarStaticDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withVin(existingVulogCarStaticDto.vin)
        .withPlate(existingVulogCarStaticDto.plate)
        .withModel(existingVulogCarStaticDto.model.name as CarModelEnum)
        .withArchived(true)
        .build();

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [
      existingVulogCarStaticDto,
      problematicVulogCarStaticDto,
    ];
    const cars: Car[] = [
      existingVulogCarStaticDto
        .toStaticCar()
        .toCar(new CarId(aUUID()), { isPlugged: null }),
    ];

    const expected_Result = {
      numberOfVulogCars: vulogStaticCarDtos.length,
      numberOfVulogCarsIgnored: 1,
      numberOfBlueSGCars: cars.length,
      numberOfBlueSGCarsUpdated: 1,
      numberOfBlueSGCarsCreated: 0,
      numberOfBlueSGCarsCreatedToReplaceOldOnes: 0,
    };

    beforeAll(async () => {
      mockVulogCredential(app);

      mockAuth();

      await carService.saveAll(cars);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });
    });

    it('Test verification', async () => {
      const result = await updateCarsBatchService.updateStaticCarInformation();

      // Check general result
      expect(result).toEqual(expected_Result);

      const resultCars: Car[] = await carService.getAll();

      expect(resultCars.length).toEqual(1);

      expect(resultCars[0]).toEqual(cars[0]);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('🔥 Batch job run successfully - A Vulog car has same model and (VIN or plate) with existing car, but different Vulog Car ID, and existing car is inactive, Vulog static car is not archived', () => {
    const existingCarId = aUUID();

    const existingVulogCarStaticDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withArchived(true)
        .withModel(CarModelEnum.BlueCar)
        .build();
    const toReplaceExistingOneVulogCarStaticDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withVin(existingVulogCarStaticDto.vin)
        .withPlate(existingVulogCarStaticDto.plate)
        .withModel(existingVulogCarStaticDto.model.name as CarModelEnum)
        .withArchived(false)
        .build();

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [
      existingVulogCarStaticDto,
      toReplaceExistingOneVulogCarStaticDto,
    ];
    const cars: Car[] = [
      existingVulogCarStaticDto
        .toStaticCar()
        .toCar(new CarId(existingCarId), { isPlugged: null }),
    ];

    const expected_Result = {
      numberOfVulogCars: vulogStaticCarDtos.length,
      numberOfVulogCarsIgnored: 0,
      numberOfBlueSGCars: 2,
      numberOfBlueSGCarsUpdated: 1,
      numberOfBlueSGCarsCreated: 0,
      numberOfBlueSGCarsCreatedToReplaceOldOnes: 1,
    };

    beforeAll(async () => {
      mockVulogCredential(app);

      mockAuth();

      await carService.saveAll(cars);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });
    });

    it('Test verification', async () => {
      const result = await updateCarsBatchService.updateStaticCarInformation();

      // Check general result
      expect(result).toEqual(expected_Result);

      const resultCars: Car[] = await carService.getAll();

      expect(resultCars.length).toEqual(2);

      const existingOne: Car = await carService.getOneById(
        new CarId(existingCarId),
      );

      const createdOneToReplaceTheExisting: Car = (
        await carRepo.findOne({
          where: {
            plate: existingOne.plate.value,
            vin: existingOne.vin,
            isActive: true,
          },
        })
      ).toCar();

      expect(existingOne.isActive).toEqual(false);

      expect(createdOneToReplaceTheExisting.isActive).toEqual(true);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('🔥 Batch job run successfully - A Vulog car has same model and (VIN or plate) with existing car, but different Vulog Car ID, and existing car is active, but existing Vulog static car is archived, new Vulog static car is not archived', () => {
    const existingCarId = aUUID();

    const existingVulogCarStaticDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withArchived(true)
        .withModel(CarModelEnum.BlueCar)
        .build();
    const toReplaceExistingOneVulogCarStaticDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withVin(existingVulogCarStaticDto.vin)
        .withPlate(existingVulogCarStaticDto.plate)
        .withModel(existingVulogCarStaticDto.model.name as CarModelEnum)
        .withArchived(false)
        .build();

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [
      existingVulogCarStaticDto,
      toReplaceExistingOneVulogCarStaticDto,
    ];

    const existingCarEntity = existingVulogCarStaticDto
      .toStaticCar()
      .toCar(new CarId(existingCarId), { isPlugged: null });
    existingCarEntity.isActive = true; // existing car is still active

    const cars: Car[] = [existingCarEntity];

    const expected_Result = {
      numberOfVulogCars: vulogStaticCarDtos.length,
      numberOfVulogCarsIgnored: 0,
      numberOfBlueSGCars: 2,
      numberOfBlueSGCarsUpdated: 2,
      numberOfBlueSGCarsCreated: 0,
      numberOfBlueSGCarsCreatedToReplaceOldOnes: 1,
    };

    beforeAll(async () => {
      mockVulogCredential(app);

      mockAuth();

      await carService.saveAll(cars);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });
    });

    it('Test verification', async () => {
      const result = await updateCarsBatchService.updateStaticCarInformation();

      // Check general result
      expect(result).toEqual(expected_Result);

      const resultCars: Car[] = await carService.getAll();

      expect(resultCars.length).toEqual(2);

      const existingOne: Car = await carService.getOneById(
        new CarId(existingCarId),
      );

      const createdOneToReplaceTheExisting: Car = (
        await carRepo.findOne({
          where: {
            plate: existingOne.plate.value,
            vin: existingOne.vin,
            isActive: true,
          },
        })
      ).toCar();

      expect(existingOne.isActive).toEqual(false);

      expect(createdOneToReplaceTheExisting.isActive).toEqual(true);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('🔥 Batch job run successfully - Existing vulog static car has different VIN or plate than existing car', () => {
    const existingCarId = aUUID();

    const existingVulogCarStaticDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withArchived(true)
        .withModel(CarModelEnum.BlueCar)
        .build();

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [
      existingVulogCarStaticDto,
    ];

    const existingCar: Car = existingVulogCarStaticDto
      .toStaticCar()
      .toCar(new CarId(existingCarId), { isPlugged: null });
    existingCar.vin = aString(); // Different
    existingCar.plate = new CarPlateNumber(aString()); // Different

    const cars: Car[] = [existingCar];

    const expected_Result = {
      numberOfVulogCars: vulogStaticCarDtos.length,
      numberOfVulogCarsIgnored: 0,
      numberOfBlueSGCars: 1,
      numberOfBlueSGCarsUpdated: 1,
      numberOfBlueSGCarsCreated: 0,
      numberOfBlueSGCarsCreatedToReplaceOldOnes: 0,
    };

    beforeAll(async () => {
      mockVulogCredential(app);

      mockAuth();

      await carService.saveAll(cars);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });
    });

    it('Test verification', async () => {
      const result = await updateCarsBatchService.updateStaticCarInformation();

      // Check general result
      expect(result).toEqual(expected_Result);

      const resultCars: Car[] = await carService.getAll();

      expect(resultCars.length).toEqual(1);

      expect(resultCars[0].plate.value).toEqual(
        existingVulogCarStaticDto.plate,
      );

      expect(resultCars[0].vin).toEqual(existingVulogCarStaticDto.vin);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('🔥 Batch job run successfully - A Vulog car has same model and (VIN or plate) with existing car, but different Vulog Car ID, and existing car is active, but existing Vulog static car is archived, new Vulog static car is archived', () => {
    const existingCarId = aUUID();

    const existingVulogCarStaticDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withArchived(true)
        .withModel(CarModelEnum.BlueCar)
        .build();
    const toReplaceExistingOneVulogCarStaticDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withVin(existingVulogCarStaticDto.vin)
        .withPlate(existingVulogCarStaticDto.plate)
        .withModel(existingVulogCarStaticDto.model.name as CarModelEnum)
        .withArchived(true)
        .build();

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [
      existingVulogCarStaticDto,
      toReplaceExistingOneVulogCarStaticDto,
    ];

    const existingCarEntity = existingVulogCarStaticDto
      .toStaticCar()
      .toCar(new CarId(existingCarId), { isPlugged: null });
    existingCarEntity.isActive = false; // existing car is still active

    const cars: Car[] = [existingCarEntity];

    const expected_Result = {
      numberOfVulogCars: vulogStaticCarDtos.length,
      numberOfVulogCarsIgnored: 0,
      numberOfBlueSGCars: 2,
      numberOfBlueSGCarsUpdated: 2,
      numberOfBlueSGCarsCreated: 0,
      numberOfBlueSGCarsCreatedToReplaceOldOnes: 1,
    };

    beforeAll(async () => {
      mockVulogCredential(app);

      mockAuth();

      await carService.saveAll(cars);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });
    });

    it('Test verification', async () => {
      const result = await updateCarsBatchService.updateStaticCarInformation();

      // Check general result
      expect(result).toEqual(expected_Result);

      const resultCars: Car[] = await carService.getAll();

      expect(resultCars.length).toEqual(2);

      const existingOne: Car = await carService.getOneById(
        new CarId(existingCarId),
      );

      const createdOneToReplaceTheExisting: Car = (
        await carRepo.findOne({
          where: {
            plate: existingOne.plate.value,
            vin: existingOne.vin,
            isActive: false,
          },
        })
      ).toCar();

      expect(existingOne.isActive).toEqual(false);

      expect(createdOneToReplaceTheExisting.isActive).toEqual(false);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });
});
