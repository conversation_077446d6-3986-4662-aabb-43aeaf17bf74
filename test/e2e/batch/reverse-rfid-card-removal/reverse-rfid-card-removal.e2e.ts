import { INestApplication } from '@nestjs/common';

import { faker } from '@faker-js/faker';
import { getRepositoryToken } from '@nestjs/typeorm';
import MockAdapter from 'axios-mock-adapter';
import { Counter } from 'src/batch/reverse-rfid-card-removal/counter';
import { ReverseRfidCardRemovalBatchReportEntity } from 'src/batch/reverse-rfid-card-removal/reverse-rfid-card-removal-batch-report.entity';
import { ReverseRfidCardRemovalAppModule } from 'src/batch/reverse-rfid-card-removal/reverse-rfid-card-removal.app.module';
import { ReverseRfidCardRemovalBatchService } from 'src/batch/reverse-rfid-card-removal/reverse-rfid-card-removal.batch.service';
import { VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { config } from 'src/config';
import { CacheService } from 'src/logic/cache/cache.service';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogUserService } from 'src/logic/vulog/vulog-user.service';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { VulogUserStatus } from 'src/model/vulog-user';
import { AppBuilder } from 'test/helpers/app.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { VulogUserBuilder } from 'test/helpers/builders/vulog-user.builder';
import { UssApiMocker } from 'test/helpers/external-services/uss.api-mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';
import { Repository } from 'typeorm';

const email = faker.internet.email();

jest.setTimeout(3600000);

// TODO: The job is never deployed to PROD. Safe to skip
describe.skip(`[E2E] Reverse RFID Card Removal`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let vulogUserRepo: Repository<VulogUserEntity>;
  let vulogUserService: VulogUserService;
  let reportRepository: Repository<ReverseRfidCardRemovalBatchReportEntity>;
  let reportService: ReverseRfidCardRemovalBatchService;
  let cacheService: CacheService;
  let mockOutgoingService: MockAdapter;
  let mockUssApiService: MockAdapter;
  let counter: Counter;

  const mockAuthResponse = createTestVulogAuthResponse();

  beforeAll(async () => {
    app = await appBuilder.buildAndStart(ReverseRfidCardRemovalAppModule);

    counter = await app.get<Counter>(Counter);

    vulogUserRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );
    vulogUserService = app.get<VulogUserService>(VulogUserService);
    reportRepository = app.get<
      Repository<ReverseRfidCardRemovalBatchReportEntity>
    >(getRepositoryToken(ReverseRfidCardRemovalBatchReportEntity));
    cacheService = app.get<CacheService>(CacheService);
    const httpApdaterService = app.get<HttpAdapterService>(HttpAdapterService);
    mockOutgoingService = new MockAdapter(
      httpApdaterService.httpService.axiosRef,
      { onNoMatch: 'throwException' },
    );

    reportService = app.get<ReverseRfidCardRemovalBatchService>(
      ReverseRfidCardRemovalBatchService,
    );
  });

  function mockAuth() {
    mockUssApiService = new UssApiMocker(app).mock();

    const userDto = createTestVulogUserDto();

    userDto.email = email;

    mockOutgoingService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, userDto);

    const userServicesAndProfiles = createTestVulogUserServicesDto();
    userServicesAndProfiles.userId = userDto.id;

    /**
     * Doing Vulog authentication mock
     */
    mockOutgoingService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(userDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    mockOutgoingService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, mockAuthResponse);
  }

  afterAll(async () => {
    await cleanUpResources();

    await app.close();

    mockOutgoingService.reset();
    mockUssApiService.reset();
  });

  async function cleanUpResources(): Promise<void> {
    await cacheService.clearAll();
    await vulogUserRepo.delete({});
    await reportRepository.delete({});
    counter.reset();

    mockOutgoingService.reset();

    jest.resetAllMocks();
  }

  describe('🔥 Batch job run successfully - total 10 items (some are satisfied, some dont), 5 items per iteration', () => {
    config.batchConfig.reverseRfidCardRemoval.processedPerIteration = 5;

    const expectedTotal = 5;
    const expectedProcessed = 5;
    const expectedErrors = '';

    const expectedVulogUserIdsHavingRfidDisabled = [
      aUUID(),
      aUUID(),
      aUUID(),
      aUUID(),
      aUUID(),
    ];

    const vulogUsers = [
      new VulogUserBuilder()
        .withId(new VulogUserId(expectedVulogUserIdsHavingRfidDisabled[0]))
        .withStatus(VulogUserStatus.SYNCHRONIZED) // valid
        .rfidDisabled()
        .build(),
      new VulogUserBuilder()
        .withId(new VulogUserId(expectedVulogUserIdsHavingRfidDisabled[1]))
        .withStatus(VulogUserStatus.SYNCHRONIZED) // valid
        .rfidDisabled()
        .build(),
      new VulogUserBuilder()
        .withId(new VulogUserId(expectedVulogUserIdsHavingRfidDisabled[2]))
        .withStatus(VulogUserStatus.UPDATING) // valid
        .rfidDisabled()
        .build(),
      new VulogUserBuilder()
        .withId(new VulogUserId(expectedVulogUserIdsHavingRfidDisabled[3]))
        .withStatus(VulogUserStatus.UPDATING) // valid
        .rfidDisabled()
        .build(),
      new VulogUserBuilder()
        .withId(new VulogUserId(expectedVulogUserIdsHavingRfidDisabled[4]))
        .withStatus(VulogUserStatus.UPDATING) // valid
        .rfidDisabled()
        .build(),
      new VulogUserBuilder()
        .withStatus(VulogUserStatus.CREATING)
        .rfidDisabled()
        .build(),
      new VulogUserBuilder()
        .withStatus(VulogUserStatus.WAITING_FOR_PROFILE)
        .rfidDisabled()
        .build(),
      new VulogUserBuilder()
        .withStatus(VulogUserStatus.CREATING)
        .rfidDisabled()
        .build(),
      new VulogUserBuilder()
        .withStatus(VulogUserStatus.WAITING_FOR_PROFILE)
        .rfidDisabled()
        .build(),
      new VulogUserBuilder()
        .withStatus(VulogUserStatus.WAITING_FOR_PROFILE)
        .rfidDisabled()
        .build(),
    ];

    const expectedVulogUserIdsUntouched = vulogUsers
      .filter(
        (vgUser) =>
          !expectedVulogUserIdsHavingRfidDisabled.includes(vgUser.id.value),
      )
      .map((vgUser) => vgUser.id.value);

    beforeAll(async () => {
      mockVulogCredential(app);

      mockAuth();

      // Create Vulog Users
      await Promise.all(
        vulogUsers.map((vgUser) => vulogUserService.save(vgUser)),
      );

      // Mock update profile if RFID is enabled
      vulogUsers
        .map((vgUser) => vgUser.profileId.value)
        .forEach((profileId) => {
          mockOutgoingService
            .onPost(
              new RegExp(
                `/user/fleets/${config.vulogConfig.fleetId.value}/profiles/${profileId}`,
              ),
            )
            .replyOnce(200);
        });

      await reportService.run();
    });

    it('The reporting numbers are correct', async () => {
      const [report] = await reportRepository.find({
        order: { createdAt: 'DESC' },
      });

      const { total, processed, errors } = report;

      expect(total).toEqual(expectedTotal);

      expect(processed).toEqual(expectedProcessed);

      expect(errors).toEqual(expectedErrors);
    });

    it('Affected records were RFID-enabled', async () => {
      const vulogUsers = (await vulogUserRepo.find()).map((vgUserEntity) =>
        vgUserEntity.toVulogUser(),
      );

      const vulogUsersHavingRfidEnabled = vulogUsers.filter(
        (vgUser) => !vgUser.isRfidDisabled(),
      );
      const vulogUsersHavingRfidEnabledIds = vulogUsersHavingRfidEnabled.map(
        (vgUser) => vgUser.id.value,
      );
      const vulogUsersUntouched = vulogUsers.filter(
        (vgUser) => !vulogUsersHavingRfidEnabledIds.includes(vgUser.id.value),
      );
      const vulogUsersUntouchedIds = vulogUsersUntouched.map(
        (vgUser) => vgUser.id.value,
      );

      expect(vulogUsersHavingRfidEnabledIds.length).toEqual(expectedTotal);

      expect(vulogUsersHavingRfidEnabledIds.length).toEqual(expectedProcessed);

      expect(vulogUsersHavingRfidEnabledIds.sort()).toEqual(
        expectedVulogUserIdsHavingRfidDisabled.sort(),
      );

      expect(vulogUsersUntouchedIds.sort()).toEqual(
        expectedVulogUserIdsUntouched.sort(),
      );
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe.skip('🔥 Batch job run successfully - total 200 items (all satisfied), 10 items per iteration', () => {
    config.batchConfig.reverseRfidCardRemoval.processedPerIteration = 10;

    const expectedTotal = 200;
    const expectedProcessed = expectedTotal;
    const expectedErrors = '';

    const vulogUsers = Array.from({
      length: expectedTotal,
    }).map(() =>
      new VulogUserBuilder()
        .withStatus(VulogUserStatus.SYNCHRONIZED)
        .rfidDisabled()
        .build(),
    );

    beforeAll(async () => {
      mockVulogCredential(app);

      mockAuth();

      // Create Vulog Users
      await Promise.all(
        vulogUsers.map((vgUser) => vulogUserService.save(vgUser)),
      );

      // Mock update profile if RFID is enabled
      vulogUsers
        .map((vgUser) => vgUser.profileId.value)
        .forEach((profileId) => {
          mockOutgoingService
            .onPost(
              new RegExp(
                `/user/fleets/${config.vulogConfig.fleetId.value}/profiles/${profileId}`,
              ),
            )
            .replyOnce(200);
        });

      await reportService.run();
    });

    it('The reporting numbers are correct', async () => {
      const [report] = await reportRepository.find({
        order: { createdAt: 'DESC' },
      });

      const { total, processed, errors } = report;

      expect(total).toEqual(expectedTotal);

      expect(processed).toEqual(expectedProcessed);

      expect(errors).toEqual(expectedErrors);
    });

    it('Affected records were RFID-enabled', async () => {
      const vulogUsers = (await vulogUserRepo.find()).map((vgUserEntity) =>
        vgUserEntity.toVulogUser(),
      );

      const vulogUsersHavingRfidEnabled = vulogUsers.filter(
        (vgUser) => !vgUser.isRfidDisabled(),
      );
      const vulogUsersHavingRfidEnabledIds = vulogUsersHavingRfidEnabled.map(
        (vgUser) => vgUser.id.value,
      );
      const vulogUsersUntouched = vulogUsers.filter(
        (vgUser) => !vulogUsersHavingRfidEnabledIds.includes(vgUser.id.value),
      );
      const vulogUsersUntouchedIds = vulogUsersUntouched.map(
        (vgUser) => vgUser.id.value,
      );

      expect(vulogUsersHavingRfidEnabledIds.length).toEqual(expectedTotal);

      expect(vulogUsersHavingRfidEnabledIds.length).toEqual(expectedProcessed);

      expect(vulogUsersHavingRfidEnabledIds.sort()).toEqual(
        vulogUsers.map((vgUser) => vgUser.id.value).sort(),
      );

      expect(vulogUsersUntouchedIds).toEqual([]);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe.skip('🔥 Batch job run successfully - total 220 items (some are satisfied, some dont, some throw error), 10 items per iteration', () => {
    config.batchConfig.reverseRfidCardRemoval.processedPerIteration = 10;

    const expectedTotal = 210;
    const expectedProcessed = 200;

    const vulogUsersSatisfied = Array.from({
      length: expectedProcessed,
    }).map(() =>
      new VulogUserBuilder()
        .withStatus(VulogUserStatus.SYNCHRONIZED)
        .rfidDisabled()
        .build(),
    );
    const expVulogUsersSatisfiedIds = vulogUsersSatisfied.map(
      (vgUser) => vgUser.id.value,
    );
    const vulogUsersSatisfiedProfileIds = vulogUsersSatisfied.map(
      (vgUser) => vgUser.profileId.value,
    );

    const vulogUsersNotSatisfied = Array.from({
      length: 10,
    }).map(() =>
      new VulogUserBuilder()
        .withStatus(VulogUserStatus.CREATING)
        .rfidDisabled()
        .build(),
    );
    const expVulogUsersNotSatisfiedIds = vulogUsersNotSatisfied.map(
      (vgUser) => vgUser.id.value,
    );

    const vulogUsersThrowError = Array.from({
      length: 10,
    }).map(() =>
      new VulogUserBuilder()
        .withStatus(VulogUserStatus.UPDATING)
        .rfidDisabled()
        .build(),
    );
    const expVulogUsersThrowErrorIds = vulogUsersThrowError.map(
      (vgUser) => vgUser.id.value,
    );
    const vulogUsersThrowErrorProfileIds = vulogUsersThrowError.map(
      (vgUser) => vgUser.profileId.value,
    );

    beforeAll(async () => {
      mockVulogCredential(app);

      mockAuth();

      // Create Vulog Users
      await Promise.all(
        vulogUsersSatisfied.map((vgUser) => vulogUserService.save(vgUser)),
      );
      await Promise.all(
        vulogUsersNotSatisfied.map((vgUser) => vulogUserService.save(vgUser)),
      );
      await Promise.all(
        vulogUsersThrowError.map((vgUser) => vulogUserService.save(vgUser)),
      );

      // Mock update profile if RFID is enabled
      vulogUsersSatisfiedProfileIds.forEach((profileId) => {
        mockOutgoingService
          .onPost(
            new RegExp(
              `/user/fleets/${config.vulogConfig.fleetId.value}/profiles/${profileId}`,
            ),
          )
          .replyOnce(200);
      });
      vulogUsersThrowErrorProfileIds.forEach((profileId) => {
        mockOutgoingService
          .onPost(
            new RegExp(
              `/user/fleets/${config.vulogConfig.fleetId.value}/profiles/${profileId}`,
            ),
          )
          .replyOnce(500);
      });

      await reportService.run();
    });

    it('The reporting numbers are correct', async () => {
      const [report] = await reportRepository.find({
        order: { createdAt: 'DESC' },
      });

      const { total, processed, errors } = report;

      expect(total).toEqual(expectedTotal);

      expect(processed).toEqual(expectedProcessed);

      expect(
        expVulogUsersThrowErrorIds.every((vgUserId) =>
          errors.includes(vgUserId),
        ),
      ).toEqual(true);
    });

    it('Affected records were RFID-enabled', async () => {
      const vulogUsers = (await vulogUserRepo.find()).map((vgUserEntity) =>
        vgUserEntity.toVulogUser(),
      );

      const vulogUsersHavingRfidEnabled = vulogUsers.filter(
        (vgUser) => !vgUser.isRfidDisabled(),
      );
      const vulogUsersHavingRfidEnabledIds = vulogUsersHavingRfidEnabled.map(
        (vgUser) => vgUser.id.value,
      );
      const vulogUsersUntouched = vulogUsers.filter(
        (vgUser) => !vulogUsersHavingRfidEnabledIds.includes(vgUser.id.value),
      );
      const vulogUsersUntouchedIds = vulogUsersUntouched.map(
        (vgUser) => vgUser.id.value,
      );

      expect(vulogUsersHavingRfidEnabledIds.length).toEqual(expectedProcessed);

      expect(vulogUsersHavingRfidEnabledIds.sort()).toEqual(
        expVulogUsersSatisfiedIds.sort(),
      );

      expect(vulogUsersUntouchedIds.sort()).toEqual(
        [...expVulogUsersNotSatisfiedIds, ...expVulogUsersThrowErrorIds].sort(),
      );
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });
});
