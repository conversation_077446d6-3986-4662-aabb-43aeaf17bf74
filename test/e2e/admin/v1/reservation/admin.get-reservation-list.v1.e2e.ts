import { PaginationSortingOrder } from '@bluesg-2/queue-pop/dist/constants/pagination';
import { HttpStatus, INestApplication } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import { cloneDeep, isUndefined, omitBy } from 'lodash';
import { DateTime } from 'luxon';
import * as qs from 'qs';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { CarPlateNumber } from 'src/common/tiny-types';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { AdminGetListReservationReqDtoV1 } from 'src/controllers/admin/v1/reservation/dto/request/admin.get-list-reservations.v1.req.dto';
import { AdminGetListReservationResDtoV1 } from 'src/controllers/admin/v1/reservation/dto/response/admin.get-list-reservations.v1.res.dto';
import { SubscriptionUssDto } from 'src/external/user-subscription/dtos/response/subscription.uss.dto';
import { UserSubscriptionExternalService } from 'src/external/user-subscription/user-subscription.external.service';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { StationService } from 'src/logic/station/station.service';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { ReservationRepository } from 'src/model/repositories/reservation.repository';
import { Reservation } from 'src/model/reservation.domain';
import { Station } from 'src/model/station';
import * as supertest from 'supertest';
import { ADMIN_JWT_MOCK_TOKEN } from 'test/helpers/admin-jwt-guard.mocker';
import { AppBuilder } from 'test/helpers/app.builder';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { SubscriptionUssDtoBuilder } from 'test/helpers/builders/dtos/uss/subscription.uss-dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { StationBuilder } from 'test/helpers/builders/station.builder';
import { VulogUserBuilder } from 'test/helpers/builders/vulog-user.builder';
import {
  anEmail,
  anEnumValue,
  aString,
  aUUID,
} from 'test/helpers/random-data.helper';
import { Repository } from 'typeorm';

const testingApiRoute = ApiRouteUtils.buildApiRouteSegment(
  '' as any,
  '' as any,
  [
    'api',
    'v1',
    RentalServiceRootRouteSegment.Admin,
    RouteSegment.Reservation.Index,
  ],
);

const authorizedUserToken = JSON.stringify({
  id: aUUID(), // See why: https://github.com/BlueSG-2/bff-authentication/commit/c4c671086c280ce9bbd376f0006eaa105c5ae150
});

describe(`[E2E] GET: ${testingApiRoute}`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let reserveRepo: Repository<ReservationEntity>;
  let reserveCustomRepo: ReservationRepository;
  let vulogUserRepo: Repository<VulogUserEntity>;
  let carRepo: Repository<CarEntity>;

  let stationService: StationService;
  let ussService: UserSubscriptionExternalService;

  let vulogUser: VulogUserEntity;
  let car: CarEntity;

  function requestFor(
    query: Partial<AdminGetListReservationReqDtoV1> = {},
  ): supertest.Test {
    const superTest = supertest(app.getHttpServer())
      .get(`${testingApiRoute}?${qs.stringify(query)}`)
      .set(ADMIN_JWT_MOCK_TOKEN, authorizedUserToken);

    return superTest;
  }

  beforeAll(async () => {
    app = await appBuilder.buildAndStart();

    reserveRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );
    vulogUserRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );
    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));
    reserveCustomRepo = app.get<ReservationRepository>(ReservationRepository);
    stationService = app.get<StationService>(StationService);
    ussService = app.get<UserSubscriptionExternalService>(
      UserSubscriptionExternalService,
    );

    vulogUser = await vulogUserRepo.save(
      VulogUserEntity.from(new VulogUserBuilder().build()),
    );
    car = await carRepo.save(CarEntity.from(new CarBuilder().build()));
  });

  afterAll(async () => {
    await clean();

    await app.close();
  });

  async function clean(): Promise<void> {
    await reserveRepo.delete({});
    await vulogUserRepo.delete({});
    await carRepo.delete({});
  }

  // NOTE: mock station and return the id
  function mockStation(reserves: Reservation[]): string[] {
    const ids = reserves
      .map((reserve) => [
        reserve.startStationId?.value,
        reserve.endStationId?.value,
      ])
      .flat(1)
      .filter((exist) => !!exist);

    const arr = Array.from(new Set(ids));

    jest.spyOn(stationService, 'getAllStations').mockResolvedValue(
      arr.map((stationId) => {
        return new StationBuilder().withId(stationId).build();
      }),
    );

    return arr;
  }

  function mockGetListSubApi(reserves: Reservation[]): SubscriptionUssDto[] {
    const response = reserves
      .map((rev) => {
        return rev.bsgUserId.value;
      })
      .map((id) => {
        return new SubscriptionUssDtoBuilder()
          .withUser(
            new InternalUserDetailResponseDtoV1Builder().withId(id).build(),
          )
          .build();
      });

    jest.spyOn(ussService, 'getListSubscriptions').mockResolvedValue(response);
    return response;
  }

  describe('When there is no filter', () => {
    let response: supertest.Response;
    let reserves: Reservation[];

    beforeAll(async () => {
      const listUser = await vulogUserRepo.save([
        VulogUserEntity.from(new VulogUserBuilder().build()),
        VulogUserEntity.from(new VulogUserBuilder().build()),
        VulogUserEntity.from(new VulogUserBuilder().build()),
        VulogUserEntity.from(new VulogUserBuilder().build()),
        VulogUserEntity.from(new VulogUserBuilder().build()),
      ]);

      await reserveRepo.save(
        Array.from({
          length: 5,
        }).map((_, index) =>
          new ReservationEntityBuilder()
            .makeCarId(car.vulogId)
            .makeBsgUserId(listUser[index].bsgUserId)
            .makeVulogTripId(null)
            .makeStatus(anEnumValue(ReservationStatus))
            .build(),
        ),
      );

      [reserves] = await reserveCustomRepo.getListReservations(
        new AdminGetListReservationReqDtoV1(),
      );

      jest
        .spyOn(stationService, 'getAllStations')
        .mockResolvedValue(new Array<Station>());
      jest.spyOn(ussService, 'getListSubscriptions').mockResolvedValue(null);

      response = await requestFor();
    });

    afterAll(async () => {
      jest.clearAllMocks();
      await clean();
    });

    it('should return the HTTP status OK', () => {
      expect(response.status).toBe(HttpStatus.OK);
    });

    it('should return the pagination details', () => {
      expect(response.body.pagination).toEqual({
        page: 1,
        pageSize: 10,
        total: 5,
        totalPages: 1,
        sortKey: 'startedAt',
        sortType: 'DESC',
      });
    });

    it('should return the list of all the reservations', async () => {
      expect(response.body.result.length).toBe(reserves.length);

      response.body.result.map((reserve) => {
        expect(reserve.subscription).toBeFalsy();
        expect(reserve.startStation).toBeFalsy();
        expect(reserve.subscription?.user).toBeFalsy();
      });

      expect(response.body.result).toMatchObject(
        AdminGetListReservationResDtoV1.from(
          reserves,
          new Map<string, Station>(),
          [],
        ).map((rev) => ({
          ...rev,
          car: omitBy(rev.car, isUndefined),
        })),
      );
    });
  });

  describe('When there is load filter', () => {
    let response: supertest.Response;
    let reserves: Reservation[];
    let mockSubs: SubscriptionUssDto[];

    beforeAll(async () => {
      const listUser = await vulogUserRepo.save([
        VulogUserEntity.from(new VulogUserBuilder().build()),
        VulogUserEntity.from(new VulogUserBuilder().build()),
        VulogUserEntity.from(new VulogUserBuilder().build()),
        VulogUserEntity.from(new VulogUserBuilder().build()),
        VulogUserEntity.from(new VulogUserBuilder().build()),
      ]);

      await reserveRepo.save(
        Array.from({
          length: 5,
        }).map((_, index) =>
          new ReservationEntityBuilder()
            .makeCarId(car.vulogId)
            .makeBsgUserId(listUser[index].bsgUserId)
            .build(),
        ),
      );

      const reqDto = new AdminGetListReservationReqDtoV1();
      reqDto.loadStation = true;
      reqDto.loadUserAndSubscription = true;

      [reserves] = await reserveCustomRepo.getListReservations(reqDto);

      mockStation(
        reserves.map((reserve) => Reservation.from(reserve.toEntity())),
      );
      mockSubs = cloneDeep(mockGetListSubApi(reserves));
      response = await requestFor(reqDto);
    });

    afterAll(async () => {
      jest.clearAllMocks();
      await clean();
    });

    it('should return the HTTP status OK', () => {
      expect(response.status).toBe(HttpStatus.OK);
    });

    it('should return the pagination details', () => {
      expect(response.body.pagination).toEqual({
        page: 1,
        pageSize: 10,
        total: 5,
        totalPages: 1,
        sortKey: 'startedAt',
        sortType: 'DESC',
      });
    });

    it('should return the list of all the users', async () => {
      const map = await stationService.getStationsMap();
      expect(response.body.result.length).toBe(reserves.length);

      response.body.result.map((reserve) => {
        expect(reserve.subscription).toBeTruthy();
        expect(reserve.startStation).toBeTruthy();
        expect(reserve.subscription?.user).toBeFalsy();
      });

      expect(response.body.result).toMatchObject(
        AdminGetListReservationResDtoV1.from(reserves, map, mockSubs).map(
          (rev) => {
            const temp = {
              ...rev,
              startStation: rev.startStation || null,
              endStation: rev.endStation || null,
            };

            delete temp.car;

            return temp;
          },
        ),
      );
    });
  });

  describe('When using pagination', () => {
    let response: supertest.Response;
    const firstCreatedAt = DateTime.now();
    const firstId: string = aUUID();

    beforeAll(async () => {
      const reserves = await reserveRepo.save([
        new ReservationEntityBuilder()
          .makeId(firstId)
          .makeStartedAt(firstCreatedAt.toJSDate())
          .makeCarId(car.vulogId)
          .makeBsgUserId(vulogUser.bsgUserId)
          .build(),
        new ReservationEntityBuilder()
          .makeStartedAt(firstCreatedAt.plus({ days: 1 }).toJSDate())
          .makeCarId(car.vulogId)
          .makeBsgUserId(vulogUser.bsgUserId)
          .build(),
        new ReservationEntityBuilder()
          .makeStartedAt(firstCreatedAt.plus({ days: 1 }).toJSDate())
          .makeCarId(car.vulogId)
          .makeBsgUserId(vulogUser.bsgUserId)
          .build(),
      ]);

      mockStation(reserves.map((reserve) => Reservation.from(reserve)));

      response = await requestFor({
        sortKey: 'startedAt',
        sortType: PaginationSortingOrder.ASCENDING,
        pageSize: 2,
      });
    });

    afterAll(async () => {
      await clean();
      jest.clearAllMocks();
    });

    it('should return the HTTP status OK', () => {
      expect(response.status).toBe(HttpStatus.OK);
    });

    it('should return the pagination details', () => {
      expect(response.body.pagination).toEqual({
        page: 1,
        pageSize: 2,
        total: 3,
        totalPages: 2,
        sortKey: 'startedAt',
        sortType: 'ASC',
      });
    });

    it('Should have the right first result', () => {
      expect(response.body.result[0].id).toStrictEqual(firstId);
    });
  });

  describe('When using the search filter', () => {
    let response: supertest.Response;
    const testId: string = aUUID();

    const earliestReserveId = aUUID();
    const newestReserveId = aUUID();
    const toBeFoundPlateNUm = aString(10);
    const toBeFoundVulogId = aUUID();
    const toBeFoundUserFullName = aString(10);
    const toBeFoundUserEmail = anEmail();

    beforeAll(async () => {
      const toBeFoundCar = await carRepo.save(
        CarEntity.from(
          new CarBuilder()
            .withPlate(new CarPlateNumber(toBeFoundPlateNUm))
            .build(),
        ),
      );

      const reserves = await reserveRepo.save([
        new ReservationEntityBuilder()
          .makeStatus(ReservationStatus.Cancelled)
          .makeCreatedAt(new Date(2001, 1, 1))
          .makeId(earliestReserveId)
          .makeCarId(toBeFoundCar.vulogId)
          .makeVulogTripId(toBeFoundVulogId)
          .makeUserFullName(toBeFoundUserFullName)
          .makeUserEmail(toBeFoundUserEmail)
          .build(),
        new ReservationEntityBuilder()
          .makeStatus(ReservationStatus.Converted)
          .makeBsgUserId(testId)
          .makeCreatedAt(new Date(2001, 2, 2))
          .makeCarId(car.vulogId)
          .build(),
        new ReservationEntityBuilder()
          .makeStatus(ReservationStatus.Creating)
          .makeCreatedAt(new Date(2001, 3, 3))
          .makeId(newestReserveId)
          .makeCarId(car.vulogId)
          .build(),
      ]);

      mockStation(reserves.map((reserve) => Reservation.from(reserve)));
    });

    afterAll(async () => {
      await clean();
      jest.clearAllMocks();
    });

    it.each([
      {
        filterField: 'status',
        compareField: 'status',
        value: [ReservationStatus.Cancelled],
        expected: ReservationStatus.Cancelled,
      },
      {
        filterField: 'from',
        compareField: 'id',
        value: new Date(2001, 3, 3),
        expected: newestReserveId,
      },
      {
        filterField: 'to',
        compareField: 'id',
        value: new Date(2001, 1, 1),
        expected: earliestReserveId,
      },
      {
        filterField: 'bsgUserId',
        compareField: 'bsgUserId',
        value: testId,
        expected: testId,
      },
      {
        filterField: 'rentalId',
        compareField: 'id',
        value: earliestReserveId,
        expected: earliestReserveId,
      },
      {
        filterField: 'plateNumber',
        compareField: 'id',
        value: toBeFoundPlateNUm.substring(1, 5),
        expected: earliestReserveId,
      },
      {
        filterField: 'vulogTripId',
        compareField: 'id',
        value: toBeFoundVulogId.substring(1, 5),
        expected: earliestReserveId,
      },
      {
        filterField: 'userFullName',
        compareField: 'id',
        value: toBeFoundUserFullName.substring(1, 5),
        expected: earliestReserveId,
      },
      {
        filterField: 'userEmail',
        compareField: 'id',
        value: toBeFoundUserEmail.substring(1, 5),
        expected: earliestReserveId,
      },
    ])(
      `should return the packages pass the filter field $filterField`,
      async ({ filterField, value, expected, compareField }) => {
        response = await requestFor({
          [filterField]: value,
        });

        expect(response.status).toBe(HttpStatus.OK);
        expect(response.body.result.length).toBe(1);
        expect(response.body.result[0][compareField]).toBe(expected);
      },
    );
  });

  describe('When using multiple value on status filter', () => {
    let response: supertest.Response;
    const filterStatus = [
      ReservationStatus.Converted,
      ReservationStatus.Creating,
    ];

    beforeAll(async () => {
      const reserves = await reserveRepo.save([
        new ReservationEntityBuilder()
          .makeStatus(ReservationStatus.Converted)
          .makeCarId(car.vulogId)
          .build(),
        new ReservationEntityBuilder()
          .makeStatus(ReservationStatus.EndTrip)
          .makeCarId(car.vulogId)
          .build(),
        new ReservationEntityBuilder()
          .makeStatus(ReservationStatus.Creating)
          .makeCarId(car.vulogId)
          .build(),
      ]);

      mockStation(reserves.map((reserve) => Reservation.from(reserve)));

      response = await requestFor({
        status: filterStatus,
      });
    });

    afterAll(async () => {
      await clean();
      jest.clearAllMocks();
    });

    it('Should return status OK', () => {
      expect(response.status).toBe(HttpStatus.OK);
    });

    it('Should only contain status Converted and Creating', () => {
      response.body.result.forEach((reserve) => {
        expect(filterStatus.includes(reserve.status));
      });
    });
  });

  describe('When using the station name filter', () => {
    let response: supertest.Response;
    const testId: string = aUUID();

    const stations = Array.from({
      length: 4,
    }).map(() => {
      return new StationBuilder().build();
    });

    const reserve1 = aUUID();
    const reserve2 = aUUID();

    beforeAll(async () => {
      await reserveRepo.save([
        new ReservationEntityBuilder()
          .makeId(reserve1)
          .makeStatus(ReservationStatus.Cancelled)
          .makeCreatedAt(new Date(2001, 1, 1))
          .makeCarId(car.vulogId)
          .makeStartStationId(stations[0].id.value)
          .makeEndStationId(stations[1].id.value)
          .build(),
        new ReservationEntityBuilder()
          .makeId(reserve2)
          .makeStatus(ReservationStatus.Converted)
          .makeBsgUserId(testId)
          .makeCreatedAt(new Date(2001, 2, 2))
          .makeCarId(car.vulogId)
          .makeStartStationId(stations[2].id.value)
          .makeEndStationId(stations[3].id.value)
          .build(),
      ]);

      jest.spyOn(stationService, 'getAllStations').mockResolvedValue(stations);
      jest.spyOn(ussService, 'getListSubscriptions').mockResolvedValue(null);
    });

    afterAll(async () => {
      await clean();
      jest.clearAllMocks();
    });

    it.each([
      {
        filterField: 'startStationName',
        compareField: 'id',
        value: stations[0].displayName.substring(0, 4),
        expected: reserve1,
      },
      {
        filterField: 'endStationName',
        compareField: 'id',
        value: stations[3].displayName.substring(0, 4),
        expected: reserve2,
      },
    ])(
      `should return the packages pass the filter field $filterField`,
      async ({ filterField, value, expected, compareField }) => {
        response = await requestFor({
          [filterField]: value,
        });

        expect(response.status).toBe(HttpStatus.OK);
        expect(response.body.result.length).toBe(1);
        expect(response.body.result[0][compareField]).toBe(expected);
      },
    );
  });
});
