import { HttpStatus, INestApplication } from '@nestjs/common';

import { JwtGuard } from '@bluesg-2/bo-auth-guard';
import { RouteSegment } from 'src/common/constants/api-path';
import { CacheService } from 'src/logic/cache/cache.service';
import { mockAdminJwtGuard } from 'test/helpers/admin-jwt-guard.mocker';
import { AppBuilder } from 'test/helpers/app.builder';
import { aLastName, anEmail, aUUID } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';

import { getRepositoryToken } from '@nestjs/typeorm';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { Repository } from 'typeorm';

import MockAdapter from 'axios-mock-adapter';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';

import { BsgUserId, VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { config } from 'src/config';
import { AdminCustomerIdParamDtoV1 } from 'src/controllers/admin/v1/customer/dto/request/admin.customer-id.param.dto.v1';
import { AdminStartRentalImmediatelyRequestDtoV1 } from 'src/controllers/admin/v1/customer/dto/request/admin.start-rental-immediately.request.dto.v1';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';

import * as supertest from 'supertest';
import { AttachRentalPackageRequestDtoV1Builder } from 'test/helpers/builders/dtos/attach-rental-package-request-dto.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { VulogUserBuilder } from 'test/helpers/builders/vulog-user.builder';
import { BillingApiMocker } from 'test/helpers/external-services/billing.api-mocker';
import { StationApiMocker } from 'test/helpers/external-services/station.api-mocker';
import { UssApiMocker } from 'test/helpers/external-services/uss.api-mocker';
import { VulogApiMocker } from 'test/helpers/external-services/vulog.api-mocker';

const testingApiRoute = `/api/v1/admin/${RouteSegment.Customer.Index}/${RouteSegment.Customer.StartRental}`;

const userId = aUUID();

const backOfficeUserId = aUUID();

const email = anEmail();

describe(`[E2E] POST: ${testingApiRoute}`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let mockHttpAdapterService: MockAdapter;
  let mockUssApiService: MockAdapter;
  let mockBsApiService: MockAdapter;
  let mockStsApiService: MockAdapter;

  let cacheService: CacheService;

  let vulogUserRepo: Repository<VulogUserEntity>;
  let reservationRepo: Repository<ReservationEntity>;
  let carRepo: Repository<CarEntity>;

  const testAuthResponse = createTestVulogAuthResponse();
  const testVulogUser = VulogUserBuilder.allSync()
    .withBsgUserId(new BsgUserId(userId))
    .build();

  beforeAll(async () => {
    app = await appBuilder
      .overrideProvider(JwtGuard, mockAdminJwtGuard(backOfficeUserId), true)
      .buildAndStart();

    vulogUserRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );
    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );
    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));

    cacheService = app.get(CacheService);

    mockHttpAdapterService = new VulogApiMocker(app).mock();
    mockUssApiService = new UssApiMocker(app).mock();
    mockStsApiService = new StationApiMocker(app).mock();
    mockBsApiService = new BillingApiMocker(app).mock();

    mockVulogCredential(app);
  });

  function requestFor(
    pathParams: AdminCustomerIdParamDtoV1,
    requestDto: AdminStartRentalImmediatelyRequestDtoV1,
  ): supertest.Test {
    const qualifiedUrl = testingApiRoute.replace(
      ':customerId',
      pathParams.customerId,
    );

    const superTest = supertest(app.getHttpServer())
      .post(`${qualifiedUrl}`)
      .send(requestDto);

    return superTest;
  }

  async function mockCredential() {
    const userDto = createTestVulogUserDto();

    userDto.email = email;

    mockHttpAdapterService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, userDto);

    const userServicesAndProfiles = createTestVulogUserServicesDto();
    userServicesAndProfiles.userId = userDto.id;

    mockHttpAdapterService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(userDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockHttpAdapterService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockHttpAdapterService
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    mockHttpAdapterService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, testAuthResponse);

    await vulogUserRepo.insert(VulogUserEntity.from(testVulogUser));

    mockVulogCredential(app);
  }

  afterAll(async () => {
    await cleanUpResources();

    await app.close();
  });

  async function cleanUpResources(): Promise<void> {
    jest.clearAllMocks();

    mockHttpAdapterService.reset();
    mockUssApiService.reset();
    mockBsApiService.reset();
    mockStsApiService.reset();

    await vulogUserRepo.clear();
    await reservationRepo.clear();
    await cacheService.clearAll();
    await carRepo.delete({});
  }

  describe('💧 Required parameters are missing!', () => {
    let response: supertest.Response;

    beforeAll(async () => {
      await mockCredential();

      response = await requestFor(
        { customerId: undefined },
        {
          carId: undefined,
          stationId: undefined,
          rentalPackage: undefined,
        },
      );
    });

    it('Assert final response', async () => {
      expect(response.body.httpStatus).toEqual(HttpStatus.BAD_REQUEST);
      expect(response.body.errorCode).toEqual('INTERNAL_SERVER_ERROR');
      expect(response.body.message).toEqual([
        'carId should not be empty',
        'carId must be a UUID',
        'stationId should not be empty',
        'stationId must be a UUID',
        'rentalPackage must be an object',
      ]);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('💧 UUID checks are failed for request body!', () => {
    let response: supertest.Response;

    beforeAll(async () => {
      await mockCredential();

      response = await requestFor(
        { customerId: aUUID() },
        {
          carId: aLastName(),
          stationId: aLastName(),
          rentalPackage: new AttachRentalPackageRequestDtoV1Builder().build(),
        },
      );
    });

    it('Assert final response', async () => {
      expect(response.body.httpStatus).toEqual(HttpStatus.BAD_REQUEST);
      expect(response.body.errorCode).toEqual('INTERNAL_SERVER_ERROR');
      expect(response.body.message).toEqual([
        'carId must be a UUID',
        'stationId must be a UUID',
      ]);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('💧 UUID checks are failed for path parameter!', () => {
    let response: supertest.Response;

    beforeAll(async () => {
      await mockCredential();

      response = await requestFor(
        { customerId: aLastName() },
        {
          carId: aUUID(),
          stationId: aUUID(),
          rentalPackage: new AttachRentalPackageRequestDtoV1Builder().build(),
        },
      );
    });

    it('Assert final response', async () => {
      expect(response.body.httpStatus).toEqual(HttpStatus.BAD_REQUEST);
      expect(response.body.errorCode).toEqual('INTERNAL_SERVER_ERROR');
      expect(response.body.message).toEqual(['customerId must be a UUID']);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });
});
