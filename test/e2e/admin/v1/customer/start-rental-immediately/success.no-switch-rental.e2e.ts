import { INestApplication } from '@nestjs/common';

import { JwtGuard } from '@bluesg-2/bo-auth-guard';
import { RootRouteSegment, RouteSegment } from 'src/common/constants/api-path';
import { CacheService } from 'src/logic/cache/cache.service';
import { mockAdminJwtGuard } from 'test/helpers/admin-jwt-guard.mocker';
import { AppBuilder } from 'test/helpers/app.builder';
import { aMD5, anEmail, aNumber, aUUID } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';

import { getRepositoryToken } from '@nestjs/typeorm';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { Repository } from 'typeorm';

import MockAdapter from 'axios-mock-adapter';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';

import * as _ from 'lodash';
import { DateTime } from 'luxon';
import * as QueryString from 'qs';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { ApiVersion } from 'src/common/constants/api-versions';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import {
  BsgUserId,
  CarId,
  CarModel,
  VulogCarId,
  VulogJourneyOrTripId,
  VulogProfileId,
  VulogUserId,
} from 'src/common/tiny-types';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { config } from 'src/config';
import { AdminCustomerIdParamDtoV1 } from 'src/controllers/admin/v1/customer/dto/request/admin.customer-id.param.dto.v1';
import { AdminStartRentalImmediatelyRequestDtoV1 } from 'src/controllers/admin/v1/customer/dto/request/admin.start-rental-immediately.request.dto.v1';
import { BillingExternalService } from 'src/external/billing/billing.external.service';
import {
  SubscriptionStatus,
  UserStatus,
} from 'src/external/user-subscription/constants';
import { InternalUserDetailResponseDtoV1 } from 'src/external/user-subscription/dtos/response/internal.user-detail.v1.response.dto';
import { AvailabilityService } from 'src/logic/availability.service';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { ReservationService } from 'src/logic/reservation.service';
import { StationPaths } from 'src/logic/station/station-paths';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';

import { AdminStartRentalImmediatelyResponseDtoV1 } from 'src/controllers/admin/v1/customer/dto/response/admin.start-rental-immediately.response.dto.v1';
import {
  VulogRealTimeBookingStatus,
  VulogRealTimeVehicleStatus,
} from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { Reservation } from 'src/model/reservation.domain';
import * as supertest from 'supertest';
import { AttachRentalPackageRequestDtoV1Builder } from 'test/helpers/builders/dtos/attach-rental-package-request-dto.builder';
import { InternalPricingPolicyResponseDtoV1Builder } from 'test/helpers/builders/dtos/internal.pricing-policy.v1.response.dto.builder';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { VulogCarStaticInfoDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car-static-info.dto.builder';
import { VulogCarRealTimeDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import {
  VulogBookingBuilder,
  VulogJourneyDtoBuilder,
} from 'test/helpers/builders/dtos/vulog/vulog-journey.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { VulogUserBuilder } from 'test/helpers/builders/vulog-user.builder';
import { BillingApiMocker } from 'test/helpers/external-services/billing.api-mocker';
import { StationApiMocker } from 'test/helpers/external-services/station.api-mocker';
import { UssApiMocker } from 'test/helpers/external-services/uss.api-mocker';
import { VulogApiMocker } from 'test/helpers/external-services/vulog.api-mocker';

const testingApiRoute = `/api/v1/admin/${RouteSegment.Customer.Index}/${RouteSegment.Customer.StartRental}`;

const userId = aUUID();

const backOfficeUserId = aUUID();

const email = anEmail();

const rentalRate = aNumber();

describe(`[E2E] POST: ${testingApiRoute}`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let mockHttpAdapterService: MockAdapter;
  let mockUssApiService: MockAdapter;
  let mockBsApiService: MockAdapter;
  let mockStsApiService: MockAdapter;

  let cacheService: CacheService;
  let reservationService: ReservationService;

  let availabilityService: AvailabilityService;

  let vulogUserRepo: Repository<VulogUserEntity>;
  let reservationRepo: Repository<ReservationEntity>;
  let carRepo: Repository<CarEntity>;

  let billingExternalService: BillingExternalService;

  const testAuthResponse = createTestVulogAuthResponse();
  const testVulogUser = VulogUserBuilder.allSync()
    .withBsgUserId(new BsgUserId(userId))
    .build();

  const expectedVulogCarId: string = aUUID();

  const expectedBsgCarId = aUUID();

  beforeAll(async () => {
    app = await appBuilder
      .overrideProvider(JwtGuard, mockAdminJwtGuard(backOfficeUserId), true)
      .buildAndStart();

    vulogUserRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );
    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );
    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));

    cacheService = app.get(CacheService);
    reservationService = app.get(ReservationService);

    availabilityService = app.get<AvailabilityService>(AvailabilityService);

    billingExternalService = app.get(BillingExternalService);

    mockHttpAdapterService = new VulogApiMocker(app).mock();
    mockUssApiService = new UssApiMocker(app).mock();
    mockStsApiService = new StationApiMocker(app).mock();
    mockBsApiService = new BillingApiMocker(app).mock();

    mockVulogCredential(app);
  });

  function requestFor(
    pathParams: AdminCustomerIdParamDtoV1,
    requestDto: AdminStartRentalImmediatelyRequestDtoV1,
  ): supertest.Test {
    const qualifiedUrl = testingApiRoute.replace(
      ':customerId',
      pathParams.customerId,
    );

    const superTest = supertest(app.getHttpServer())
      .post(`${qualifiedUrl}`)
      .send(requestDto);

    return superTest;
  }

  async function mockCredential() {
    const userDto = createTestVulogUserDto();

    userDto.email = email;

    mockHttpAdapterService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, userDto);

    const userServicesAndProfiles = createTestVulogUserServicesDto();
    userServicesAndProfiles.userId = userDto.id;

    mockHttpAdapterService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(userDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockHttpAdapterService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockHttpAdapterService
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    mockHttpAdapterService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, testAuthResponse);

    await vulogUserRepo.insert(VulogUserEntity.from(testVulogUser));

    mockVulogCredential(app);
  }

  afterAll(async () => {
    await cleanUpResources();

    await app.close();
  });

  async function cleanUpResources(): Promise<void> {
    jest.clearAllMocks();

    mockHttpAdapterService.reset();
    mockUssApiService.reset();
    mockBsApiService.reset();
    mockStsApiService.reset();

    await vulogUserRepo.clear();
    await reservationRepo.clear();
    await cacheService.clearAll();
    await carRepo.delete({});
  }

  describe('🔥 Star Rental immediately on behalf of customer (Rental package dynamic)', () => {
    const zoneId = aMD5();
    const now = DateTime.now();

    const stationDto1 = new StationDtoBuilder().withZoneId(zoneId).build();
    const stationDto2 = new StationDtoBuilder().build();
    const stationDtos = [stationDto1, stationDto2];

    const bs_isPackageAvailable = {
      isAvailable: true,
      isDynamic: true,
    };

    const ussUserDetail: InternalUserDetailResponseDtoV1 =
      new InternalUserDetailResponseDtoV1Builder()
        .withId(userId)
        .withUserStatus(UserStatus.VERIFIED)
        .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
        .build();

    const rentalPackageParams = new AttachRentalPackageRequestDtoV1Builder()
      .withMinAllowedBatteryPercentage(60)
      .build();

    const allocateCarStep_carDto = {
      static: new VulogCarStaticInfoDtoBuilder()
        .withId(expectedVulogCarId)
        .withModel(CarModelEnum.OpelCorsaE)
        .withServiceId(config.vulogConfig.serviceId.value)
        .build(),
      realTime: new VulogCarRealTimeDtoBuilder()
        .withId(expectedVulogCarId)
        .withServiceId(config.vulogConfig.serviceId.value)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
        .withBookingStatus(VulogRealTimeBookingStatus.Available)
        .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
        .withAutonomy(100)
        .build(),
    };

    const allocateCarStep_reservedAt = now.toJSDate();
    const allocateCarStep_expiresAt = now.plus({ minutes: 15 }).toJSDate();

    const allocateCarStep_vulogJourneyDto = new VulogJourneyDtoBuilder()
      .withVehicleId(expectedVulogCarId)
      .withServiceId(config.vulogConfig.serviceId.value)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(allocateCarStep_reservedAt.toISOString())
          .withMaxTripStartDate(allocateCarStep_expiresAt.toISOString())
          .build(),
      )
      .build();

    const startRentalStep_vulogJourneyDto = new VulogJourneyDtoBuilder()
      .asRental()
      .withVehicleId(expectedVulogCarId)
      .withId(allocateCarStep_vulogJourneyDto.id)
      .withServiceId(config.vulogConfig.serviceId.value)
      .withBooking(
        new VulogBookingBuilder()
          .withBookingDate(allocateCarStep_reservedAt.toISOString())
          .withMaxTripStartDate(allocateCarStep_expiresAt.toISOString())
          .build(),
      )
      .build();

    const allocateCarStep_expectedReservationEntity: ReservationEntity =
      new ReservationEntityBuilder()
        .makeBsgUserId(userId)
        .makeCarId(expectedVulogCarId)
        .makeCarModel(allocateCarStep_carDto.static.model.name as CarModelEnum)
        .makeStartStationId(stationDto1.id)
        .makeVulogTripId(allocateCarStep_vulogJourneyDto.id)
        .makeStatus(ReservationStatus.Reserved)
        .makeLocal(false)
        .makeAllocateAgain(false)
        .makeReservedAt(allocateCarStep_reservedAt)
        .makeExpiresAt(allocateCarStep_expiresAt)
        .makePlateNumber(allocateCarStep_carDto.static.plate)
        .makePricingPolicy(undefined)
        .makeRentalPackageData(undefined)
        .makeCanceledAt(undefined)
        .makeCreatedBy(backOfficeUserId)
        .makeModifiedBy(backOfficeUserId)
        .makeStartedAt(undefined)
        .makeAbuseReleasesAt(undefined)
        .makeCarCleanliness(undefined)
        .makeEndedAt(undefined)
        .makeExpiresAt(undefined)
        .makeCreatedBy(backOfficeUserId)
        .makeModifiedBy(backOfficeUserId)
        .makeAmount(undefined)
        .makeEndStationId(undefined)
        .build();

    delete allocateCarStep_expectedReservationEntity.id;

    const attachRentalPackage_expectedReservationEntity: ReservationEntity =
      new ReservationEntityBuilder()
        .makeBsgUserId(userId)
        .makeCarId(expectedVulogCarId)
        .makeCarModel(allocateCarStep_carDto.static.model.name as CarModelEnum)
        .makeStartStationId(stationDto1.id)
        .makeVulogTripId(allocateCarStep_vulogJourneyDto.id)
        .makeStatus(ReservationStatus.Reserved)
        .makeLocal(false)
        .makeAllocateAgain(false)
        .makeReservedAt(allocateCarStep_reservedAt)
        .makeExpiresAt(allocateCarStep_expiresAt)
        .makePlateNumber(allocateCarStep_carDto.static.plate)
        .makePricingPolicy(undefined)
        .makeRentalPackageData({
          ...rentalPackageParams,
          isDynamic: bs_isPackageAvailable.isDynamic,
        })
        .makeCanceledAt(undefined)
        .makeCreatedBy(backOfficeUserId)
        .makeModifiedBy(backOfficeUserId)
        .makeRating(undefined)
        .makeStartedAt(undefined)
        .build();

    delete attachRentalPackage_expectedReservationEntity.id;

    const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withId(config.vulogConfig.serviceId.value)
      .withZones([stationDto1.zoneId])
      .withVehicles([allocateCarStep_carDto.static.id])
      .build();

    const mockPricingPolicyResponseDto =
      new InternalPricingPolicyResponseDtoV1Builder()
        .withCarModel(allocateCarStep_carDto.static.model.name as CarModelEnum)
        .withSubscriptionPlanId(
          ussUserDetail.currentSubscription.subscriptionPlanId,
        )
        .withRentalRate(rentalRate)
        .build();

    const clone_allocateCarStep_carDto = _.cloneDeep(allocateCarStep_carDto);

    clone_allocateCarStep_carDto.realTime.vehicle_status =
      VulogRealTimeVehicleStatus.InUse;
    clone_allocateCarStep_carDto.realTime.booking_status =
      VulogRealTimeBookingStatus.Booked;

    const startRentalStep_carDto = {
      static: allocateCarStep_carDto.static,
      realTime: clone_allocateCarStep_carDto.realTime,
    };

    let response: supertest.Response;

    let latestRental: Reservation;

    beforeAll(async () => {
      await mockCredential();

      await carRepo.save([
        CarEntity.from(
          allocateCarStep_carDto.static
            .toStaticCar()
            .toCar(new CarId(expectedBsgCarId)),
        ),
      ]);

      // VulogCarService.getSpecificCarInRealtime
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [allocateCarStep_carDto.realTime]);

      // UserSubscriptionExternalService.validateUssUser
      mockUssApiService
        .onGet(
          `${ApiRouteUtils.buildApiRouteSegment(
            RootRouteSegment.Internal,
            ApiVersion.V1,
            ['users', userId],
          )}`,
        )
        .reply(200, {
          success: true,
          result: JSON.parse(JSON.stringify(ussUserDetail)),
        } as BaseResponseDto);

      // BillingExternalService.internalApi.checkPackageAvailable
      jest
        .spyOn(billingExternalService.internalApi, 'checkPackageAvailable')
        .mockResolvedValue(bs_isPackageAvailable);

      // StationService.getStation
      mockStsApiService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos.map((stn) => stn.toRawJsonObject()),
        });

      /**
       * "Allocate Car" step
       */

      // VulogCarService.bookCar
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.bookJourney(
            new VulogCarId(allocateCarStep_carDto.realTime.id),
          )}`,
        )
        .replyOnce(200, allocateCarStep_vulogJourneyDto);

      // VulogCarService.bookCar
      jest
        .spyOn(availabilityService, 'getAvailableCarsForSpecificStation')
        .mockResolvedValueOnce([
          allocateCarStep_carDto.realTime.toRealtimeCar(
            new CarModel(CarModelEnum.OpelCorsaE),
          ),
        ]);

      /**
       * "Attach Rental Package" step
       */

      // VulogFleetService.getFleetServices
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .replyOnce(200, [mockVulogFleetServiceDto]);

      /**
       * "Start Rental in BlueSG" step
       */
      // BillingExternalService.getPricingPolicy
      mockBsApiService
        .onGet(new RegExp('pricing-policy'))
        .reply(200, BaseResponseDto.success([mockPricingPolicyResponseDto]));

      // VulogCarService.getStaticCar
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .replyOnce(200, [allocateCarStep_carDto.static], { last: true });

      // StationExternalService.getStationDetail
      mockStsApiService
        .onGet(`/api/v1/stations/${stationDto1.id}`)
        .replyOnce(200, BaseResponseDto.success(stationDto1.toRawJsonObject()));

      // getCarsCounterForStation - AvailabilityService.getAllAvailableCars
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, [startRentalStep_carDto.realTime]);

      // getCarsCounterForStation - VulogCarService.getAllStaticCars
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, pageSize: 200 })}`,
        )
        .replyOnce(200, [allocateCarStep_carDto.static], { last: true });

      // StationService.getAllStations
      mockHttpAdapterService
        .onGet(`${StationPaths.stations()}?page=1&pageSize=200`)
        .replyOnce(
          200,
          BaseResponseDto.success([stationDto1.toRawJsonObject()]),
        );

      mockHttpAdapterService
        .onGet(`${StationPaths.stations()}?page=2&pageSize=200`)
        .replyOnce(200, BaseResponseDto.success([]));

      // VulogCarService.getAllStaticCars in AvailabilityService.mapCarsToStations
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeListAllVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .replyOnce(200, [allocateCarStep_carDto.static], { last: true });

      /**
       * "Unlock Car" step
       */

      // Mock VVG unlockAndMobilize
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.vvgUnlockAndMobilizeVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(allocateCarStep_carDto.realTime.id),
          )}`,
        )
        .replyOnce(() => {
          return [200];
        });

      /**
       * "Start Rental on Vulog" step
       */
      // VulogCarService.startRental
      mockHttpAdapterService
        .onPost(
          `${VulogPaths.startOrEndTrip(
            new VulogJourneyOrTripId(
              allocateCarStep_expectedReservationEntity.vulogTripId,
            ),
          )}`,
        )
        .replyOnce(200, startRentalStep_vulogJourneyDto.toRawJsonObject());

      response = await requestFor(
        { customerId: userId },
        {
          carId: expectedBsgCarId,
          stationId: stationDto1.id,
          rentalPackage: rentalPackageParams,
        },
      );

      latestRental = await reservationService.getOneByVulogId(
        new VulogJourneyOrTripId(allocateCarStep_vulogJourneyDto.id),
      );
    });

    it('API response is expected', async () => {
      expect(
        JSON.parse(
          JSON.stringify(
            AdminStartRentalImmediatelyResponseDtoV1.from(latestRental, {
              packageId: rentalPackageParams.packageId,
              isDynamic: bs_isPackageAvailable.isDynamic,
            }),
          ),
        ),
      ).toEqual(response.body.result);
    });

    it('Rental is found', async () => {
      expect(latestRental).toBeDefined();

      expect(latestRental.status).toEqual(ReservationStatus.Converted);

      expect(latestRental.startedAt).toBeDefined();
    });

    it('Rental is utilized by expected BSG user', async () => {
      expect(latestRental.bsgUserId.value).toEqual(userId);
    });

    it('Expected car is allocated to the rental', async () => {
      expect(latestRental.carId.value).toEqual(expectedVulogCarId);

      expect(latestRental.carModel).toEqual(CarModelEnum.OpelCorsaE);

      expect(latestRental.plateNumber.value).toEqual(
        clone_allocateCarStep_carDto.static.plate,
      );
    });

    it('Expected the rental is started at expected station', async () => {
      expect(latestRental.startStationId.value).toEqual(stationDto1.id);
    });

    it('Expected the rental is correlated to a Vulog Trip', async () => {
      expect(latestRental.vulogTripId.value).toBeDefined();
    });

    it('Expected pricing information (policy, package) is attached to the rental', async () => {
      expect(latestRental.pricingPolicy.pricingPolicyId).toEqual(
        mockPricingPolicyResponseDto.id,
      );

      expect(latestRental.rentalPackageData).toEqual({
        ...rentalPackageParams,
        isDynamic: bs_isPackageAvailable.isDynamic,
      });
    });

    it('Expected the rental is started by Back Office staff', async () => {
      expect(latestRental.getCreatedBy).toBeDefined();
      expect(latestRental.getModifiedBy).toBeDefined();
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });
});
