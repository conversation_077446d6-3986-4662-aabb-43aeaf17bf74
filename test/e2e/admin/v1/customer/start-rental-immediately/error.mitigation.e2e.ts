import { INestApplication } from '@nestjs/common';

import { JwtGuard } from '@bluesg-2/bo-auth-guard';
import { RootRouteSegment, RouteSegment } from 'src/common/constants/api-path';
import { CacheService } from 'src/logic/cache/cache.service';
import { mockAdminJwtGuard } from 'test/helpers/admin-jwt-guard.mocker';
import { AppBuilder } from 'test/helpers/app.builder';
import { aMD5, anEmail, aNumber, aUUID } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';

import { getRepositoryToken } from '@nestjs/typeorm';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { Repository } from 'typeorm';

import MockAdapter from 'axios-mock-adapter';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';

import {
  BsgUserId,
  CarId,
  CarModel,
  VulogCarId,
  VulogJourneyOrTripId,
  VulogProfileId,
  VulogUserId,
} from 'src/common/tiny-types';
import { config } from 'src/config';
import { AdminCustomerIdParamDtoV1 } from 'src/controllers/admin/v1/customer/dto/request/admin.customer-id.param.dto.v1';
import { AdminStartRentalImmediatelyRequestDtoV1 } from 'src/controllers/admin/v1/customer/dto/request/admin.start-rental-immediately.request.dto.v1';
import { BillingExternalService } from 'src/external/billing/billing.external.service';
import { AvailabilityService } from 'src/logic/availability.service';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { RentalService } from 'src/logic/rental.service';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';

import * as _ from 'lodash';
import { DateTime } from 'luxon';
import * as QueryString from 'qs';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { ApiVersion } from 'src/common/constants/api-versions';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import {
  SubscriptionStatus,
  UserStatus,
} from 'src/external/user-subscription/constants';
import { InternalUserDetailResponseDtoV1 } from 'src/external/user-subscription/dtos/response/internal.user-detail.v1.response.dto';
import { StationPaths } from 'src/logic/station/station-paths';
import { VulogCarConnectionProblemService } from 'src/logic/vulog/vulog-car-connection-problem.service';
import {
  VulogRealTimeBookingStatus,
  VulogRealTimeVehicleStatus,
} from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import * as supertest from 'supertest';
import { AttachRentalPackageRequestDtoV1Builder } from 'test/helpers/builders/dtos/attach-rental-package-request-dto.builder';
import { InternalPricingPolicyResponseDtoV1Builder } from 'test/helpers/builders/dtos/internal.pricing-policy.v1.response.dto.builder';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { VulogCarStaticInfoDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car-static-info.dto.builder';
import { VulogCarRealTimeDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import {
  VulogBookingBuilder,
  VulogJourneyDtoBuilder,
} from 'test/helpers/builders/dtos/vulog/vulog-journey.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { VulogUserBuilder } from 'test/helpers/builders/vulog-user.builder';
import { BillingApiMocker } from 'test/helpers/external-services/billing.api-mocker';
import { StationApiMocker } from 'test/helpers/external-services/station.api-mocker';
import { UssApiMocker } from 'test/helpers/external-services/uss.api-mocker';
import { VulogApiMocker } from 'test/helpers/external-services/vulog.api-mocker';

interface TestInput {
  step:
    | 'allocate_car'
    | 'switch_car'
    | 'attach_rental_package'
    | 'start_rental_in_bluesg'
    | 'unlock_car'
    | 'start_rental_on_vulog';
  mockCallback: (...args) => void;
  cancelAndTerminate: boolean;
}

const testingApiRoute = `/api/v1/admin/${RouteSegment.Customer.Index}/${RouteSegment.Customer.StartRental}`;

const userId = aUUID();

const backOfficeUserId = aUUID();

const email = anEmail();

const rentalRate = aNumber();

describe(`[E2E] POST: ${testingApiRoute}`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let mockHttpAdapterService: MockAdapter;
  let mockUssApiService: MockAdapter;
  let mockBsApiService: MockAdapter;
  let mockStsApiService: MockAdapter;

  let cacheService: CacheService;

  let rentalService: RentalService;
  let availabilityService: AvailabilityService;
  let vulogCarService: VulogCarService;

  let vulogUserRepo: Repository<VulogUserEntity>;
  let reservationRepo: Repository<ReservationEntity>;
  let carRepo: Repository<CarEntity>;

  let vulogCarConnectionProblemService: VulogCarConnectionProblemService;

  let billingExternalService: BillingExternalService;

  let spyOn_VulogCarService__terminateTrip: jest.SpyInstance;
  let spyOn_RentalService__startRental: jest.SpyInstance;

  const testAuthResponse = createTestVulogAuthResponse();
  const testVulogUser = VulogUserBuilder.allSync()
    .withBsgUserId(new BsgUserId(userId))
    .build();

  const expectedBsgCarId: string = aUUID();

  const expectedVulogCarId: string = aUUID();

  beforeAll(async () => {
    app = await appBuilder
      .overrideProvider(JwtGuard, mockAdminJwtGuard(backOfficeUserId), true)
      .buildAndStart();

    vulogUserRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );
    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );
    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));

    cacheService = app.get(CacheService);

    rentalService = app.get(RentalService);
    vulogCarService = app.get(VulogCarService);
    availabilityService = app.get<AvailabilityService>(AvailabilityService);

    vulogCarConnectionProblemService = app.get(
      VulogCarConnectionProblemService,
    );

    billingExternalService = app.get(BillingExternalService);

    mockHttpAdapterService = new VulogApiMocker(app).mock();
    mockUssApiService = new UssApiMocker(app).mock();
    mockStsApiService = new StationApiMocker(app).mock();
    mockBsApiService = new BillingApiMocker(app).mock();

    spyOn_VulogCarService__terminateTrip = jest.spyOn(
      vulogCarService,
      'terminateTrip',
    );
    spyOn_RentalService__startRental = jest.spyOn(rentalService, 'startRental');

    mockVulogCredential(app);
  });

  function requestFor(
    pathParams: AdminCustomerIdParamDtoV1,
    requestDto: AdminStartRentalImmediatelyRequestDtoV1,
  ): supertest.Test {
    const qualifiedUrl = testingApiRoute.replace(
      ':customerId',
      pathParams.customerId,
    );

    const superTest = supertest(app.getHttpServer())
      .post(`${qualifiedUrl}`)
      .send(requestDto);

    return superTest;
  }

  async function mockCredential() {
    const userDto = createTestVulogUserDto();

    userDto.email = email;

    mockHttpAdapterService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, userDto);

    const userServicesAndProfiles = createTestVulogUserServicesDto();
    userServicesAndProfiles.userId = userDto.id;

    mockHttpAdapterService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(userDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockHttpAdapterService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockHttpAdapterService
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    mockHttpAdapterService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, testAuthResponse);

    await vulogUserRepo.insert(VulogUserEntity.from(testVulogUser));

    mockVulogCredential(app);
  }

  afterAll(async () => {
    await cleanUpResources();

    await app.close();
  });

  async function cleanUpResources(): Promise<void> {
    jest.clearAllMocks();

    mockHttpAdapterService.reset();
    mockUssApiService.reset();
    mockBsApiService.reset();
    mockStsApiService.reset();

    await vulogUserRepo.clear();
    await reservationRepo.clear();
    await cacheService.clearAll();
    await carRepo.delete({});
  }

  describe.each<TestInput>([
    {
      step: 'allocate_car',
      mockCallback: (carBundleDto) => {
        // VulogCarService.bookCar
        mockHttpAdapterService
          .onPost(
            `${VulogPaths.bookJourney(
              new VulogCarId(carBundleDto.realTime.id),
            )}`,
          )
          .replyOnce(500);
      },
      cancelAndTerminate: false,
    },
    {
      step: 'switch_car',
      mockCallback: (carBundleDto) => {
        mockHttpAdapterService
          .onPost(
            `${VulogPaths.bookJourney(
              new VulogCarId(carBundleDto.realTime.id),
            )}`,
          )
          .replyOnce(500);
      },
      cancelAndTerminate: true,
    },
    {
      step: 'attach_rental_package',
      mockCallback: () => {
        jest
          .spyOn(billingExternalService.internalApi, 'checkPackageAvailable')
          .mockRejectedValueOnce({});
      },
      cancelAndTerminate: true,
    },
    {
      step: 'start_rental_in_bluesg',
      mockCallback: () => {
        mockBsApiService.onGet(new RegExp('pricing-policy')).reply(500);
      },
      cancelAndTerminate: true,
    },
    {
      step: 'unlock_car',
      mockCallback: () => {
        jest
          .spyOn(
            vulogCarConnectionProblemService,
            'retryWhenEncounterCarConnectionProblem',
          )
          .mockRejectedValueOnce({});
      },
      cancelAndTerminate: true,
    },
    {
      step: 'start_rental_on_vulog',
      mockCallback: () => {
        spyOn_RentalService__startRental.mockRejectedValueOnce(null);
      },
      cancelAndTerminate: true,
    },
  ])(
    'Mitigation for step "$step"',
    ({ mockCallback, step, cancelAndTerminate }) => {
      const zoneId = aMD5();
      const now = DateTime.now();

      const stationDto1 = new StationDtoBuilder().withZoneId(zoneId).build();
      const stationDto2 = new StationDtoBuilder().build();
      const stationDtos = [stationDto1, stationDto2];

      const bs_isPackageAvailable = {
        isAvailable: true,
        isDynamic: true,
      };

      const ussUserDetail: InternalUserDetailResponseDtoV1 =
        new InternalUserDetailResponseDtoV1Builder()
          .withId(userId)
          .withUserStatus(UserStatus.VERIFIED)
          .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
          .build();

      const rentalPackageParams = new AttachRentalPackageRequestDtoV1Builder()
        .withMinAllowedBatteryPercentage(60)
        .build();

      const unexpectedVulogCarId = aUUID();
      const unexpectedBsgCarId = aUUID();

      const unexpectedCarDto = {
        static: new VulogCarStaticInfoDtoBuilder()
          .withId(unexpectedVulogCarId)
          .withModel(CarModelEnum.OpelCorsaE)
          .withServiceId(config.vulogConfig.serviceId.value)
          .build(),
        realTime: new VulogCarRealTimeDtoBuilder()
          .withId(unexpectedVulogCarId)
          .withServiceId(config.vulogConfig.serviceId.value)
          .withFleetId(config.vulogConfig.fleetId.value)
          .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
          .withBookingStatus(VulogRealTimeBookingStatus.Available)
          .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
          .withAutonomy(100)
          .build(),
      };

      const switchCarStep_carDto = {
        static: new VulogCarStaticInfoDtoBuilder()
          .withId(expectedVulogCarId)
          .withModel(CarModelEnum.OpelCorsaE)
          .withServiceId(config.vulogConfig.serviceId.value)
          .build(),
        realTime: new VulogCarRealTimeDtoBuilder()
          .withId(expectedVulogCarId)
          .withServiceId(config.vulogConfig.serviceId.value)
          .withFleetId(config.vulogConfig.fleetId.value)
          .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
          .withBookingStatus(VulogRealTimeBookingStatus.Available)
          .withZones([new VulogZone(zoneId, 1, 'allowed', false)])
          .withAutonomy(100)
          .build(),
      };

      const allocateCarStep_reservedAt = now.toJSDate();
      const allocateCarStep_expiresAt = now.plus({ minutes: 15 }).toJSDate();

      const allocateCarStep_vulogJourneyDto = new VulogJourneyDtoBuilder()
        .withVehicleId(unexpectedVulogCarId)
        .withServiceId(config.vulogConfig.serviceId.value)
        .withBooking(
          new VulogBookingBuilder()
            .withBookingDate(allocateCarStep_reservedAt.toISOString())
            .withMaxTripStartDate(allocateCarStep_expiresAt.toISOString())
            .build(),
        )
        .build();

      const switchCarStep_vulogJourneyDto = new VulogJourneyDtoBuilder()
        .withVehicleId(expectedVulogCarId)
        .withServiceId(config.vulogConfig.serviceId.value)
        .withBooking(
          new VulogBookingBuilder()
            .withBookingDate(allocateCarStep_reservedAt.toISOString())
            .withMaxTripStartDate(allocateCarStep_expiresAt.toISOString())
            .build(),
        )
        .build();

      const startRentalStep_vulogJourneyDto = new VulogJourneyDtoBuilder()
        .asRental()
        .withId(switchCarStep_vulogJourneyDto.id)
        .withVehicleId(expectedVulogCarId)
        .withServiceId(config.vulogConfig.serviceId.value)
        .withBooking(
          new VulogBookingBuilder()
            .withBookingDate(allocateCarStep_reservedAt.toISOString())
            .withMaxTripStartDate(allocateCarStep_expiresAt.toISOString())
            .build(),
        )
        .build();

      const allocateCarStep_expectedReservationEntity: ReservationEntity =
        new ReservationEntityBuilder()
          .makeBsgUserId(userId)
          .makeCarId(unexpectedVulogCarId)
          .makeCarModel(unexpectedCarDto.static.model.name as CarModelEnum)
          .makeStartStationId(stationDto1.id)
          .makeVulogTripId(allocateCarStep_vulogJourneyDto.id)
          .makeStatus(ReservationStatus.Reserved)
          .makeLocal(false)
          .makeAllocateAgain(false)
          .makeReservedAt(allocateCarStep_reservedAt)
          .makeExpiresAt(allocateCarStep_expiresAt)
          .makePlateNumber(unexpectedCarDto.static.plate)
          .makePricingPolicy(undefined)
          .makeRentalPackageData(undefined)
          .makeCanceledAt(undefined)
          .makeCreatedBy(backOfficeUserId)
          .makeModifiedBy(backOfficeUserId)
          .makeStartedAt(undefined)
          .makeAbuseReleasesAt(undefined)
          .makeCarCleanliness(undefined)
          .makeEndedAt(undefined)
          .makeExpiresAt(undefined)
          .makeCreatedBy(backOfficeUserId)
          .makeModifiedBy(backOfficeUserId)
          .makeAmount(undefined)
          .makeEndStationId(undefined)
          .build();

      delete allocateCarStep_expectedReservationEntity.id;

      const attachRentalPackage_expectedReservationEntity: ReservationEntity =
        new ReservationEntityBuilder()
          .makeBsgUserId(userId)
          .makeCarId(expectedVulogCarId)
          .makeCarModel(switchCarStep_carDto.static.model.name as CarModelEnum)
          .makeStartStationId(stationDto1.id)
          .makeVulogTripId(switchCarStep_vulogJourneyDto.id)
          .makeStatus(ReservationStatus.Reserved)
          .makeLocal(false)
          .makeAllocateAgain(false)
          .makeReservedAt(allocateCarStep_reservedAt)
          .makeExpiresAt(allocateCarStep_expiresAt)
          .makePlateNumber(switchCarStep_carDto.static.plate)
          .makePricingPolicy(undefined)
          .makeRentalPackageData({
            ...rentalPackageParams,
            isDynamic: bs_isPackageAvailable.isDynamic,
          })
          .makeCanceledAt(undefined)
          .makeCreatedBy(backOfficeUserId)
          .makeModifiedBy(backOfficeUserId)
          .makeRating(undefined)
          .makeStartedAt(undefined)
          .build();

      delete attachRentalPackage_expectedReservationEntity.id;

      const mockVulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withId(config.vulogConfig.serviceId.value)
        .withZones([stationDto1.zoneId])
        .withVehicles([switchCarStep_carDto.static.id])
        .build();

      const mockPricingPolicyResponseDto =
        new InternalPricingPolicyResponseDtoV1Builder()
          .withCarModel(switchCarStep_carDto.static.model.name as CarModelEnum)
          .withSubscriptionPlanId(
            ussUserDetail.currentSubscription.subscriptionPlanId,
          )
          .withRentalRate(rentalRate)
          .build();

      const clone_allocateCarStep_carDto = _.cloneDeep(switchCarStep_carDto);

      clone_allocateCarStep_carDto.realTime.vehicle_status =
        VulogRealTimeVehicleStatus.InUse;
      clone_allocateCarStep_carDto.realTime.booking_status =
        VulogRealTimeBookingStatus.Booked;

      const startRentalStep_carDto = {
        static: switchCarStep_carDto.static,
        realTime: clone_allocateCarStep_carDto.realTime,
      };

      beforeAll(async () => {
        await mockCredential();

        await carRepo.save([
          CarEntity.from(
            switchCarStep_carDto.static
              .toStaticCar()
              .toCar(new CarId(expectedBsgCarId)),
          ),
          CarEntity.from(
            unexpectedCarDto.static
              .toStaticCar()
              .toCar(new CarId(unexpectedBsgCarId)),
          ),
        ]);

        // VulogCarService.getSpecificCarInRealtime
        mockHttpAdapterService
          .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
          .replyOnce(200, [
            unexpectedCarDto.realTime, // will be chosen as sorting order
            switchCarStep_carDto.realTime,
          ]);

        // UserSubscriptionExternalService.validateUssUser
        mockUssApiService
          .onGet(
            `${ApiRouteUtils.buildApiRouteSegment(
              RootRouteSegment.Internal,
              ApiVersion.V1,
              ['users', userId],
            )}`,
          )
          .reply(200, {
            success: true,
            result: JSON.parse(JSON.stringify(ussUserDetail)),
          } as BaseResponseDto);

        // BillingExternalService.internalApi.checkPackageAvailable
        jest
          .spyOn(billingExternalService.internalApi, 'checkPackageAvailable')
          .mockResolvedValueOnce(bs_isPackageAvailable);

        // StationService.getStation
        mockStsApiService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 1,
              pageSize: 200,
            })}`,
          )
          .replyOnce(200, {
            result: stationDtos.map((stn) => stn.toRawJsonObject()),
          });

        /**
         * "Allocate Car" step
         */

        // VulogCarService.bookCar

        if (step === 'allocate_car') {
          mockCallback(unexpectedCarDto);
        } else {
          mockHttpAdapterService
            .onPost(
              `${VulogPaths.bookJourney(
                new VulogCarId(unexpectedCarDto.realTime.id),
              )}`,
            )
            .replyOnce(200, allocateCarStep_vulogJourneyDto);
        }

        // VulogCarService.bookCar
        jest
          .spyOn(availabilityService, 'getAvailableCarsForSpecificStation')
          .mockResolvedValueOnce([
            unexpectedCarDto.realTime
              .toRealtimeCar(new CarModel(CarModelEnum.OpelCorsaE))
              .toCar(),
          ]);

        /**
         * "Switch Allocation" step
         */
        // ReservationService.switchAllocationCar

        mockHttpAdapterService
          .onDelete(
            `${VulogPaths.vehicleBookingBackOffice(
              config.vulogConfig.fleetId,
              new VulogCarId(allocateCarStep_expectedReservationEntity.carId),
            )}`,
          )
          .replyOnce(async () => {
            await reservationRepo.update(
              {
                vulogTripId: allocateCarStep_vulogJourneyDto.id,
              },
              { allocateAgain: false },
            );

            return [200];
          });

        if (step === 'switch_car') {
          mockCallback(switchCarStep_carDto);
        } else {
          mockHttpAdapterService
            .onPost(
              `${VulogPaths.bookJourney(
                new VulogCarId(switchCarStep_carDto.realTime.id),
              )}`,
            )
            .replyOnce(200, switchCarStep_vulogJourneyDto);
        }

        /**
         * "Attach Rental Package" step
         */

        // VulogFleetService.getFleetServices
        if (step === 'attach_rental_package') {
          mockCallback(null);
        }

        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
          )
          .replyOnce(200, [mockVulogFleetServiceDto]);

        /**
         * "Start Rental in BlueSG" step
         */
        // BillingExternalService.getPricingPolicy
        if (step === 'start_rental_in_bluesg') {
          mockCallback();
        }

        mockBsApiService
          .onGet(new RegExp('pricing-policy'))
          .reply(200, BaseResponseDto.success([mockPricingPolicyResponseDto]));

        // VulogCarService.getStaticCar
        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeListingAllStaticVehicles(
              config.vulogConfig.fleetId,
            )}?${QueryString.stringify({ page: 0, size: 200 })}`,
          )
          .replyOnce(200, [switchCarStep_carDto.static], { last: true });

        // StationExternalService.getStationDetail
        mockStsApiService
          .onGet(`/api/v1/stations/${stationDto1.id}`)
          .replyOnce(
            200,
            BaseResponseDto.success(stationDto1.toRawJsonObject()),
          );

        // getCarsCounterForStation - AvailabilityService.getAllAvailableCars
        mockHttpAdapterService
          .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
          .replyOnce(200, [startRentalStep_carDto.realTime]);

        // getCarsCounterForStation - VulogCarService.getAllStaticCars
        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeListingAllStaticVehicles(
              config.vulogConfig.fleetId,
            )}?${QueryString.stringify({ page: 0, pageSize: 200 })}`,
          )
          .replyOnce(200, [switchCarStep_carDto.static], { last: true });

        // StationService.getAllStations
        mockHttpAdapterService
          .onGet(`${StationPaths.stations()}?page=1&pageSize=200`)
          .replyOnce(
            200,
            BaseResponseDto.success([stationDto1.toRawJsonObject()]),
          );

        mockHttpAdapterService
          .onGet(`${StationPaths.stations()}?page=2&pageSize=200`)
          .replyOnce(200, BaseResponseDto.success([]));

        // VulogCarService.getAllStaticCars in AvailabilityService.mapCarsToStations
        mockHttpAdapterService
          .onGet(
            `${VulogPaths.backOfficeListAllVehicles(
              config.vulogConfig.fleetId,
            )}?${QueryString.stringify({ page: 0, size: 200 })}`,
          )
          .replyOnce(200, [switchCarStep_carDto.static], { last: true });

        /**
         * "Unlock Car" step
         */

        if (step === 'unlock_car') {
          mockCallback();
        } else {
          // Mock VVG unlockAndMobilize
          mockHttpAdapterService
            .onPost(
              `${VulogPaths.vvgUnlockAndMobilizeVehicle(
                config.vulogConfig.fleetId,
                new VulogCarId(switchCarStep_carDto.realTime.id),
              )}`,
            )
            .replyOnce(() => {
              return [200];
            });
        }

        /**
         * "Start Rental on Vulog" step
         */
        // VulogCarService.startRental
        if (step === 'start_rental_on_vulog') {
          mockCallback();
        }

        mockHttpAdapterService
          .onPost(
            `${VulogPaths.startOrEndTrip(
              new VulogJourneyOrTripId(switchCarStep_vulogJourneyDto.id),
            )}`,
          )
          .replyOnce(200, startRentalStep_vulogJourneyDto.toRawJsonObject());

        // In case if terminating trip on Vulog
        spyOn_VulogCarService__terminateTrip.mockResolvedValue(null);

        await requestFor(
          { customerId: userId },
          {
            carId: expectedBsgCarId,
            stationId: stationDto1.id,
            rentalPackage: rentalPackageParams,
          },
        );
      });

      it(`Error assertion for step ${step}`, async () => {
        const [reservationEntity] = await reservationRepo.find({});

        expect(await reservationRepo.count({})).toEqual(1);

        if (step === 'allocate_car' && !cancelAndTerminate) {
          expect(reservationEntity.status).toEqual(ReservationStatus.Creating);

          expect(
            spyOn_VulogCarService__terminateTrip.mock.calls.length,
          ).toEqual(0);
        } else {
          expect(reservationEntity.status).toEqual(ReservationStatus.Cancelled);
          expect(reservationEntity.canceledAt).toBeDefined();
          expect(reservationEntity.deletedBy).toEqual(
            `Cancelled by back office staff ${backOfficeUserId} because failed to start rental immediately on behalf of customer`,
          );

          expect(
            spyOn_VulogCarService__terminateTrip.mock.calls.length,
          ).toEqual(1);

          expect(
            spyOn_VulogCarService__terminateTrip.mock.calls[0][0].value,
          ).toEqual(expectedVulogCarId);
        }
      });

      afterAll(async () => {
        await cleanUpResources();
      });
    },
  );
});
