import * as supertest from 'supertest';

import { INestApplication } from '@nestjs/common';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { faker } from '@faker-js/faker';
import { getRepositoryToken } from '@nestjs/typeorm';
import { HttpStatusCode } from 'axios';
import MockAdapter from 'axios-mock-adapter';
import * as _ from 'lodash';
import * as QueryString from 'qs';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { PageResult } from 'src/common/api/page-result';
import { RootRouteSegment, RouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { PaginationSortingOrder } from 'src/common/constants/pagination';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import {
  BsgUserId,
  CarModel,
  CarPlateNumber,
  VulogProfileId,
  VulogUserId,
} from 'src/common/tiny-types';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { MapUtils } from 'src/common/utils/map.util';
import { config } from 'src/config';
import {
  AdminCarQueryParamsDtoV1,
  CarQuerySortKey,
} from 'src/controllers/admin/v1/car/dto/request/admin.car.query.v1.dto';
import { AdminCarWithMetadataResponseDtoV1 } from 'src/controllers/admin/v1/car/dto/response/admin.car-with-metadata.response.dto.v1';
import {
  SubscriptionStatus,
  UserStatus,
} from 'src/external/user-subscription/constants';
import { CacheService } from 'src/logic/cache/cache.service';
import { Car } from 'src/logic/car/domain/car';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { StationPaths } from 'src/logic/station/station-paths';
import { StationService } from 'src/logic/station/station.service';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { StationDto } from 'src/model/dtos/station.dto';
import { VulogCarRealTimeDto } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogCarStaticInfoDto } from 'src/model/dtos/vulog-car-static-info/vulog-car-static-info.dto';
import { VulogFleetServiceDto } from 'src/model/dtos/vulog-fleet-service/vulog-fleet-service.dto';
import {
  VulogZone,
  VulogZoneType,
} from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { VulogEventEntity } from 'src/model/repositories/entities/vulog-event.entity';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { Station } from 'src/model/station';
import { AppBuilder } from 'test/helpers/app.builder';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { VulogCarStaticInfoDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car-static-info.dto.builder';
import {
  VulogCarRealTimeDtoBuilder,
  VulogZoneBuilder,
} from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import { VulogUserBuilder } from 'test/helpers/builders/vulog-user.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { UssApiMocker } from 'test/helpers/external-services/uss.api-mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';
import { Repository } from 'typeorm';

const testingApiRoute = `/api/v1/admin/${RouteSegment.Car.Index}/${RouteSegment.Car.List}`;

const userId = aUUID();

const email = faker.internet.email();

describe(`[E2E] GET: ${testingApiRoute}`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let reservationRepo: Repository<ReservationEntity>;
  let vulogUserRepo: Repository<VulogUserEntity>;
  let carRepo: Repository<CarEntity>;
  let vulogEventRepo: Repository<VulogEventEntity>;

  let stationService: StationService;
  let cacheService: CacheService;
  let mockOutgoingService: MockAdapter;
  let mockUssApiService: MockAdapter;

  const mockAuthResponse = createTestVulogAuthResponse();
  const mockVulogUser = VulogUserBuilder.creating()
    .withBsgUserId(new BsgUserId(userId))
    .build();

  const ussUserDto = new InternalUserDetailResponseDtoV1Builder()
    .withId(userId)
    .withUserStatus(UserStatus.VERIFIED)
    .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
    .build();

  function requestFor(queryParams?: AdminCarQueryParamsDtoV1): supertest.Test {
    const superTest = supertest(app.getHttpServer())
      .get(`${testingApiRoute}?${QueryString.stringify(queryParams)}`)
      .send();

    return superTest;
  }

  beforeAll(async () => {
    app = await appBuilder
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(aUUID()), true)
      .buildAndStart();

    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));
    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );
    vulogUserRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );
    vulogEventRepo = app.get<Repository<VulogEventEntity>>(
      getRepositoryToken(VulogEventEntity),
    );
    cacheService = app.get<CacheService>(CacheService);
    const httpApdaterService = app.get<HttpAdapterService>(HttpAdapterService);
    mockOutgoingService = new MockAdapter(
      httpApdaterService.httpService.axiosRef,
      { onNoMatch: 'throwException' },
    );
    mockUssApiService = new UssApiMocker(app).mock();
    stationService = app.get<StationService>(StationService);

    const userDto = createTestVulogUserDto();

    userDto.email = email;

    mockOutgoingService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, userDto);

    const userServicesAndProfiles = createTestVulogUserServicesDto();
    userServicesAndProfiles.userId = userDto.id;

    /**
     * Doing Vulog authentication mock
     */
    mockOutgoingService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(userDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    mockOutgoingService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, mockAuthResponse);

    mockUssApiService
      .onGet(
        `${ApiRouteUtils.buildApiRouteSegment(
          RootRouteSegment.Internal,
          ApiVersion.V1,
          ['users', userId],
        )}`,
      )
      .reply(200, {
        success: true,
        result: JSON.parse(JSON.stringify(ussUserDto)),
      } as BaseResponseDto);

    await vulogUserRepo.save(VulogUserEntity.from(mockVulogUser));

    mockVulogCredential(app);
  });

  afterAll(async () => {
    await cleanUpResources();

    await app.close();

    mockOutgoingService.reset();
    mockUssApiService.reset();
  });

  async function cleanUpResources(): Promise<void> {
    await reservationRepo.delete({});
    await carRepo.delete({});
    await vulogEventRepo.delete({});
    await vulogUserRepo.delete({});

    await cacheService.clearAll();

    jest.clearAllMocks();
  }

  describe('🔥 Listing out cars with metadata successfully: missing query parameters (default values filled)', () => {
    let responseObject: supertest.Response;

    const cars: Car[] = [
      new CarBuilder().withPlate(new CarPlateNumber('plate-01')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-02')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-03')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-04')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-05')).build(),
    ];

    const vulogCarRealTimeDtos: VulogCarRealTimeDto[] = [];

    let customerFleetServiceDto: VulogFleetServiceDto;

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [];

    const stationDtos: StationDto[] = [];

    beforeAll(async () => {
      // Save cars into DB
      await Promise.all(
        cars.map(async (car) => {
          await carRepo.save(CarEntity.from(car));
        }),
      );

      // Build Vulog Fleet Service DTO
      customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withVehicles(cars.map((car) => car.vulogId.value))
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

      // Map realtime car DTOs
      cars.forEach((car) =>
        vulogCarRealTimeDtos.push(
          new VulogCarRealTimeDtoBuilder()
            .withAutonomy(100)
            .withBattery(100)
            .withFleetId(config.vulogConfig.fleetId.value)
            .withServiceId(customerFleetServiceDto.id)
            .withZones([_.sample(vulogZones)])
            .withId(car.vulogId.value)
            .withVin(car.vin)
            .withPlate(car.plate.value)
            .build(),
        ),
      );

      // Map static car DTOs
      cars.forEach((car) => {
        vulogStaticCarDtos.push(
          new VulogCarStaticInfoDtoBuilder()
            .withId(car.vulogId.value)
            .withModel(car.model)
            .withPlate(car.plate.value)
            .withServiceId(customerFleetServiceDto.id)
            .withVin(car.vin)
            .build(),
        );
      });

      // Map vulog zone DTOs
      vulogZones.forEach((vgZone) => {
        stationDtos.push(
          new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
        );
      });

      // Listing out real-time cars from Vulog
      mockOutgoingService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, vulogCarRealTimeDtos);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      responseObject = await requestFor();
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 200', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.Ok);
    });

    it('Should return all expected data', async () => {
      const response: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body.result;

      const staticCars = vulogStaticCarDtos.map((staticCarDto) =>
        staticCarDto.toStaticCar(),
      );

      const staticCarsPlateMap = MapUtils.build(
        staticCars,
        (stCar) => stCar.plate.value,
      );

      const realtimeCars = vulogCarRealTimeDtos.map((vgRltCar) =>
        vgRltCar.toRealtimeCar(
          staticCarsPlateMap.get(vgRltCar.plate).model,
          customerFleetServiceDto.toFleetService().getZoneIdsAsMap(),
        ),
      );

      const realTimeCarsPlateMap = MapUtils.build(
        realtimeCars,
        (rltCar) => rltCar.plate?.value,
      );

      const fleetServicesIdMap = MapUtils.build(
        [customerFleetServiceDto.toFleetService()],
        (fltSvc) => fltSvc.id.value,
      );

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      const zoneIdStationMap = await stationService.getZoneStationMap();

      const expectedResponseDtos: AdminCarWithMetadataResponseDtoV1[] = cars
        .map((car: Car) => {
          const realtimeCar = realTimeCarsPlateMap.get(car.plate?.value);
          const staticCar = staticCarsPlateMap.get(car.plate?.value);
          const fleetService = fleetServicesIdMap.get(
            staticCar?.serviceId?.value,
          );
          const stickyStation: Station =
            zoneIdStationMap[
              realtimeCar?.allowedStickyZone?.zoneId
            ]?.toStation();

          return AdminCarWithMetadataResponseDtoV1.from(
            car,
            stickyStation,
            fleetService,
            realtimeCar,
          );
        })
        .sort((a, b) => a.plateNumber.localeCompare(b.plateNumber))
        .map((item) => JSON.parse(JSON.stringify(item)));

      expect(response).toMatchObject(expectedResponseDtos);
    });

    it('Should return expected pagination', () => {
      const returnedResp: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body;

      const { page, pageSize, sortKey, sortType, total, totalPages } =
        returnedResp.pagination as any;

      expect(page).toEqual(1);

      expect(pageSize).toEqual(10);

      expect(sortKey).toEqual('plateNumber');

      expect(sortType).toEqual('ASC');

      expect(total).toEqual(cars.length);

      expect(totalPages).toEqual(Math.ceil(cars.length / 10));
    });
  });

  describe('🔥 Listing out cars with metadata successfully: no items found', () => {
    let responseObject: supertest.Response;

    const cars: Car[] = [
      new CarBuilder().withPlate(new CarPlateNumber('plate-01')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-02')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-03')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-04')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-05')).build(),
    ];

    const vulogCarRealTimeDtos: VulogCarRealTimeDto[] = [];

    let customerFleetServiceDto: VulogFleetServiceDto;

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [];

    const stationDtos: StationDto[] = [];

    beforeAll(async () => {
      // Build Vulog Fleet Service DTO
      customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withVehicles(cars.map((car) => car.vulogId.value))
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

      // Map realtime car DTOs
      cars.forEach((car) =>
        vulogCarRealTimeDtos.push(
          new VulogCarRealTimeDtoBuilder()
            .withAutonomy(100)
            .withBattery(100)
            .withFleetId(config.vulogConfig.fleetId.value)
            .withServiceId(customerFleetServiceDto.id)
            .withZones([_.sample(vulogZones)])
            .withId(car.vulogId.value)
            .withVin(car.vin)
            .withPlate(car.plate.value)
            .build(),
        ),
      );

      // Map static car DTOs
      cars.forEach((car) => {
        vulogStaticCarDtos.push(
          new VulogCarStaticInfoDtoBuilder()
            .withId(car.vulogId.value)
            .withModel(car.model)
            .withPlate(car.plate.value)
            .withServiceId(customerFleetServiceDto.id)
            .withVin(car.vin)
            .build(),
        );
      });

      // Map vulog zone DTOs
      vulogZones.forEach((vgZone) => {
        stationDtos.push(
          new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
        );
      });

      // Listing out real-time cars from Vulog
      mockOutgoingService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, vulogCarRealTimeDtos);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      responseObject = await requestFor();
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 200', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.Ok);
    });

    it('Should return all expected data', async () => {
      const response: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body.result;

      expect(response).toMatchObject([]);
    });
  });

  describe('🔥 Listing out cars with metadata successfully: only one page of data', () => {
    let responseObject: supertest.Response;

    const cars: Car[] = [
      new CarBuilder().withPlate(new CarPlateNumber('plate-01')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-02')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-03')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-04')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-05')).build(),
    ];

    const vulogCarRealTimeDtos: VulogCarRealTimeDto[] = [];

    let customerFleetServiceDto: VulogFleetServiceDto;

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [];

    const stationDtos: StationDto[] = [];

    beforeAll(async () => {
      // Save cars into DB
      await Promise.all(
        cars.map(async (car) => {
          await carRepo.save(CarEntity.from(car));
        }),
      );

      // Build Vulog Fleet Service DTO
      customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withVehicles(cars.map((car) => car.vulogId.value))
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

      // Map realtime car DTOs
      cars.forEach((car) =>
        vulogCarRealTimeDtos.push(
          new VulogCarRealTimeDtoBuilder()
            .withAutonomy(100)
            .withBattery(100)
            .withFleetId(config.vulogConfig.fleetId.value)
            .withServiceId(customerFleetServiceDto.id)
            .withZones([_.sample(vulogZones)])
            .withId(car.vulogId.value)
            .withVin(car.vin)
            .withPlate(car.plate.value)
            .build(),
        ),
      );

      // Map static car DTOs
      cars.forEach((car) => {
        vulogStaticCarDtos.push(
          new VulogCarStaticInfoDtoBuilder()
            .withId(car.vulogId.value)
            .withModel(car.model)
            .withPlate(car.plate.value)
            .withServiceId(customerFleetServiceDto.id)
            .withVin(car.vin)
            .build(),
        );
      });

      // Map vulog zone DTOs
      vulogZones.forEach((vgZone) => {
        stationDtos.push(
          new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
        );
      });

      // Listing out real-time cars from Vulog
      mockOutgoingService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, vulogCarRealTimeDtos);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      responseObject = await requestFor();
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 200', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.Ok);
    });

    it('Should return all expected data', async () => {
      const response: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body.result;

      const realtimeCars = vulogCarRealTimeDtos.map((vgRltCar) =>
        vgRltCar.toRealtimeCar(),
      );

      const realTimeCarsPlateMap = MapUtils.build(
        realtimeCars,
        (rltCar) => rltCar.plate?.value,
      );

      const fleetServicesIdMap = MapUtils.build(
        [customerFleetServiceDto.toFleetService()],
        (fltSvc) => fltSvc.id.value,
      );

      const staticCars = vulogStaticCarDtos.map((staticCarDto) =>
        staticCarDto.toStaticCar(),
      );

      const staticCarsPlateMap = MapUtils.build(
        staticCars,
        (stCar) => stCar.plate.value,
      );

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      const zoneIdStationMap = await stationService.getZoneStationMap();

      const expectedResponseDtos: AdminCarWithMetadataResponseDtoV1[] = cars
        .map((car: Car) => {
          const realtimeCar = realTimeCarsPlateMap.get(car.plate?.value);
          const staticCar = staticCarsPlateMap.get(car.plate?.value);
          const fleetService = fleetServicesIdMap.get(
            staticCar?.serviceId?.value,
          );
          const stickyStation: Station =
            zoneIdStationMap[
              realtimeCar?.allowedStickyZone?.zoneId
            ]?.toStation();

          return AdminCarWithMetadataResponseDtoV1.from(
            car,
            stickyStation,
            fleetService,
            realtimeCar,
          );
        })
        .sort((a, b) => a.plateNumber.localeCompare(b.plateNumber))
        .map((item) => JSON.parse(JSON.stringify(item)));

      expect(response).toMatchObject(expectedResponseDtos);
    });

    it('Should return expected pagination', () => {
      const returnedResp: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body;

      const { page, pageSize, sortKey, sortType, total, totalPages } =
        returnedResp.pagination as any;

      expect(page).toEqual(1);

      expect(pageSize).toEqual(10);

      expect(sortKey).toEqual('plateNumber');

      expect(sortType).toEqual('ASC');

      expect(total).toEqual(cars.length);

      expect(totalPages).toEqual(Math.ceil(cars.length / 10));
    });
  });

  describe('🔥 Listing out cars with metadata successfully: multiple pages of data', () => {
    let responseObject: supertest.Response;

    const cars: Car[] = [
      new CarBuilder().withPlate(new CarPlateNumber('plate-01')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-02')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-03')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-04')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-05')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-06')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-07')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-08')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-09')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-10')).build(),
    ];

    const vulogCarRealTimeDtos: VulogCarRealTimeDto[] = [];

    let customerFleetServiceDto: VulogFleetServiceDto;

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [];

    const stationDtos: StationDto[] = [];

    beforeAll(async () => {
      // Save cars into DB
      await Promise.all(
        cars.map(async (car) => {
          await carRepo.save(CarEntity.from(car));
        }),
      );

      // Build Vulog Fleet Service DTO
      customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withVehicles(cars.map((car) => car.vulogId.value))
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

      // Map realtime car DTOs
      cars.forEach((car) =>
        vulogCarRealTimeDtos.push(
          new VulogCarRealTimeDtoBuilder()
            .withAutonomy(100)
            .withBattery(100)
            .withFleetId(config.vulogConfig.fleetId.value)
            .withServiceId(customerFleetServiceDto.id)
            .withZones([_.sample(vulogZones)])
            .withId(car.vulogId.value)
            .withVin(car.vin)
            .withPlate(car.plate.value)
            .build(),
        ),
      );

      // Map static car DTOs
      cars.forEach((car) => {
        vulogStaticCarDtos.push(
          new VulogCarStaticInfoDtoBuilder()
            .withId(car.vulogId.value)
            .withModel(car.model)
            .withPlate(car.plate.value)
            .withServiceId(customerFleetServiceDto.id)
            .withVin(car.vin)
            .build(),
        );
      });

      // Map vulog zone DTOs
      vulogZones.forEach((vgZone) => {
        stationDtos.push(
          new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
        );
      });

      // Listing out real-time cars from Vulog
      mockOutgoingService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, vulogCarRealTimeDtos);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      responseObject = await requestFor({
        sortKey: 'plateNumber',
        pageSize: 5,
        page: 2,
      } as any);
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 200', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.Ok);
    });

    it('Should return all expected data', async () => {
      const response: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body.result;

      const realtimeCars = vulogCarRealTimeDtos.map((vgRltCar) =>
        vgRltCar.toRealtimeCar(),
      );

      const realTimeCarsPlateMap = MapUtils.build(
        realtimeCars,
        (rltCar) => rltCar.plate?.value,
      );

      const fleetServicesIdMap = MapUtils.build(
        [customerFleetServiceDto.toFleetService()],
        (fltSvc) => fltSvc.id.value,
      );

      const staticCars = vulogStaticCarDtos.map((staticCarDto) =>
        staticCarDto.toStaticCar(),
      );

      const staticCarsPlateMap = MapUtils.build(
        staticCars,
        (stCar) => stCar.plate.value,
      );

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      const zoneIdStationMap = await stationService.getZoneStationMap();

      const expectedResponseDtos: AdminCarWithMetadataResponseDtoV1[] = cars
        .map((car: Car) => {
          const realtimeCar = realTimeCarsPlateMap.get(car.plate?.value);
          const staticCar = staticCarsPlateMap.get(car.plate?.value);
          const fleetService = fleetServicesIdMap.get(
            staticCar?.serviceId?.value,
          );
          const stickyStation: Station =
            zoneIdStationMap[
              realtimeCar?.allowedStickyZone?.zoneId
            ]?.toStation();

          return AdminCarWithMetadataResponseDtoV1.from(
            car,
            stickyStation,
            fleetService,
            realtimeCar,
          );
        })
        .sort((a, b) => a.plateNumber.localeCompare(b.plateNumber))
        .map((item) => JSON.parse(JSON.stringify(item)))
        .slice(5, cars.length);

      expect(response).toMatchObject(expectedResponseDtos);
    });

    it('Should return expected pagination', () => {
      const returnedResp: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body;

      const { page, pageSize, sortKey, sortType, total, totalPages } =
        returnedResp.pagination as any;

      expect(page).toEqual(2);

      expect(pageSize).toEqual(5);

      expect(sortKey).toEqual('plateNumber');

      expect(sortType).toEqual('ASC');

      expect(total).toEqual(cars.length);

      expect(totalPages).toEqual(Math.ceil(cars.length / 5));
    });
  });

  describe('🔥 Listing out cars with metadata successfully: searchable text matches with VIN of car', () => {
    let responseObject: supertest.Response;

    const almostMatchedVin = 'vin-';

    const cars: Car[] = [
      new CarBuilder()
        .withPlate(new CarPlateNumber('plate-01'))
        .withVin(`${almostMatchedVin}01`)
        .build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-02')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-03')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-04')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-05')).build(),
    ];

    const vulogCarRealTimeDtos: VulogCarRealTimeDto[] = [];

    let customerFleetServiceDto: VulogFleetServiceDto;

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [];

    const stationDtos: StationDto[] = [];

    beforeAll(async () => {
      // Save cars into DB
      await Promise.all(
        cars.map(async (car) => {
          await carRepo.save(CarEntity.from(car));
        }),
      );

      // Build Vulog Fleet Service DTO
      customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withVehicles(cars.map((car) => car.vulogId.value))
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

      // Map realtime car DTOs
      cars.forEach((car) =>
        vulogCarRealTimeDtos.push(
          new VulogCarRealTimeDtoBuilder()
            .withAutonomy(100)
            .withBattery(100)
            .withFleetId(config.vulogConfig.fleetId.value)
            .withServiceId(customerFleetServiceDto.id)
            .withZones([_.sample(vulogZones)])
            .withId(car.vulogId.value)
            .withVin(car.vin)
            .withPlate(car.plate.value)
            .build(),
        ),
      );

      // Map static car DTOs
      cars.forEach((car) => {
        vulogStaticCarDtos.push(
          new VulogCarStaticInfoDtoBuilder()
            .withId(car.vulogId.value)
            .withModel(car.model)
            .withPlate(car.plate.value)
            .withServiceId(customerFleetServiceDto.id)
            .withVin(car.vin)
            .build(),
        );
      });

      // Map vulog zone DTOs
      vulogZones.forEach((vgZone) => {
        stationDtos.push(
          new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
        );
      });

      // Listing out real-time cars from Vulog
      mockOutgoingService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, vulogCarRealTimeDtos);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      responseObject = await requestFor({ search: almostMatchedVin } as any);
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 200', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.Ok);
    });

    it('Should return all expected data', async () => {
      const response: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body.result;

      const realtimeCars = vulogCarRealTimeDtos.map((vgRltCar) =>
        vgRltCar.toRealtimeCar(),
      );

      const realTimeCarsPlateMap = MapUtils.build(
        realtimeCars,
        (rltCar) => rltCar.plate?.value,
      );

      const fleetServicesIdMap = MapUtils.build(
        [customerFleetServiceDto.toFleetService()],
        (fltSvc) => fltSvc.id.value,
      );

      const staticCars = vulogStaticCarDtos.map((staticCarDto) =>
        staticCarDto.toStaticCar(),
      );

      const staticCarsPlateMap = MapUtils.build(
        staticCars,
        (stCar) => stCar.plate.value,
      );

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      const zoneIdStationMap = await stationService.getZoneStationMap();

      const matchedCar = cars.find((car) => car.vin.includes(almostMatchedVin));

      const realtimeCar = realTimeCarsPlateMap.get(matchedCar.plate?.value);
      const staticCar = staticCarsPlateMap.get(matchedCar.plate?.value);
      const fleetService = fleetServicesIdMap.get(staticCar?.serviceId?.value);
      const stickyStation: Station =
        zoneIdStationMap[realtimeCar?.allowedStickyZone?.zoneId]?.toStation();

      const expectedResponseDtos: AdminCarWithMetadataResponseDtoV1 =
        AdminCarWithMetadataResponseDtoV1.from(
          matchedCar,
          stickyStation,
          fleetService,
          realtimeCar,
        );

      expect(response).toMatchObject([
        JSON.parse(JSON.stringify(expectedResponseDtos)),
      ]);
    });

    it('Should return expected pagination', () => {
      const returnedResp: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body;

      const { page, pageSize, sortKey, sortType, total, totalPages } =
        returnedResp.pagination as any;

      expect(page).toEqual(1);

      expect(pageSize).toEqual(10);

      expect(sortKey).toEqual('plateNumber');

      expect(sortType).toEqual('ASC');

      expect(total).toEqual(1);

      expect(totalPages).toEqual(1);
    });
  });

  describe('🔥 Listing out cars with metadata successfully: searchable text matches with plate number of car', () => {
    let responseObject: supertest.Response;

    const almostMatchedPlateNumber = 'plate-0';

    const cars: Car[] = [
      new CarBuilder()
        .withPlate(new CarPlateNumber(`${almostMatchedPlateNumber}1`))
        .build(),
      new CarBuilder()
        .withPlate(new CarPlateNumber(`${almostMatchedPlateNumber}2`))
        .build(),
      new CarBuilder()
        .withPlate(new CarPlateNumber(`${almostMatchedPlateNumber}3`))
        .build(),
      new CarBuilder()
        .withPlate(new CarPlateNumber(`${almostMatchedPlateNumber}4`))
        .build(),
      new CarBuilder()
        .withPlate(new CarPlateNumber(`${almostMatchedPlateNumber}5`))
        .build(),
    ];

    const vulogCarRealTimeDtos: VulogCarRealTimeDto[] = [];

    let customerFleetServiceDto: VulogFleetServiceDto;

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [];

    const stationDtos: StationDto[] = [];

    beforeAll(async () => {
      // Save cars into DB
      await Promise.all(
        cars.map(async (car) => {
          await carRepo.save(CarEntity.from(car));
        }),
      );

      // Build Vulog Fleet Service DTO
      customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withVehicles(cars.map((car) => car.vulogId.value))
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

      // Map realtime car DTOs
      cars.forEach((car) =>
        vulogCarRealTimeDtos.push(
          new VulogCarRealTimeDtoBuilder()
            .withAutonomy(100)
            .withBattery(100)
            .withFleetId(config.vulogConfig.fleetId.value)
            .withServiceId(customerFleetServiceDto.id)
            .withZones([_.sample(vulogZones)])
            .withId(car.vulogId.value)
            .withVin(car.vin)
            .withPlate(car.plate.value)
            .build(),
        ),
      );

      // Map static car DTOs
      cars.forEach((car) => {
        vulogStaticCarDtos.push(
          new VulogCarStaticInfoDtoBuilder()
            .withId(car.vulogId.value)
            .withModel(car.model)
            .withPlate(car.plate.value)
            .withServiceId(customerFleetServiceDto.id)
            .withVin(car.vin)
            .build(),
        );
      });

      // Map vulog zone DTOs
      vulogZones.forEach((vgZone) => {
        stationDtos.push(
          new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
        );
      });

      // Listing out real-time cars from Vulog
      mockOutgoingService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, vulogCarRealTimeDtos);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      responseObject = await requestFor({
        search: almostMatchedPlateNumber,
      } as any);
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 200', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.Ok);
    });

    it('Should return all expected data', async () => {
      const response: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body.result;

      const realtimeCars = vulogCarRealTimeDtos.map((vgRltCar) =>
        vgRltCar.toRealtimeCar(),
      );

      const realTimeCarsPlateMap = MapUtils.build(
        realtimeCars,
        (rltCar) => rltCar.plate?.value,
      );

      const fleetServicesIdMap = MapUtils.build(
        [customerFleetServiceDto.toFleetService()],
        (fltSvc) => fltSvc.id.value,
      );

      const staticCars = vulogStaticCarDtos.map((staticCarDto) =>
        staticCarDto.toStaticCar(),
      );

      const staticCarsPlateMap = MapUtils.build(
        staticCars,
        (stCar) => stCar.plate.value,
      );

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      const zoneIdStationMap = await stationService.getZoneStationMap();

      const expectedResponseDtos: AdminCarWithMetadataResponseDtoV1[] = cars
        .map((car: Car) => {
          const realtimeCar = realTimeCarsPlateMap.get(car.plate?.value);
          const staticCar = staticCarsPlateMap.get(car.plate?.value);
          const fleetService = fleetServicesIdMap.get(
            staticCar?.serviceId?.value,
          );
          const stickyStation: Station =
            zoneIdStationMap[
              realtimeCar?.allowedStickyZone?.zoneId
            ]?.toStation();

          return AdminCarWithMetadataResponseDtoV1.from(
            car,
            stickyStation,
            fleetService,
            realtimeCar,
          );
        })
        .sort((a, b) => a.plateNumber.localeCompare(b.plateNumber))
        .filter((car) => car.plateNumber.includes(almostMatchedPlateNumber))
        .map((item) => JSON.parse(JSON.stringify(item)));

      expect(response).toMatchObject(expectedResponseDtos);
    });

    it('Should return expected pagination', () => {
      const returnedResp: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body;

      const { page, pageSize, sortKey, sortType, total, totalPages } =
        returnedResp.pagination as any;

      expect(page).toEqual(1);

      expect(pageSize).toEqual(10);

      expect(sortKey).toEqual('plateNumber');

      expect(sortType).toEqual('ASC');

      expect(total).toEqual(cars.length);

      expect(totalPages).toEqual(Math.ceil(cars.length / 10));
    });
  });

  describe('🔥 Listing out cars with metadata successfully: searchable text matches with both VIN of car or plate number of car', () => {
    let responseObject: supertest.Response;

    const almostMatchedPlateNumber = 'plate-0';

    const cars: Car[] = [
      new CarBuilder()
        .withPlate(new CarPlateNumber(`${almostMatchedPlateNumber}1`))
        .withVin(`${almostMatchedPlateNumber}1`)
        .build(),
      new CarBuilder()
        .withPlate(new CarPlateNumber(`${almostMatchedPlateNumber}2`))
        .withVin(`${almostMatchedPlateNumber}2`)
        .build(),
      new CarBuilder()
        .withPlate(new CarPlateNumber(`${almostMatchedPlateNumber}3`))
        .withVin(`${almostMatchedPlateNumber}3`)
        .build(),
      new CarBuilder()
        .withPlate(new CarPlateNumber(`${almostMatchedPlateNumber}4`))
        .withVin(`${almostMatchedPlateNumber}4`)
        .build(),
      new CarBuilder() // Random VIN
        .withPlate(new CarPlateNumber(`${almostMatchedPlateNumber}5`))
        .build(),
    ];

    const vulogCarRealTimeDtos: VulogCarRealTimeDto[] = [];

    let customerFleetServiceDto: VulogFleetServiceDto;

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [];

    const stationDtos: StationDto[] = [];

    beforeAll(async () => {
      // Save cars into DB
      await Promise.all(
        cars.map(async (car) => {
          await carRepo.save(CarEntity.from(car));
        }),
      );

      // Build Vulog Fleet Service DTO
      customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withVehicles(cars.map((car) => car.vulogId.value))
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

      // Map realtime car DTOs
      cars.forEach((car) =>
        vulogCarRealTimeDtos.push(
          new VulogCarRealTimeDtoBuilder()
            .withAutonomy(100)
            .withBattery(100)
            .withFleetId(config.vulogConfig.fleetId.value)
            .withServiceId(customerFleetServiceDto.id)
            .withZones([_.sample(vulogZones)])
            .withId(car.vulogId.value)
            .withVin(car.vin)
            .withPlate(car.plate.value)
            .build(),
        ),
      );

      // Map static car DTOs
      cars.forEach((car) => {
        vulogStaticCarDtos.push(
          new VulogCarStaticInfoDtoBuilder()
            .withId(car.vulogId.value)
            .withModel(car.model)
            .withPlate(car.plate.value)
            .withServiceId(customerFleetServiceDto.id)
            .withVin(car.vin)
            .build(),
        );
      });

      // Map vulog zone DTOs
      vulogZones.forEach((vgZone) => {
        stationDtos.push(
          new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
        );
      });

      // Listing out real-time cars from Vulog
      mockOutgoingService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, vulogCarRealTimeDtos);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      responseObject = await requestFor({
        search: almostMatchedPlateNumber,
      } as any);
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 200', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.Ok);
    });

    it('Should return all expected data', async () => {
      const response: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body.result;

      const realtimeCars = vulogCarRealTimeDtos.map((vgRltCar) =>
        vgRltCar.toRealtimeCar(),
      );

      const realTimeCarsPlateMap = MapUtils.build(
        realtimeCars,
        (rltCar) => rltCar.plate?.value,
      );

      const fleetServicesIdMap = MapUtils.build(
        [customerFleetServiceDto.toFleetService()],
        (fltSvc) => fltSvc.id.value,
      );

      const staticCars = vulogStaticCarDtos.map((staticCarDto) =>
        staticCarDto.toStaticCar(),
      );

      const staticCarsPlateMap = MapUtils.build(
        staticCars,
        (stCar) => stCar.plate.value,
      );

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      const zoneIdStationMap = await stationService.getZoneStationMap();

      const expectedResponseDtos: AdminCarWithMetadataResponseDtoV1[] = cars
        .map((car: Car) => {
          const realtimeCar = realTimeCarsPlateMap.get(car.plate?.value);
          const staticCar = staticCarsPlateMap.get(car.plate?.value);
          const fleetService = fleetServicesIdMap.get(
            staticCar?.serviceId?.value,
          );
          const stickyStation: Station =
            zoneIdStationMap[
              realtimeCar?.allowedStickyZone?.zoneId
            ]?.toStation();

          return AdminCarWithMetadataResponseDtoV1.from(
            car,
            stickyStation,
            fleetService,
            realtimeCar,
          );
        })
        .sort((a, b) => a.plateNumber.localeCompare(b.plateNumber))
        .filter(
          (car) =>
            car.plateNumber.includes(almostMatchedPlateNumber) ||
            car.vin.includes(almostMatchedPlateNumber),
        )
        .map((item) => JSON.parse(JSON.stringify(item)));

      expect(response).toMatchObject(expectedResponseDtos);
    });

    it('Should return expected pagination', () => {
      const returnedResp: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body;

      const { page, pageSize, sortKey, sortType, total, totalPages } =
        returnedResp.pagination as any;

      expect(page).toEqual(1);

      expect(pageSize).toEqual(10);

      expect(sortKey).toEqual('plateNumber');

      expect(sortType).toEqual('ASC');

      expect(total).toEqual(5);

      expect(totalPages).toEqual(1);
    });
  });

  describe('🔥 Listing out cars with metadata successfully: sort by keys and sort by types', () => {
    const cars: Car[] = [
      new CarBuilder()
        .withPlate(new CarPlateNumber('plate-01'))
        .withVin('vin-01')
        .build(),
      new CarBuilder()
        .withPlate(new CarPlateNumber('plate-02'))
        .withVin('vin-02')
        .build(),
      new CarBuilder()
        .withPlate(new CarPlateNumber('plate-03'))
        .withVin('vin-03')
        .build(),
      new CarBuilder()
        .withPlate(new CarPlateNumber('plate-04'))
        .withVin('vin-04')
        .build(),
      new CarBuilder()
        .withPlate(new CarPlateNumber('plate-05'))
        .withVin('vin-05')
        .build(),
    ];

    const carsForCategory: Car[] = [
      new CarBuilder()
        .withPlate(new CarPlateNumber('plate-01'))
        .withVin('vin-01')
        .withModel(CarModelEnum.BlueCar)
        .build(),
      new CarBuilder()
        .withPlate(new CarPlateNumber('plate-02'))
        .withVin('vin-02')
        .withModel(CarModelEnum.OpelCorsaE)
        .build(),
    ];

    const vulogCarRealTimeDtos: VulogCarRealTimeDto[] = [];

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [];

    const stationDtos: StationDto[] = [];

    const customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withVehicles(cars.map((car) => car.vulogId.value))
      .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
      .build();

    // Map realtime car DTOs
    cars.forEach((car) =>
      vulogCarRealTimeDtos.push(
        new VulogCarRealTimeDtoBuilder()
          .withAutonomy(100)
          .withBattery(100)
          .withFleetId(config.vulogConfig.fleetId.value)
          .withServiceId(customerFleetServiceDto.id)
          .withZones([
            _.sample(
              vulogZones
                .map((zone) => zone.zoneId)
                .map((zoneId) =>
                  new VulogZoneBuilder()
                    .withZoneId(zoneId)
                    .withSticky(false)
                    .build(),
                ),
            ),
          ])
          .withId(car.vulogId.value)
          .withVin(car.vin)
          .withPlate(car.plate.value)
          .build(),
      ),
    );

    // Map static car DTOs
    cars.forEach((car) => {
      vulogStaticCarDtos.push(
        new VulogCarStaticInfoDtoBuilder()
          .withId(car.vulogId.value)
          .withModel(car.model)
          .withPlate(car.plate.value)
          .withServiceId(customerFleetServiceDto.id)
          .withVin(car.vin)
          .build(),
      );
    });

    // Map vulog zone DTOs
    vulogZones.forEach((vgZone) => {
      stationDtos.push(
        new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
      );
    });

    const cases = [
      {
        sortKey: 'id' as CarQuerySortKey,
        sortType: PaginationSortingOrder.ASCENDING,
        cars,
        vulogCarRealTimeDtos,
        vulogStaticCarDtos,
        stationDtos,
        customerFleetServiceDto,
        expectedResult: (() => {
          const realtimeCars = vulogCarRealTimeDtos.map((vgRltCar) =>
            vgRltCar.toRealtimeCar(),
          );

          const realTimeCarsPlateMap = MapUtils.build(
            realtimeCars,
            (rltCar) => rltCar.plate?.value,
          );

          const fleetServicesIdMap = MapUtils.build(
            [customerFleetServiceDto.toFleetService()],
            (fltSvc) => fltSvc.id.value,
          );

          const staticCars = vulogStaticCarDtos.map((staticCarDto) =>
            staticCarDto.toStaticCar(),
          );

          const staticCarsPlateMap = MapUtils.build(
            staticCars,
            (stCar) => stCar.plate.value,
          );

          const zoneIdStationMap = stationDtos.reduce((prev, curr) => {
            prev[curr.zoneId] = curr;

            return prev;
          }, {});

          const expectedResponseDtos: AdminCarWithMetadataResponseDtoV1[] =
            _.orderBy(
              cars.map((car: Car) => {
                const realtimeCar = realTimeCarsPlateMap.get(car.plate?.value);
                const staticCar = staticCarsPlateMap.get(car.plate?.value);
                const fleetService = fleetServicesIdMap.get(
                  staticCar?.serviceId?.value,
                );
                const stickyStation: Station =
                  zoneIdStationMap[
                    realtimeCar?.allowedStickyZone?.zoneId
                  ]?.toStation();

                return AdminCarWithMetadataResponseDtoV1.from(
                  car,
                  stickyStation,
                  fleetService,
                  realtimeCar,
                );
              }),
              (obj) => obj.id,
              'asc',
            ).map((item) => JSON.parse(JSON.stringify(item)));

          return expectedResponseDtos;
        })(),
      },
      {
        sortKey: 'plateNumber' as CarQuerySortKey,
        sortType: PaginationSortingOrder.ASCENDING,
        cars,
        vulogCarRealTimeDtos,
        vulogStaticCarDtos,
        stationDtos,
        customerFleetServiceDto,
        expectedResult: (() => {
          const realtimeCars = vulogCarRealTimeDtos.map((vgRltCar) =>
            vgRltCar.toRealtimeCar(),
          );

          const realTimeCarsPlateMap = MapUtils.build(
            realtimeCars,
            (rltCar) => rltCar.plate?.value,
          );

          const fleetServicesIdMap = MapUtils.build(
            [customerFleetServiceDto.toFleetService()],
            (fltSvc) => fltSvc.id.value,
          );

          const staticCars = vulogStaticCarDtos.map((staticCarDto) =>
            staticCarDto.toStaticCar(),
          );

          const staticCarsPlateMap = MapUtils.build(
            staticCars,
            (stCar) => stCar.plate.value,
          );

          const zoneIdStationMap = stationDtos.reduce((prev, curr) => {
            prev[curr.zoneId] = curr;

            return prev;
          }, {});

          const expectedResponseDtos: AdminCarWithMetadataResponseDtoV1[] =
            _.orderBy(
              cars.map((car: Car) => {
                const realtimeCar = realTimeCarsPlateMap.get(car.plate?.value);
                const staticCar = staticCarsPlateMap.get(car.plate?.value);
                const fleetService = fleetServicesIdMap.get(
                  staticCar?.serviceId?.value,
                );
                const stickyStation: Station =
                  zoneIdStationMap[
                    realtimeCar?.allowedStickyZone?.zoneId
                  ]?.toStation();

                return AdminCarWithMetadataResponseDtoV1.from(
                  car,
                  stickyStation,
                  fleetService,
                  realtimeCar,
                );
              }),
              (obj) => obj.plateNumber,
              'asc',
            ).map((item) => JSON.parse(JSON.stringify(item)));

          return expectedResponseDtos;
        })(),
      },
      {
        sortKey: 'vin' as CarQuerySortKey,
        sortType: PaginationSortingOrder.ASCENDING,
        cars,
        vulogCarRealTimeDtos,
        vulogStaticCarDtos,
        stationDtos,
        customerFleetServiceDto,
        expectedResult: (() => {
          const realtimeCars = vulogCarRealTimeDtos.map((vgRltCar) =>
            vgRltCar.toRealtimeCar(),
          );

          const realTimeCarsPlateMap = MapUtils.build(
            realtimeCars,
            (rltCar) => rltCar.plate?.value,
          );

          const fleetServicesIdMap = MapUtils.build(
            [customerFleetServiceDto.toFleetService()],
            (fltSvc) => fltSvc.id.value,
          );

          const staticCars = vulogStaticCarDtos.map((staticCarDto) =>
            staticCarDto.toStaticCar(),
          );

          const staticCarsPlateMap = MapUtils.build(
            staticCars,
            (stCar) => stCar.plate.value,
          );

          const zoneIdStationMap = stationDtos.reduce((prev, curr) => {
            prev[curr.zoneId] = curr;

            return prev;
          }, {});

          const expectedResponseDtos: AdminCarWithMetadataResponseDtoV1[] =
            _.orderBy(
              cars.map((car: Car) => {
                const realtimeCar = realTimeCarsPlateMap.get(car.plate?.value);
                const staticCar = staticCarsPlateMap.get(car.plate?.value);
                const fleetService = fleetServicesIdMap.get(
                  staticCar?.serviceId?.value,
                );
                const stickyStation: Station =
                  zoneIdStationMap[
                    realtimeCar?.allowedStickyZone?.zoneId
                  ]?.toStation();

                return AdminCarWithMetadataResponseDtoV1.from(
                  car,
                  stickyStation,
                  fleetService,
                  realtimeCar,
                );
              }),
              (obj) => obj.vin,
              'asc',
            ).map((item) => JSON.parse(JSON.stringify(item)));

          return expectedResponseDtos;
        })(),
      },
      {
        sortKey: 'category' as CarQuerySortKey,
        sortType: PaginationSortingOrder.ASCENDING,
        cars: carsForCategory,
        vulogCarRealTimeDtos,
        vulogStaticCarDtos,
        stationDtos,
        customerFleetServiceDto,
        expectedResult: (() => {
          const realtimeCars = vulogCarRealTimeDtos.map((vgRltCar) =>
            vgRltCar.toRealtimeCar(),
          );

          const realTimeCarsPlateMap = MapUtils.build(
            realtimeCars,
            (rltCar) => rltCar.plate?.value,
          );

          const fleetServicesIdMap = MapUtils.build(
            [customerFleetServiceDto.toFleetService()],
            (fltSvc) => fltSvc.id.value,
          );

          const staticCars = vulogStaticCarDtos.map((staticCarDto) =>
            staticCarDto.toStaticCar(),
          );

          const staticCarsPlateMap = MapUtils.build(
            staticCars,
            (stCar) => stCar.plate.value,
          );

          const zoneIdStationMap = stationDtos.reduce((prev, curr) => {
            prev[curr.zoneId] = curr;

            return prev;
          }, {});

          const expectedResponseDtos: AdminCarWithMetadataResponseDtoV1[] =
            _.orderBy(
              carsForCategory.map((car: Car) => {
                const realtimeCar = realTimeCarsPlateMap.get(car.plate?.value);
                const staticCar = staticCarsPlateMap.get(car.plate?.value);
                const fleetService = fleetServicesIdMap.get(
                  staticCar?.serviceId?.value,
                );
                const stickyStation: Station =
                  zoneIdStationMap[
                    realtimeCar?.allowedStickyZone?.zoneId
                  ]?.toStation();

                return AdminCarWithMetadataResponseDtoV1.from(
                  car,
                  stickyStation,
                  fleetService,
                  realtimeCar,
                );
              }),
              ['category'],
              'asc',
            ).map((item) => JSON.parse(JSON.stringify(item)));

          return expectedResponseDtos;
        })(),
      },
      {
        sortKey: 'id' as CarQuerySortKey,
        sortType: PaginationSortingOrder.DESCENDING,
        cars,
        vulogCarRealTimeDtos,
        vulogStaticCarDtos,
        stationDtos,
        customerFleetServiceDto,
        expectedResult: (() => {
          const realtimeCars = vulogCarRealTimeDtos.map((vgRltCar) =>
            vgRltCar.toRealtimeCar(),
          );

          const realTimeCarsPlateMap = MapUtils.build(
            realtimeCars,
            (rltCar) => rltCar.plate?.value,
          );

          const fleetServicesIdMap = MapUtils.build(
            [customerFleetServiceDto.toFleetService()],
            (fltSvc) => fltSvc.id.value,
          );

          const staticCars = vulogStaticCarDtos.map((staticCarDto) =>
            staticCarDto.toStaticCar(),
          );

          const staticCarsPlateMap = MapUtils.build(
            staticCars,
            (stCar) => stCar.plate.value,
          );

          const zoneIdStationMap = stationDtos.reduce((prev, curr) => {
            prev[curr.zoneId] = curr;

            return prev;
          }, {});

          const expectedResponseDtos: AdminCarWithMetadataResponseDtoV1[] =
            _.orderBy(
              cars.map((car: Car) => {
                const realtimeCar = realTimeCarsPlateMap.get(car.plate?.value);
                const staticCar = staticCarsPlateMap.get(car.plate?.value);
                const fleetService = fleetServicesIdMap.get(
                  staticCar?.serviceId?.value,
                );
                const stickyStation: Station =
                  zoneIdStationMap[
                    realtimeCar?.allowedStickyZone?.zoneId
                  ]?.toStation();

                return AdminCarWithMetadataResponseDtoV1.from(
                  car,
                  stickyStation,
                  fleetService,
                  realtimeCar,
                );
              }),
              (obj) => obj.id,
              'desc',
            ).map((item) => JSON.parse(JSON.stringify(item)));

          return expectedResponseDtos;
        })(),
      },
      {
        sortKey: 'plateNumber' as CarQuerySortKey,
        sortType: PaginationSortingOrder.DESCENDING,
        cars,
        vulogCarRealTimeDtos,
        vulogStaticCarDtos,
        stationDtos,
        customerFleetServiceDto,
        expectedResult: (() => {
          const realtimeCars = vulogCarRealTimeDtos.map((vgRltCar) =>
            vgRltCar.toRealtimeCar(),
          );

          const realTimeCarsPlateMap = MapUtils.build(
            realtimeCars,
            (rltCar) => rltCar.plate?.value,
          );

          const fleetServicesIdMap = MapUtils.build(
            [customerFleetServiceDto.toFleetService()],
            (fltSvc) => fltSvc.id.value,
          );

          const staticCars = vulogStaticCarDtos.map((staticCarDto) =>
            staticCarDto.toStaticCar(),
          );

          const staticCarsPlateMap = MapUtils.build(
            staticCars,
            (stCar) => stCar.plate.value,
          );

          const zoneIdStationMap = stationDtos.reduce((prev, curr) => {
            prev[curr.zoneId] = curr;

            return prev;
          }, {});

          const expectedResponseDtos: AdminCarWithMetadataResponseDtoV1[] =
            _.orderBy(
              cars.map((car: Car) => {
                const realtimeCar = realTimeCarsPlateMap.get(car.plate?.value);
                const staticCar = staticCarsPlateMap.get(car.plate?.value);
                const fleetService = fleetServicesIdMap.get(
                  staticCar?.serviceId?.value,
                );
                const stickyStation: Station =
                  zoneIdStationMap[
                    realtimeCar?.allowedStickyZone?.zoneId
                  ]?.toStation();

                return AdminCarWithMetadataResponseDtoV1.from(
                  car,
                  stickyStation,
                  fleetService,
                  realtimeCar,
                );
              }),
              (obj) => obj.plateNumber,
              'desc',
            ).map((item) => JSON.parse(JSON.stringify(item)));

          return expectedResponseDtos;
        })(),
      },
      {
        sortKey: 'vin' as CarQuerySortKey,
        sortType: PaginationSortingOrder.DESCENDING,
        cars,
        vulogCarRealTimeDtos,
        vulogStaticCarDtos,
        stationDtos,
        customerFleetServiceDto,
        expectedResult: (() => {
          const realtimeCars = vulogCarRealTimeDtos.map((vgRltCar) =>
            vgRltCar.toRealtimeCar(),
          );

          const realTimeCarsPlateMap = MapUtils.build(
            realtimeCars,
            (rltCar) => rltCar.plate?.value,
          );

          const fleetServicesIdMap = MapUtils.build(
            [customerFleetServiceDto.toFleetService()],
            (fltSvc) => fltSvc.id.value,
          );

          const staticCars = vulogStaticCarDtos.map((staticCarDto) =>
            staticCarDto.toStaticCar(),
          );

          const staticCarsPlateMap = MapUtils.build(
            staticCars,
            (stCar) => stCar.plate.value,
          );

          const zoneIdStationMap = stationDtos.reduce((prev, curr) => {
            prev[curr.zoneId] = curr;

            return prev;
          }, {});

          const expectedResponseDtos: AdminCarWithMetadataResponseDtoV1[] =
            _.orderBy(
              cars.map((car: Car) => {
                const realtimeCar = realTimeCarsPlateMap.get(car.plate?.value);
                const staticCar = staticCarsPlateMap.get(car.plate?.value);
                const fleetService = fleetServicesIdMap.get(
                  staticCar?.serviceId?.value,
                );
                const stickyStation: Station =
                  zoneIdStationMap[
                    realtimeCar?.allowedStickyZone?.zoneId
                  ]?.toStation();

                return AdminCarWithMetadataResponseDtoV1.from(
                  car,
                  stickyStation,
                  fleetService,
                  realtimeCar,
                );
              }),
              (obj) => obj.vin,
              'desc',
            ).map((item) => JSON.parse(JSON.stringify(item)));

          return expectedResponseDtos;
        })(),
      },
      {
        sortKey: 'category' as CarQuerySortKey,
        sortType: PaginationSortingOrder.DESCENDING,
        cars: carsForCategory,
        vulogCarRealTimeDtos,
        vulogStaticCarDtos,
        stationDtos,
        customerFleetServiceDto,
        expectedResult: (() => {
          const realtimeCars = vulogCarRealTimeDtos.map((vgRltCar) =>
            vgRltCar.toRealtimeCar(),
          );

          const realTimeCarsPlateMap = MapUtils.build(
            realtimeCars,
            (rltCar) => rltCar.plate?.value,
          );

          const fleetServicesIdMap = MapUtils.build(
            [customerFleetServiceDto.toFleetService()],
            (fltSvc) => fltSvc.id.value,
          );

          const staticCars = vulogStaticCarDtos.map((staticCarDto) =>
            staticCarDto.toStaticCar(),
          );

          const staticCarsPlateMap = MapUtils.build(
            staticCars,
            (stCar) => stCar.plate.value,
          );

          const zoneIdStationMap = stationDtos.reduce((prev, curr) => {
            prev[curr.zoneId] = curr;

            return prev;
          }, {});

          const expectedResponseDtos: AdminCarWithMetadataResponseDtoV1[] =
            _.orderBy(
              carsForCategory.map((car: Car) => {
                const realtimeCar = realTimeCarsPlateMap.get(car.plate?.value);
                const staticCar = staticCarsPlateMap.get(car.plate?.value);
                const fleetService = fleetServicesIdMap.get(
                  staticCar?.serviceId?.value,
                );
                const stickyStation: Station =
                  zoneIdStationMap[
                    realtimeCar?.allowedStickyZone?.zoneId
                  ]?.toStation();

                return AdminCarWithMetadataResponseDtoV1.from(
                  car,
                  stickyStation,
                  fleetService,
                  realtimeCar,
                );
              }),
              ['category'],
              'desc',
            ).map((item) => JSON.parse(JSON.stringify(item)));

          return expectedResponseDtos;
        })(),
      },
    ];

    test.each(cases)(
      '🔑📈 sort key: $sortKey, sort type: $sortType',
      async ({
        cars,
        customerFleetServiceDto,
        expectedResult,
        sortKey: sortKeyAssert,
        sortType: sortTypeAssert,
        stationDtos,
        vulogCarRealTimeDtos,
        vulogStaticCarDtos,
      }) => {
        let responseObject: supertest.Response;

        // Save cars into DB
        await Promise.all(
          cars.map(async (car) => {
            await carRepo.save(CarEntity.from(car));
          }),
        );

        // Listing out real-time cars from Vulog
        mockOutgoingService
          .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
          .replyOnce(200, vulogCarRealTimeDtos);

        // Listing out fleet services from Vulog
        mockOutgoingService
          .onGet(
            `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
          )
          .reply(200, [customerFleetServiceDto]);

        // Listing out static cars from Vulog
        mockOutgoingService
          .onGet(
            `${VulogPaths.backOfficeListingAllStaticVehicles(
              config.vulogConfig.fleetId,
            )}?${QueryString.stringify({ page: 0, size: 200 })}`,
          )
          .reply(200, vulogStaticCarDtos, { last: true });

        // Listing out stations from STS
        mockOutgoingService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 1,
              pageSize: 200,
            })}`,
          )
          .replyOnce(200, {
            result: stationDtos,
          });

        mockOutgoingService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 2,
              pageSize: 200,
            })}`,
          )
          .replyOnce(200, {
            result: [], // Breaking loop
          });

        // eslint-disable-next-line prefer-const
        responseObject = await requestFor({
          sortKey: sortKeyAssert,
          sortType: sortTypeAssert,
        } as any);

        expect(responseObject.statusCode).toBe(HttpStatusCode.Ok);

        const response: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
          responseObject.body.result;

        expect(response).toMatchObject(expectedResult);

        const returnedResp: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
          responseObject.body;

        const { page, pageSize, sortKey, sortType, total, totalPages } =
          returnedResp.pagination as any;

        expect(page).toEqual(1);

        expect(pageSize).toEqual(10);

        expect(sortKey).toEqual(sortKeyAssert);

        expect(sortType).toEqual(sortTypeAssert);

        expect(total).toEqual(cars.length);

        expect(totalPages).toEqual(Math.ceil(cars.length / 10));

        await cleanUpResources();
      },
    );
  });

  describe('🔥 Listing out cars with metadata successfully: cars out of fleet services', () => {
    let responseObject: supertest.Response;

    const cars: Car[] = [
      new CarBuilder().withPlate(new CarPlateNumber('plate-01')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-02')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-03')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-04')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-05')).build(),
    ];

    const vulogCarRealTimeDtos: VulogCarRealTimeDto[] = [];

    let customerFleetServiceDto: VulogFleetServiceDto;

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [];

    const stationDtos: StationDto[] = [];

    beforeAll(async () => {
      // Save cars into DB
      await Promise.all(
        cars.map(async (car) => {
          await carRepo.save(CarEntity.from(car));
        }),
      );

      // Build Vulog Fleet Service DTO
      customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withVehicles(cars.map((car) => car.vulogId.value))
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

      // Map realtime car DTOs
      cars.forEach((car) =>
        vulogCarRealTimeDtos.push(
          new VulogCarRealTimeDtoBuilder()
            .withAutonomy(100)
            .withBattery(100)
            .withFleetId(config.vulogConfig.fleetId.value)
            .withZones([_.sample(vulogZones)])
            .withId(car.vulogId.value)
            .withVin(car.vin)
            .withPlate(car.plate.value)
            .build(),
        ),
      );

      // Map static car DTOs
      cars.forEach((car) => {
        vulogStaticCarDtos.push(
          new VulogCarStaticInfoDtoBuilder()
            .withId(car.vulogId.value)
            .withModel(car.model)
            .withPlate(car.plate.value)
            .withVin(car.vin)
            .withServiceId(customerFleetServiceDto.id)
            .build(),
        );
      });

      // Map vulog zone DTOs
      vulogZones.forEach((vgZone) => {
        stationDtos.push(
          new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
        );
      });

      // Listing out real-time cars from Vulog
      mockOutgoingService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, vulogCarRealTimeDtos);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      responseObject = await requestFor();
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 200', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.Ok);
    });

    it('Should return all expected data', async () => {
      const response: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body.result;

      const staticCarsMap = MapUtils.build(cars, (car) => car.plate.value);

      const realtimeCars = vulogCarRealTimeDtos.map((vgRltCar) =>
        vgRltCar.toRealtimeCar(
          new CarModel(staticCarsMap.get(vgRltCar.plate).model),
          customerFleetServiceDto.toFleetService().getZoneIdsAsMap(),
        ),
      );

      const realTimeCarsPlateMap = MapUtils.build(
        realtimeCars,
        (rltCar) => rltCar.plate?.value,
      );

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      const zoneIdStationMap = await stationService.getZoneStationMap();

      const expectedResponseDtos: AdminCarWithMetadataResponseDtoV1[] = cars
        .map((car: Car) => {
          const realtimeCar = realTimeCarsPlateMap.get(car.plate?.value);
          const fleetService = customerFleetServiceDto;
          const stickyStation: Station =
            zoneIdStationMap[
              realtimeCar?.allowedStickyZone?.zoneId
            ]?.toStation();

          return AdminCarWithMetadataResponseDtoV1.from(
            car,
            stickyStation,
            fleetService.toFleetService(),
            realtimeCar,
          );
        })
        .sort((a, b) => a.plateNumber.localeCompare(b.plateNumber))
        .map((item) => JSON.parse(JSON.stringify(item)));

      expect(response).toMatchObject(expectedResponseDtos);
    });

    it('Should return expected pagination', () => {
      const returnedResp: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body;

      const { page, pageSize, sortKey, sortType, total, totalPages } =
        returnedResp.pagination as any;

      expect(page).toEqual(1);

      expect(pageSize).toEqual(10);

      expect(sortKey).toEqual('plateNumber');

      expect(sortType).toEqual('ASC');

      expect(total).toEqual(cars.length);

      expect(totalPages).toEqual(Math.ceil(cars.length / 10));
    });
  });

  describe('🔥 Listing out cars with metadata successfully: cars not sticky with stations', () => {
    let responseObject: supertest.Response;

    const cars: Car[] = [
      new CarBuilder().withPlate(new CarPlateNumber('plate-01')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-02')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-03')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-04')).build(),
      new CarBuilder().withPlate(new CarPlateNumber('plate-05')).build(),
    ];

    const vulogCarRealTimeDtos: VulogCarRealTimeDto[] = [];

    let customerFleetServiceDto: VulogFleetServiceDto;

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ];

    const vulogStaticCarDtos: VulogCarStaticInfoDto[] = [];

    const stationDtos: StationDto[] = [];

    beforeAll(async () => {
      // Save cars into DB
      await Promise.all(
        cars.map(async (car) => {
          await carRepo.save(CarEntity.from(car));
        }),
      );

      // Build Vulog Fleet Service DTO
      customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withVehicles(cars.map((car) => car.vulogId.value))
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

      // Map realtime car DTOs
      cars.forEach((car) =>
        vulogCarRealTimeDtos.push(
          new VulogCarRealTimeDtoBuilder()
            .withAutonomy(100)
            .withBattery(100)
            .withFleetId(config.vulogConfig.fleetId.value)
            .withServiceId(customerFleetServiceDto.id)
            .withZones([_.sample(vulogZones)])
            .withId(car.vulogId.value)
            .withVin(car.vin)
            .withPlate(car.plate.value)
            .build(),
        ),
      );

      // Map static car DTOs
      cars.forEach((car) => {
        vulogStaticCarDtos.push(
          new VulogCarStaticInfoDtoBuilder()
            .withId(car.vulogId.value)
            .withModel(car.model)
            .withPlate(car.plate.value)
            .withServiceId(customerFleetServiceDto.id)
            .withVin(car.vin)
            .build(),
        );
      });

      // Map vulog zone DTOs
      vulogZones.forEach(() => {
        stationDtos.push(new StationDtoBuilder().build()); // Not sticky
      });

      // Listing out real-time cars from Vulog
      mockOutgoingService
        .onGet(`${VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId)}`)
        .replyOnce(200, vulogCarRealTimeDtos);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, vulogStaticCarDtos, { last: true });

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      responseObject = await requestFor();
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 200', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.Ok);
    });

    it('Should return all expected data', async () => {
      const response: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body.result;

      const realtimeCars = vulogCarRealTimeDtos.map((vgRltCar) =>
        vgRltCar.toRealtimeCar(),
      );

      const realTimeCarsPlateMap = MapUtils.build(
        realtimeCars,
        (rltCar) => rltCar.plate?.value,
      );

      const fleetServicesIdMap = MapUtils.build(
        [customerFleetServiceDto.toFleetService()],
        (fltSvc) => fltSvc.id.value,
      );

      const staticCars = vulogStaticCarDtos.map((staticCarDto) =>
        staticCarDto.toStaticCar(),
      );

      const staticCarsPlateMap = MapUtils.build(
        staticCars,
        (stCar) => stCar.plate.value,
      );

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      const expectedResponseDtos: AdminCarWithMetadataResponseDtoV1[] = cars
        .map((car: Car) => {
          const realtimeCar = realTimeCarsPlateMap.get(car.plate?.value);
          const staticCar = staticCarsPlateMap.get(car.plate?.value);
          const fleetService = fleetServicesIdMap.get(
            staticCar?.serviceId?.value,
          );
          const stickyStation = null;

          return AdminCarWithMetadataResponseDtoV1.from(
            car,
            stickyStation,
            fleetService,
            realtimeCar,
          );
        })
        .sort((a, b) => a.plateNumber.localeCompare(b.plateNumber))
        .map((item) => JSON.parse(JSON.stringify(item)));

      expect(response).toMatchObject(expectedResponseDtos);
    });

    it('Should return expected pagination', () => {
      const returnedResp: PageResult<AdminCarWithMetadataResponseDtoV1[]> =
        responseObject.body;

      const { page, pageSize, sortKey, sortType, total, totalPages } =
        returnedResp.pagination as any;

      expect(page).toEqual(1);

      expect(pageSize).toEqual(10);

      expect(sortKey).toEqual('plateNumber');

      expect(sortType).toEqual('ASC');

      expect(total).toEqual(cars.length);

      expect(totalPages).toEqual(Math.ceil(cars.length / 10));
    });
  });

  describe('💧 Listing out cars with metadata failed: failed to pass query params validation', () => {
    const cases = [
      {
        paramName: 'sortKey',
        paramValue: 'invalid',
      },
    ];

    test.each(cases)(
      '`$paramName` does not bypass validation',
      async ({ paramName, paramValue }) => {
        let responseObject: supertest.Response;

        // eslint-disable-next-line prefer-const
        responseObject = await requestFor({
          [paramName]: paramValue,
        } as any);

        expect(responseObject.statusCode).toBe(HttpStatusCode.BadRequest);

        const errorMessage = responseObject.body.message;

        expect(errorMessage).toContain(
          'sortKey must be one of the following values: ',
        );

        await cleanUpResources();
      },
    );
  });
});
