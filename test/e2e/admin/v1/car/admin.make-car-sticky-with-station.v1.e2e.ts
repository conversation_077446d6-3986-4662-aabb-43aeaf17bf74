import * as supertest from 'supertest';

import { INestApplication } from '@nestjs/common';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { faker } from '@faker-js/faker';
import { getRepositoryToken } from '@nestjs/typeorm';
import { HttpStatusCode } from 'axios';
import MockAdapter from 'axios-mock-adapter';
import * as _ from 'lodash';
import { DateTime } from 'luxon';
import * as QueryString from 'qs';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { RootRouteSegment, RouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import {
  BsgUserId,
  CarPlateNumber,
  VulogCarId,
  VulogProfileId,
  VulogUserId,
  VulogZoneId,
} from 'src/common/tiny-types';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { config } from 'src/config';
import { AdminMakeStationStickyWithCarParamDtoV1 } from 'src/controllers/admin/v1/car/dto/request/admin.station-sticky-with-car.param.dto.v1';
import { AdminStickyCarStationResponseDtoV1 } from 'src/controllers/admin/v1/car/dto/response/admin.sticky-car-station.response.dto.v1';
import { StickyServiceResponseDto } from 'src/controllers/admin/v1/car/dto/response/sticky-service.response.dto';
import { StickyStationResponseDto } from 'src/controllers/admin/v1/car/dto/response/sticky-station.response.dto';
import {
  SubscriptionStatus,
  UserStatus,
} from 'src/external/user-subscription/constants';
import { CacheService } from 'src/logic/cache/cache.service';
import { Car } from 'src/logic/car/domain/car';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { StationPaths } from 'src/logic/station/station-paths';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { StationDto } from 'src/model/dtos/station.dto';
import { VulogCarRealTimeDto } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogCarStaticInfoDto } from 'src/model/dtos/vulog-car-static-info/vulog-car-static-info.dto';
import { VulogFleetServiceDto } from 'src/model/dtos/vulog-fleet-service/vulog-fleet-service.dto';
import {
  VulogZone,
  VulogZoneType,
} from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { VulogEventEntity } from 'src/model/repositories/entities/vulog-event.entity';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { AppBuilder } from 'test/helpers/app.builder';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { VulogCarStaticInfoDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car-static-info.dto.builder';
import {
  VulogCarRealTimeDtoBuilder,
  VulogZoneBuilder,
} from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { VulogUserBuilder } from 'test/helpers/builders/vulog-user.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { UssApiMocker } from 'test/helpers/external-services/uss.api-mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';
import { Repository } from 'typeorm';

const testingApiRoute = `/api/v1/admin/${RouteSegment.Car.Index}/${RouteSegment.Car.AttachStationToCar}`;

const userId = aUUID();

const email = faker.internet.email();

const customerUsableFleetServiceID = config.vulogConfig.serviceId;

describe(`[E2E] PUT: ${testingApiRoute}`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let reservationRepo: Repository<ReservationEntity>;
  let vulogUserRepo: Repository<VulogUserEntity>;
  let carRepo: Repository<CarEntity>;
  let vulogEventRepo: Repository<VulogEventEntity>;

  let cacheService: CacheService;
  let mockOutgoingService: MockAdapter;
  let mockUssApiService: MockAdapter;

  const mockAuthResponse = createTestVulogAuthResponse();
  const mockVulogUser = VulogUserBuilder.creating()
    .withBsgUserId(new BsgUserId(userId))
    .build();

  const ussUserDto = new InternalUserDetailResponseDtoV1Builder()
    .withId(userId)
    .withUserStatus(UserStatus.VERIFIED)
    .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
    .build();

  function requestFor(
    pathParams: AdminMakeStationStickyWithCarParamDtoV1,
  ): supertest.Test {
    const qualifiedUrl = testingApiRoute
      .replace(':plateNumber', pathParams.plateNumber)
      .replace(':stationId', pathParams.stationId);

    const superTest = supertest(app.getHttpServer())
      .put(`${qualifiedUrl}`)
      .send();

    return superTest;
  }

  beforeAll(async () => {
    app = await appBuilder
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(aUUID()), true)
      .buildAndStart();

    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));
    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );
    vulogUserRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );
    vulogEventRepo = app.get<Repository<VulogEventEntity>>(
      getRepositoryToken(VulogEventEntity),
    );
    cacheService = app.get<CacheService>(CacheService);
    const httpApdaterService = app.get<HttpAdapterService>(HttpAdapterService);
    mockOutgoingService = new MockAdapter(
      httpApdaterService.httpService.axiosRef,
      { onNoMatch: 'throwException' },
    );
    mockUssApiService = new UssApiMocker(app).mock();

    const userDto = createTestVulogUserDto();

    userDto.email = email;

    mockOutgoingService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, userDto);

    const userServicesAndProfiles = createTestVulogUserServicesDto();
    userServicesAndProfiles.userId = userDto.id;

    /**
     * Doing Vulog authentication mock
     */
    mockOutgoingService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(userDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    mockOutgoingService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, mockAuthResponse);

    mockUssApiService
      .onGet(
        `${ApiRouteUtils.buildApiRouteSegment(
          RootRouteSegment.Internal,
          ApiVersion.V1,
          ['users', userId],
        )}`,
      )
      .reply(200, {
        success: true,
        result: JSON.parse(JSON.stringify(ussUserDto)),
      } as BaseResponseDto);

    await vulogUserRepo.save(VulogUserEntity.from(mockVulogUser));

    mockVulogCredential(app);
  });

  afterAll(async () => {
    await cleanUpResources();

    await app.close();

    mockOutgoingService.reset();
    mockUssApiService.reset();
  });

  async function cleanUpResources(): Promise<void> {
    await reservationRepo.delete({});
    await carRepo.delete({});
    await vulogEventRepo.delete({});
    await vulogUserRepo.delete({});

    await cacheService.clearAll();

    jest.clearAllMocks();
  }

  describe('🔥 Make station sticky with car successfully', () => {
    let responseObject: supertest.Response;

    const expectedToBeStickyStationId = aUUID();

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
    ];

    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber('plate-01'))
      .build();

    const customerFleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withId(customerUsableFleetServiceID.value)
        .withVehicles([car.vulogId.value])
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

    const vulogCarRealTimeDto: VulogCarRealTimeDto =
      new VulogCarRealTimeDtoBuilder()
        .withAutonomy(100)
        .withBattery(100)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withServiceId(customerFleetServiceDto.id)
        .withZones(vulogZones)
        .withId(car.vulogId.value)
        .withVin(car.vin)
        .withPlate(car.plate.value)
        .build();

    const vulogStaticCarDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withId(car.vulogId.value)
        .withModel(car.model)
        .withPlate(car.plate.value)
        .withServiceId(customerFleetServiceDto.id)
        .withVin(car.vin)
        .build();

    const stationDtos: StationDto[] = vulogZones.map((vgZone) =>
      new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
    );

    stationDtos[0].id = expectedToBeStickyStationId;

    beforeAll(async () => {
      // Save cars into DB
      await carRepo.save(CarEntity.from(car));

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, vulogCarRealTimeDto);

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      // Making PUT call to Vulog to stick station with car
      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            new VulogZoneId(stationDtos[0].zoneId),
          )}`,
        )
        .replyOnce(200);

      const updatedVulogCarRealtimeDto = _.cloneDeep(vulogCarRealTimeDto);

      const toBeStickyZone = updatedVulogCarRealtimeDto.zones.find(
        (zone) => zone.zoneId === stationDtos[0].zoneId,
      );

      toBeStickyZone.sticky = true;

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, updatedVulogCarRealtimeDto);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, [vulogStaticCarDto], { last: true });

      responseObject = await requestFor({
        plateNumber: car.plate.value,
        stationId: expectedToBeStickyStationId,
      });
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 200', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.Ok);
    });

    it('Verify response', async () => {
      const response: AdminStickyCarStationResponseDtoV1 =
        responseObject.body.result;

      expect(response).toEqual(
        JSON.parse(
          JSON.stringify({
            id: car.id,
            vulogCarId: car.vulogId.value,
            plateNumber: car.plate.value,
            vin: car.vin,
            category: car.model,
            disabled: vulogCarRealTimeDto[0]?.disabled,
            stickyStation: StickyStationResponseDto.from(
              stationDtos[0].toStation(),
            ),
            service: StickyServiceResponseDto.from(
              customerFleetServiceDto.toFleetService(),
            ),
          }),
        ),
      );
    });
  });

  describe.skip('🔥 Make station sticky with car successfully, but needs to detach from invalid sticky zones first', () => {
    let responseObject: supertest.Response;

    const expectedToBeStickyStationId = aUUID();

    const nonCustomerVulogZones: VulogZone[] = [
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(true)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(true)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(true)
        .build(),
    ];

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
    ];

    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber('plate-01'))
      .build();

    const customerFleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withId(customerUsableFleetServiceID.value)
        .withVehicles([car.vulogId.value])
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

    const vulogCarRealTimeDto: VulogCarRealTimeDto =
      new VulogCarRealTimeDtoBuilder()
        .withAutonomy(100)
        .withBattery(100)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withServiceId(customerFleetServiceDto.id)
        .withZones([...vulogZones, ...nonCustomerVulogZones])
        .withId(car.vulogId.value)
        .withVin(car.vin)
        .withPlate(car.plate.value)
        .build();

    const vulogStaticCarDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withId(car.vulogId.value)
        .withModel(car.model)
        .withPlate(car.plate.value)
        .withServiceId(customerFleetServiceDto.id)
        .withVin(car.vin)
        .build();

    const stationDtos: StationDto[] = vulogZones.map((vgZone) =>
      new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
    );

    stationDtos[0].id = expectedToBeStickyStationId;

    beforeAll(async () => {
      // Save cars into DB
      await carRepo.save(CarEntity.from(car));

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, vulogCarRealTimeDto);

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      // Detach invalid sticky zones from car
      nonCustomerVulogZones.forEach((zone) => {
        mockOutgoingService
          .onPut(
            `${VulogPaths.backOfficeStickStationWithCar(
              config.vulogConfig.fleetId,
              car.vulogId,
              new VulogZoneId(zone.zoneId),
            )}`,
          )
          .replyOnce(200);
      });

      // Making PUT call to Vulog to stick station with car
      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            new VulogZoneId(stationDtos[0].zoneId),
          )}`,
        )
        .replyOnce(200);

      let updatedVulogCarRealtimeDto = _.cloneDeep(vulogCarRealTimeDto);

      // After detaching invalid sticky zones from car
      updatedVulogCarRealtimeDto.zones = updatedVulogCarRealtimeDto.zones.map(
        (zone) => {
          zone.sticky = false;

          return zone;
        },
      );

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, updatedVulogCarRealtimeDto);

      updatedVulogCarRealtimeDto = _.cloneDeep(vulogCarRealTimeDto);

      const toBeStickyZone = updatedVulogCarRealtimeDto.zones.find(
        (zone) => zone.zoneId === stationDtos[0].zoneId,
      );

      toBeStickyZone.sticky = true;

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, updatedVulogCarRealtimeDto);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, [vulogStaticCarDto], { last: true });

      responseObject = await requestFor({
        plateNumber: car.plate.value,
        stationId: expectedToBeStickyStationId,
      });
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 200', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.Ok);
    });

    it('Verify response', async () => {
      const response: AdminStickyCarStationResponseDtoV1 =
        responseObject.body.result;

      expect(response).toEqual(
        JSON.parse(
          JSON.stringify({
            id: car.id,
            vulogCarId: car.vulogId.value,
            plateNumber: car.plate.value,
            vin: car.vin,
            category: car.model,
            disabled: vulogCarRealTimeDto[0]?.disabled,
            stickyStation: StickyStationResponseDto.from(
              stationDtos[0].toStation(),
            ),
            service: StickyServiceResponseDto.from(
              customerFleetServiceDto.toFleetService(),
            ),
          }),
        ),
      );
    });
  });

  describe('💦 Expected station is not under customer fleet service', () => {
    let responseObject: supertest.Response;

    const expectedToBeStickyStationId = aUUID();

    const nonCustomerVulogZone: VulogZone = new VulogZoneBuilder()
      .withType(VulogZoneType.Allowed)
      .withSticky(false)
      .build();

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
    ];

    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber('plate-01'))
      .build();

    const customerFleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withId(customerUsableFleetServiceID.value)
        .withVehicles([car.vulogId.value])
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

    const vulogCarRealTimeDto: VulogCarRealTimeDto =
      new VulogCarRealTimeDtoBuilder()
        .withAutonomy(100)
        .withBattery(100)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withServiceId(customerFleetServiceDto.id)
        .withZones(vulogZones)
        .withId(car.vulogId.value)
        .withVin(car.vin)
        .withPlate(car.plate.value)
        .build();

    const vulogStaticCarDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withId(car.vulogId.value)
        .withModel(car.model)
        .withPlate(car.plate.value)
        .withServiceId(customerFleetServiceDto.id)
        .withVin(car.vin)
        .build();

    const stationDtos: StationDto[] = vulogZones.map((vgZone) =>
      new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
    );

    stationDtos.push(
      new StationDtoBuilder().withZoneId(nonCustomerVulogZone.zoneId).build(),
    );

    stationDtos[stationDtos.length - 1].id = expectedToBeStickyStationId;

    beforeAll(async () => {
      // Save cars into DB
      await carRepo.save(CarEntity.from(car));

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, vulogCarRealTimeDto);

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      // Making PUT call to Vulog to stick station with car
      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            new VulogZoneId(stationDtos[0].zoneId),
          )}`,
        )
        .replyOnce(200);

      const updatedVulogCarRealtimeDto = _.cloneDeep(vulogCarRealTimeDto);

      const toBeStickyZone = updatedVulogCarRealtimeDto.zones.find(
        (zone) => zone.zoneId === stationDtos[0].zoneId,
      );

      toBeStickyZone.sticky = true;

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, updatedVulogCarRealtimeDto);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, [vulogStaticCarDto], { last: true });

      responseObject = await requestFor({
        plateNumber: car.plate.value,
        stationId: expectedToBeStickyStationId,
      });
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 400', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.BadRequest);
    });

    it('Verify response', async () => {
      const responseBody = responseObject.body;

      expect(responseBody.message).toEqual(
        `An error has occurred. - Cause: Expected station ${expectedToBeStickyStationId} is not under customer fleet service. Pinning is not allowed`,
      );
    });
  });

  describe.skip('💦 Make station sticky with car successfully, but needs to detach from invalid sticky zones first, but failed', () => {
    let responseObject: supertest.Response;

    const expectedToBeStickyStationId = aUUID();

    const nonCustomerVulogZones: VulogZone[] = [
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(true)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(true)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(true)
        .build(),
    ];

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
    ];

    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber('plate-01'))
      .build();

    const customerFleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withId(customerUsableFleetServiceID.value)
        .withVehicles([car.vulogId.value])
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

    const vulogCarRealTimeDto: VulogCarRealTimeDto =
      new VulogCarRealTimeDtoBuilder()
        .withAutonomy(100)
        .withBattery(100)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withServiceId(customerFleetServiceDto.id)
        .withZones([...vulogZones, ...nonCustomerVulogZones])
        .withId(car.vulogId.value)
        .withVin(car.vin)
        .withPlate(car.plate.value)
        .build();

    const vulogStaticCarDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withId(car.vulogId.value)
        .withModel(car.model)
        .withPlate(car.plate.value)
        .withServiceId(customerFleetServiceDto.id)
        .withVin(car.vin)
        .build();

    const stationDtos: StationDto[] = vulogZones.map((vgZone) =>
      new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
    );

    stationDtos[0].id = expectedToBeStickyStationId;

    beforeAll(async () => {
      // Save cars into DB
      await carRepo.save(CarEntity.from(car));

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, vulogCarRealTimeDto);

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      // Detach invalid sticky zones from car
      nonCustomerVulogZones.forEach((zone) => {
        mockOutgoingService
          .onPut(
            `${VulogPaths.backOfficeStickStationWithCar(
              config.vulogConfig.fleetId,
              car.vulogId,
              new VulogZoneId(zone.zoneId),
            )}`,
          )
          .replyOnce(500);
      });

      // Making PUT call to Vulog to stick station with car
      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            new VulogZoneId(stationDtos[0].zoneId),
          )}`,
        )
        .replyOnce(200);

      let updatedVulogCarRealtimeDto = _.cloneDeep(vulogCarRealTimeDto);

      // After detaching invalid sticky zones from car
      updatedVulogCarRealtimeDto.zones = updatedVulogCarRealtimeDto.zones.map(
        (zone) => {
          zone.sticky = false;

          return zone;
        },
      );

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, updatedVulogCarRealtimeDto);

      updatedVulogCarRealtimeDto = _.cloneDeep(vulogCarRealTimeDto);

      const toBeStickyZone = updatedVulogCarRealtimeDto.zones.find(
        (zone) => zone.zoneId === stationDtos[0].zoneId,
      );

      toBeStickyZone.sticky = true;

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, updatedVulogCarRealtimeDto);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, [vulogStaticCarDto], { last: true });

      responseObject = await requestFor({
        plateNumber: car.plate.value,
        stationId: expectedToBeStickyStationId,
      });
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return response', () => {
      expect(responseObject.statusCode).toBe(
        HttpStatusCode.InternalServerError,
      );
    });

    it('Verify response', async () => {
      const responseBody = responseObject.body;

      expect(responseBody.message).toEqual(
        `An error has occurred. - Cause: Detected non-customer sticky zones, but failed to detach them from car. Please try again.`,
      );
    });
  });

  describe('💦 Car not found', () => {
    let responseObject: supertest.Response;

    const expectedToBeStickyStationId = aUUID();

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
    ];

    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber('plate-01'))
      .build();

    const stationDtos: StationDto[] = vulogZones.map((vgZone) =>
      new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
    );

    stationDtos[0].id = expectedToBeStickyStationId;

    beforeAll(async () => {
      responseObject = await requestFor({
        plateNumber: car.plate.value,
        stationId: expectedToBeStickyStationId,
      });
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 400', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.BadRequest);
    });

    it('Verify response', async () => {
      const responseBody = responseObject.body;

      expect(responseBody.message).toEqual(
        `An error has occurred. - Cause: Car [Plate: ${car.plate.value}] is not found`,
      );
    });
  });

  describe('💦 Station not found', () => {
    let responseObject: supertest.Response;

    const expectedToBeStickyStationId = aUUID();

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
    ];

    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber('plate-01'))
      .build();

    const stationDtos: StationDto[] = vulogZones.map((vgZone) =>
      new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
    );

    stationDtos[0].id = expectedToBeStickyStationId;

    beforeAll(async () => {
      // Save cars into DB
      await carRepo.save(CarEntity.from(car));

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [],
        });

      responseObject = await requestFor({
        plateNumber: car.plate.value,
        stationId: expectedToBeStickyStationId,
      });
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 400', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.BadRequest);
    });

    it('Verify response', async () => {
      const responseBody = responseObject.body;

      expect(responseBody.message).toEqual(
        `Station [ID: ${expectedToBeStickyStationId}] is not found`,
      );
    });
  });

  describe('💦 On-going reservation is existing', () => {
    let responseObject: supertest.Response;

    const expectedToBeStickyStationId = aUUID();

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
    ];

    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber('plate-01'))
      .build();

    const customerFleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withId(customerUsableFleetServiceID.value)
        .withVehicles([car.vulogId.value])
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

    const vulogCarRealTimeDto: VulogCarRealTimeDto =
      new VulogCarRealTimeDtoBuilder()
        .withAutonomy(100)
        .withBattery(100)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withServiceId(customerFleetServiceDto.id)
        .withZones(vulogZones)
        .withId(car.vulogId.value)
        .withVin(car.vin)
        .withPlate(car.plate.value)
        .build();

    const vulogStaticCarDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withId(car.vulogId.value)
        .withModel(car.model)
        .withPlate(car.plate.value)
        .withServiceId(customerFleetServiceDto.id)
        .withVin(car.vin)
        .build();

    const stationDtos: StationDto[] = vulogZones.map((vgZone) =>
      new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
    );

    stationDtos[0].id = expectedToBeStickyStationId;

    const existingStationReservationEntity = new ReservationEntityBuilder()
      .makeCarId(car.vulogId.value)
      .makeCarModel(car.model)
      .makePlateNumber(car.plate.value)
      .makeStatus(ReservationStatus.Reserved)
      .makeExpiresAt(DateTime.now().plus({ hours: 5 }).toJSDate())
      .build();

    beforeAll(async () => {
      // Save cars into DB
      await carRepo.save(CarEntity.from(car));

      await reservationRepo.save(existingStationReservationEntity);

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, vulogCarRealTimeDto);

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      // Making PUT call to Vulog to stick station with car
      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            new VulogZoneId(stationDtos[0].zoneId),
          )}`,
        )
        .replyOnce(200);

      const updatedVulogCarRealtimeDto = _.cloneDeep(vulogCarRealTimeDto);

      const toBeStickyZone = updatedVulogCarRealtimeDto.zones.find(
        (zone) => zone.zoneId === stationDtos[0].zoneId,
      );

      toBeStickyZone.sticky = true;

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, updatedVulogCarRealtimeDto);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, [vulogStaticCarDto], { last: true });

      responseObject = await requestFor({
        plateNumber: car.plate.value,
        stationId: expectedToBeStickyStationId,
      });
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 400', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.BadRequest);
    });

    it('Verify response', async () => {
      const responseBody = responseObject.body;

      expect(responseBody.message).toEqual(
        `An error has occurred. - Cause: The car [Vulog Car Id: ${car.vulogId.value}] is currently in use. Could not perform attaching station to car`,
      );
    });
  });

  describe('💦 On-going rental is existing', () => {
    let responseObject: supertest.Response;

    const expectedToBeStickyStationId = aUUID();

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
    ];

    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber('plate-01'))
      .build();

    const customerFleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withId(customerUsableFleetServiceID.value)
        .withVehicles([car.vulogId.value])
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

    const vulogCarRealTimeDto: VulogCarRealTimeDto =
      new VulogCarRealTimeDtoBuilder()
        .withAutonomy(100)
        .withBattery(100)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withServiceId(customerFleetServiceDto.id)
        .withZones(vulogZones)
        .withId(car.vulogId.value)
        .withVin(car.vin)
        .withPlate(car.plate.value)
        .build();

    const vulogStaticCarDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withId(car.vulogId.value)
        .withModel(car.model)
        .withPlate(car.plate.value)
        .withServiceId(customerFleetServiceDto.id)
        .withVin(car.vin)
        .build();

    const stationDtos: StationDto[] = vulogZones.map((vgZone) =>
      new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
    );

    stationDtos[0].id = expectedToBeStickyStationId;

    const existingStationReservationEntity = new ReservationEntityBuilder()
      .makeCarId(car.vulogId.value)
      .makeCarModel(car.model)
      .makePlateNumber(car.plate.value)
      .makeStatus(ReservationStatus.Converted)
      .makeExpiresAt(DateTime.now().plus({ hours: 5 }).toJSDate())
      .makeEndedAt(null)
      .build();

    beforeAll(async () => {
      // Save cars into DB
      await carRepo.save(CarEntity.from(car));

      await reservationRepo.save(existingStationReservationEntity);

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, vulogCarRealTimeDto);

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      // Making PUT call to Vulog to stick station with car
      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            new VulogZoneId(stationDtos[0].zoneId),
          )}`,
        )
        .replyOnce(200);

      const updatedVulogCarRealtimeDto = _.cloneDeep(vulogCarRealTimeDto);

      const toBeStickyZone = updatedVulogCarRealtimeDto.zones.find(
        (zone) => zone.zoneId === stationDtos[0].zoneId,
      );

      toBeStickyZone.sticky = true;

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, updatedVulogCarRealtimeDto);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, [vulogStaticCarDto], { last: true });

      responseObject = await requestFor({
        plateNumber: car.plate.value,
        stationId: expectedToBeStickyStationId,
      });
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 400', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.BadRequest);
    });

    it('Verify response', async () => {
      const responseBody = responseObject.body;

      expect(responseBody.message).toEqual(
        `An error has occurred. - Cause: The car [Vulog Car Id: ${car.vulogId.value}] is currently in use. Could not perform attaching station to car`,
      );
    });
  });

  describe('💦 Already sticked', () => {
    let responseObject: supertest.Response;

    const expectedToBeStickyStationId = aUUID();

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(true)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
    ];

    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber('plate-01'))
      .build();

    const customerFleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withId(customerUsableFleetServiceID.value)
        .withVehicles([car.vulogId.value])
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

    const vulogCarRealTimeDto: VulogCarRealTimeDto =
      new VulogCarRealTimeDtoBuilder()
        .withAutonomy(100)
        .withBattery(100)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withServiceId(customerFleetServiceDto.id)
        .withZones(vulogZones)
        .withId(car.vulogId.value)
        .withVin(car.vin)
        .withPlate(car.plate.value)
        .build();

    const vulogStaticCarDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withId(car.vulogId.value)
        .withModel(car.model)
        .withPlate(car.plate.value)
        .withServiceId(customerFleetServiceDto.id)
        .withVin(car.vin)
        .build();

    const stationDtos: StationDto[] = vulogZones.map((vgZone) =>
      new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
    );

    stationDtos[0].id = expectedToBeStickyStationId;

    beforeAll(async () => {
      // Save cars into DB
      await carRepo.save(CarEntity.from(car));

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, vulogCarRealTimeDto);

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      // Making PUT call to Vulog to stick station with car
      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            new VulogZoneId(stationDtos[0].zoneId),
          )}`,
        )
        .replyOnce(200);

      const updatedVulogCarRealtimeDto = _.cloneDeep(vulogCarRealTimeDto);

      const toBeStickyZone = updatedVulogCarRealtimeDto.zones.find(
        (zone) => zone.zoneId === stationDtos[0].zoneId,
      );

      toBeStickyZone.sticky = true;

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, updatedVulogCarRealtimeDto);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, [vulogStaticCarDto], { last: true });

      responseObject = await requestFor({
        plateNumber: car.plate.value,
        stationId: expectedToBeStickyStationId,
      });
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return status 400', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.BadRequest);
    });

    it('Verify response', async () => {
      const response = responseObject.body;

      expect(response.message).toEqual(
        `An error has occurred. - Cause: The car [Vulog Car ID: ${car.vulogId.value}] is currently attached with station [Station ID: ${expectedToBeStickyStationId}]. Please detach it from current station before station attachment.`,
      );
    });
  });

  describe('💦 Failed to stick at Vulog', () => {
    let responseObject: supertest.Response;

    const expectedToBeStickyStationId = aUUID();

    const vulogZones: VulogZone[] = [
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
      new VulogZoneBuilder()
        .withType(VulogZoneType.Allowed)
        .withSticky(false)
        .build(),
    ];

    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber('plate-01'))
      .build();

    const customerFleetServiceDto: VulogFleetServiceDto =
      new VulogFleetServiceDtoBuilder()
        .withId(customerUsableFleetServiceID.value)
        .withVehicles([car.vulogId.value])
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

    const vulogCarRealTimeDto: VulogCarRealTimeDto =
      new VulogCarRealTimeDtoBuilder()
        .withAutonomy(100)
        .withBattery(100)
        .withFleetId(config.vulogConfig.fleetId.value)
        .withServiceId(customerFleetServiceDto.id)
        .withZones(vulogZones)
        .withId(car.vulogId.value)
        .withVin(car.vin)
        .withPlate(car.plate.value)
        .build();

    const vulogStaticCarDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withId(car.vulogId.value)
        .withModel(car.model)
        .withPlate(car.plate.value)
        .withServiceId(customerFleetServiceDto.id)
        .withVin(car.vin)
        .build();

    const stationDtos: StationDto[] = vulogZones.map((vgZone) =>
      new StationDtoBuilder().withZoneId(vgZone.zoneId).build(),
    );

    stationDtos[0].id = expectedToBeStickyStationId;

    beforeAll(async () => {
      // Save cars into DB
      await carRepo.save(CarEntity.from(car));

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, vulogCarRealTimeDto);

      // Listing out stations from STS
      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockOutgoingService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      // Making PUT call to Vulog to stick station with car
      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            new VulogZoneId(stationDtos[0].zoneId),
          )}`,
        )
        .replyOnce(500);

      const updatedVulogCarRealtimeDto = _.cloneDeep(vulogCarRealTimeDto);

      const toBeStickyZone = updatedVulogCarRealtimeDto.zones.find(
        (zone) => zone.zoneId === stationDtos[0].zoneId,
      );

      toBeStickyZone.sticky = true;

      // Get specific realtime car from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeVehicles(
            config.vulogConfig.fleetId,
            new VulogCarId(vulogStaticCarDto.id),
          )}`,
        )
        .replyOnce(200, updatedVulogCarRealtimeDto);

      // Listing out fleet services from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // Listing out static cars from Vulog
      mockOutgoingService
        .onGet(
          `${VulogPaths.backOfficeListingAllStaticVehicles(
            config.vulogConfig.fleetId,
          )}?${QueryString.stringify({ page: 0, size: 200 })}`,
        )
        .reply(200, [vulogStaticCarDto], { last: true });

      responseObject = await requestFor({
        plateNumber: car.plate.value,
        stationId: expectedToBeStickyStationId,
      });
    });

    afterAll(async () => {
      await cleanUpResources();
    });

    it('Should return response', () => {
      expect(responseObject.statusCode).toBe(
        HttpStatusCode.InternalServerError,
      );
    });

    it('Verify response', async () => {
      const response = responseObject.body;

      expect(response.message).toEqual(
        `An error has occurred. - Cause: Failed to attach car at Vulog`,
      );
    });
  });
});
