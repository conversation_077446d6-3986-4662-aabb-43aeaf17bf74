import * as supertest from 'supertest';

import { HttpStatus, INestApplication } from '@nestjs/common';

import MockAdapter from 'axios-mock-adapter';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { config } from 'src/config';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { VulogAuthService } from 'src/logic/vulog/vulog-auth.service';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogFeatureCollectionDto } from 'src/model/dtos/vulog-feature/vulog-feature-collection.dto';
import { AppBuilder } from 'test/helpers/app.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { VulogFeatureCollectionDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-feature-collection.dto.builder';

const testingApiRoute = ApiRouteUtils.buildApiRouteSegment(
  '' as any,
  '' as any,
  [
    'api',
    'v1',
    RentalServiceRootRouteSegment.Internal,
    RouteSegment.VulogZone.Index,
    RouteSegment.VulogZone.List,
  ],
);

describe(`[E2E] GET: ${testingApiRoute}`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;
  let mockHttpAdapterService: MockAdapter;

  function requestFor(): supertest.Test {
    const superTest = supertest(app.getHttpServer())
      .get(testingApiRoute)
      .send();

    return superTest;
  }

  beforeAll(async () => {
    app = await appBuilder.buildAndStart();

    const vulogAuthService: VulogAuthService = app.get(VulogAuthService);

    jest.spyOn(vulogAuthService, 'setupAuthConfig').mockResolvedValueOnce({
      headers: {
        Authorization: `Bearer hehe`,
        'x-api-key': 'hehe',
      },
    });

    const httpAdapterService = app.get(HttpAdapterService);
    mockHttpAdapterService = new MockAdapter(httpAdapterService.axiosInstance);
  });

  afterAll(async () => {
    await app.close();
  });

  async function clear(): Promise<void> {
    mockHttpAdapterService.reset();
  }

  describe('🔥 Get Vulog Zones successfully', () => {
    let response: supertest.Response;
    const vulogFeatureCollectionDto: VulogFeatureCollectionDto =
      new VulogFeatureCollectionDtoBuilder().build();

    beforeAll(async () => {
      mockHttpAdapterService
        .onGet(`${VulogPaths.backOfficeZones(config.vulogConfig.fleetId)}`)
        .replyOnce(200, vulogFeatureCollectionDto);

      const mockAuthResponse = createTestVulogAuthResponse();

      mockHttpAdapterService
        .onPost(`${VulogPaths.auth()}`)
        .reply(200, mockAuthResponse);

      response = await requestFor();
    });

    afterAll(async () => {
      await clear();
    });

    it('Should return status OK', () => {
      expect(response.status).toBe(HttpStatus.OK);

      const features = vulogFeatureCollectionDto.features;

      expect(response.body.result).toEqual(
        JSON.parse(JSON.stringify(features)),
      );
    });
  });
});
