import { HttpStatus, INestApplication } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DateTime } from 'luxon';
import * as QueryString from 'qs';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { InternalReservationQueryParamsDtoV1 } from 'src/controllers/internal/v1/reservation/dto/request/internal.reservation.query.v1.dto';
import { InternalReservationResponseDtoV1 } from 'src/controllers/internal/v1/reservation/dto/response/internal.reservation.response.dto.v1';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import * as supertest from 'supertest';
import { AppBuilder } from 'test/helpers/app.builder';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { aUUID } from 'test/helpers/random-data.helper';
import { Repository } from 'typeorm';

const testingApiRoute = ApiRouteUtils.buildApiRouteSegment(
  '' as any,
  '' as any,
  [
    'api',
    'v1',
    RentalServiceRootRouteSegment.Internal,
    RouteSegment.Reservation.Index,
    RouteSegment.Reservation.RecentlyUpdated,
  ],
);

describe(`[E2E] GET: ${testingApiRoute}`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let reservationRepository: Repository<ReservationEntity>;

  let carRepository: Repository<CarEntity>;

  function requestFor(
    params?: Partial<InternalReservationQueryParamsDtoV1>,
  ): supertest.Test {
    const superTest = supertest(app.getHttpServer())
      .get(`${testingApiRoute}?${QueryString.stringify(params)}`)
      .send();

    return superTest;
  }

  beforeAll(async () => {
    app = await appBuilder.buildAndStart();

    reservationRepository = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );

    carRepository = app.get<Repository<CarEntity>>(
      getRepositoryToken(CarEntity),
    );

    await reservationRepository.clear();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('when get reservations updated within last xx minutes', () => {
    const now = DateTime.now();
    const bsgUserId1 = aUUID();
    const bsgUserId2 = aUUID();
    let car1: CarEntity;
    let reservation1: ReservationEntity;
    let reservation2: ReservationEntity;
    let car2: CarEntity;

    beforeAll(async () => {
      car1 = await carRepository.save(CarEntity.from(new CarBuilder().build()));

      reservation1 = await reservationRepository.save(
        new ReservationEntityBuilder()
          .makeStatus(ReservationStatus.Converted)
          .makeBsgUserId(bsgUserId1)
          .makeCreatedAt(now.minus({ minutes: 100 }).toJSDate())
          .makeModifiedAt(now.minus({ minutes: 10 }).toJSDate())
          .makeCarId(car1.vulogId)
          .build(),
      );

      car2 = await carRepository.save(CarEntity.from(new CarBuilder().build()));

      reservation2 = await reservationRepository.save(
        new ReservationEntityBuilder()
          .makeStatus(ReservationStatus.Converted)
          .makeBsgUserId(bsgUserId2)
          .makeCreatedAt(now.minus({ minutes: 100 }).toJSDate())
          .makeModifiedAt(now.minus({ minutes: 1 }).toJSDate())
          .makeCarId(car2.vulogId)
          .build(),
      );

      const allReservations = await reservationRepository.find({});
      expect(allReservations.length).toEqual(2);
    });

    afterAll(async () => {
      await reservationRepository.clear();
      await carRepository.clear();
    });

    it('should throw bad request with malformed from param', async () => {
      const response = await requestFor({
        'updated-from': 'foobar',
      });

      expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);

      expect(response.body.message).toEqual([
        'updated-from must be a valid ISO date time and must not be in future',
      ]);
    });

    it('should throw bad request with from param in the future', async () => {
      const response = await requestFor({
        'updated-from': now.plus({ minutes: 2 }).toUTC().toISO(),
      });

      expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);

      expect(response.body.message).toEqual([
        'updated-from must be a valid ISO date time and must not be in future',
      ]);
    });

    it('should return reservations updated within specified time range', async () => {
      const response = await requestFor({
        'updated-from': now.minus({ minutes: 11 }).toUTC().toISO(),
      });

      expect(response.statusCode).toEqual(HttpStatus.OK);

      const result = response.body.result as InternalReservationResponseDtoV1[];
      expect(result.length).toEqual(2);

      const resultMap = new Map(result.map((r) => [r.vulogCarId, r]));

      expect(resultMap.has(car1.vulogId)).toBeTruthy();
      expect(resultMap.has(car2.vulogId)).toBeTruthy();

      expectToMatchReservationResponse(
        resultMap.get(car1.vulogId)!,
        car1.vulogId,
        reservation1,
      );

      expectToMatchReservationResponse(
        resultMap.get(car2.vulogId)!,
        car2.vulogId,
        reservation2,
      );
    });

    it('should only return the latest reservation for each car', async () => {
      const newerReservation = await reservationRepository.save(
        new ReservationEntityBuilder()
          .makeStatus(ReservationStatus.Converted)
          .makeBsgUserId(bsgUserId1)
          .makeCreatedAt(now.minus({ minutes: 50 }).toJSDate())
          .makeModifiedAt(now.toJSDate())
          .makeCarId(car1.vulogId)
          .build(),
      );

      const response = await requestFor({
        'updated-from': now.minus({ minutes: 20 }).toUTC().toISO(),
      });

      expect(response.statusCode).toEqual(HttpStatus.OK);

      const result = response.body.result as InternalReservationResponseDtoV1[];
      expect(result.length).toEqual(2);

      const resultMap = new Map(result.map((r) => [r.vulogCarId, r]));

      expectToMatchReservationResponse(
        resultMap.get(car1.vulogId)!,
        car1.vulogId,
        newerReservation,
      );

      expectToMatchReservationResponse(
        resultMap.get(car2.vulogId)!,
        car2.vulogId,
        reservation2,
      );
    });
  });
});

function expectToMatchReservationResponse(
  actual: InternalReservationResponseDtoV1,
  expectedVulogCarId: string,
  expectedReservation: ReservationEntity,
): void {
  expect(actual.vulogCarId).toEqual(expectedVulogCarId);
  expect(actual.status).toEqual(expectedReservation.status);
  expect(actual.createdAt).toEqual(expectedReservation.createdAt.toISOString());
  expect(actual.modifiedAt).toEqual(
    expectedReservation.modifiedAt.toISOString(),
  );
  expect(actual.userId).toEqual(expectedReservation.bsgUserId);
}
