import * as supertest from 'supertest';

import { HttpStatus, INestApplication } from '@nestjs/common';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DateTime } from 'luxon';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { RentalPlateNumberAndTimeDtoV1 } from 'src/controllers/internal/v1/reservation/dto/request/get-reservations-by-plate-no.v1.dto';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { AppBuilder } from 'test/helpers/app.builder';
import { GetReservationsByPlateNoAndTimeV1Dto } from 'test/helpers/builders/dtos/internal/v1/get-reservations-by-plate-no-v1-dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aDate, anAlphaNumeric, aUUID } from 'test/helpers/random-data.helper';
import { Repository } from 'typeorm';

const testingApiRoute = ApiRouteUtils.buildApiRouteSegment(
  '' as any,
  '' as any,
  [
    'api',
    'v1',
    RentalServiceRootRouteSegment.Internal,
    RouteSegment.Reservation.Index,
    RouteSegment.Reservation.Search,
  ],
);

describe(`[E2E] POST: ${testingApiRoute}`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let reserveRepo: Repository<ReservationEntity>;

  function requestFor(pairs: RentalPlateNumberAndTimeDtoV1[]): supertest.Test {
    const dto = new GetReservationsByPlateNoAndTimeV1Dto();
    dto.plateNumberAndTxnDate = pairs;

    const superTest = supertest(app.getHttpServer())
      .post(testingApiRoute)
      .send(dto);
    return superTest;
  }

  beforeAll(async () => {
    app = await appBuilder
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(aUUID()), true)
      .buildAndStart();

    reserveRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );
  });

  afterAll(async () => {
    await app.close();
  });

  async function clean(): Promise<void> {
    await reserveRepo.delete({});
  }

  describe.skip('When send many rental packages and they are all exist!', () => {
    let responseObject: supertest.Response;
    let plateAndTimePair: RentalPlateNumberAndTimeDtoV1[];
    const numberOfPairs = 2;

    beforeAll(async () => {
      plateAndTimePair = Array.from({
        length: numberOfPairs,
      }).map(() => ({
        plateNumber: anAlphaNumeric(),
        time: aDate(),
      }));

      await reserveRepo.save(
        plateAndTimePair.map((pair) =>
          new ReservationEntityBuilder()
            .makePlateNumber(pair.plateNumber)
            .makeStartedAt(
              DateTime.fromJSDate(pair.time)
                .minus({
                  seconds: 5,
                })
                .toJSDate(),
            )
            .makeEndedAt(
              DateTime.fromJSDate(pair.time)
                .plus({
                  seconds: 5,
                })
                .toJSDate(),
            )
            .makeStatus(ReservationStatus.Converted)
            .build(),
        ),
      );

      responseObject = await requestFor(plateAndTimePair);
    });

    afterAll(async () => {
      // await clean();
    });

    it('Should return status CREATED', () => {
      expect(responseObject.status).toBe(HttpStatus.CREATED);
    });

    it('Should return right number of pair', () => {
      expect(responseObject.body.result['reservations'].length).toEqual(
        numberOfPairs,
      );
    });
  });

  describe('When send many rental packages and they are all not exist!', () => {
    let responseObject: supertest.Response;
    let plateAndTimePair: RentalPlateNumberAndTimeDtoV1[];
    const numberOfPairs = 10;

    beforeAll(async () => {
      plateAndTimePair = Array.from({
        length: numberOfPairs,
      }).map(() => ({
        plateNumber: anAlphaNumeric(),
        time: aDate(),
      }));

      responseObject = await requestFor(plateAndTimePair);
    });

    afterAll(async () => {
      await clean();
    });

    it('Should return status CREATED', () => {
      expect(responseObject.status).toBe(HttpStatus.CREATED);
    });

    it('Should return right number of pair', () => {
      expect(responseObject.body.result['cars'].length).toEqual(0);
    });
  });

  describe.skip('When send many rental packages and only a few exist!', () => {
    let responseObject: supertest.Response;
    let plateAndTimePair: RentalPlateNumberAndTimeDtoV1[];
    const numberOfPairs = 10;

    beforeAll(async () => {
      plateAndTimePair = Array.from({
        length: numberOfPairs,
      }).map(() => ({
        plateNumber: anAlphaNumeric(),
        time: aDate(),
      }));

      await reserveRepo.save(
        plateAndTimePair
          .map((pair, index) => {
            if (index % 2 === 0) {
              return new ReservationEntityBuilder()
                .makePlateNumber(pair.plateNumber)
                .makeStartedAt(
                  DateTime.fromJSDate(pair.time)
                    .minus({
                      seconds: 5,
                    })
                    .toJSDate(),
                )
                .makeEndedAt(
                  DateTime.fromJSDate(pair.time)
                    .plus({
                      seconds: 5,
                    })
                    .toJSDate(),
                )
                .makeStatus(ReservationStatus.Converted)
                .build();
            }
          })
          .filter((val) => val),
      );

      responseObject = await requestFor(plateAndTimePair);
    });

    afterAll(async () => {
      await clean();
    });

    it('Should return status CREATED', () => {
      expect(responseObject.status).toBe(HttpStatus.CREATED);
    });

    it('Should return right reserve', () => {
      const expectedRes = plateAndTimePair
        .map((reserve, index) => {
          if (index % 2 === 0) {
            return expect.objectContaining({
              plateNumber: reserve.plateNumber,
            });
          }
        })
        .filter((val) => val);

      expect(responseObject.body.result['reservations']).toEqual(
        expect.arrayContaining(expectedRes),
      );
    });

    it('Should return right number of pair', () => {
      expect(responseObject.body.result['reservations'].length).toEqual(
        numberOfPairs / 2,
      );
    });
  });
});
