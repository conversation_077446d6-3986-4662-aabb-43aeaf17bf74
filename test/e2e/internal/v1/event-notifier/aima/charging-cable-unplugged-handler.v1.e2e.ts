import * as supertest from 'supertest';

import { INestApplication } from '@nestjs/common';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { getRepositoryToken } from '@nestjs/typeorm';
import { HttpStatusCode } from 'axios';
import { RouteSegment } from 'src/common/constants/api-path';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { VulogChargingCableUnpluggedWebhookDtoV1 } from 'src/logic/vulog/dto/webhook/aima/vulog-charging-cable-unplugged-webhook.v1.dto';
import { EventbridgeEventDto } from 'src/logic/vulog/dto/webhook/eventbridge-event.dto';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { VulogEventEntity } from 'src/model/repositories/entities/vulog-event.entity';
import { EventBusService } from 'src/shared/event-bus/event-bus.service';
import { AppBuilder } from 'test/helpers/app.builder';
import { EventbridgeEventDtoBuilder } from 'test/helpers/builders/dtos/vulog/event/eventbridge-event.dto.builder';
import { VulogChargingCableUnpluggedBodyV1Builder } from 'test/helpers/builders/dtos/vulog/event/vulog-charging-cable-unplugged-body-v1.builder';
import { VulogChargingCableUnpluggedWebhookV1Builder } from 'test/helpers/builders/dtos/vulog/event/vulog-charging-cable-unplugged-webhook-dto-v1.builder';
import { RealtimeCarBuilder } from 'test/helpers/builders/realtime-car.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { sleep } from 'test/helpers/util/sleep.util';
import { Repository } from 'typeorm';

const testingApiRoute = ApiRouteUtils.buildApiRouteSegment(
  '' as any,
  '' as any,
  [
    'api/v1/internal',
    RouteSegment.EventNotifier.Index,
    RouteSegment.EventNotifier.EventBridge,
  ],
);

describe(`[E2E] POST: ${testingApiRoute}, Vulog Event: ChargingCableUnplugged`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let reserveRepo: Repository<ReservationEntity>;
  let carRepo: Repository<CarEntity>;
  let vulogEventRepo: Repository<VulogEventEntity>;

  let eventService: EventBusService;

  function requestFor(dto: EventbridgeEventDto): supertest.Test {
    const superTest = supertest(app.getHttpServer())
      .post(testingApiRoute)
      .send({
        ...dto,
        moreAttribute: 'hello',
      });
    return superTest;
  }

  beforeAll(async () => {
    app = await appBuilder
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(aUUID()), true)
      .buildAndStart();

    reserveRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );
    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));
    vulogEventRepo = app.get<Repository<VulogEventEntity>>(
      getRepositoryToken(VulogEventEntity),
    );

    eventService = app.get<EventBusService>(EventBusService);

    eventService.nonBlockingEmit = jest.fn();
  });

  afterAll(async () => {
    await clean();
    await app.close();
  });

  async function clean(): Promise<void> {
    await reserveRepo.delete({});
    await carRepo.delete({});
    await vulogEventRepo.delete({});

    jest.clearAllMocks();
  }

  describe('When successfully plug charging cable to car', () => {
    let responseObject: supertest.Response;
    let event: VulogChargingCableUnpluggedWebhookDtoV1;

    const car = new RealtimeCarBuilder().build();

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car.toCar()));

      event = new VulogChargingCableUnpluggedWebhookV1Builder()
        .makeVehicleId(car.id.value)
        .makeVehiclePlate(car.plate.value)
        .makeVehicleVin(car.vin)
        .build();

      responseObject = await requestFor(
        new EventbridgeEventDtoBuilder().buildDetail(event).getResult(),
      );

      await sleep(1);
    });

    afterAll(async () => {
      await clean();
    });

    it('Should return status 201', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.Created);
    });

    it('Should update the car', async () => {
      const updatedCar = await carRepo.findOne({
        where: {
          vulogId: car.id.value,
        },
      });

      expect(updatedCar.isPlugged).toBe(false);
    });
  });

  describe('Should throw an error when Vulog Event is containing error', () => {
    let responseObject: supertest.Response;

    let event: VulogChargingCableUnpluggedWebhookDtoV1;

    const errorFromVulog = 'Error from Vulog';

    beforeAll(async () => {
      const car = new RealtimeCarBuilder().build();
      await carRepo.save(CarEntity.from(car.toCar()));

      event = new VulogChargingCableUnpluggedWebhookV1Builder()
        .makeVehicleId(car.id.value)
        .makeVehiclePlate(car.plate.value)
        .makeVehicleVin(car.vin)
        .makeBody(
          new VulogChargingCableUnpluggedBodyV1Builder()
            .withError(true)
            .withErrorMessage(errorFromVulog)
            .build(),
        )
        .build();

      responseObject = await requestFor(
        new EventbridgeEventDtoBuilder().buildDetail(event).getResult(),
      );
    });

    afterAll(async () => {
      await clean();
    });

    it('Should return status 400', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.BadRequest);
    });

    it('Should return the expected error message', async () => {
      expect(responseObject.body.message).toEqual(
        `An error happened while handling Vulog event - Cause: Vulog event received, but with error information: ${errorFromVulog}. No further action is performed.`,
      );
    });
  });
});
