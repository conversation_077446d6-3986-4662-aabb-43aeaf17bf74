import { VulogEventNotifierType } from 'src/common/constants/vulog-event-type';
import { VulogUserService } from 'src/logic/vulog/vulog-user.service';
import * as supertest from 'supertest';

import { INestApplication } from '@nestjs/common';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { CorrelationIdService } from '@bluesg-2/monitoring';
import { CorrelationId } from '@bluesg-2/monitoring/dist/correlation-id/correlation-id.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { HttpStatusCode } from 'axios';
import { DateTime } from 'luxon';
import { RouteSegment } from 'src/common/constants/api-path';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ReservationError } from 'src/common/errors/reservation-error';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import * as uuid from 'src/common/utils/uuid.util';
import { BillingExternalService } from 'src/external/billing/billing.external.service';
import { UserSubscriptionExternalService } from 'src/external/user-subscription/user-subscription.external.service';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { CarService } from 'src/logic/car/car.service';
import { Car } from 'src/logic/car/domain/car';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { CarNotFoundError } from 'src/logic/car/errors/car-not-found.error';
import { ReservationService } from 'src/logic/reservation.service';
import { VulogEventNotifierWebhookDtoV1 } from 'src/logic/vulog/dto/webhook/aima/vulog-event-notifier-webhook.v1.dto';
import { VulogTripDto } from 'src/model/dtos/vulog-trip/vulog-trip.dto';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { VulogEventEntity } from 'src/model/repositories/entities/vulog-event.entity';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { Reservation } from 'src/model/reservation.domain';
import { AppBuilder } from 'test/helpers/app.builder';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { InternalPricingPolicyResponseDtoV1Builder } from 'test/helpers/builders/dtos/internal.pricing-policy.v1.response.dto.builder';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { VulogStartTripByAdminWebhookV1Builder } from 'test/helpers/builders/dtos/vulog/event/vulog-start-trip-by-admin-webhook-dto-v1.builder';
import { VulogTripAdditionalInfoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-trip-additional-info.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { createTestVulogUserEntity } from 'test/helpers/builders/entities/test-vulog-user-entity.builder';
import { VulogTripBuilder } from 'test/helpers/builders/trip/vulog-trip.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { sleep } from 'test/helpers/util/sleep.util';
import { Repository } from 'typeorm';
import { LOCAL_EVENT_EMITTER_TOKEN } from '~shared/local-event-emitter/constants';
import { LocalEventEmitterService } from '~shared/local-event-emitter/local-event-emitter.service';

const testingApiRoute = ApiRouteUtils.buildApiRouteSegment(
  '' as any,
  '' as any,
  [
    `api/v1/internal/${RouteSegment.EventNotifier.Index}`,
    RouteSegment.EventNotifier.EventBridge,
  ],
);

describe(`[E2E] POST: ${testingApiRoute}, Vulog Event: StartTrip`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let reserveRepo: Repository<ReservationEntity>;
  let vulogUserRepo: Repository<VulogUserEntity>;
  let carRepo: Repository<CarEntity>;
  let vulogEventRepo: Repository<VulogEventEntity>;
  let vulogCarService: VulogCarService;
  let vulogUserService: VulogUserService;
  let localEventEmitterService: LocalEventEmitterService;
  let reservationService: ReservationService;
  let carService: CarService;

  let userSubExternalService: UserSubscriptionExternalService;
  let billingExternalService: BillingExternalService;

  let correlationIdService: CorrelationIdService;

  function requestFor(dto: VulogEventNotifierWebhookDtoV1): supertest.Test {
    const superTest = supertest(app.getHttpServer())
      .post(testingApiRoute)
      .send({ detail: dto });
    return superTest;
  }

  beforeAll(async () => {
    app = await appBuilder
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(aUUID()), true)
      .buildAndStart();

    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));
    reserveRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );
    vulogUserRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );
    vulogEventRepo = app.get<Repository<VulogEventEntity>>(
      getRepositoryToken(VulogEventEntity),
    );

    userSubExternalService = app.get(UserSubscriptionExternalService);
    billingExternalService = app.get(BillingExternalService);

    correlationIdService = app.get<CorrelationIdService>(CorrelationIdService);
    vulogCarService = app.get<VulogCarService>(VulogCarService);
    reservationService = app.get<ReservationService>(ReservationService);
    localEventEmitterService = app.get<LocalEventEmitterService>(
      LOCAL_EVENT_EMITTER_TOKEN,
    );
    vulogUserService = app.get<VulogUserService>(VulogUserService);
    carService = app.get<CarService>(CarService);
  });

  afterAll(async () => {
    await clean();
    await app.close();
  });

  async function clean(): Promise<void> {
    await reserveRepo.delete({});
    await vulogUserRepo.delete({});
    await carRepo.delete({});
    await vulogEventRepo.delete({});

    jest.resetAllMocks();
    jest.clearAllMocks();
  }

  describe('When successfully start a trip by admin, ', () => {
    let userInfo: VulogUserEntity;
    let car: Car;
    let event: VulogEventNotifierWebhookDtoV1;
    let responseObject: supertest.Response;
    const now = DateTime.now();
    let reservedTrip: ReservationEntity;

    beforeAll(async () => {
      userInfo = await vulogUserRepo.save(createTestVulogUserEntity());
      car = new CarBuilder().build();
      await carRepo.save(CarEntity.from(car));

      reservedTrip = new ReservationEntityBuilder()
        .makeBsgUserId(userInfo.bsgUserId)
        .makeCarId(car.id.value)
        .makeCarModel(car.model)
        .makePlateNumber(car.plate.value)
        .makeStatus(ReservationStatus.Reserved)
        .makeStartedAt(new Date())
        .build();
      await reserveRepo.save(reservedTrip);

      jest
        .spyOn(reservationService, 'getOneByVulogId')
        .mockResolvedValue(Reservation.from(reservedTrip));

      jest.spyOn(carService, 'getOneByVulogId').mockResolvedValue(car);

      jest.spyOn(localEventEmitterService, 'dispatchEvent');

      // NOTE: mock external service
      jest
        .spyOn(userSubExternalService.internalApi, 'getUserDetail')
        .mockResolvedValueOnce(
          new InternalUserDetailResponseDtoV1Builder().build(),
        );

      jest
        .spyOn(billingExternalService.internalApi, 'getPricingPolicy')
        .mockResolvedValueOnce([
          new InternalPricingPolicyResponseDtoV1Builder().build(),
        ]);

      event = new VulogStartTripByAdminWebhookV1Builder()
        .makeVehicleId(car.vulogId.value)
        .makeVulogUserId(userInfo.vulogUserId)
        .makeVehiclePlate(car.plate.value)
        .makeVehicleVin(car.vin)
        .makeType(VulogEventNotifierType.StartTrip)
        .makeTripId(reservedTrip.vulogTripId)
        .makeDate(now.toISO())
        .build();
      responseObject = await requestFor(event);
      await sleep(1);
    });

    afterAll(async () => {
      await clean();
      jest.clearAllMocks();
    });

    it('should return status 201', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.Created);
    });

    it('should save the reservation', async () => {
      const savedReservation = await reserveRepo.findOne({
        where: {
          vulogTripId: reservedTrip.vulogTripId,
        },
      });
      expect(savedReservation).toMatchObject({
        status: ReservationStatus.Converted,
      });

      expect(
        DateTime.fromJSDate(savedReservation.startedAt).diff(now).as('seconds'),
      ).toBeLessThanOrEqual(1);
    });
  });

  describe('When user is not exist, ', () => {
    let car: Car;
    let vulogTrip: VulogTripDto;
    let event: VulogEventNotifierWebhookDtoV1;
    let responseObject: supertest.Response;
    const generateUserId = aUUID();

    beforeAll(async () => {
      car = new CarBuilder().build();
      await carRepo.save(CarEntity.from(car));

      vulogTrip = new VulogTripBuilder()
        .makeAdditionalInfo(
          new VulogTripAdditionalInfoBuilder().makeIsCancel(false).build(),
        )
        .build();

      jest
        .spyOn(vulogCarService, 'getSpecificATrip')
        .mockResolvedValue(vulogTrip.toRentalInfo());

      event = new VulogStartTripByAdminWebhookV1Builder()
        .makeVehicleId(car.vulogId.value)
        .makeVulogUserId(generateUserId)
        .makeVehiclePlate(car.plate.value)
        .makeVehicleVin(car.vin)
        .makeType(VulogEventNotifierType.StartTrip)
        .makeTripId(vulogTrip.tripId)
        .build();
      responseObject = await requestFor(event);

      await sleep(1);
    });

    afterAll(async () => {
      await clean();
    });

    it('should return status BadRequest', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.BadRequest);
    });
    it('should return expected message error', () => {
      expect(responseObject.body.message).toEqual(
        `An error happened while handling Vulog event - Cause: User with Vulog ID [${generateUserId}] is not found`,
      );
    });
  });

  describe('When not found current rental, ', () => {
    let userInfo: VulogUserEntity;
    let car: Car;
    let event: VulogEventNotifierWebhookDtoV1;
    let responseObject: supertest.Response;
    const generatedTripId = aUUID();
    let spy: jest.SpyInstance<any, unknown[]>;
    const corelationId = aUUID();

    beforeAll(async () => {
      spy = jest.spyOn(localEventEmitterService, 'throwError');
      jest
        .spyOn(correlationIdService, 'getCorrelationId')
        .mockReturnValue(new CorrelationId(corelationId));
      userInfo = await vulogUserRepo.save(createTestVulogUserEntity());
      car = new CarBuilder().build();
      await carRepo.save(CarEntity.from(car));

      jest.spyOn(reserveRepo, 'findOne').mockResolvedValue(null);
      jest
        .spyOn(vulogUserService, 'findOneByVulogUserId')
        .mockResolvedValue(userInfo.toVulogUser());
      jest.spyOn(carService, 'getOneByVulogId').mockResolvedValue(car);

      jest.spyOn(uuid, 'generateUUID').mockReturnValue(corelationId);

      event = new VulogStartTripByAdminWebhookV1Builder()
        .makeVehicleId(car.vulogId.value)
        .makeVulogUserId(userInfo.vulogUserId)
        .makeVehiclePlate(car.plate.value)
        .makeVehicleVin(car.vin)
        .makeType(VulogEventNotifierType.StartTrip)
        .makeTripId(generatedTripId)
        .build();
      responseObject = await requestFor(event);
      await sleep(1);
    });

    afterAll(async () => {
      await clean();
    });

    it('should return status 201', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.Created);
    });
    it.skip('should return right error message', () => {
      expect(spy).toBeCalledWith(new ReservationError('hehe'), corelationId);
    });
  });

  // NOTE: new logic no longer check for car exist
  describe.skip('When not found vulog car, ', () => {
    let userInfo: VulogUserEntity;
    let car: Car;
    let reservedTrip: ReservationEntity;
    let event: VulogEventNotifierWebhookDtoV1;
    let responseObject: supertest.Response;
    const generatedTripId = aUUID();
    let spy: jest.SpyInstance<any, unknown[]>;
    const corelationId = aUUID();

    beforeAll(async () => {
      spy = jest.spyOn(localEventEmitterService, 'throwError');
      jest
        .spyOn(correlationIdService, 'getCorrelationId')
        .mockReturnValue(new CorrelationId(corelationId));
      userInfo = await vulogUserRepo.save(createTestVulogUserEntity());
      car = new CarBuilder().build();
      await carRepo.save(CarEntity.from(car));
      reservedTrip = new ReservationEntityBuilder()
        .makeBsgUserId(userInfo.bsgUserId)
        .makeCarId(car.id.value)
        .makeCarModel(car.model)
        .makePlateNumber(car.plate.value)
        .makeStatus(ReservationStatus.Reserved)
        .makeStartedAt(new Date())
        .build();
      jest
        .spyOn(reservationService, 'getOneByVulogId')
        .mockResolvedValue(Reservation.from(reservedTrip));

      jest
        .spyOn(vulogUserService, 'findOneByVulogUserId')
        .mockResolvedValue(userInfo.toVulogUser());

      jest.spyOn(uuid, 'generateUUID').mockReturnValue(corelationId);

      jest.spyOn(carService, 'getOneByVulogId').mockResolvedValue(null);

      event = new VulogStartTripByAdminWebhookV1Builder()
        .makeVehicleId(car.vulogId.value)
        .makeVulogUserId(userInfo.vulogUserId)
        .makeVehiclePlate(car.plate.value)
        .makeVehicleVin(car.vin)
        .makeType(VulogEventNotifierType.StartTrip)
        .makeTripId(generatedTripId)
        .build();
      responseObject = await requestFor(event);

      await sleep(1);
    });

    afterAll(async () => {
      await clean();
    });

    it('should return status 201', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.Created);
    });
    it('should return right error message', () => {
      expect(spy).toBeCalledWith(
        new CarNotFoundError(`The car [${car.vulogId.value}] is not found`),
        uuid.generateUUID(),
      );
    });
  });
});
