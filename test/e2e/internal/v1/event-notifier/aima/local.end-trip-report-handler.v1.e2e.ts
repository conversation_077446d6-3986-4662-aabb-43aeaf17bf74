import * as supertest from 'supertest';

import { HttpStatus, INestApplication } from '@nestjs/common';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { AppBuilder } from 'test/helpers/app.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aMD5, aUUID } from 'test/helpers/random-data.helper';
import { Repository } from 'typeorm';

import MockAdapter from 'axios-mock-adapter';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';

import { DateTime } from 'luxon';

import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { config } from 'src/config';
import { InternalStationResponseDtoV1 } from 'src/external/station/dtos/response/internal.station-response.v1.response.dto';
import { CacheService } from 'src/logic/cache/cache.service';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { InternalStationResponseDtoV1Builder } from 'test/helpers/builders/dtos/internal-station.response.v1.dto.builder';
import { createTestVulogCarStaticInfoDto } from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import { VulogUserDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-user.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';

import { faker } from '@faker-js/faker';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { VulogEventNotifierType } from 'src/common/constants/vulog-event-type';
import {
  SubscriptionStatus,
  UserStatus,
} from 'src/external/user-subscription/constants';
import { UserSubscriptionExternalService } from 'src/external/user-subscription/user-subscription.external.service';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { StationAdapterService } from 'src/logic/station/station-adapter.service';
import { VulogEndTripReportWebhookDtoV1 } from 'src/logic/vulog/dto/webhook/aima/vulog-end-trip-report-webhook.v1.dto';
import { EventbridgeEventDto } from 'src/logic/vulog/dto/webhook/eventbridge-event.dto';
import { VulogClientService } from 'src/logic/vulog/vulog-client.service';
import { VulogCarStaticInfoDto } from 'src/model/dtos/vulog-car-static-info/vulog-car-static-info.dto';
import { VvgVehicleStatusDto } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import {
  VulogEventEntity,
  VulogEventSource,
} from 'src/model/repositories/entities/vulog-event.entity';
import { VulogEventRepository } from 'src/model/repositories/vulog-event.repository';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { EventbridgeEventDtoBuilder } from 'test/helpers/builders/dtos/vulog/event/eventbridge-event.dto.builder';
import { VulogEndTripReportWebhookDtoV1Builder } from 'test/helpers/builders/dtos/vulog/event/vulog-end-trip-report-webhook-dto-v1.builder';
import { VulogCarStaticInfoDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car-static-info.dto.builder';
import { VvgVehicleStatusDtoBuilder } from 'test/helpers/builders/dtos/vulog/vvg/vvg-vehicle-status.dto.builder';
import { UssApiMocker } from 'test/helpers/external-services/uss.api-mocker';
import { VulogApiMocker } from 'test/helpers/external-services/vulog.api-mocker';
import { sleep } from 'test/helpers/util/sleep.util';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';

jest.setTimeout(50000);

const userId = aUUID();

const eventNotifierUrl = `/api/v1/${RentalServiceRootRouteSegment.Internal}/${RouteSegment.EventNotifier.Index}/${RouteSegment.EventNotifier.EventBridge}`;

describe(`[E2E] POST: ${eventNotifierUrl}`, () => {
  let app: INestApplication;

  let mockAdapter_VulogApiService: MockAdapter;
  let mockAdapter_UssApiService: MockAdapter;

  let cacheService: CacheService;

  let userRepo: Repository<VulogUserEntity>;
  let reservationRepo: Repository<ReservationEntity>;
  let carRepo: Repository<CarEntity>;
  let featureFlagService: FeatureFlagService;
  let vulogCarService: VulogCarService;
  let vulogEventRepository: VulogEventRepository;

  let vulogClientService: VulogClientService;
  let stationAdapterService: StationAdapterService;
  let userSubscriptionExternalService: UserSubscriptionExternalService;

  function requestFor(dto: EventbridgeEventDto): supertest.Test {
    const superTest = supertest(app.getHttpServer())
      .post(eventNotifierUrl)
      .send(dto);

    return superTest;
  }

  const vulogUserDto = new VulogUserDtoBuilder().withId(userId).build();
  const vulogUserProfileDto = createTestVulogUserServicesDto();

  beforeAll(async () => {
    app = await new AppBuilder()
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(aUUID()), true)
      .buildAndStart();

    userRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );

    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );

    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );

    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));

    const vulogApiMocker = new VulogApiMocker(app);

    mockAdapter_VulogApiService = vulogApiMocker.mock();
    mockAdapter_UssApiService = new UssApiMocker(app).mock();
    featureFlagService = app.get<FeatureFlagService>(FeatureFlagService);

    cacheService = app.get(CacheService);

    vulogCarService = app.get<VulogCarService>(VulogCarService);

    vulogClientService = app.get<VulogClientService>(VulogClientService);

    stationAdapterService = app.get<StationAdapterService>(
      StationAdapterService,
    );

    userSubscriptionExternalService = app.get<UserSubscriptionExternalService>(
      UserSubscriptionExternalService,
    );

    vulogEventRepository = app.get<VulogEventRepository>(VulogEventRepository);

    // Make Vulog Credentials Ready
    await vulogApiMocker.makeVulogCredentialsReady(
      vulogUserDto,
      vulogUserProfileDto,
    );
  });

  afterAll(async () => {
    await cleanResources(true);

    await app.close();
  });

  async function cleanResources(truncateUsers = false): Promise<void> {
    jest.clearAllMocks();

    mockAdapter_VulogApiService.reset();
    mockAdapter_UssApiService.reset();

    if (truncateUsers) await userRepo.clear();

    await reservationRepo.clear();
    await cacheService.clearAll();
    await carRepo.delete({});
    await vulogEventRepository.clearAll();
  }

  describe(`Given: BlueSG staff ends the trip on AiMA, Vulog sends CRS AiMA ${VulogEventNotifierType.EndTripReport} event | When: there is an on-going rental | Then: the rental should be ended gracefully`, () => {
    const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());

    const mock_internalStationResponseDtoV1: InternalStationResponseDtoV1 =
      new InternalStationResponseDtoV1Builder().build();

    const mock_CarStaticInfoDto: VulogCarStaticInfoDto =
      createTestVulogCarStaticInfoDto(
        carEntity.vulogId,
        config.vulogConfig.fleetId.value,
        carEntity.model,
        carEntity.vin,
      );

    const mock_VulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withId(config.vulogConfig.serviceId.value)
      .withZones([mock_internalStationResponseDtoV1.zoneId])
      .withVehicles([mock_CarStaticInfoDto.id])
      .build();

    const mock_vulogTripId: string = aMD5();

    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(carEntity.vulogId)
      .makePlateNumber(carEntity.plate)
      .makeStatus(ReservationStatus.Converted) // On-going rental
      .makeStartedAt(DateTime.now().toJSDate())
      .makeEndedAt(null)
      .makePlateNumber(carEntity.plate)
      .makeBsgUserId(userId)
      .makeVulogTripId(mock_vulogTripId)
      .makeStartStationId(mock_internalStationResponseDtoV1.id)
      .makeDuration(50)
      .build();

    const mock_vvgVehicleStatusDto: VvgVehicleStatusDto =
      new VvgVehicleStatusDtoBuilder()
        .withId(carEntity.vulogId)
        .withDoorsAndWindowsClosed(true)
        .withDoorsLocked(true)
        .withEngineOn(false)
        .withPlugged(true)
        .withCablePlugged(true)
        .withImmobilizerOn(true)
        .build();

    const vulogEndTripReportEventDto: VulogEndTripReportWebhookDtoV1 =
      new VulogEndTripReportWebhookDtoV1Builder()
        .buildId(aUUID())
        .buildUserId(userId)
        .buildVehicleId(carEntity.vulogId)
        .buildVehiclePlate(carEntity.plate)
        .buildOrigin('BOX')
        .buildEndDate(
          DateTime.fromJSDate(reservationEntity.startedAt)
            .plus({ hours: 1 })
            .toISO(),
        )
        .buildDuration(faker.number.int({ min: 10, max: 60 }))
        .buildTripDuration(faker.number.int({ min: 10, max: 60 }))
        .buildTripId(reservationEntity.vulogTripId)
        .buildStartZones([mock_internalStationResponseDtoV1.zoneId])
        .buildEndZones([mock_internalStationResponseDtoV1.zoneId])
        .buildTrigger('placeholder_trigger')
        .getResult();

    const mock_eventBridgeDto: EventbridgeEventDto =
      new EventbridgeEventDtoBuilder()
        .buildDetail(vulogEndTripReportEventDto)
        .getResult();

    const mock_vulogStaticCarDto: VulogCarStaticInfoDto =
      new VulogCarStaticInfoDtoBuilder()
        .withId(carEntity.vulogId)
        .withModel(carEntity.model)
        .withPlate(carEntity.plate)
        .withServiceId(mock_VulogFleetServiceDto.id)
        .withVin(carEntity.vin)
        .build();

    let apiResponse: supertest.Response;

    beforeAll(async () => {
      // Car should be sticked with a station in ending rental step
      jest
        .spyOn(featureFlagService, 'stickStationWithCarInRentalFlow')
        .mockResolvedValueOnce(true);

      await carRepo.insert(carEntity);

      await reservationRepo.insert(reservationEntity);

      // -----------------------
      // VulogFleetService.getFleetServices
      jest
        .spyOn(vulogClientService, 'getAsArray')
        .mockResolvedValueOnce([mock_VulogFleetServiceDto]);

      // VulogTelemetryService.getCarStatusSessionCommand
      jest
        .spyOn(vulogClientService, 'get')
        .mockResolvedValueOnce(mock_vvgVehicleStatusDto);

      // StationService.getAllStations
      jest
        .spyOn(stationAdapterService, 'getAll')
        .mockResolvedValue([mock_internalStationResponseDtoV1]);

      // Stick station with car. Already covered in another test, so mock Service directly
      jest
        .spyOn(vulogCarService, 'toggleStickyStationWithCar')
        .mockResolvedValueOnce(null);

      // Listing out static cars from Vulog
      jest
        .spyOn(vulogClientService, 'getAllPagesAsArray')
        .mockResolvedValueOnce([mock_vulogStaticCarDto]);

      // Get user details from USS
      jest
        .spyOn(userSubscriptionExternalService.internalApi, 'getUserDetail')
        .mockResolvedValueOnce(
          new InternalUserDetailResponseDtoV1Builder()
            .withId(userId)
            .withUserStatus(UserStatus.VERIFIED)
            .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
            .build(),
        );
      // -----------------------

      apiResponse = await requestFor(mock_eventBridgeDto);

      await sleep(1);
    });

    it('The status code of HTTP request is expected', async () => {
      expect(apiResponse.status).toEqual(HttpStatus.CREATED);
    });

    it('The information of vulog event is expected', async () => {
      const vulogEventEntity: VulogEventEntity =
        await vulogEventRepository.findOne({
          where: { eventId: vulogEndTripReportEventDto.id },
        });

      expect(vulogEventEntity).toBeDefined();
      expect(vulogEventEntity.eventId).toEqual(vulogEndTripReportEventDto.id);
      expect(vulogEventEntity.origin).toEqual(
        vulogEndTripReportEventDto.origin,
      );
      expect(vulogEventEntity.payload).toEqual(
        JSON.parse(JSON.stringify(vulogEndTripReportEventDto)),
      );
      expect(vulogEventEntity.source).toEqual(VulogEventSource.AiMA);
      expect(vulogEventEntity.trigger).toEqual(
        vulogEndTripReportEventDto.trigger,
      );
      expect(vulogEventEntity.type).toEqual(
        VulogEventNotifierType.EndTripReport,
      );
    });

    it('The information of rental is expected', async () => {
      const resultEntity = await reservationRepo.findOne({
        where: { id: reservationEntity.id },
      });

      expect(resultEntity.status).toEqual(ReservationStatus.Ended);
      expect(resultEntity.endedAt).toBeDefined();
      expect(resultEntity.endStationId).toEqual(
        mock_internalStationResponseDtoV1.id,
      );
      expect(resultEntity.duration).toBeDefined();
    });

    afterAll(async () => {
      await cleanResources();
    });
  });
});
