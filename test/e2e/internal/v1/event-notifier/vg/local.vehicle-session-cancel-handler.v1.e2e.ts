import * as supertest from 'supertest';

import { HttpStatus, INestApplication } from '@nestjs/common';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { getRepositoryToken } from '@nestjs/typeorm';
import { HttpStatusCode } from 'axios';
import MockAdapter from 'axios-mock-adapter';
import * as configcat from 'configcat-node';
import * as _ from 'lodash';
import { DateTime } from 'luxon';
import * as QueryString from 'qs';
import { RouteSegment } from 'src/common/constants/api-path';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { BsgUserId } from 'src/common/tiny-types';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { config } from 'src/config';
import { InternalStationResponseDtoV1 } from 'src/external/station/dtos/response/internal.station-response.v1.response.dto';
import {
  SubscriptionStatus,
  UserStatus,
} from 'src/external/user-subscription/constants';
import { UserSubscriptionExternalService } from 'src/external/user-subscription/user-subscription.external.service';
import { CacheService } from 'src/logic/cache/cache.service';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { ReservationCacheService } from 'src/logic/reservation/reservation.cache.service';
import { StationAdapterService } from 'src/logic/station/station-adapter.service';
import { StationPaths } from 'src/logic/station/station-paths';
import { VulogEndTripReportBodyV1 } from 'src/logic/vulog/dto/webhook/aima/vulog-end-trip-report-webhook.v1.dto';
import { VulogEventNotifierWebhookDtoV1 } from 'src/logic/vulog/dto/webhook/aima/vulog-event-notifier-webhook.v1.dto';
import { EventbridgeEventDto } from 'src/logic/vulog/dto/webhook/eventbridge-event.dto';
import {
  VulogVehicleGatewayEventNotificationDto,
  VulogVehicleGatewayEventType,
  VulogVehicleGatewayOrigin,
} from 'src/logic/vulog/dto/webhook/vg/vulog-vehicle-gateway-event-notification.dto';
import { VulogClientService } from 'src/logic/vulog/vulog-client.service';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { StationDto } from 'src/model/dtos/station.dto';
import {
  VulogRealTimeBookingStatus,
  VulogRealTimeVehicleStatus,
} from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogCarStaticInfoDto } from 'src/model/dtos/vulog-car-static-info/vulog-car-static-info.dto';
import {
  VulogZone,
  VulogZoneType,
} from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { VvgVehicleStatusDto } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import {
  VulogEventEntity,
  VulogEventSource,
} from 'src/model/repositories/entities/vulog-event.entity';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { VulogEventRepository } from 'src/model/repositories/vulog-event.repository';
import { ReservationInfo } from 'src/model/reservation-info';
import { EventBusService } from 'src/shared/event-bus/event-bus.service';
import { AppBuilder } from 'test/helpers/app.builder';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { InternalStationResponseDtoV1Builder } from 'test/helpers/builders/dtos/internal-station.response.v1.dto.builder';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogCarStaticInfoDto } from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { EventbridgeEventDtoBuilder } from 'test/helpers/builders/dtos/vulog/event/eventbridge-event.dto.builder';
import { VulogCancelTripReportBodyV1Builder } from 'test/helpers/builders/dtos/vulog/event/vulog-cancel-trip-report-body-v1.builder';
import { VulogCancelTripWebhookV1Builder } from 'test/helpers/builders/dtos/vulog/event/vulog-cancel-trip-report-webhook-dto-v1.builder';
import { VulogCarStaticInfoDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car-static-info.dto.builder';
import {
  VulogCarRealTimeDtoBuilder,
  VulogZoneBuilder,
} from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import {
  VulogBookingBuilder,
  VulogJourneyDtoBuilder,
} from 'test/helpers/builders/dtos/vulog/vulog-journey.dto.builder';
import { VulogTripAdditionalInfoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-trip-additional-info.builder';
import { VulogUserDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-user.dto.builder';
import { VulogVgVehicleCancelTripDtoBuilder } from 'test/helpers/builders/dtos/vulog/vvg/vvg-vehicle-cancel-trip.dto.builder';
import { VulogVgVehicleSessionCancelDtoBuilder } from 'test/helpers/builders/dtos/vulog/vvg/vvg-vehicle-session-cancel.dto.builder';
import { VvgVehicleStatusDtoBuilder } from 'test/helpers/builders/dtos/vulog/vvg/vvg-vehicle-status.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { createTestVulogUserEntity } from 'test/helpers/builders/entities/test-vulog-user-entity.builder';
import { RealtimeCarBuilder } from 'test/helpers/builders/realtime-car.builder';
import { VulogTripBuilder } from 'test/helpers/builders/trip/vulog-trip.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { VulogApiMocker } from 'test/helpers/external-services/vulog.api-mocker';
import { aMD5, aUUID } from 'test/helpers/random-data.helper';
import { sleep } from 'test/helpers/util/sleep.util';
import { Repository } from 'typeorm';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';

jest.setTimeout(100000);

const testingApiRoute = ApiRouteUtils.buildApiRouteSegment(
  '' as any,
  '' as any,
  [
    'api/v1/internal',
    RouteSegment.EventNotifier.Index,
    RouteSegment.EventNotifier.EventBridge,
  ],
);

const userId = aUUID();

// WIP: Need proper mock function to run the test
describe(`[E2E] POST: ${testingApiRoute}`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let reservationRepo: Repository<ReservationEntity>;
  let vulogUserRepo: Repository<VulogUserEntity>;
  let carRepo: Repository<CarEntity>;
  let cacheService: CacheService;
  let reservationCacheService: ReservationCacheService;

  let featureFlagService: FeatureFlagService;
  let vulogCarService: VulogCarService;
  let vulogEventRepository: VulogEventRepository;

  let vulogClientService: VulogClientService;
  let stationAdapterService: StationAdapterService;
  let userSubscriptionExternalService: UserSubscriptionExternalService;

  const mockConfigCatClient_getValueAsync = jest.fn();

  let mockOutgoingService: MockAdapter;

  let eventService: EventBusService;

  function requestFor(dto: EventbridgeEventDto): supertest.Test {
    const superTest = supertest(app.getHttpServer())
      .post(testingApiRoute)
      .send({
        ...dto,
        moreAttribute: 'hello',
      });
    return superTest;
  }

  const vulogUserDto = new VulogUserDtoBuilder().withId(userId).build();
  const vulogUserProfileDto = createTestVulogUserServicesDto();

  beforeAll(async () => {
    jest.spyOn(configcat, 'getClient').mockImplementation(
      () =>
        ({
          getValueAsync: mockConfigCatClient_getValueAsync,
        } as any),
    );

    // Toggle feature flag to true
    mockConfigCatClient_getValueAsync.mockResolvedValue(true);

    app = await appBuilder
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(aUUID()), true)
      .buildAndStart();

    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );
    vulogUserRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );
    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));

    eventService = app.get<EventBusService>(EventBusService);
    cacheService = app.get<CacheService>(CacheService);
    reservationCacheService = app.get<ReservationCacheService>(
      ReservationCacheService,
    );

    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );

    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );

    const vulogApiMocker = new VulogApiMocker(app);
    vulogApiMocker.mock();

    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));

    featureFlagService = app.get<FeatureFlagService>(FeatureFlagService);

    cacheService = app.get(CacheService);

    vulogCarService = app.get<VulogCarService>(VulogCarService);

    vulogClientService = app.get<VulogClientService>(VulogClientService);

    stationAdapterService = app.get<StationAdapterService>(
      StationAdapterService,
    );

    userSubscriptionExternalService = app.get<UserSubscriptionExternalService>(
      UserSubscriptionExternalService,
    );

    vulogEventRepository = app.get<VulogEventRepository>(VulogEventRepository);

    const httpApdaterService = app.get<HttpAdapterService>(HttpAdapterService);

    mockOutgoingService = new MockAdapter(
      httpApdaterService.httpService.axiosRef,
      { onNoMatch: 'throwException' },
    );

    eventService.nonBlockingEmit = jest.fn();

    // Make Vulog Credentials Ready
    await vulogApiMocker.makeVulogCredentialsReady(
      vulogUserDto,
      vulogUserProfileDto,
    );
  });

  afterAll(async () => {
    await clean(true);

    await app.close();
  });

  async function clean(truncateUsers = false): Promise<void> {
    jest.clearAllMocks();

    if (truncateUsers) await vulogUserRepo.clear();

    await reservationRepo.clear();
    await cacheService.clearAll();
    await carRepo.delete({});
    await vulogEventRepository.clearAll();
  }

  describe.each([
    { type: VulogVehicleGatewayEventType.VEHICLE_CANCEL_TRIP },
    { type: VulogVehicleGatewayEventType.VEHICLE_SESSION_CANCEL },
  ])(
    'Event "$type". Given that the customer has on-going reservation in BlueSG, when staff cancels journey on Vulog, then the reservation should be cancelled gracefully in BlueSG',
    ({ type }) => {
      let responseObject: supertest.Response;
      let reservation: ReservationEntity;
      let event: VulogVehicleGatewayEventNotificationDto;
      const mock_vulogRealtimeCar = new RealtimeCarBuilder().build();

      const vulogZones: VulogZone[] = [
        new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
        new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
        new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
      ];

      let vulogUserEntity = createTestVulogUserEntity(aUUID());

      const customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withId(config.vulogConfig.serviceId.value)
        .withVehicles([mock_vulogRealtimeCar.id.value])
        .withZones(vulogZones.map((vulogZone) => vulogZone.zoneId))
        .build();

      let mock_reservationInfo: ReservationInfo;

      let eventBridgeDto: EventbridgeEventDto;

      let stationDto: StationDto;

      beforeAll(async () => {
        await carRepo.save(CarEntity.from(mock_vulogRealtimeCar.toCar()));
        vulogUserEntity = await vulogUserRepo.save(vulogUserEntity);
        const vulogTrip: any = new VulogTripBuilder()
          .makeAdditionalInfo(
            new VulogTripAdditionalInfoBuilder().makeIsCancel(false).build(),
          )
          .build();

        event;

        switch (type) {
          case VulogVehicleGatewayEventType.VEHICLE_SESSION_CANCEL:
            event = new VulogVgVehicleSessionCancelDtoBuilder()
              .buildId()
              .buildVehicleId(mock_vulogRealtimeCar.id.value)
              .buildSessionId(vulogTrip.id)
              .buildOrigin(VulogVehicleGatewayOrigin.API)
              .buildDate(DateTime.now().toISO())
              .getResult();
            break;
          case VulogVehicleGatewayEventType.VEHICLE_CANCEL_TRIP:
            event = new VulogVgVehicleCancelTripDtoBuilder()
              .buildId()
              .buildVehicleId(mock_vulogRealtimeCar.id.value)
              .buildUserId(userId)
              .buildSessionId(vulogTrip.id)
              .buildOrigin(VulogVehicleGatewayOrigin.API)
              .buildDate(DateTime.now().toISO())
              .getResult();
            break;
        }

        eventBridgeDto = new EventbridgeEventDtoBuilder()
          .buildDetail(event)
          .getResult();

        stationDto = new StationDtoBuilder().build();

        reservation = await reservationRepo.save(
          new ReservationEntityBuilder()
            .makeStatus(ReservationStatus.Reserved)
            .makeExpiresAt(DateTime.now().plus({ minutes: 15 }).toJSDate())
            .makeVulogTripId(event.sessionId)
            .makeBsgUserId(vulogUserEntity.bsgUserId)
            .makeCarId(mock_vulogRealtimeCar.id.value)
            .makePlateNumber(mock_vulogRealtimeCar.plate.value)
            .makeAllocateAgain(false)
            .makeStartStationId(stationDto.id)
            .build(),
        );

        mock_reservationInfo = new VulogJourneyDtoBuilder()
          .withId(reservation.vulogTripId)
          .withVehicleId(reservation.carId)
          .withBooking(
            new VulogBookingBuilder(
              reservation.reservedAt.toISOString(),
              reservation.expiresAt.toISOString(),
            ).build(),
          )
          .build()
          .toReservationInfo();

        // Listing out stations from STS
        mockOutgoingService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 1,
              pageSize: 200,
            })}`,
          )
          .replyOnce(200, {
            result: [stationDto],
          });

        mockOutgoingService
          .onGet(
            `${StationPaths.stations()}?${QueryString.stringify({
              page: 2,
              pageSize: 200,
            })}`,
          )
          .replyOnce(200, {
            result: [], // Breaking loop
          });

        // Listing out real-time cars from Vulog
        mockOutgoingService
          .onGet(
            `${VulogPaths.backOfficeVehicles(
              config.vulogConfig.fleetId,
              mock_vulogRealtimeCar.id,
            )}`,
          )
          .replyOnce(
            200,
            new VulogCarRealTimeDtoBuilder()
              .withAutonomy(100)
              .withBattery(100)
              .withFleetId(config.vulogConfig.fleetId.value)
              .withId(mock_vulogRealtimeCar.id.value)
              .withVin(mock_vulogRealtimeCar.vin)
              .withPlate(mock_vulogRealtimeCar.plate.value)
              .withZones([_.sample(vulogZones)])
              .withBookingStatus(VulogRealTimeBookingStatus.Available)
              .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
              .build(),
          );

        // Listing out fleet services from Vulog
        mockOutgoingService
          .onGet(
            `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
          )
          .reply(200, [customerFleetServiceDto]);

        await reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.update(
          new BsgUserId(vulogUserEntity.bsgUserId),
          mock_reservationInfo,
        );
      });

      afterAll(async () => {
        await clean();
      });

      it('Verify expectation', async () => {
        let carSwitchPerformedState =
          await reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.retrieve(
            new BsgUserId(vulogUserEntity.bsgUserId),
          );

        expect(!!carSwitchPerformedState).toEqual(true);

        responseObject = await requestFor(eventBridgeDto);

        await sleep(1);

        expect(responseObject.statusCode).toBe(HttpStatusCode.Created);

        const updatedReservation = await reservationRepo.findOne({
          where: {
            id: reservation.id,
          },
        });

        expect(updatedReservation).toMatchObject({
          status: ReservationStatus.Cancelled,
        });

        expect(updatedReservation.canceledAt).toBeDefined();

        carSwitchPerformedState =
          await reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.retrieve(
            new BsgUserId(vulogUserEntity.bsgUserId),
          );

        expect(!!carSwitchPerformedState).toEqual(false);
      });
    },
  );

  describe.each([
    { type: VulogVehicleGatewayEventType.VEHICLE_CANCEL_TRIP },
    { type: VulogVehicleGatewayEventType.VEHICLE_SESSION_CANCEL },
  ])(
    'Event type "$type". Given that the customer has on-going rental in BlueSG but on-going reservation (journey) on Vulog, when staff cancels journey on Vulog, then the reservation should be ended gracefully in BlueSG',
    ({ type }) => {
      const carEntity: CarEntity = CarEntity.from(new CarBuilder().build());

      const mock_internalStationResponseDtoV1: InternalStationResponseDtoV1 =
        new InternalStationResponseDtoV1Builder().build();

      const mock_CarStaticInfoDto: VulogCarStaticInfoDto =
        createTestVulogCarStaticInfoDto(
          carEntity.vulogId,
          config.vulogConfig.fleetId.value,
          carEntity.model,
          carEntity.vin,
        );

      const mock_VulogFleetServiceDto = new VulogFleetServiceDtoBuilder()
        .withId(config.vulogConfig.serviceId.value)
        .withZones([mock_internalStationResponseDtoV1.zoneId])
        .withVehicles([mock_CarStaticInfoDto.id])
        .build();

      const mock_vulogTripId: string = aMD5();

      const reservationEntity: ReservationEntity =
        new ReservationEntityBuilder()
          .makeCarId(carEntity.vulogId)
          .makePlateNumber(carEntity.plate)
          .makeStatus(ReservationStatus.Converted) // On-going rental
          .makeStartedAt(DateTime.now().toJSDate())
          .makeEndedAt(null)
          .makePlateNumber(carEntity.plate)
          .makeBsgUserId(userId)
          .makeVulogTripId(mock_vulogTripId)
          .makeStartStationId(mock_internalStationResponseDtoV1.id)
          .makeDuration(50)
          .build();

      const mock_vvgVehicleStatusDto: VvgVehicleStatusDto =
        new VvgVehicleStatusDtoBuilder()
          .withId(carEntity.vulogId)
          .withDoorsAndWindowsClosed(true)
          .withDoorsLocked(true)
          .withEngineOn(false)
          .withPlugged(true)
          .withCablePlugged(true)
          .withImmobilizerOn(true)
          .build();

      let event: VulogVehicleGatewayEventNotificationDto;

      switch (type) {
        case VulogVehicleGatewayEventType.VEHICLE_SESSION_CANCEL:
          event = new VulogVgVehicleSessionCancelDtoBuilder()
            .buildId()
            .buildVehicleId(carEntity.vulogId)
            .buildSessionId(reservationEntity.vulogTripId)
            .buildOrigin(VulogVehicleGatewayOrigin.API)
            .buildDate(DateTime.now().toISO())
            .getResult();
          break;
        case VulogVehicleGatewayEventType.VEHICLE_CANCEL_TRIP:
          event = new VulogVgVehicleCancelTripDtoBuilder()
            .buildId()
            .buildVehicleId(carEntity.vulogId)
            .buildUserId(userId)
            .buildSessionId(reservationEntity.vulogTripId)
            .buildOrigin(VulogVehicleGatewayOrigin.API)
            .buildDate(DateTime.now().toISO())
            .getResult();
          break;
      }

      const mock_eventBridgeDto: EventbridgeEventDto =
        new EventbridgeEventDtoBuilder().buildDetail(event).getResult();

      const mock_vulogStaticCarDto: VulogCarStaticInfoDto =
        new VulogCarStaticInfoDtoBuilder()
          .withId(carEntity.vulogId)
          .withModel(carEntity.model)
          .withPlate(carEntity.plate)
          .withServiceId(mock_VulogFleetServiceDto.id)
          .withVin(carEntity.vin)
          .build();

      let apiResponse: supertest.Response;

      beforeAll(async () => {
        // Car should be sticked with a station in ending rental step
        jest
          .spyOn(featureFlagService, 'stickStationWithCarInRentalFlow')
          .mockResolvedValueOnce(true);

        await carRepo.insert(carEntity);

        await reservationRepo.insert(reservationEntity);

        // -----------------------
        // VulogFleetService.getFleetServices
        jest
          .spyOn(vulogClientService, 'getAsArray')
          .mockResolvedValueOnce([mock_VulogFleetServiceDto]);

        // VulogTelemetryService.getCarStatusSessionCommand
        jest
          .spyOn(vulogClientService, 'get')
          .mockResolvedValueOnce(mock_vvgVehicleStatusDto);

        // StationService.getAllStations
        jest
          .spyOn(stationAdapterService, 'getAll')
          .mockResolvedValue([mock_internalStationResponseDtoV1]);

        // Stick station with car. Already covered in another test, so mock Service directly
        jest
          .spyOn(vulogCarService, 'toggleStickyStationWithCar')
          .mockResolvedValueOnce(null);

        // Listing out static cars from Vulog
        jest
          .spyOn(vulogClientService, 'getAllPagesAsArray')
          .mockResolvedValueOnce([mock_vulogStaticCarDto]);

        // Get user details from USS
        jest
          .spyOn(userSubscriptionExternalService.internalApi, 'getUserDetail')
          .mockResolvedValueOnce(
            new InternalUserDetailResponseDtoV1Builder()
              .withId(userId)
              .withUserStatus(UserStatus.VERIFIED)
              .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
              .build(),
          );
        // -----------------------

        apiResponse = await requestFor(mock_eventBridgeDto);

        await sleep(1);
      });

      it('The status code of HTTP request is expected', async () => {
        expect(apiResponse.status).toEqual(HttpStatus.CREATED);
      });

      it('The information of vulog event is expected', async () => {
        const vulogEventEntity: VulogEventEntity =
          await vulogEventRepository.findOne({
            where: { eventId: event.id },
          });

        expect(vulogEventEntity).toBeDefined();
        expect(vulogEventEntity.eventId).toEqual(event.id);
        expect(vulogEventEntity.origin).toEqual(event.origin);
        expect(vulogEventEntity.payload).toEqual(
          JSON.parse(JSON.stringify(event)),
        );
        expect(vulogEventEntity.source).toEqual(
          VulogEventSource.VEHICLE_GATEWAY,
        );
        expect(vulogEventEntity.type).toEqual(type);
      });

      it('The information of rental is expected', async () => {
        const resultEntity = await reservationRepo.findOne({
          where: { id: reservationEntity.id },
        });

        expect(resultEntity.status).toEqual(ReservationStatus.Ended);
        expect(resultEntity.endedAt).toBeDefined();
        expect(resultEntity.endStationId).toEqual(
          mock_internalStationResponseDtoV1.id,
        );
        expect(resultEntity.duration).toBeDefined();
      });

      afterAll(async () => {
        await clean();
      });
    },
  );

  describe('Should throw an error when Vulog Event is containing error', () => {
    let responseObject: supertest.Response;

    let event: VulogEventNotifierWebhookDtoV1<VulogEndTripReportBodyV1>;

    const errorFromVulog = 'Error from Vulog';

    let eventBridgeDto: EventbridgeEventDto;

    beforeAll(async () => {
      const car = new RealtimeCarBuilder().build();
      await carRepo.save(CarEntity.from(car.toCar()));
      const user = await vulogUserRepo.save(createTestVulogUserEntity());
      const vulogTrip: any = new VulogTripBuilder()
        .makeAdditionalInfo(
          new VulogTripAdditionalInfoBuilder().makeIsCancel(false).build(),
        )
        .build();

      event = new VulogCancelTripWebhookV1Builder()
        .makeVehicleId(car.id.value)
        .makeVulogUserId(user.vulogUserId)
        .makeTripId(vulogTrip.id)
        .makeVehiclePlate(car.plate.value)
        .makeVehicleVin(car.vin)
        .makeBody(
          new VulogCancelTripReportBodyV1Builder()
            .withError(errorFromVulog)
            .build(),
        )
        .build();

      eventBridgeDto = new EventbridgeEventDtoBuilder()
        .buildDetail(event)
        .getResult();

      await reservationRepo.save(
        new ReservationEntityBuilder()
          .makeStatus(ReservationStatus.Reserved)
          .makeExpiresAt(DateTime.now().plus({ minutes: 15 }).toJSDate())
          .makeVulogTripId(event.tripId)
          .makeBsgUserId(user.id)
          .makeCarId(car.id.value)
          .build(),
      );

      responseObject = await requestFor(eventBridgeDto);
    });

    afterAll(async () => {
      await clean();
    });

    it('Should return status 400', () => {
      expect(responseObject.statusCode).toBe(HttpStatusCode.BadRequest);
    });

    it('Should return the expected error message', async () => {
      expect(responseObject.body.message).toEqual(
        `An error happened while handling Vulog event - Cause: Vulog event received, but with error information: ${errorFromVulog}. No further action is performed.`,
      );
    });
  });
});
