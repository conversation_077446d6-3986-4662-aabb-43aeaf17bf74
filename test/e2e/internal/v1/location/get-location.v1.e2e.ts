import { HttpStatus, INestApplication } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DateTime } from 'luxon';
import * as QueryString from 'qs';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { InternalLocationQueryParamsDtoV1 } from 'src/controllers/internal/v1/location/dto/request/internal.location.query.v1.dto';
import { InternalLocationResponseDtoV1 } from 'src/controllers/internal/v1/location/dto/response/internal.location.response.dto.v1';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import * as supertest from 'supertest';
import { AppBuilder } from 'test/helpers/app.builder';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { Repository } from 'typeorm';

const testingApiRoute = ApiRouteUtils.buildApiRouteSegment(
  '' as any,
  '' as any,
  [
    'api',
    'v1',
    RentalServiceRootRouteSegment.Internal,
    RouteSegment.Location.Index,
  ],
);

describe(`[E2E] GET: ${testingApiRoute}`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let reservationRepository: Repository<ReservationEntity>;

  let carRepository: Repository<CarEntity>;

  function requestFor(
    params?: Partial<InternalLocationQueryParamsDtoV1>,
  ): supertest.Test {
    const superTest = supertest(app.getHttpServer())
      .get(`${testingApiRoute}?${QueryString.stringify(params)}`)
      .send();

    return superTest;
  }

  beforeAll(async () => {
    app = await appBuilder.buildAndStart();

    reservationRepository = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );

    carRepository = app.get<Repository<CarEntity>>(
      getRepositoryToken(CarEntity),
    );
  });

  afterAll(async () => {
    await app.close();
  });

  describe('when get locations from ended rental within last xx minutes', () => {
    const now = DateTime.now();

    let car1: CarEntity;
    let car1Ended: ReservationEntity;
    let car2: CarEntity;
    let car2Ended: ReservationEntity;
    let car3: CarEntity;
    let car3EndedWithoutStation: ReservationEntity;
    let endedWithoutCar: ReservationEntity;

    beforeAll(async () => {
      car1 = await carRepository.save(CarEntity.from(new CarBuilder().build()));

      await reservationRepository.save(
        new ReservationEntityBuilder()
          .makeStatus(ReservationStatus.Converted)
          .makeStartedAt(now.minus({ minutes: 4 }).toJSDate())
          .makeEndedAt(null)
          .makeCarId(car1.vulogId)
          .build(),
      );

      car1Ended = await reservationRepository.save(
        new ReservationEntityBuilder()
          .makeStatus(ReservationStatus.Ended)
          .makeStartedAt(now.minus({ minutes: 30 }).toJSDate())
          .makeEndedAt(now.minus({ minutes: 5 }).toJSDate())
          .makeCarId(car1.vulogId)
          .build(),
      );

      car2 = await carRepository.save(CarEntity.from(new CarBuilder().build()));

      car2Ended = await reservationRepository.save(
        new ReservationEntityBuilder()
          .makeStatus(ReservationStatus.Ended)
          .makeStartedAt(now.minus({ minutes: 20 }).toJSDate())
          .makeEndedAt(now.minus({ minutes: 10 }).toJSDate())
          .makeCarId(car2.vulogId)
          .build(),
      );

      car3 = await carRepository.save(CarEntity.from(new CarBuilder().build()));

      await reservationRepository.save(
        new ReservationEntityBuilder()
          .makeStatus(ReservationStatus.Ended)
          .makeStartedAt(now.minus({ minutes: 30 }).toJSDate())
          .makeEndedAt(now.minus({ minutes: 11 }).toJSDate())
          .makeCarId(car3.vulogId)
          .build(),
      );

      // reservations without end station or car would not be returned
      car3EndedWithoutStation = await reservationRepository.save(
        new ReservationEntityBuilder()
          .makeStatus(ReservationStatus.Ended)
          .makeStartedAt(now.minus({ minutes: 5 }).toJSDate())
          .makeEndedAt(now.minus({ minutes: 2 }).toJSDate())
          .makeCarId(car3.vulogId)
          .makeEndStationId(null)
          .build(),
      );

      expect(car3EndedWithoutStation.endStationId).toBeFalsy();

      endedWithoutCar = await reservationRepository.save(
        new ReservationEntityBuilder()
          .makeStatus(ReservationStatus.Ended)
          .makeStartedAt(now.minus({ minutes: 10 }).toJSDate())
          .makeEndedAt(now.minus({ minutes: 4 }).toJSDate())
          .makeCarId(null)
          .build(),
      );

      expect(endedWithoutCar.carId).toBeFalsy();

      const allReservations = await reservationRepository.find({});
      expect(allReservations.length).toEqual(6);
    });

    afterAll(async () => {
      await reservationRepository.clear();
      await carRepository.clear();
    });

    it('should throw bad request with malformed from param', async () => {
      const response = await requestFor({
        from: 'foobar',
      });

      expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);

      expect(response.body.message).toEqual([
        'from must be a valid ISO date time and must not be in future',
      ]);
    });

    it('should throw bad request with from param in the future', async () => {
      const response = await requestFor({
        from: now.plus({ minutes: 2 }).toUTC().toISO(),
      });

      expect(response.statusCode).toEqual(HttpStatus.BAD_REQUEST);

      expect(response.body.message).toEqual([
        'from must be a valid ISO date time and must not be in future',
      ]);
    });

    it('should return locations from 0 rentals ended within last 2 minutes', async () => {
      const response = await requestFor({
        from: now.minus({ minutes: 2 }).toISO(),
      });

      expect(response.statusCode).toEqual(HttpStatus.OK);

      const result = response.body.result as InternalLocationResponseDtoV1[];
      expect(result.length).toEqual(0);
    });

    it('should return locations from 1 rental ended within last 5 minutes', async () => {
      const response = await requestFor({
        from: now.minus({ minutes: 5 }).toUTC().toISO(),
      });

      expect(response.statusCode).toEqual(HttpStatus.OK);

      const result = response.body.result as InternalLocationResponseDtoV1[];
      expect(result.length).toEqual(1);
      expectToMatchLocationResponse(result[0], car1.vulogId, car1Ended);
    });

    it('should return locations from 2 rentals ended within last 10 minutes', async () => {
      const response = await requestFor({
        from: now.minus({ minutes: 10 }).toUTC().toISO(),
      });

      expect(response.statusCode).toEqual(HttpStatus.OK);

      const result = response.body.result as InternalLocationResponseDtoV1[];
      expect(result.length).toEqual(2);
      expectToMatchLocationResponse(result[0], car2.vulogId, car2Ended);
      expectToMatchLocationResponse(result[1], car1.vulogId, car1Ended);
    });
  });
});

function expectToMatchLocationResponse(
  actual: InternalLocationResponseDtoV1,
  expectedVulogCarId: string,
  expectedReservation: ReservationEntity,
): void {
  expect(actual.vulogCarId).toEqual(expectedVulogCarId);
  expect(actual.stationId).toEqual(expectedReservation.endStationId);
  expect(actual.locatedAt).toEqual(expectedReservation.endedAt.toISOString());
}
