import * as supertest from 'supertest';

import { HttpStatus, INestApplication } from '@nestjs/common';

import { faker } from '@faker-js/faker';
import MockAdapter from 'axios-mock-adapter';
import { DateTime } from 'luxon';
import * as QueryString from 'qs';
import {
  RentalServiceRootRouteSegment,
  RouteSegment,
} from 'src/common/constants/api-path';
import { VulogCarId, VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { config } from 'src/config';

import { InternalGetRealtimeCarQueryParamsDtoV1 } from 'src/controllers/internal/v1/car/dto/request/internal.get-realtime-car.query.v1.dto';
import { InternalRealtimeCarResponseDtoV1 } from 'src/controllers/internal/v1/car/dto/response/internal.realtime-car.response.dto.v1';
import { CacheService } from 'src/logic/cache/cache.service';
import { CarService } from 'src/logic/car/car.service';
import { Car } from 'src/logic/car/domain/car';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { StationPaths } from 'src/logic/station/station-paths';
import { VulogAuthService } from 'src/logic/vulog/vulog-auth.service';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { AvailableCarsAtStationMap } from 'src/model/available-cars-at-station-map';
import { StationDto } from 'src/model/dtos/station.dto';
import { VulogCarEventType } from 'src/model/dtos/vulog-car-event/vulog-car-event.dto';
import { VulogCarStaticInfoDto } from 'src/model/dtos/vulog-car-static-info/vulog-car-static-info.dto';
import { VulogZoneType } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { VvgVehicleStatusDto } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { CarRepository } from 'src/model/repositories/car.repository';
import { AppBuilder } from 'test/helpers/app.builder';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { VulogCarEventDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car-event.dto.builder';
import { VulogCarStaticInfoDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car-static-info.dto.builder';
import { VulogZoneBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { VulogFleetServiceDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-fleet-service.dto.builder';
import { VvgVehicleStatusDtoBuilder } from 'test/helpers/builders/dtos/vulog/vvg/vvg-vehicle-status.dto.builder';
import { aDateISOString, aUUID } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';

const mockAuthResponse = createTestVulogAuthResponse();

const testingApiRoute = ApiRouteUtils.buildApiRouteSegment(
  '' as any,
  '' as any,
  [
    'api',
    'v1',
    RentalServiceRootRouteSegment.Internal,
    RouteSegment.Car.Index,
    RouteSegment.Car.Realtime,
  ],
);

const email = faker.internet.email();

describe(`[E2E] POST: ${testingApiRoute}`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;
  let mockHttpAdapterService: MockAdapter;
  let cacheService: CacheService;
  let vulogAuthService: VulogAuthService;
  let carService: CarService;
  let carRepo: CarRepository;

  function requestFor(
    body?: Partial<InternalGetRealtimeCarQueryParamsDtoV1>,
  ): supertest.Test {
    const superTest = supertest(app.getHttpServer())
      .get(`${testingApiRoute}?${QueryString.stringify(body)}`)
      .send();

    return superTest;
  }

  beforeAll(async () => {
    app = await appBuilder.buildAndStart();

    vulogAuthService = app.get(VulogAuthService);

    const httpAdapterService = app.get(HttpAdapterService);
    mockHttpAdapterService = new MockAdapter(httpAdapterService.axiosInstance, {
      onNoMatch: 'throwException',
    });

    carService = app.get(CarService);

    cacheService = app.get(CacheService);

    carRepo = app.get(CarRepository);

    cacheService = app.get<CacheService>(CacheService);

    const userDto = createTestVulogUserDto();

    userDto.email = email;

    mockHttpAdapterService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, userDto);

    const userServicesAndProfiles = createTestVulogUserServicesDto();
    userServicesAndProfiles.userId = userDto.id;

    /**
     * Doing Vulog authentication mock
     */
    mockHttpAdapterService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(userDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockHttpAdapterService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockHttpAdapterService
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    mockHttpAdapterService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, mockAuthResponse);

    mockVulogCredential(app);
  });

  afterAll(async () => {
    await app.close();
  });

  async function clear(): Promise<void> {
    jest.clearAllMocks();

    await carRepo.clearAll();

    await cacheService.clearAll();
  }

  beforeEach(async () => {
    jest.spyOn(vulogAuthService, 'setupAuthConfig').mockResolvedValue({
      headers: {
        Authorization: `Bearer dummy`,
        'x-api-key': 'dummy',
      },
    });

    mockHttpAdapterService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, mockAuthResponse);
  });

  describe('🔥 Get specific car that is not charging', () => {
    let response: supertest.Response;
    const stationDtos: StationDto[] = [new StationDtoBuilder().build()];

    const ocpiEvseLastUpdated = DateTime.now().minus({ minutes: 5 });

    const vulogZone = new VulogZoneBuilder()
      .withSticky(true)
      .withType(VulogZoneType.Allowed)
      .withZoneId(stationDtos[0].id)
      .build();
    const availableVvgCarStatusDtos: VvgVehicleStatusDto[] = [
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withCablePlugged(true)
        .withIsCharging(false)
        .withZone(vulogZone)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withCablePlugged(true)
        .withIsCharging(false)
        .withZone(vulogZone)
        .build(),
    ];
    const staticCars: Car[] = [
      new CarBuilder()
        .withVulogId(new VulogCarId(availableVvgCarStatusDtos[0].id))
        .withRealtimeMetadata({
          batteryPercentage:
            availableVvgCarStatusDtos[0].status.tractionBattery.percentage,
        })
        .build(),
      new CarBuilder()
        .withVulogId(new VulogCarId(availableVvgCarStatusDtos[1].id))
        .withRealtimeMetadata({
          batteryPercentage:
            availableVvgCarStatusDtos[1].status.tractionBattery.percentage,
        })
        .build(),
    ];
    const customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withId(config.vulogConfig.serviceId.value)
      .withVehicles([staticCars[0].vulogId.value, staticCars[1].vulogId.value])
      .withZones([vulogZone.zoneId])
      .build();
    const availabilityDetailMap: AvailableCarsAtStationMap = {
      [stationDtos[0].id]: {
        station: stationDtos[0].toStation(),
        availableCars: availableVvgCarStatusDtos.map((vlgRltCarDto, index) =>
          vlgRltCarDto.toTelemetryCarInfo(
            staticCars[index],
            customerFleetServiceDto.toFleetService().getZoneIdsAsMap(),
            null, // Intentionally marked as NULL as not used in this testing
          ),
        ),
      },
    };
    const vulogCarEventDtoByVehicleId = {
      [availableVvgCarStatusDtos[0].id]: [
        new VulogCarEventDtoBuilder()
          .withType(VulogCarEventType.VEHICLE_CHARGING_PLUGGED)
          .withDate(ocpiEvseLastUpdated.minus({ minutes: 6 }).toISO())
          .withVehicleId(availableVvgCarStatusDtos[0].id)
          .build(),
      ],
      [availableVvgCarStatusDtos[1].id]: [
        new VulogCarEventDtoBuilder()
          .withType(VulogCarEventType.VEHICLE_CHARGING_PLUGGED)
          .withDate(ocpiEvseLastUpdated.minus({ minutes: 5 }).toISO())
          .withVehicleId(availableVvgCarStatusDtos[1].id)
          .build(),
      ],
    };
    const vulogCarStaticInfoDtos: VulogCarStaticInfoDto[] = [
      new VulogCarStaticInfoDtoBuilder()
        .withId(availableVvgCarStatusDtos[0].id)
        .withModel(staticCars[0].model)
        .withServiceId(customerFleetServiceDto.id)
        .build(),
      new VulogCarStaticInfoDtoBuilder()
        .withId(availableVvgCarStatusDtos[1].id)
        .withModel(staticCars[1].model)
        .withServiceId(customerFleetServiceDto.id)
        .build(),
    ];

    beforeAll(async () => {
      // Save cars into database
      await Promise.all(
        staticCars.map(async (car) => {
          await carService.save(car);
        }),
      );

      // Listing out stations from STS
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      await cacheService.set(
        config.redisConfig.carAvailabilityDetails.cacheKey,
        availabilityDetailMap,
        {
          ttl: 30, // seconds
        },
      );

      // Get specific real-time car
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(availableVvgCarStatusDtos[0].id),
          )}`,
        )
        .replyOnce(200, availableVvgCarStatusDtos[0]);

      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(availableVvgCarStatusDtos[1].id),
          )}`,
        )
        .replyOnce(200, availableVvgCarStatusDtos[1]);

      // Listing out fleet services from Vulog
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // List out events for a car
      mockHttpAdapterService
        .onGet(
          new RegExp(
            `${VulogPaths.backOfficeCarEvents(
              config.vulogConfig.fleetId,
              new VulogCarId(availableVvgCarStatusDtos[0].id),
            )}`,
            'gi',
          ),
        )
        .reply(
          200,
          vulogCarEventDtoByVehicleId[availableVvgCarStatusDtos[0].id],
        );

      mockHttpAdapterService
        .onGet(
          new RegExp(
            `${VulogPaths.backOfficeCarEvents(
              config.vulogConfig.fleetId,
              new VulogCarId(availableVvgCarStatusDtos[1].id),
            )}`,
            'gi',
          ),
        )
        .reply(
          200,
          vulogCarEventDtoByVehicleId[availableVvgCarStatusDtos[1].id],
        );

      // List all static car information
      mockHttpAdapterService
        .onGet(
          new RegExp(
            `${VulogPaths.backOfficeListingAllStaticVehicles(
              config.vulogConfig.fleetId,
            )}`,
            'gi',
          ),
        )
        .reply(200, vulogCarStaticInfoDtos, { last: true });

      response = await requestFor({
        atStation: stationDtos[0].id,
        charging: false,
        stsPluggedAt: ocpiEvseLastUpdated.toISO(),
      });
    });

    afterAll(async () => {
      await clear();
    });

    it('Should return status OK', () => {
      expect(response.status).toEqual(HttpStatus.OK);

      expect(response.body.result.pluggedAt).toBeDefined();

      delete response.body.result.pluggedAt;

      expect(response.body.result).toEqual(
        JSON.parse(
          JSON.stringify(
            InternalRealtimeCarResponseDtoV1.from(
              staticCars[0],
              undefined,
              customerFleetServiceDto.toFleetService(),
            ),
          ),
        ),
      );
    });
  });

  describe('💦 Station (following request body) is not found', () => {
    let response: supertest.Response;
    const stationId = aUUID();

    beforeAll(async () => {
      // Listing out stations from STS
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [],
        });

      response = await requestFor({
        atStation: stationId,
        charging: false,
        stsPluggedAt: aDateISOString(),
      });
    });

    afterAll(async () => {
      await clear();
    });

    it('Should return status OK', () => {
      expect(response.status).toBe(HttpStatus.NOT_FOUND);

      expect(response.body.message).toEqual('Station not found');
    });
  });

  describe('🔥 Get specific car that is not charging, no matching car', () => {
    let response: supertest.Response;
    const stationDtos: StationDto[] = [new StationDtoBuilder().build()];

    const ocpiEvseLastUpdated = DateTime.now().minus({ minutes: 5 });

    const vulogZone = new VulogZoneBuilder()
      .withSticky(true)
      .withType(VulogZoneType.Allowed)
      .withZoneId(stationDtos[0].id)
      .build();
    const availableVvgCarStatusDtos: VvgVehicleStatusDto[] = [
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withCablePlugged(true)
        .withIsCharging(false)
        .withZone(vulogZone)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withCablePlugged(true)
        .withIsCharging(false)
        .withZone(vulogZone)
        .build(),
    ];
    const staticCars: Car[] = [
      new CarBuilder()
        .withVulogId(new VulogCarId(availableVvgCarStatusDtos[0].id))
        .build(),
      new CarBuilder()
        .withVulogId(new VulogCarId(availableVvgCarStatusDtos[1].id))
        .build(),
    ];

    const availabilityDetailMap: AvailableCarsAtStationMap = {
      [stationDtos[0].id]: {
        station: stationDtos[0].toStation(),
        availableCars: [], // no available cars
      },
    };

    beforeAll(async () => {
      // Save cars into database
      await Promise.all(
        staticCars.map(async (car) => {
          await carService.save(car);
        }),
      );

      // Listing out stations from STS
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      await cacheService.set(
        config.redisConfig.carAvailabilityDetails.cacheKey,
        availabilityDetailMap,
        {
          ttl: 30, // seconds
        },
      );

      response = await requestFor({
        atStation: stationDtos[0].id,
        charging: false,
        stsPluggedAt: ocpiEvseLastUpdated.toISO(),
      });
    });

    afterAll(async () => {
      await clear();
    });

    it('Should return status OK', () => {
      expect(response.status).toEqual(HttpStatus.OK);

      expect(response.body.result).toEqual(null);
    });
  });

  describe('💦 Cannot get list of real-time car', () => {
    let response: supertest.Response;
    const stationDtos: StationDto[] = [new StationDtoBuilder().build()];

    const ocpiEvseLastUpdated = DateTime.now().minus({ minutes: 5 });

    const vulogZone = new VulogZoneBuilder()
      .withSticky(true)
      .withType(VulogZoneType.Allowed)
      .withZoneId(stationDtos[0].id)
      .build();
    const availableVvgCarStatusDtos: VvgVehicleStatusDto[] = [
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withCablePlugged(true)
        .withIsCharging(false)
        .withZone(vulogZone)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withCablePlugged(true)
        .withIsCharging(false)
        .withZone(vulogZone)
        .build(),
    ];
    const staticCars: Car[] = [
      new CarBuilder()
        .withVulogId(new VulogCarId(availableVvgCarStatusDtos[0].id))
        .build(),
      new CarBuilder()
        .withVulogId(new VulogCarId(availableVvgCarStatusDtos[1].id))
        .build(),
    ];
    const customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withId(config.vulogConfig.serviceId.value)
      .withVehicles([staticCars[0].vulogId.value, staticCars[1].vulogId.value])
      .withZones([vulogZone.zoneId])
      .build();
    const availabilityDetailMap: AvailableCarsAtStationMap = {
      [stationDtos[0].id]: {
        station: stationDtos[0].toStation(),
        availableCars: availableVvgCarStatusDtos.map((vlgRltCarDto, index) =>
          vlgRltCarDto.toTelemetryCarInfo(
            staticCars[index],
            customerFleetServiceDto.toFleetService().getZoneIdsAsMap(),
            null, // Intentionally marked as NULL as not used in this testing
          ),
        ),
      },
    };
    const vulogCarEventDtoByVehicleId = {
      [availableVvgCarStatusDtos[0].id]: [
        new VulogCarEventDtoBuilder()
          .withType(VulogCarEventType.VEHICLE_CHARGING_PLUGGED)
          .withDate(ocpiEvseLastUpdated.minus({ minutes: 6 }).toISO())
          .withVehicleId(availableVvgCarStatusDtos[0].id)
          .build(),
      ],
      [availableVvgCarStatusDtos[1].id]: [
        new VulogCarEventDtoBuilder()
          .withType(VulogCarEventType.VEHICLE_CHARGING_PLUGGED)
          .withDate(ocpiEvseLastUpdated.minus({ minutes: 5 }).toISO())
          .withVehicleId(availableVvgCarStatusDtos[1].id)
          .build(),
      ],
    };
    const vulogCarStaticInfoDtos: VulogCarStaticInfoDto[] = [
      new VulogCarStaticInfoDtoBuilder()
        .withId(availableVvgCarStatusDtos[0].id)
        .withModel(staticCars[0].model)
        .withServiceId(customerFleetServiceDto.id)
        .build(),
      new VulogCarStaticInfoDtoBuilder()
        .withId(availableVvgCarStatusDtos[1].id)
        .withModel(staticCars[1].model)
        .withServiceId(customerFleetServiceDto.id)
        .build(),
    ];

    beforeAll(async () => {
      // Save cars into database
      await Promise.all(
        staticCars.map(async (car) => {
          await carService.save(car);
        }),
      );

      // Listing out stations from STS
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      await cacheService.set(
        config.redisConfig.carAvailabilityDetails.cacheKey,
        availabilityDetailMap,
        {
          ttl: 30, // seconds
        },
      );

      // Get specific real-time car
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(availableVvgCarStatusDtos[0].id),
          )}`,
        )
        .replyOnce(500); // Error

      // Listing out fleet services from Vulog
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // List out events for a car
      mockHttpAdapterService
        .onGet(
          new RegExp(
            `${VulogPaths.backOfficeCarEvents(
              config.vulogConfig.fleetId,
              new VulogCarId(availableVvgCarStatusDtos[0].id),
            )}`,
            'gi',
          ),
        )
        .reply(
          200,
          vulogCarEventDtoByVehicleId[availableVvgCarStatusDtos[0].id],
        );

      mockHttpAdapterService
        .onGet(
          new RegExp(
            `${VulogPaths.backOfficeCarEvents(
              config.vulogConfig.fleetId,
              new VulogCarId(availableVvgCarStatusDtos[1].id),
            )}`,
            'gi',
          ),
        )
        .reply(
          200,
          vulogCarEventDtoByVehicleId[availableVvgCarStatusDtos[1].id],
        );

      // List all static car information
      mockHttpAdapterService
        .onGet(
          new RegExp(
            `${VulogPaths.backOfficeListingAllStaticVehicles(
              config.vulogConfig.fleetId,
            )}`,
            'gi',
          ),
        )
        .reply(200, vulogCarStaticInfoDtos, { last: true });

      response = await requestFor({
        atStation: stationDtos[0].id,
        charging: false,
        stsPluggedAt: ocpiEvseLastUpdated.toISO(),
      });
    });

    afterAll(async () => {
      await clear();
    });

    it('Should return status OK', () => {
      expect(response.status).toEqual(HttpStatus.INTERNAL_SERVER_ERROR);

      expect(response.body.message).toEqual(
        'An error has occurred. - Cause: Cannot get list of real-time car',
      );
    });
  });

  describe('🔥 There is no available match with plugged and not charging cars at station', () => {
    let response: supertest.Response;
    const stationDtos: StationDto[] = [new StationDtoBuilder().build()];

    const ocpiEvseLastUpdated = DateTime.now().minus({ minutes: 5 });

    const vulogZone = new VulogZoneBuilder()
      .withSticky(true)
      .withType(VulogZoneType.Allowed)
      .withZoneId(stationDtos[0].id)
      .build();
    const availableVvgCarStatusDtos: VvgVehicleStatusDto[] = [
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withCablePlugged(false) // invalid
        .withIsCharging(false)
        .withZone(vulogZone)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withCablePlugged(false) // invalid
        .withIsCharging(false)
        .withZone(vulogZone)
        .build(),
    ];
    const staticCars: Car[] = [
      new CarBuilder()
        .withVulogId(new VulogCarId(availableVvgCarStatusDtos[0].id))
        .build(),
      new CarBuilder()
        .withVulogId(new VulogCarId(availableVvgCarStatusDtos[1].id))
        .build(),
    ];
    const customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withId(config.vulogConfig.serviceId.value)
      .withVehicles([staticCars[0].vulogId.value, staticCars[1].vulogId.value])
      .withZones([vulogZone.zoneId])
      .build();
    const availabilityDetailMap: AvailableCarsAtStationMap = {
      [stationDtos[0].id]: {
        station: stationDtos[0].toStation(),
        availableCars: availableVvgCarStatusDtos.map((vlgRltCarDto, index) =>
          vlgRltCarDto.toTelemetryCarInfo(
            staticCars[index],
            customerFleetServiceDto.toFleetService().getZoneIdsAsMap(),
            null, // Intentionally marked as NULL as not used in this testing
          ),
        ),
      },
    };
    const vulogCarEventDtoByVehicleId = {
      [availableVvgCarStatusDtos[0].id]: [
        new VulogCarEventDtoBuilder()
          .withType(VulogCarEventType.VEHICLE_CHARGING_PLUGGED)
          .withDate(ocpiEvseLastUpdated.minus({ minutes: 6 }).toISO())
          .withVehicleId(availableVvgCarStatusDtos[0].id)
          .build(),
      ],
      [availableVvgCarStatusDtos[1].id]: [
        new VulogCarEventDtoBuilder()
          .withType(VulogCarEventType.VEHICLE_CHARGING_PLUGGED)
          .withDate(ocpiEvseLastUpdated.minus({ minutes: 5 }).toISO())
          .withVehicleId(availableVvgCarStatusDtos[1].id)
          .build(),
      ],
    };
    const vulogCarStaticInfoDtos: VulogCarStaticInfoDto[] = [
      new VulogCarStaticInfoDtoBuilder()
        .withId(availableVvgCarStatusDtos[0].id)
        .withModel(staticCars[0].model)
        .withServiceId(customerFleetServiceDto.id)
        .build(),
      new VulogCarStaticInfoDtoBuilder()
        .withId(availableVvgCarStatusDtos[1].id)
        .withModel(staticCars[1].model)
        .withServiceId(customerFleetServiceDto.id)
        .build(),
    ];

    beforeAll(async () => {
      // Save cars into database
      await Promise.all(
        staticCars.map(async (car) => {
          await carService.save(car);
        }),
      );

      // Listing out stations from STS
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      await cacheService.set(
        config.redisConfig.carAvailabilityDetails.cacheKey,
        availabilityDetailMap,
        {
          ttl: 30, // seconds
        },
      );

      // Get specific real-time car
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(availableVvgCarStatusDtos[0].id),
          )}`,
        )
        .replyOnce(200, availableVvgCarStatusDtos[0]);

      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(availableVvgCarStatusDtos[1].id),
          )}`,
        )
        .replyOnce(200, availableVvgCarStatusDtos[1]);

      // Listing out fleet services from Vulog
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // List out events for a car
      mockHttpAdapterService
        .onGet(
          new RegExp(
            `${VulogPaths.backOfficeCarEvents(
              config.vulogConfig.fleetId,
              new VulogCarId(availableVvgCarStatusDtos[0].id),
            )}`,
            'gi',
          ),
        )
        .reply(
          200,
          vulogCarEventDtoByVehicleId[availableVvgCarStatusDtos[0].id],
        );

      mockHttpAdapterService
        .onGet(
          new RegExp(
            `${VulogPaths.backOfficeCarEvents(
              config.vulogConfig.fleetId,
              new VulogCarId(availableVvgCarStatusDtos[1].id),
            )}`,
            'gi',
          ),
        )
        .reply(
          200,
          vulogCarEventDtoByVehicleId[availableVvgCarStatusDtos[1].id],
        );

      // List all static car information
      mockHttpAdapterService
        .onGet(
          new RegExp(
            `${VulogPaths.backOfficeListingAllStaticVehicles(
              config.vulogConfig.fleetId,
            )}`,
            'gi',
          ),
        )
        .reply(200, vulogCarStaticInfoDtos, { last: true });

      response = await requestFor({
        atStation: stationDtos[0].id,
        charging: false,
        stsPluggedAt: ocpiEvseLastUpdated.toISO(),
      });
    });

    afterAll(async () => {
      await clear();
    });

    it('Should return status OK', () => {
      expect(response.status).toEqual(HttpStatus.OK);

      expect(response.body.result).toEqual(null);
    });
  });

  describe('💦 Cannot get VEHICLE_CHARGING_PLUGGED event for car', () => {
    let response: supertest.Response;
    const stationDtos: StationDto[] = [new StationDtoBuilder().build()];

    const ocpiEvseLastUpdated = DateTime.now().minus({ minutes: 5 });

    const vulogZone = new VulogZoneBuilder()
      .withSticky(true)
      .withType(VulogZoneType.Allowed)
      .withZoneId(stationDtos[0].id)
      .build();
    const availableVvgCarStatusDtos: VvgVehicleStatusDto[] = [
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withCablePlugged(true)
        .withIsCharging(false)
        .withZone(vulogZone)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withCablePlugged(true)
        .withIsCharging(false)
        .withZone(vulogZone)
        .build(),
    ];
    const staticCars: Car[] = [
      new CarBuilder()
        .withVulogId(new VulogCarId(availableVvgCarStatusDtos[0].id))
        .build(),
      new CarBuilder()
        .withVulogId(new VulogCarId(availableVvgCarStatusDtos[1].id))
        .build(),
    ];
    const customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withId(config.vulogConfig.serviceId.value)
      .withVehicles([staticCars[0].vulogId.value, staticCars[1].vulogId.value])
      .withZones([vulogZone.zoneId])
      .build();
    const availabilityDetailMap: AvailableCarsAtStationMap = {
      [stationDtos[0].id]: {
        station: stationDtos[0].toStation(),
        availableCars: availableVvgCarStatusDtos.map((vlgRltCarDto, index) =>
          vlgRltCarDto.toTelemetryCarInfo(
            staticCars[index],
            customerFleetServiceDto.toFleetService().getZoneIdsAsMap(),
            null, // Intentionally marked as NULL as not used in this testing
          ),
        ),
      },
    };

    const vulogCarStaticInfoDtos: VulogCarStaticInfoDto[] = [
      new VulogCarStaticInfoDtoBuilder()
        .withId(availableVvgCarStatusDtos[0].id)
        .withModel(staticCars[0].model)
        .withServiceId(customerFleetServiceDto.id)
        .build(),
      new VulogCarStaticInfoDtoBuilder()
        .withId(availableVvgCarStatusDtos[1].id)
        .withModel(staticCars[1].model)
        .withServiceId(customerFleetServiceDto.id)
        .build(),
    ];

    beforeAll(async () => {
      // Save cars into database
      await Promise.all(
        staticCars.map(async (car) => {
          await carService.save(car);
        }),
      );

      // Listing out stations from STS
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      await cacheService.set(
        config.redisConfig.carAvailabilityDetails.cacheKey,
        availabilityDetailMap,
        {
          ttl: 30, // seconds
        },
      );

      // Get specific real-time car
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(availableVvgCarStatusDtos[0].id),
          )}`,
        )
        .replyOnce(200, availableVvgCarStatusDtos[0]);

      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(availableVvgCarStatusDtos[1].id),
          )}`,
        )
        .replyOnce(200, availableVvgCarStatusDtos[1]);

      // Listing out fleet services from Vulog
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // List out events for a car
      mockHttpAdapterService
        .onGet(
          new RegExp(
            `${VulogPaths.backOfficeCarEvents(
              config.vulogConfig.fleetId,
              new VulogCarId(availableVvgCarStatusDtos[0].id),
            )}`,
            'gi',
          ),
        )
        .reply(500); // Invalid

      // List all static car information
      mockHttpAdapterService
        .onGet(
          new RegExp(
            `${VulogPaths.backOfficeListingAllStaticVehicles(
              config.vulogConfig.fleetId,
            )}`,
            'gi',
          ),
        )
        .reply(200, vulogCarStaticInfoDtos, { last: true });

      response = await requestFor({
        atStation: stationDtos[0].id,
        charging: false,
        stsPluggedAt: ocpiEvseLastUpdated.toISO(),
      });
    });

    afterAll(async () => {
      await clear();
    });

    it('Should return status OK', () => {
      expect(response.status).toEqual(HttpStatus.INTERNAL_SERVER_ERROR);

      expect(response.body.message).toEqual(
        'An error has occurred. - Cause: Cannot get VEHICLE_CHARGING_PLUGGED event for car(s)',
      );
    });
  });

  describe('🔥 There is no suitable VEHICLE_CHARGING_PLUGGED event for car(s)', () => {
    let response: supertest.Response;
    const stationDtos: StationDto[] = [new StationDtoBuilder().build()];
    const ocpiEvseLastUpdated = DateTime.now().minus({ minutes: 5 });

    const vulogZone = new VulogZoneBuilder()
      .withSticky(true)
      .withType(VulogZoneType.Allowed)
      .withZoneId(stationDtos[0].id)
      .build();
    const availableVvgCarStatusDtos: VvgVehicleStatusDto[] = [
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withCablePlugged(true)
        .withIsCharging(false)
        .withZone(vulogZone)
        .build(),
      new VvgVehicleStatusDtoBuilder()
        .withAutonomy(100)
        .withCablePlugged(true)
        .withIsCharging(false)
        .withZone(vulogZone)
        .build(),
    ];
    const staticCars: Car[] = [
      new CarBuilder()
        .withVulogId(new VulogCarId(availableVvgCarStatusDtos[0].id))
        .build(),
      new CarBuilder()
        .withVulogId(new VulogCarId(availableVvgCarStatusDtos[1].id))
        .build(),
    ];
    const customerFleetServiceDto = new VulogFleetServiceDtoBuilder()
      .withId(config.vulogConfig.serviceId.value)
      .withVehicles([staticCars[0].vulogId.value, staticCars[1].vulogId.value])
      .withZones([vulogZone.zoneId])
      .build();
    const availabilityDetailMap: AvailableCarsAtStationMap = {
      [stationDtos[0].id]: {
        station: stationDtos[0].toStation(),
        availableCars: availableVvgCarStatusDtos.map((vlgRltCarDto, index) =>
          vlgRltCarDto.toTelemetryCarInfo(
            staticCars[index],
            customerFleetServiceDto.toFleetService().getZoneIdsAsMap(),
            null, // Intentionally marked as NULL as not used in this testing
          ),
        ),
      },
    };
    const vulogCarEventDtoByVehicleId = {
      [availableVvgCarStatusDtos[0].id]: [],
      [availableVvgCarStatusDtos[1].id]: [],
    };
    const vulogCarStaticInfoDtos: VulogCarStaticInfoDto[] = [
      new VulogCarStaticInfoDtoBuilder()
        .withId(availableVvgCarStatusDtos[0].id)
        .withModel(staticCars[0].model)
        .withServiceId(customerFleetServiceDto.id)
        .build(),
      new VulogCarStaticInfoDtoBuilder()
        .withId(availableVvgCarStatusDtos[1].id)
        .withModel(staticCars[1].model)
        .withServiceId(customerFleetServiceDto.id)
        .build(),
    ];

    beforeAll(async () => {
      // Save cars into database
      await Promise.all(
        staticCars.map(async (car) => {
          await carService.save(car);
        }),
      );

      // Listing out stations from STS
      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: stationDtos,
        });

      mockHttpAdapterService
        .onGet(
          `${StationPaths.stations()}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .replyOnce(200, {
          result: [], // Breaking loop
        });

      await cacheService.set(
        config.redisConfig.carAvailabilityDetails.cacheKey,
        availabilityDetailMap,
        {
          ttl: 30, // seconds
        },
      );

      // Get specific real-time car
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(availableVvgCarStatusDtos[0].id),
          )}`,
        )
        .replyOnce(200, availableVvgCarStatusDtos[0]);

      mockHttpAdapterService
        .onGet(
          `${VulogPaths.vvgVehicle(
            config.vulogConfig.fleetId,
            new VulogCarId(availableVvgCarStatusDtos[1].id),
          )}`,
        )
        .replyOnce(200, availableVvgCarStatusDtos[1]);

      // Listing out fleet services from Vulog
      mockHttpAdapterService
        .onGet(
          `${VulogPaths.backOfficeFleetServices(config.vulogConfig.fleetId)}`,
        )
        .reply(200, [customerFleetServiceDto]);

      // List out events for a car
      mockHttpAdapterService
        .onGet(
          new RegExp(
            `${VulogPaths.backOfficeCarEvents(
              config.vulogConfig.fleetId,
              new VulogCarId(availableVvgCarStatusDtos[0].id),
            )}`,
            'gi',
          ),
        )
        .reply(
          200,
          vulogCarEventDtoByVehicleId[availableVvgCarStatusDtos[0].id],
        );

      mockHttpAdapterService
        .onGet(
          new RegExp(
            `${VulogPaths.backOfficeCarEvents(
              config.vulogConfig.fleetId,
              new VulogCarId(availableVvgCarStatusDtos[1].id),
            )}`,
            'gi',
          ),
        )
        .reply(
          200,
          vulogCarEventDtoByVehicleId[availableVvgCarStatusDtos[1].id],
        );

      // List all static car information
      mockHttpAdapterService
        .onGet(
          new RegExp(
            `${VulogPaths.backOfficeListingAllStaticVehicles(
              config.vulogConfig.fleetId,
            )}`,
            'gi',
          ),
        )
        .reply(200, vulogCarStaticInfoDtos, { last: true });

      response = await requestFor({
        atStation: stationDtos[0].id,
        charging: false,
        stsPluggedAt: ocpiEvseLastUpdated.toISO(),
      });
    });

    afterAll(async () => {
      await clear();
    });

    it('Should return status OK', () => {
      expect(response.status).toEqual(HttpStatus.OK);

      expect(response.body.result).toEqual(null);
    });
  });
});
