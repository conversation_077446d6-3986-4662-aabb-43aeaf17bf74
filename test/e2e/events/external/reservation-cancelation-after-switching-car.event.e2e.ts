/* eslint-disable @typescript-eslint/naming-convention */

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { faker } from '@faker-js/faker';
import { HttpStatus, INestApplication } from '@nestjs/common';

import {
  ClientProxy,
  ClientsModule,
  TcpOptions,
  Transport,
} from '@nestjs/microservices';
import MockAdapter from 'axios-mock-adapter';
import { DateTime } from 'luxon';
import { AppModule } from 'src/app.module';

import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import { MessageEventPattern } from 'src/common/constants/event';
import { CarId, VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { config } from 'src/config';
import { CacheService } from 'src/logic/cache/cache.service';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { ReservationCacheService } from 'src/logic/reservation/reservation.cache.service';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogUserService } from 'src/logic/vulog/vulog-user.service';
import { VulogJourneyDto } from 'src/model/dtos/vulog-journey/vulog-journey.dto';
import { CarRepository } from 'src/model/repositories/car.repository';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { ReservationRepository } from 'src/model/repositories/reservation.repository';
import { VulogUserRepository } from 'src/model/repositories/vulog-user.repository';
import { Reservation } from 'src/model/reservation.domain';
import { AppBuilder } from 'test/helpers/app.builder';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { VulogAimaErrorResponseDtoBuilder } from 'test/helpers/builders/dtos/axios/vulog.aima.error.response.dto.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import {
  VulogBookingBuilder,
  VulogJourneyDtoBuilder,
} from 'test/helpers/builders/dtos/vulog/vulog-journey.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { createTestVulogUserEntity } from 'test/helpers/builders/entities/test-vulog-user-entity.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';
import { InternalApiService } from '~shared/http/internal-api.service';

const AWS_SQS_CLIENT_PROXY_TOKEN = 'AWS_SQS';

const userId = aUUID();

const email = faker.internet.email();

jest.setTimeout(100000);

describe(`[Event - External] Consume Self Event - Event: "${MessageEventPattern.ReservationCancelationAfterSwitchingCar}"`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let awsSqsClientProxy: ClientProxy;

  let mockHttpAdapterService: MockAdapter;
  let mockUssApiService: MockAdapter;
  let vulogUserService: VulogUserService;
  let reservationRepository: ReservationRepository;
  let cacheService: CacheService;
  let reservationCacheService: ReservationCacheService;
  let vulogUserRepository: VulogUserRepository;
  let carRepository: CarRepository;

  const mockUserServicesAndProfiles = createTestVulogUserServicesDto();

  const mockAuthResponse = createTestVulogAuthResponse();

  const mockUssUser = new InternalUserDetailResponseDtoV1Builder()
    .withId(userId)
    .withEmail(email)
    .build();

  const mockVulogUserEntity = createTestVulogUserEntity(
    userId,
    mockUserServicesAndProfiles.profiles[0].profileId,
  );

  beforeAll(async () => {
    app = await appBuilder
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(aUUID()), true)
      .buildAndStartModuleMetadata({
        imports: [
          AppModule,

          ClientsModule.register([
            { name: AWS_SQS_CLIENT_PROXY_TOKEN, transport: Transport.TCP },
          ]),
        ],
      });

    cacheService = app.get(CacheService);
    reservationCacheService = app.get(ReservationCacheService);

    vulogUserService = app.get(VulogUserService);
    reservationRepository = app.get(ReservationRepository);

    vulogUserRepository = app.get(VulogUserRepository);
    carRepository = app.get(CarRepository);

    const httpAdapterService = app.get(HttpAdapterService);
    mockHttpAdapterService = new MockAdapter(
      httpAdapterService.httpService.axiosRef,
    );

    const internalApiService1: InternalApiService = app.get(
      config.httpServiceConfig.userSubscriptionName,
    );
    mockUssApiService = new MockAdapter(
      internalApiService1.httpService.axiosRef,
    );

    const userDto = createTestVulogUserDto();

    userDto.id = userId;
    userDto.email = email;

    mockHttpAdapterService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, userDto);

    mockHttpAdapterService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(userDto.id),
        )}`,
      )
      .reply(200, mockUserServicesAndProfiles);

    mockHttpAdapterService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(mockUserServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, mockUserServicesAndProfiles);

    mockHttpAdapterService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, mockAuthResponse);

    mockUssApiService
      .onGet(
        `${
          config.httpServiceConfig.userSubscriptionUrl
        }${ApiRouteUtils.buildApiRouteSegment(
          RootRouteSegment.Internal,
          ApiVersion.V1,
          ['users', userId],
        )}`,
      )
      .reply(200, BaseResponseDto.success(mockUssUser));

    await vulogUserService.save(mockVulogUserEntity.toVulogUser());

    mockVulogCredential(app);

    awsSqsClientProxy = app.get(AWS_SQS_CLIENT_PROXY_TOKEN);

    // Starting a default consumer
    // <!!!> Note: We don't use our SqsEventTransportStrategy here because we
    // does not consume message from AWS SQS when doing testing
    app.connectMicroservice<TcpOptions>({
      transport: Transport.TCP,
    });

    await app.startAllMicroservices();

    // Starting a a default publisher
    await awsSqsClientProxy.connect();
  });

  async function cleanResources() {
    await reservationRepository.clearAll();
    await vulogUserRepository.clearAll();
    await carRepository.clearAll();
    await cacheService.clearAll();
  }

  afterAll(async () => {
    await cleanResources();

    await app.close();

    jest.clearAllMocks();
  });

  describe(`🔥 Success: Consume "${MessageEventPattern.ReservationCancelationAfterSwitchingCar}" successfully`, () => {
    const reservationId = aUUID();

    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeId(reservationId)
      .makeBsgUserId(userId)
      .makeReservedAt(DateTime.now().minus({ minutes: 15 }).toJSDate())
      .makeExpiresAt(DateTime.now().plus({ minutes: 15 }).toJSDate())
      .build();
    const reservation: Reservation = Reservation.from(reservationEntity);
    const mock_vulogJourneyDtoBuilder: VulogJourneyDto =
      new VulogJourneyDtoBuilder()
        .withBooking(
          new VulogBookingBuilder(
            reservationEntity.reservedAt.toISOString(),
            reservationEntity.expiresAt.toISOString(),
          ).build(),
        )
        .build();

    beforeEach(async () => {
      await reservationRepository.saveOne(reservation);

      await reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.update(
        reservation.bsgUserId,
        mock_vulogJourneyDtoBuilder.toReservationInfo(),
      );

      // VulogCarService - cancel current reservation
      mockHttpAdapterService
        .onDelete(
          `${VulogPaths.vehicleBookingBackOffice(
            config.vulogConfig.fleetId,
            reservation.carId,
          )}`,
        )
        .replyOnce(200);

      await new Promise((resolve, reject) => {
        awsSqsClientProxy
          // To be precise, it should be `emit` when we implement event-based
          // pattern. We use `.send()` here for making sure our message
          // handler confirms finishing with response just like in request-response pattern
          .send(
            MessageEventPattern.ReservationCancelationAfterSwitchingCar,
            // No need to serialize payload as string because of internal
            // testing having no custom strategy
            {
              event:
                MessageEventPattern.ReservationCancelationAfterSwitchingCar,
              payload: null,
              reservationId: reservationId,
              bsgUserId: userId,
            },
          )
          .subscribe(
            (response) => {
              resolve(response);
            },
            (err) => {
              reject(err);
            },
          );
      });
    });

    it('Checking saved data in the database', async () => {
      const carSwitchPerformed: boolean =
        await reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.retrieve(
          reservation.bsgUserId,
        );

      expect(!!carSwitchPerformed).toEqual(false);
    });

    afterEach(async () => {
      await cleanResources();
    });
  });

  describe(`🔥 Success: Consume "${MessageEventPattern.ReservationCancelationAfterSwitchingCar}" successfully, but car reservation is already expired in Vulog AiMA `, () => {
    const reservationId = aUUID();

    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeId(reservationId)
      .makeBsgUserId(userId)
      .makeReservedAt(DateTime.now().minus({ minutes: 15 }).toJSDate())
      .makeExpiresAt(DateTime.now().plus({ minutes: 15 }).toJSDate())
      .build();
    const reservation: Reservation = Reservation.from(reservationEntity);
    const mock_vulogJourneyDtoBuilder: VulogJourneyDto =
      new VulogJourneyDtoBuilder()
        .withBooking(
          new VulogBookingBuilder(
            reservationEntity.reservedAt.toISOString(),
            reservationEntity.expiresAt.toISOString(),
          ).build(),
        )
        .build();
    const carEntity: CarEntity = CarEntity.from(
      new CarBuilder(
        new CarId(aUUID()),
        reservation.carId,
        reservation.carModel,
        reservation.plateNumber,
      ).build(),
    );

    beforeEach(async () => {
      await carRepository.save([carEntity]);
      await reservationRepository.saveOne(reservation);

      await reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.update(
        reservation.bsgUserId,
        mock_vulogJourneyDtoBuilder.toReservationInfo(),
      );

      // VulogCarService - cancel current reservation
      mockHttpAdapterService
        .onDelete(
          `${VulogPaths.vehicleBookingBackOffice(
            config.vulogConfig.fleetId,
            reservation.carId,
          )}`,
        )
        .replyOnce(
          HttpStatus.NOT_FOUND,
          new VulogAimaErrorResponseDtoBuilder()
            .withCode('vehicleNotFound') // `vehicleNotFound` means that the reservation is expired or cancelled
            .build(),
        );

      await new Promise((resolve, reject) => {
        awsSqsClientProxy
          // To be precise, it should be `emit` when we implement event-based
          // pattern. We use `.send()` here for making sure our message
          // handler confirms finishing with response just like in request-response pattern
          .send(
            MessageEventPattern.ReservationCancelationAfterSwitchingCar,
            // No need to serialize payload as string because of internal
            // testing having no custom strategy
            {
              event:
                MessageEventPattern.ReservationCancelationAfterSwitchingCar,
              payload: null,
              reservationId: reservationId,
              bsgUserId: userId,
            },
          )
          .subscribe(
            (response) => {
              resolve(response);
            },
            (err) => {
              reject(err);
            },
          );
      });
    });

    it('Checking saved data in the database', async () => {
      const carSwitchPerformed: boolean =
        await reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.retrieve(
          reservation.bsgUserId,
        );

      expect(!!carSwitchPerformed).toEqual(false);
    });

    afterEach(async () => {
      await cleanResources();
    });
  });

  describe(`💦 Failed: Consume "${MessageEventPattern.ReservationCancelationAfterSwitchingCar}" successfully, but car reservation is not expired in Vulog AiMA, unexpected error occurred`, () => {
    const reservationId = aUUID();

    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeId(reservationId)
      .makeBsgUserId(userId)
      .makeReservedAt(DateTime.now().minus({ minutes: 15 }).toJSDate())
      .makeExpiresAt(DateTime.now().plus({ minutes: 15 }).toJSDate())
      .build();
    const reservation: Reservation = Reservation.from(reservationEntity);
    const mock_vulogJourneyDtoBuilder: VulogJourneyDto =
      new VulogJourneyDtoBuilder()
        .withBooking(
          new VulogBookingBuilder(
            reservationEntity.reservedAt.toISOString(),
            reservationEntity.expiresAt.toISOString(),
          ).build(),
        )
        .build();

    const carEntity: CarEntity = CarEntity.from(
      new CarBuilder(
        new CarId(aUUID()),
        reservation.carId,
        reservation.carModel,
        reservation.plateNumber,
      ).build(),
    );

    let error;

    beforeEach(async () => {
      await carRepository.save([carEntity]);
      await reservationRepository.saveOne(reservation);

      await reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.update(
        reservation.bsgUserId,
        mock_vulogJourneyDtoBuilder.toReservationInfo(),
      );

      // VulogCarService - cancel current reservation
      mockHttpAdapterService
        .onDelete(
          `${VulogPaths.vehicleBookingBackOffice(
            config.vulogConfig.fleetId,
            reservation.carId,
          )}`,
        )
        .replyOnce(
          HttpStatus.NOT_FOUND,
          new VulogAimaErrorResponseDtoBuilder()
            .withCode('NOT_vehicleNotFound') // `vehicleNotFound` means that the reservation is expired or cancelled
            .build(),
        );

      await new Promise((resolve, reject) => {
        awsSqsClientProxy
          // To be precise, it should be `emit` when we implement event-based
          // pattern. We use `.send()` here for making sure our message
          // handler confirms finishing with response just like in request-response pattern
          .send(
            MessageEventPattern.ReservationCancelationAfterSwitchingCar,
            // No need to serialize payload as string because of internal
            // testing having no custom strategy
            {
              event:
                MessageEventPattern.ReservationCancelationAfterSwitchingCar,
              payload: null,
              reservationId: reservationId,
              bsgUserId: userId,
            },
          )
          .subscribe(
            (response) => {
              resolve(response);
            },
            (err) => {
              reject(err);
            },
          );
      }).catch((err) => {
        error = err;
      });
    });

    it('Checking saved data in the database', async () => {
      const carSwitchPerformed: boolean =
        await reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.retrieve(
          reservation.bsgUserId,
        );

      expect(!!carSwitchPerformed).toEqual(false);

      expect(error).toEqual({
        message:
          'Unexpected error when trying cancel reservation. Need to consume the message again to handle.',
        status: 'error',
      });
    });

    afterEach(async () => {
      await cleanResources();
    });
  });

  describe(`💦 Failed: Malformed payload`, () => {
    const reservationId = aUUID();

    let error;

    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeId(reservationId)
      .makeBsgUserId(userId)
      .makeReservedAt(DateTime.now().minus({ minutes: 15 }).toJSDate())
      .makeExpiresAt(DateTime.now().plus({ minutes: 15 }).toJSDate())
      .build();
    const reservation: Reservation = Reservation.from(reservationEntity);
    const mock_vulogJourneyDtoBuilder: VulogJourneyDto =
      new VulogJourneyDtoBuilder()
        .withBooking(
          new VulogBookingBuilder(
            reservationEntity.reservedAt.toISOString(),
            reservationEntity.expiresAt.toISOString(),
          ).build(),
        )
        .build();

    beforeEach(async () => {
      await reservationRepository.saveOne(reservation);

      await reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.update(
        reservation.bsgUserId,
        mock_vulogJourneyDtoBuilder.toReservationInfo(),
      );

      // VulogCarService - cancel current reservation
      mockHttpAdapterService
        .onDelete(
          `${VulogPaths.vehicleBookingBackOffice(
            config.vulogConfig.fleetId,
            reservation.carId,
          )}`,
        )
        .replyOnce(200);

      await new Promise((resolve, reject) => {
        awsSqsClientProxy
          // To be precise, it should be `emit` when we implement event-based
          // pattern. We use `.send()` here for making sure our message
          // handler confirms finishing with response just like in request-response pattern
          .send(
            MessageEventPattern.ReservationCancelationAfterSwitchingCar,
            // No need to serialize payload as string because of internal
            // testing having no custom strategy
            {
              event:
                MessageEventPattern.ReservationCancelationAfterSwitchingCar,
              payload: null,
              malformed_reservationId: reservationId,
              malformed_bsgUserId: userId,
            },
          )
          .subscribe(
            (response) => {
              resolve(response);
            },
            (err) => {
              reject(err);
            },
          );
      }).catch((err) => {
        error = err;
      });
    });

    it('verify expectation', async () => {
      expect(error).toEqual({
        message: 'Payload is not aligned with current code implementation',
        status: 'error',
      });
    });

    afterEach(async () => {
      await cleanResources();
    });
  });

  describe(`💦 Failed: Reservation is not found`, () => {
    const reservationId = aUUID();

    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeId(reservationId)
      .makeBsgUserId(userId)
      .makeReservedAt(DateTime.now().minus({ minutes: 15 }).toJSDate())
      .makeExpiresAt(DateTime.now().plus({ minutes: 15 }).toJSDate())
      .build();
    const reservation: Reservation = Reservation.from(reservationEntity);
    const mock_vulogJourneyDtoBuilder: VulogJourneyDto =
      new VulogJourneyDtoBuilder()
        .withBooking(
          new VulogBookingBuilder(
            reservationEntity.reservedAt.toISOString(),
            reservationEntity.expiresAt.toISOString(),
          ).build(),
        )
        .build();

    let error;

    beforeEach(async () => {
      await reservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.update(
        reservation.bsgUserId,
        mock_vulogJourneyDtoBuilder.toReservationInfo(),
      );

      // VulogCarService - cancel current reservation
      mockHttpAdapterService
        .onDelete(
          `${VulogPaths.vehicleBookingBackOffice(
            config.vulogConfig.fleetId,
            reservation.carId,
          )}`,
        )
        .replyOnce(200);

      await new Promise((resolve, reject) => {
        awsSqsClientProxy
          // To be precise, it should be `emit` when we implement event-based
          // pattern. We use `.send()` here for making sure our message
          // handler confirms finishing with response just like in request-response pattern
          .send(
            MessageEventPattern.ReservationCancelationAfterSwitchingCar,
            // No need to serialize payload as string because of internal
            // testing having no custom strategy
            {
              event:
                MessageEventPattern.ReservationCancelationAfterSwitchingCar,
              payload: null,
              reservationId: reservationId,
              bsgUserId: userId,
            },
          )
          .subscribe(
            (response) => {
              resolve(response);
            },
            (err) => {
              reject(err);
            },
          );
      }).catch((err) => {
        error = err;
      });
    });

    it('verify expectation', async () => {
      expect(error).toEqual({
        message: `Reservation (ID: ${reservationId}) is not found`,
        status: 'error',
      });
    });

    afterEach(async () => {
      await cleanResources();
    });
  });
});
