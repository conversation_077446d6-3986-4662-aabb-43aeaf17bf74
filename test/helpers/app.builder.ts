import {
  JwtGuard,
  PasswordNotChangedGuard,
  RolesGuard,
} from '@bluesg-2/bo-auth-guard';
import { AppLogger, CorrelationIdClsMiddleware } from '@bluesg-2/monitoring';
import { INestApplication, ModuleMetadata } from '@nestjs/common';
import { Test, TestingModuleBuilder } from '@nestjs/testing';
import { ClsMiddleware } from 'nestjs-cls';
import { ApiSetup } from 'src/api/api-setup';
import { AppModule } from 'src/app.module';
import { mockAdminJwtGuard } from './admin-jwt-guard.mocker';
import { mockPasswordNotChangedGuard } from './password-not-changed-guard.mocker';
import { aUUID } from './random-data.helper';
import { mockRolesGuard } from './roles-guard.mocker';

const apiSetup = new ApiSetup();

export class AppBuilder {
  private providersToOverride: Provider[] = [
    {
      name: JwtGuard,
      value: mockAdminJwtGuard(aUUID()),
      isGuard: true,
    },
    {
      name: PasswordNotChangedGuard,
      value: mockPasswordNotChangedGuard(),
      isGuard: true,
    },
    {
      name: RolesGuard,
      value: mockRolesGuard(),
      isGuard: true,
    },
  ];

  overrideProvider(
    provider: unknown,
    newValue: unknown,
    isGuard?: boolean,
  ): AppBuilder {
    this.providersToOverride.push({
      name: provider,
      value: newValue,
      isGuard,
    });

    return this;
  }

  async buildAndStart(appModule = AppModule): Promise<INestApplication> {
    const moduleRef = Test.createTestingModule({
      imports: [appModule],
    });

    this.providersToOverride.forEach((provider) => {
      if (provider.isGuard) {
        moduleRef.overrideGuard(provider.name).useValue(provider.value);

        return;
      }

      moduleRef.overrideProvider(provider.name).useValue(provider.value);
    });

    return (await this.buildApp(moduleRef)).init();
  }

  async buildAndStartModuleMetadata(
    moduleMetadata: ModuleMetadata,
  ): Promise<INestApplication> {
    const moduleRef = Test.createTestingModule(moduleMetadata);

    this.providersToOverride.forEach((provider) => {
      if (provider.isGuard) {
        moduleRef.overrideGuard(provider.name).useValue(provider.value);

        return;
      }

      moduleRef.overrideProvider(provider.name).useValue(provider.value);
    });

    return (await this.buildApp(moduleRef)).init();
  }

  private async buildApp(
    module: TestingModuleBuilder,
  ): Promise<INestApplication> {
    const app = (await module.compile()).createNestApplication({
      bufferLogs: true,
    });

    app.use(
      new ClsMiddleware({
        setup: (cls, req, res): void => {
          CorrelationIdClsMiddleware.setup(cls, req, res);
        },
      }).use,
    );

    app.useLogger(app.get(AppLogger));

    apiSetup.setup(app);

    return app;
  }
}

export class Provider {
  name: unknown;

  value: unknown;

  isGuard?: boolean = false;
}
