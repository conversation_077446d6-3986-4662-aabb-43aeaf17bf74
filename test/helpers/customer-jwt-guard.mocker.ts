import { ExecutionContext } from '@nestjs/common';

export const CUSTOMER_JWT_MOCK_TOKEN = 'CUSTOMER_JWT_MOCK_TOKEN';

export function mockCustomerJwtGuard(userId: string) {
  return {
    canActivate: (context: ExecutionContext) => {
      let token: unknown = {
        id: userId,
      };

      const req = context.switchToHttp().getRequest();
      const tokenFromHeader =
        req.headers[CUSTOMER_JWT_MOCK_TOKEN.toLowerCase()];

      if (tokenFromHeader) {
        token = JSON.parse(tokenFromHeader);
      }

      req.user = token;

      return true;
    },
  };
}
