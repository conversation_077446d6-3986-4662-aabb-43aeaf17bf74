import { faker } from '@faker-js/faker';
import { Md5 } from 'ts-md5';

export function aNumber(max = 10000): number {
  return faker.number.int(max);
}

export function aDecimal(max = 1000): number {
  return faker.number.float({
    min: 1,
    precision: 0.01,
    max,
  });
}

export function aString(length = 30): string {
  return faker.string.sample(length);
}

export function aNoun(): string {
  return faker.word.noun();
}

export function aFirstName(): string {
  return faker.person.firstName();
}

export function aLastName(): string {
  return faker.person.lastName();
}

export function aGender(): string {
  return faker.person.gender();
}

export function aUserAgent(): string {
  return faker.internet.userAgent();
}

export function aBoolean(): boolean {
  return faker.datatype.boolean();
}

export function aDate(): Date {
  return faker.date.past({ years: 5 });
}

export function aBirthDate(): Date {
  return faker.date.birthdate();
}

export function aDateISOString(): string {
  return faker.date.past({ years: 5 }).toISOString();
}

export function aUUID(): string {
  return faker.string.uuid();
}

export function aUrl(): string {
  return faker.internet.url();
}

export function aVersion(): string {
  return faker.system.semver();
}

export function aMD5(): string {
  return Md5.hashStr(aUUID());
}

export function anEmail(): string {
  return faker.internet.email();
}

export function aFutureDate(): Date {
  return faker.date.future(1);
}

export function aUUIDArray(length = 5): string[] {
  return [...Array(length)].map(() => aUUID());
}

export function anUsername(): string {
  return faker.internet.userName();
}

export function aLongitude(): number {
  return faker.location.longitude();
}

export function aLatitude(): number {
  return faker.location.latitude();
}

export function aLocationAddress(): string {
  return faker.location.streetAddress();
}

/**
 * TS do not accept Enum as a type, so "best" way to get a random Enum is using this function as:
 * const myEnum = Enum[anEnumValue(Enum)]
 * @param enumSource
 */
export function anEnumValue<
  E extends string,
  T extends string | number,
>(enumSource: {
  [key in E]: T;
}): T {
  return faker.helpers.enumValue(enumSource);
}

export function anAlphaNumeric(length = 10): string {
  return faker.string.alphanumeric(length);
}

/* Vulog Data */
export function aPlate(): string {
  return faker.vehicle.vrm();
}

export function aVin(): string {
  return faker.vehicle.vin();
}

export function aCap(): number {
  return faker.number.float(10);
}
