import { INestApplication } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import {
  VulogAuthResponse,
  VulogAuthService,
} from 'src/logic/vulog/vulog-auth.service';

export function mockVulogCredential(app: INestApplication | TestingModule) {
  const vulogAuthService: VulogAuthService = app.get(VulogAuthService);

  jest.spyOn(vulogAuthService, 'setupAuthConfig').mockResolvedValue({
    headers: {
      Authorization: `Bearer hehe`,
      'x-api-key': 'hehe',
    },
  });
}

export function mockGetCachedAccessToken(
  app: INestApplication | TestingModule,
  mockValue: string | null,
) {
  const vulogAuthService: VulogAuthService = app.get(VulogAuthService);

  jest
    .spyOn(vulogAuthService, 'getCachedAccessToken')
    .mockResolvedValue(mockValue);
}

export function mockAuthWithRefreshToken(
  app: INestApplication | TestingModule,
  mockValue: VulogAuthResponse,
) {
  const vulogAuthService: VulogAuthService = app.get(VulogAuthService);

  jest
    .spyOn(vulogAuthService, 'authWithRefreshToken')
    .mockResolvedValue(mockValue);
}

export function mockAuthWithPassword(
  app: INestApplication | TestingModule,
  mockValue: VulogAuthResponse,
) {
  const vulogAuthService: VulogAuthService = app.get(VulogAuthService);

  jest.spyOn(vulogAuthService, 'authWithPassword').mockResolvedValue(mockValue);
}
