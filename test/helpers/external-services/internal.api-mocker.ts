import { INestApplication, Type } from '@nestjs/common';
import { InternalApiService } from '../../../src/shared/http/internal-api.service';
import { ApiMocker } from './api.mocker';

export abstract class InternalApiMocker extends ApiMocker {
  protected constructor(
    app: INestApplication,
    // eslint-disable-next-line @typescript-eslint/ban-types
    serviceName: string | symbol | Function | Type<any>,
  ) {
    super(app, (app.get(serviceName) as InternalApiService).httpService);
  }
}
