import { INestApplication } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BsgUserId, VulogProfileId, VulogUserId } from 'src/common/tiny-types';
import { config } from 'src/config';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogUserServicesDto } from 'src/model/dtos/vulog-user/vulog-user-services.dto';
import { VulogUserDto } from 'src/model/dtos/vulog-user/vulog-user.dto';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { Repository } from 'typeorm';
import { HttpAdapterService } from '../../../src/logic/http-adapter.service';
import { createTestVulogAuthResponse } from '../builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from '../builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from '../builders/dtos/test-vulog-user-services-dto.builder';
import { VulogUserBuilder } from '../builders/vulog-user.builder';
import { mockVulogCredential } from '../util/mock-vulog-credential.util';
import { ApiMocker } from './api.mocker';

export class VulogApiMocker extends ApiMocker {
  constructor(app: INestApplication) {
    super(app, app.get(HttpAdapterService).httpService);
  }

  async makeVulogCredentialsReady(
    userDto: VulogUserDto = createTestVulogUserDto(),
    userServicesAndProfiles: VulogUserServicesDto = createTestVulogUserServicesDto(),
  ) {
    this.mockInstance
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, userDto);

    userServicesAndProfiles.userId = userDto.id;

    this.mockInstance
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(userDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    this.mockInstance
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    this.mockInstance
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    this.mockInstance
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, createTestVulogAuthResponse());

    const userRepo = this.app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );

    const testVulogUser = VulogUserBuilder.allSync()
      .withBsgUserId(new BsgUserId(userDto.id))
      .withId(new VulogUserId(userDto.id))
      .build();

    await userRepo.insert(VulogUserEntity.from(testVulogUser));

    mockVulogCredential(this.app);
  }
}
