import { HttpService } from '@nestjs/axios';
import { INestApplication } from '@nestjs/common';
import MockAdapter from 'axios-mock-adapter';

export class ApiMocker {
  protected mockInstance: MockAdapter;

  constructor(
    protected app: INestApplication,
    protected httpService: HttpService,
  ) {}

  mock(): MockAdapter {
    if (!this.mockInstance) {
      this.mockInstance = new MockAdapter(this.httpService.axiosRef, {
        onNoMatch: 'throwException',
      });
    }

    return this.mockInstance;
  }
}
