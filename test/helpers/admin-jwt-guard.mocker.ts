import { TokenPayloadV1 } from '@bluesg-2/bo-auth-guard';
import { ExecutionContext } from '@nestjs/common';

export const ADMIN_JWT_MOCK_TOKEN = 'ADMIN_JWT_MOCK_TOKEN';

export function mockAdminJwtGuard(userId: string) {
  return {
    canActivate: (context: ExecutionContext) => {
      let token: Pick<TokenPayloadV1, 'userId'> = {
        userId: userId,
      };

      const req = context.switchToHttp().getRequest();
      const tokenFromHeader = req.headers[ADMIN_JWT_MOCK_TOKEN];

      if (tokenFromHeader) {
        token = JSON.parse(tokenFromHeader);
      }

      req.user = token;

      return true;
    },
  };
}
