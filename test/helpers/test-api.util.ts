import { INestApplication } from '@nestjs/common';
import { CacheService } from 'src/logic/cache/cache.service';
import { AppBuilder, Provider } from './app.builder';
import EmptyFunction = jest.EmptyFunction;

export class ApiTestContext {
  constructor(public app: INestApplication = null) {}

  getHttpServer() {
    return this.app.getHttpServer();
  }

  async clearCache() {
    const cache = this.app.get<CacheService>(CacheService);
    await cache.clearAll();
  }
}

export function testApi(
  fc: (ApiTestContext) => void,
  overrideProviders?: Provider[],
): EmptyFunction {
  return () => {
    const appBuilder = new AppBuilder();
    const context = new ApiTestContext();

    beforeAll(async () => {
      overrideProviders?.forEach((provider) => {
        appBuilder.overrideProvider(
          provider.name,
          provider.value,
          provider.isGuard,
        );
      });

      context.app = await appBuilder.buildAndStart();
    });

    afterAll(async () => {
      await context.app.close();
      await context.clearCache();
    });

    fc(context);
  };
}
