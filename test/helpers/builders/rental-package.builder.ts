import { RentalPackageId } from 'src/common/tiny-types';
import { RentalPackage } from 'src/model/rental-package';
import { aBoolean, aNumber, aString, aUUID } from '../random-data.helper';

export class RentalPackageBuilder extends RentalPackage {
  packageId: RentalPackageId = new RentalPackageId(aUUID());

  hrid?: string = aString();

  name: string = aString();

  price: number = aNumber();

  duration: number = aNumber();

  minAllowedBatteryPercentage: number = aNumber();

  isDynamic?: boolean = aBoolean();

  withPrice(value: number): RentalPackageBuilder {
    this.price = value;

    return this;
  }

  withDuration(value: number): RentalPackageBuilder {
    this.duration = value;

    return this;
  }

  withMinAllowedBatteryPercentage(value: number): RentalPackageBuilder {
    this.minAllowedBatteryPercentage = value;

    return this;
  }

  withIsDynamic(value: boolean): RentalPackageBuilder {
    this.isDynamic = value;

    return this;
  }

  build(): RentalPackage {
    return new RentalPackage({
      packageId: this.packageId,
      duration: this.duration,
      name: this.name,
      price: this.price,
      hrid: this.hrid,
      minAllowedBatteryPercentage: this.minAllowedBatteryPercentage || 0,
      isDynamic: !!this.isDynamic,
    });
  }
}
