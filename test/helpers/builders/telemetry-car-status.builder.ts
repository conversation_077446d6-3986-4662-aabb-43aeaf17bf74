import { CarPlateNumber, VulogZoneId } from 'src/common/tiny-types';
import { VvgCarSession } from 'src/model/dtos/vvg-car/vvg-car-fields/vvg-car-session';
import {
  VulogVehicleCardsAndKey,
  VulogVehicleSimCard,
} from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { TelemetryCarStatus } from 'src/model/telemetry-car-status';
import { aBoolean, aNumber } from '../random-data.helper';
import { RealtimeCarBuilder } from './realtime-car.builder';
import {
  VulogVehicleCardsAndKeyBuilder,
  VulogVehicleSimCardBuilder,
  VvgCarSessionBuilder,
} from './telemetry-car-info.builder';

export class TelemetryCarStatusBuilder extends RealtimeCarBuilder {
  mileage: number = aNumber();

  cardsAndKey: VulogVehicleCardsAndKey =
    new VulogVehicleCardsAndKeyBuilder().build();

  simCard: VulogVehicleSimCard = new VulogVehicleSimCardBuilder().build();

  engineOn: boolean = aBoolean();

  areDoorsAndWindowsClosed?: boolean = aBoolean();

  parkingBrakeOn?: boolean = aBoolean();

  locked?: boolean = aBoolean();

  immobilizerOn?: boolean = aBoolean();

  activeSession?: VvgCarSession = new VvgCarSessionBuilder().build();

  withAreDoorsAndWindowsClosed(value: boolean) {
    this.areDoorsAndWindowsClosed = value;

    return this;
  }

  withImmobilizerOn(value: boolean) {
    this.immobilizerOn = value;

    return this;
  }

  withLocked(value: boolean) {
    this.locked = value;

    return this;
  }

  withEngineOn(value: boolean) {
    this.engineOn = value;

    return this;
  }

  withIsPluggedIn(value: boolean) {
    this.isPluggedIn = value;

    return this;
  }

  withIsCharging(value: boolean) {
    this.isCharging = value;

    return this;
  }

  withParkingBrakeOn(value: boolean) {
    this.parkingBrakeOn = value;

    return this;
  }

  build(): TelemetryCarStatus {
    return new TelemetryCarStatus({
      id: this.id,
      vuboxId: this.vuboxId,
      status: this.status,
      statusLabel: this.statusLabel,
      model: this.model,
      plate: new CarPlateNumber(this.plate),
      vin: this.vin,
      sourceId: this.sourceId,
      energyLevel: this.energyLevel,
      isCharging: this.isCharging,
      isPluggedIn: this.isPluggedIn,
      location: this.location,
      areDoorsAndWindowsClosed: this.areDoorsAndWindowsClosed,
      alerts: this.alerts,
      zoneIds: this.zoneIds.map((zoneId) => new VulogZoneId(zoneId)),
      zoneMaps: this.zoneMaps,
      isDoorLocked: this.isDoorLocked,
      isDoorClosed: this.isDoorClosed,
      isEngineOn: this.isEngineOn,
      serviceType: this.serviceType,
      serviceId: this.serviceId,
      allowedStickyZone: this.allowedStickyZone,
      immobilizerOn: this.immobilizerOn,
      currentStation: this.currentStation,
      lastActiveDate: this.lastActiveDate,
      mileage: this.mileage,
      cardsAndKey: this.cardsAndKey,
      simCard: this.simCard,
      engineOn: this.engineOn,
      locked: this.locked,
      parkingBrakeOn: this.parkingBrakeOn,
      activeSession: this.activeSession,
    });
  }
}
