import { plainToInstance } from 'class-transformer';
import {
  CarModel,
  CarPlateNumber,
  VulogCarId,
  VulogZoneId,
} from 'src/common/tiny-types';
import { config } from 'src/config';
import { VulogAdditionalTripInfo } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-additional-trip-info';
import { VulogEventInfo } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-event-info';
import { VulogTicketInfo } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-ticket-info';
import { VulogTripDto } from 'src/model/dtos/vulog-trip/vulog-trip.dto';
import {
  aDate,
  aMD5,
  anAlphaNumeric,
  aNumber,
  anUsername,
  aString,
  aUUID,
} from 'test/helpers/random-data.helper';
import { RealtimeCar } from '../../../../src/model/realtime.car';
import { VulogTripAdditionalInfoBuilder } from '../dtos/vulog/vulog-trip-additional-info.builder';

export class VulogTripBuilder {
  constructor(
    protected id: string = aUUID(),
    protected tripId: string = aUUID(),
    protected fleetId: string = config.vulogConfig.fleetId.value,
    protected userId: string = aUUID(),
    protected profileId: string = aUUID(),
    protected profileType: string = aString(),
    protected vehicleId: string = aUUID(),
    protected length: number = aNumber(),
    protected duration: number = aNumber(),
    protected pauseDuration: number = aNumber(),
    protected tripDuration: number = aNumber(),
    protected bookingDuration: number = aNumber(),
    protected drivingDuration: number = aNumber(),
    protected date: string = aDate().toISOString(),
    protected endDate: string = aDate().toISOString(),
    protected pricingId: string = aUUID(),
    protected productIds: string[] = [aUUID()],
    protected serviceId: string = aUUID(),
    protected serviceType: string = aString(),
    protected additionalInf: VulogAdditionalTripInfo = new VulogTripAdditionalInfoBuilder().build(),
    protected theorStartDate?: string,
    protected theorEndDate?: string,
    protected ticketInfo?: VulogTicketInfo,
    protected tripEvents?: VulogEventInfo[],
    protected car?: RealtimeCar,
  ) {}

  public makeProfileId(value: string = aUUID()) {
    this.profileId = value;

    return this;
  }

  public makeFleetId(value: string = aUUID()) {
    this.fleetId = value;

    return this;
  }

  public makeServiceType(value: string = aUUID()) {
    this.serviceType = value;

    return this;
  }

  public makeServiceId(value: string = aUUID()) {
    this.serviceId = value;

    return this;
  }

  public makeVehicleId(value: string = aUUID()) {
    this.vehicleId = value;

    return this;
  }

  public makeUserId(value: string = aUUID()) {
    this.userId = value;

    return this;
  }

  public makeTripId(tripId: string = aUUID()) {
    this.tripId = tripId;

    return this;
  }

  public makeTheorEndDate(date: string = aDate().toISOString()) {
    this.theorEndDate = date;

    return this;
  }

  public makePricingId(pricingId: string = aUUID()) {
    this.pricingId = pricingId;

    return this;
  }

  public makeVehicleInfo(carId: string = aUUID()) {
    this.car = new RealtimeCar({
      id: new VulogCarId(carId),
      model: new CarModel(anUsername()),
      plate: new CarPlateNumber(aString()),
      sourceId: new VulogCarId(carId),
      energyLevel: aNumber(),
      isCharging: false,
      isPluggedIn: false,
      location: {
        latitude: aNumber(),
        longitude: aNumber(),
      },
      areDoorsAndWindowsClosed: true,
      alerts: {
        hasFlatTire: false,
        hasTireUnderPressure: false,
        hasCrash: false,
      },
      zoneIds: [new VulogZoneId(aMD5())],
      vin: anAlphaNumeric(),
      disabled: null, // placeholder
      outOfServiceReason: null, // placeholder
      allowedStickyZone: null, // placeholder
    });

    return this;
  }

  public makeAdditionalInfo(info: VulogAdditionalTripInfo) {
    this.additionalInf = info;

    return this;
  }

  public build(): VulogTripDto {
    return plainToInstance(VulogTripDto, {
      id: this.id,
      tripId: this.tripId,
      fleetId: this.fleetId,
      userId: this.userId,
      profileId: this.profileId,
      profileType: this.profileType,
      vehicleId: this.vehicleId,
      length: this.length,
      duration: this.duration,
      pauseDuration: this.pauseDuration,
      tripDuration: this.tripDuration,
      bookingDuration: this.bookingDuration,
      drivingDuration: this.drivingDuration,
      date: this.date,
      endDate: this.endDate,
      pricingId: this.pricingId,
      productIds: this.productIds,
      serviceId: this.serviceId,
      serviceType: this.serviceType,
      car: this.car,
      additionalInfo: this.additionalInf,
    });
  }
}
