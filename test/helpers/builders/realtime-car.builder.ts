import { DateTime } from 'luxon';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import {
  CarModel,
  CarPlateNumber,
  VulogCarId,
  VulogServiceId,
  VulogZoneId,
} from 'src/common/tiny-types';
import { VulogRealTimeVehicleStatus } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogFleetServiceType } from 'src/model/dtos/vulog-fleet-service/vulog-fleet-service.dto';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { Station } from 'src/model/station';
import { Alerts, Location, RealtimeCar } from '../../../src/model/realtime.car';
import {
  aBoolean,
  aLatitude,
  aLongitude,
  aMD5,
  aNumber,
  aString,
  aUUID,
} from '../random-data.helper';
import { VulogZoneBuilder } from './dtos/vulog/vulog-car.real-time.dto.builder';
import { StationBuilder } from './station.builder';

export class RealtimeCarBuilder {
  id: VulogCarId = new VulogCarId(aUUID());

  vuboxId?: string = aString();

  status?: VulogRealTimeVehicleStatus = VulogRealTimeVehicleStatus.Available;

  statusLabel?: string;

  model: CarModel = new CarModel(CarModelEnum.BlueCar);

  plate: string = aString();

  vin: string = aString();

  sourceId: VulogCarId = this.id;

  energyLevel: number = aNumber(100);

  isCharging: boolean = aBoolean();

  isPluggedIn: boolean = aBoolean();

  location: Location = new Location({
    longitude: aLongitude(),
    latitude: aLatitude(),
  });

  areDoorsAndWindowsClosed?: boolean = aBoolean();

  alerts?: Alerts = new Alerts({
    hasCrash: aBoolean(),
    hasFlatTire: aBoolean(),
    hasTireUnderPressure: aBoolean(),
  });

  zoneIds?: string[] = [aMD5()];

  zoneMaps?: Map<string, VulogZoneId> = new Map();

  isDoorLocked?: boolean = aBoolean();

  isDoorClosed?: boolean = aBoolean();

  isEngineOn?: boolean = aBoolean();

  serviceType?: VulogFleetServiceType = VulogFleetServiceType.FreeFloating;

  // The service ID. Returned only when the vehicle has the In Use status.
  serviceId?: VulogServiceId = new VulogServiceId(aUUID());

  disabled: boolean = aBoolean();

  outOfServiceReason: string = aString();

  allowedStickyZone: VulogZone = new VulogZoneBuilder().build();

  immobilizerOn?: boolean = aBoolean();

  currentStation?: Station = new StationBuilder().build();

  lastActiveDate?: DateTime = DateTime.now();

  withEnergyLevel(energyLevel: number): RealtimeCarBuilder {
    this.energyLevel = energyLevel;
    return this;
  }

  build(): RealtimeCar {
    return new RealtimeCar({
      id: this.id,
      vuboxId: this.vuboxId,
      status: this.status,
      statusLabel: this.statusLabel,
      model: this.model,
      plate: new CarPlateNumber(this.plate),
      vin: this.vin,
      sourceId: this.sourceId,
      energyLevel: this.energyLevel,
      isCharging: this.isCharging,
      isPluggedIn: this.isPluggedIn,
      location: this.location,
      areDoorsAndWindowsClosed: this.areDoorsAndWindowsClosed,
      alerts: this.alerts,
      zoneIds: this.zoneIds.map((zoneId) => new VulogZoneId(zoneId)),
      zoneMaps: this.zoneMaps,
      isDoorLocked: this.isDoorLocked,
      isDoorClosed: this.isDoorClosed,
      isEngineOn: this.isEngineOn,
      serviceType: this.serviceType,
      serviceId: this.serviceId,
      disabled: this.disabled,
      outOfServiceReason: this.outOfServiceReason,
      allowedStickyZone: this.allowedStickyZone,
      immobilizerOn: this.immobilizerOn,
      currentStation: this.currentStation,
      lastActiveDate: this.lastActiveDate,
    });
  }
}
