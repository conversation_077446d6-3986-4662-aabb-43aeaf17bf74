import { StationId } from 'src/common/tiny-types';
import { Station } from 'src/model/station';
import { StationStatus } from '../../../src/external/station/dtos/station-status';
import {
  aLatitude,
  aLongitude,
  aMD5,
  aNumber,
  aString,
  aUUID,
} from '../random-data.helper';

export class StationBuilder {
  id: string = aUUID();

  sourceId = aString();

  zoneId: string = aMD5();

  displayName: string = aString(10);

  latitude: number = aLatitude();

  longitude: number = aLongitude();

  street: string = aString();

  postalCode: string = aNumber(5).toString();

  status: StationStatus = StationStatus.Open;

  withId(id: string): StationBuilder {
    this.id = id;
    return this;
  }

  withZoneId(zoneId: string): StationBuilder {
    this.zoneId = zoneId;
    return this;
  }

  withStatus(status: StationStatus): StationBuilder {
    this.status = status;
    return this;
  }

  build(): Station {
    return new Station({
      id: new StationId(this.id),
      sourceId: this.sourceId,
      zoneId: this.zoneId,
      displayName: this.displayName,
      latitude: this.latitude,
      longitude: this.longitude,
      street: this.street,
      postalCode: this.postalCode,
      status: this.status,
    });
  }
}
