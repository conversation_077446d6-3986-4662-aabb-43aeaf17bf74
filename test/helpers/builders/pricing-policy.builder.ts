import { PricingPolicyId } from 'src/common/tiny-types';
import { PricingPolicy } from 'src/model/pricing-policy';
import { aNumber, aUUID } from '../random-data.helper';

export class PricingPolicyBuilder {
  pricingPolicyId: string = aUUID();

  rentalRate: number = aNumber();

  withRentalRate(value: number): PricingPolicyBuilder {
    this.rentalRate = value;

    return this;
  }

  build(): PricingPolicy {
    const value = new PricingPolicy();

    value.pricingPolicyId = new PricingPolicyId(this.pricingPolicyId);
    value.rentalRate = this.rentalRate;

    return value;
  }
}
