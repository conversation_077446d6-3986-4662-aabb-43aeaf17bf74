import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { CarId, CarPlateNumber, VulogCarId } from 'src/common/tiny-types';
import { Car, CarRealtimeMetadata } from 'src/logic/car/domain/car';
import { anEnumValue, aString, aUUID } from '../random-data.helper';

export class CarBuilder {
  private realtimeMetadata?: CarRealtimeMetadata;

  constructor(
    private id = new CarId(aUUID()),
    private vulogId = new VulogCarId(aUUID()),
    private model = anEnumValue(CarModelEnum),
    private plate = new CarPlateNumber(aString(10)),
    private vin = aString(),
    private vuboxId = aUUID(),
  ) {}

  withVulogId(vulogId: VulogCarId): CarBuilder {
    this.vulogId = vulogId;
    return this;
  }

  withPlate(plate: CarPlateNumber): CarBuilder {
    this.plate = plate;
    return this;
  }

  withVin(value: string): CarBuilder {
    this.vin = value;
    return this;
  }

  withModel(model: CarModelEnum): CarBuilder {
    this.model = model;
    return this;
  }

  withRealtimeMetadata(value: CarRealtimeMetadata): CarBuilder {
    this.realtimeMetadata = value;

    return this;
  }

  build(): Car {
    return new Car({
      id: this.id.value,
      vulogId: this.vulogId.value,
      model: this.model,
      plate: this.plate.value,
      vin: this.vin,
      vuboxId: this.vuboxId,
      realtimeMetadata: this.realtimeMetadata,
    });
  }
}
