import { GeoLocation } from '@bluesg-2/queue-pop/dist/common/domains/value-objects/geo-location';
import { PricingPolicy } from 'src/model/pricing-policy';
import { RentalPackage } from 'src/model/rental-package';
import { CarModelEnum } from '../../../src/common/enum/car-model.enum';
import { ReservationStatus } from '../../../src/common/enum/reservation.enum';
import {
  BsgUserId,
  CarPlateNumber,
  ReservationId,
  StationId,
  VulogCarId,
  VulogJourneyOrTripId,
} from '../../../src/common/tiny-types';
import { VulogDate } from '../../../src/common/tiny-types/vulog-date.type';
import { Reservation } from '../../../src/model/reservation.domain';
import {
  aBoolean,
  aDate,
  aFutureDate,
  aLatitude,
  aLongitude,
  anEnumValue,
  aNumber,
  aString,
  aUUID,
} from '../random-data.helper';

export class ReservationBuilder {
  constructor(
    private id = new ReservationId(aUUID()),
    private bsgUserId = new BsgUserId(aUUID()),
    private carId = new VulogCarId(aUUID()),
    private carModel = anEnumValue(CarModelEnum),
    private startStationId = new StationId(aUUID()),
    private endStationId = new StationId(aUUID()),
    private vulogTripId = new VulogJourneyOrTripId(aUUID()),
    private status = anEnumValue(ReservationStatus),
    private plateNumber = new CarPlateNumber(aString(8)),
    private rentalPackageData: RentalPackage = null,
    private local = aBoolean(),
    private startedAt = VulogDate.fromJsDate(aDate()),
    private endedAt = VulogDate.fromJsDate(aDate()),
    private reservedAt = VulogDate.fromJsDate(aDate()),
    private expiresAt = VulogDate.fromJsDate(aFutureDate()),
    private canceledAt = VulogDate.fromJsDate(aDate()),
    private abuseReleasesAt = VulogDate.fromJsDate(aDate()),
    private location = new GeoLocation({
      longitude: `${aLongitude()}`,
      latitude: '' + aLatitude(),
    }),
    private rating = aString(),
    private carCleanliness = aString(),
    private amount = aNumber(),
    private duration = aNumber(),
    private pricingPolicy = null,
  ) {}

  withDuration(value: number): ReservationBuilder {
    this.duration = value;
    return this;
  }

  withRentalPackageData(value: RentalPackage) {
    this.rentalPackageData = value;
    return this;
  }

  withPricingPolicy(value: PricingPolicy): ReservationBuilder {
    this.pricingPolicy = value;
    return this;
  }

  build(): Reservation {
    return new Reservation(
      {
        bsgUserId: this.bsgUserId,
        amount: this.amount,
        carId: this.carId,
        abuseReleasesAt: this.abuseReleasesAt,
        rating: this.rating,
        carCleanliness: this.carCleanliness,
        pricingPolicy: this.pricingPolicy,
        duration: this.duration,
        location: this.location,
        reservedAt: this.reservedAt,
        expiresAt: this.expiresAt,
        startStationId: this.startStationId,
        status: this.status,
        plateNumber: this.plateNumber,
        local: this.local,
        carModel: this.carModel,
        canceledAt: this.canceledAt,
        endedAt: this.endedAt,
        endStationId: this.endStationId,
        vulogTripId: this.vulogTripId,
        startedAt: this.startedAt,
        rentalPackageData: this.rentalPackageData,
      },
      this.id,
    );
  }
}
