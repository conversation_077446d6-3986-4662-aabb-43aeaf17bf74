import { CarPlateNumber, VulogZoneId } from 'src/common/tiny-types';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { VvgCarSession } from 'src/model/dtos/vvg-car/vvg-car-fields/vvg-car-session';
import {
  VulogVehicleCardsAndKey,
  VulogVehicleSimCard,
} from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { TelemetryCarInfo } from 'src/model/telemetry-car-info';
import {
  aBoolean,
  aDateISOString,
  aNumber,
  aString,
} from '../random-data.helper';
import { VulogZoneBuilder } from './dtos/vulog/vulog-car.real-time.dto.builder';
import { RealtimeCarBuilder } from './realtime-car.builder';

export class VulogVehicleCardsAndKeyBuilder {
  key: boolean = aBoolean();

  card1: boolean = aBoolean();

  card2: boolean = aBoolean();

  build(): VulogVehicleCardsAndKey {
    const dto = new VulogVehicleCardsAndKey();

    dto.key = this.key;
    dto.card1 = this.card1;
    dto.card2 = this.card2;

    return dto;
  }
}

export class VulogVehicleSimCardBuilder {
  imsi: string = aString();

  iccid: string = aString();

  provider: string = aString();

  build(): VulogVehicleSimCard {
    const dto = new VulogVehicleSimCard();

    dto.imsi = this.imsi;
    dto.iccid = this.iccid;
    dto.provider = this.provider;

    return dto;
  }
}

export class VvgCarSessionBuilder {
  sessionId: string = aString();

  userId: string = aString();

  rfid: string = aString();

  createdDate: string = aDateISOString();

  expirationDate: string = aDateISOString();

  sessionLocale: string = aString();

  build(): VvgCarSession {
    const dto = new VvgCarSession(
      this.sessionId,
      this.userId,
      this.rfid,
      this.createdDate,
      this.expirationDate,
      this.sessionLocale,
    );

    return dto;
  }
}

export class TelemetryCarInfoBuilder extends RealtimeCarBuilder {
  mileage: number = aNumber();

  cardsAndKey: VulogVehicleCardsAndKey =
    new VulogVehicleCardsAndKeyBuilder().build();

  simCard: VulogVehicleSimCard = new VulogVehicleSimCardBuilder().build();

  engineOn: boolean = aBoolean();

  locked?: boolean = aBoolean();

  parkingBrakeOn?: boolean = aBoolean();

  immobilizerOn?: boolean = aBoolean();

  currentZones?: VulogZone[] = [new VulogZoneBuilder().build()];

  activeSession?: VvgCarSession = new VvgCarSessionBuilder().build();

  withAreDoorsAndWindowsClosed(value: boolean) {
    this.areDoorsAndWindowsClosed = value;

    return this;
  }

  withImmobilizerOn(value: boolean) {
    this.immobilizerOn = value;

    return this;
  }

  withLocked(value: boolean) {
    this.locked = value;

    return this;
  }

  withEngineOn(value: boolean) {
    this.engineOn = value;

    return this;
  }

  withIsPluggedIn(value: boolean) {
    this.isPluggedIn = value;

    return this;
  }

  withIsCharging(value: boolean) {
    this.isCharging = value;

    return this;
  }

  withParkingBrakeOn(value: boolean) {
    this.parkingBrakeOn = value;

    return this;
  }

  build(): TelemetryCarInfo {
    return new TelemetryCarInfo({
      id: this.id,
      vuboxId: this.vuboxId,
      status: this.status,
      statusLabel: this.statusLabel,
      model: this.model,
      plate: new CarPlateNumber(this.plate),
      vin: this.vin,
      sourceId: this.sourceId,
      energyLevel: this.energyLevel,
      isCharging: this.isCharging,
      isPluggedIn: this.isPluggedIn,
      location: this.location,
      areDoorsAndWindowsClosed: this.areDoorsAndWindowsClosed,
      alerts: this.alerts,
      zoneIds: this.zoneIds.map((zoneId) => new VulogZoneId(zoneId)),
      zoneMaps: this.zoneMaps,
      isDoorLocked: this.isDoorLocked,
      isDoorClosed: this.isDoorClosed,
      isEngineOn: this.isEngineOn,
      serviceType: this.serviceType,
      serviceId: this.serviceId,
      allowedStickyZone: this.allowedStickyZone,
      immobilizerOn: this.immobilizerOn,
      currentStation: this.currentStation,
      lastActiveDate: this.lastActiveDate,
      mileage: this.mileage,
      cardsAndKey: this.cardsAndKey,
      simCard: this.simCard,
      engineOn: this.engineOn,
      locked: this.locked,
      parkingBrakeOn: this.parkingBrakeOn,
      currentZones: this.currentZones,
      activeSession: this.activeSession,
    });
  }
}
