import {
  PreReservation,
  PreReservationStatus,
  QueuePopCarModel,
} from '@bluesg-2/queue-pop';
import { GeoLocation } from '@bluesg-2/queue-pop/dist/common/domains/value-objects/geo-location';
import { DateTime } from 'luxon';
import { aBoolean, anEnumValue, aUUID } from 'test/helpers/random-data.helper';

export class PreReservationBuilder extends PreReservation {
  id?: string = aUUID();
  stationIds: string[] = [aUUID(), aUUID(), aUUID()];
  userId: string = aUUID();
  status: PreReservationStatus =
    PreReservationStatus[anEnumValue(PreReservationStatus)];
  reservedAt: DateTime = DateTime.now();
  fulfilledAt?: DateTime = DateTime.now();
  canceledAt?: DateTime = DateTime.now();
  expiresAt: DateTime = DateTime.now();
  sentLocation?: GeoLocation = null;
  cancelReservation: boolean = aBoolean();
  carCategories: QueuePopCarModel[] = [
    QueuePopCarModel.BlueCar,
    QueuePopCarModel.OpelCorsaE,
  ];

  validate;
  toEntity;
  putStationsFirstInQueue;

  build(): PreReservation {
    return {
      id: this.id,
      stationIds: this.stationIds,
      userId: this.userId,
      status: this.status,
      reservedAt: this.reservedAt,
      fulfilledAt: this.fulfilledAt,
      canceledAt: this.canceledAt,
      expiresAt: this.expiresAt,
      sentLocation: this.sentLocation,
      validate: null,
      toEntity: null,
      putStationsFirstInQueue: null,
      carCategories: this.carCategories,
      cancelReservation: this.cancelReservation,
    };
  }
}
