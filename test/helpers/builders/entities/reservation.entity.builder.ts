import { DateTime } from 'luxon';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import {
  aBoolean,
  aDate,
  aMD5,
  anEnumValue,
  aNumber,
  aPlate,
  aString,
  aUUID,
} from 'test/helpers/random-data.helper';

export class ReservationEntityBuilder {
  id = aUUID();
  bsgUserId = aUUID();
  vulogUser = null;
  carId = aUUID();
  car = null;
  carModel = CarModelEnum.BlueCar;
  startStationId = aUUID();
  endStationId = aUUID();
  vulogEndGpsCoordinates = null;
  mobileEndGpsCoordinates = null;
  plateNumber = aPlate();
  vulogTripId = aMD5();
  status = anEnumValue(ReservationStatus);
  rentalPackageData = null;
  local = aBoolean();
  allocateAgain = aBoolean();
  startedAt = aDate();
  endedAt = DateTime.fromJSDate(this.startedAt).plus({ hours: 1 }).toJSDate();
  reservedAt = DateTime.fromJSDate(this.startedAt)
    .minus({ minutes: 25 })
    .toJSDate();
  expiresAt = DateTime.fromJSDate(this.startedAt)
    .plus({ minutes: 5 })
    .toJSDate();
  canceledAt = null;
  abuseReleasesAt = null;
  rating = aString();
  carCleanliness = aString();
  amount = aNumber();
  duration = aNumber();
  pricingPolicy = null;
  isEndingRentalDataAdded = null;
  userFullName: string = null;
  userEmail: string = null;
  createdAt = aDate();
  modifiedAt = aDate();
  deletedAt = null;
  deletedBy = null;
  createdBy = null;
  modifiedBy = null;

  public makeId(id: string) {
    this.id = id;
    return this;
  }

  public makeCreatedAt(date: Date) {
    this.createdAt = date;
    return this;
  }

  public makeModifiedAt(date: Date) {
    this.modifiedAt = date;
    return this;
  }

  public makeReservationId(id: string) {
    this.id = id;
    return this;
  }

  public makeCarId(carId: string) {
    this.carId = carId;
    return this;
  }

  public makeCarModel(carModel: CarModelEnum) {
    this.carModel = carModel;
    return this;
  }

  public makeVulogTripId(vulogTripId: string) {
    this.vulogTripId = vulogTripId;
    return this;
  }

  public makeStartStationId(startStationId: string) {
    this.startStationId = startStationId;
    return this;
  }

  public makeEndStationId(endStationId: string) {
    this.endStationId = endStationId;
    return this;
  }

  public makeStatus(status: ReservationStatus) {
    this.status = status;

    return this;
  }

  public makeBsgUserId(bsgUserId: string) {
    this.bsgUserId = bsgUserId;

    return this;
  }

  public makeReservedAt(reservedAt: Date) {
    this.reservedAt = reservedAt;

    return this;
  }

  public makeExpiresAt(expiresAt: Date) {
    this.expiresAt = expiresAt;

    return this;
  }

  public makeCanceledAt(canceledAt: Date) {
    this.canceledAt = canceledAt;

    return this;
  }

  public makeRentalPackageData(rentalPackageData: unknown) {
    this.rentalPackageData = rentalPackageData
      ? JSON.parse(JSON.stringify(rentalPackageData))
      : undefined;

    return this;
  }

  public makePlateNumber(val: string) {
    this.plateNumber = val;
    return this;
  }

  public makeStartedAt(val: Date) {
    this.startedAt = val;
    return this;
  }

  public makeEndedAt(val: Date) {
    this.endedAt = val;
    return this;
  }

  public makeAbuseReleasesAt(val: Date) {
    this.abuseReleasesAt = val;
    return this;
  }

  public makeRating(val: string) {
    this.rating = val;
    return this;
  }

  public makeCarCleanliness(val: string) {
    this.carCleanliness = val;
    return this;
  }

  public makeLocal(local: boolean) {
    this.local = local;
    return this;
  }

  public makeRentalPackageDate(value: unknown) {
    this.rentalPackageData = value;
    return this;
  }

  public makePricingPolicy(value: unknown) {
    this.pricingPolicy = value;
    return this;
  }

  public makeAllocateAgain(value: boolean) {
    this.allocateAgain = value;
    return this;
  }

  public makeCreatedBy(value: string) {
    this.createdBy = value;
    return this;
  }

  public makeModifiedBy(value: string) {
    this.modifiedBy = value;
    return this;
  }

  public makeAmount(value: number) {
    this.amount = value;
    return this;
  }

  public makeDuration(value: number) {
    this.duration = value;

    return this;
  }

  public makeUserFullName(value: string) {
    this.userFullName = value;

    return this;
  }

  public makeUserEmail(value: string) {
    this.userEmail = value;

    return this;
  }

  public build(): ReservationEntity {
    return new ReservationEntity({
      id: this.id,
      bsgUserId: this.bsgUserId,
      carId: this.carId,
      plateNumber: this?.plateNumber,
      createdAt: this?.createdAt ? new Date(this.createdAt) : undefined,
      modifiedAt: this?.modifiedAt ? new Date(this.modifiedAt) : undefined,
      deletedAt: this.deletedAt,
      deletedBy: this.deletedBy,
      carModel: this.carModel,
      startStationId: this.startStationId,
      endStationId: this.endStationId,
      mobileEndGpsCoordinates: this.mobileEndGpsCoordinates
        ? this.mobileEndGpsCoordinates.toPointGeometry()
        : null,
      vulogEndGpsCoordinates: this.vulogEndGpsCoordinates
        ? this.vulogEndGpsCoordinates.toPointGeometry()
        : null,
      rentalPackageData: this.rentalPackageData,
      status: this.status,
      vulogTripId: this.vulogTripId,
      createdBy: this.createdBy,
      modifiedBy: this.modifiedBy,
      local: this.local,
      allocateAgain: this.allocateAgain,
      startedAt: this?.startedAt ? new Date(this.startedAt) : null,
      endedAt: this?.endedAt ? new Date(this.endedAt) : null,
      reservedAt: this?.reservedAt ? new Date(this.reservedAt) : null,
      expiresAt: this?.expiresAt ? new Date(this.expiresAt) : null,
      canceledAt: this?.canceledAt ? new Date(this.canceledAt) : null,
      abuseReleasesAt: this?.abuseReleasesAt
        ? new Date(this.abuseReleasesAt)
        : null,
      carCleanliness: this.carCleanliness,
      rating: this.rating,
      amount: this?.amount,
      duration: this.duration,
      pricingPolicy: this?.pricingPolicy,
      isEndingRentalDataAdded: this?.isEndingRentalDataAdded,
      userFullName: this.userFullName,
      userEmail: this.userEmail,
    });
  }
}
