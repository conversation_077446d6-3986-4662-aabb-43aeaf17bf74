import { PreReservationStatus } from '@bluesg-2/queue-pop';
import { PreReservationEntity } from '@bluesg-2/queue-pop/dist/entities/pre-reservation.entity';
import { faker } from '@faker-js/faker';

export const createTestPreReservationEntity = (
  status?: PreReservationStatus,
) => {
  const entity = new PreReservationEntity();

  entity.id = faker.string.uuid();
  entity.userId = faker.string.uuid();
  entity.stationId = faker.string.uuid();
  entity.status = status
    ? status
    : faker.helpers.enumValue(PreReservationStatus);
  entity.createdAt = faker.date.recent();
  entity.modifiedAt = faker.date.recent();

  return entity;
};
