import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { BsgUserId } from '../../../../src/common/tiny-types';
import { VulogUserBuilder } from '../vulog-user.builder';

export const createTestVulogUserEntity = (
  bsgUserId?: string,
  profileId?: string,
) => {
  const builder = new VulogUserBuilder();

  if (bsgUserId) builder.withBsgUserId(new BsgUserId(bsgUserId));

  if (profileId) builder.withProfileId(profileId);

  return VulogUserEntity.from(builder.build());
};
