import { CarRealtimeMetadata } from '../../../src/model/rental-info';
import { aBoolean, aNumber } from '../random-data.helper';

export class CarRealtimeMetadataBuilder {
  constructor(
    public battery: number = aNumber(100),
    public isDoorClosed: boolean = aBoolean(),
    public isDoorLocked: boolean = aBoolean(),
    public isEngineOn: boolean = aBoolean(),
    public isCharging: boolean = aBoolean(),
  ) {}

  withBattery(battery: number): CarRealtimeMetadataBuilder {
    this.battery = battery;

    return this;
  }

  withIsDoorLocked(isDoorLocked: boolean): CarRealtimeMetadataBuilder {
    this.isDoorLocked = isDoorLocked;

    return this;
  }

  build(): CarRealtimeMetadata {
    return new CarRealtimeMetadata(
      this.battery,
      this.isDoorClosed,
      this.isDoorLocked,
      this.isEngineOn,
      this.isCharging,
    );
  }
}
