import {
  BsgUserId,
  VulogProfileId,
  VulogUserId,
} from '../../../src/common/tiny-types';
import {
  DISABLED_RFID_SUFFIX,
  VulogUser,
  VulogUserStatus,
} from '../../../src/model/vulog-user';
import {
  aBoolean,
  aDate,
  anEmail,
  anEnumValue,
  aString,
  aUUID,
} from '../random-data.helper';

export class VulogUserBuilder {
  constructor(
    public bsgUserId = new BsgUserId(aUUID()),
    public email = anEmail(),
    public status: VulogUserStatus = anEnumValue(VulogUserStatus),
    private password = aString(),
    public profileId = aUUID(),
    public id = new VulogUserId(aUUID()),
    public isRegistered = aBoolean(),
    public readonly createdAt = aDate(),
    public updatedAt = aDate(),
    public entityId = aUUID(),
    public rfid = aString(20),
  ) {}

  withBsgUserId(bsgUserId: BsgUserId): VulogUserBuilder {
    this.bsgUserId = bsgUserId;
    return this;
  }

  withId(id: VulogUserId): VulogUserBuilder {
    this.id = id;

    return this;
  }

  withEntityId(entityId: string): VulogUserBuilder {
    this.entityId = entityId;

    return this;
  }

  withProfileId(profileId: string): VulogUserBuilder {
    this.profileId = profileId;

    return this;
  }

  withStatus(status: VulogUserStatus): VulogUserBuilder {
    this.status = status;
    return this;
  }

  withIsRegistered(isRegistered: boolean): VulogUserBuilder {
    this.isRegistered = isRegistered;
    return this;
  }

  withRfid(value: string): VulogUserBuilder {
    this.rfid = value;
    return this;
  }

  rfidDisabled(): VulogUserBuilder {
    this.rfid = this.rfid.concat(DISABLED_RFID_SUFFIX);

    return this;
  }

  static allSync(): VulogUserBuilder {
    return new VulogUserBuilder()
      .withStatus(VulogUserStatus.SYNCHRONIZED)
      .withIsRegistered(true);
  }

  static creating(): VulogUserBuilder {
    return new VulogUserBuilder()
      .withStatus(VulogUserStatus.CREATING)
      .withIsRegistered(false)
      .withProfileId(null)
      .withId(null);
  }

  build(): VulogUser {
    return new VulogUser(
      this.bsgUserId,
      this.email,
      this.status,
      this.password,
      this.profileId ? new VulogProfileId(this.profileId) : null,
      this.id,
      this.isRegistered,
      this.createdAt,
      this.updatedAt,
      this.entityId,
      this.rfid,
    );
  }
}
