import { CarModelEnum } from 'src/common/enum/car-model.enum';
import {
  CarModel,
  CarPlateNumber,
  VulogCarId,
  VulogServiceId,
} from 'src/common/tiny-types';
import { VulogFleetServiceType } from 'src/model/dtos/vulog-fleet-service/vulog-fleet-service.dto';
import { StaticCar } from 'src/model/static.car';
import { anEnumValue, aPlate, aUUID, aVin } from '../random-data.helper';

export class StaticCarBuilder {
  id: VulogCarId = new VulogCarId(aUUID());

  model: CarModel = new CarModel(anEnumValue(CarModelEnum));

  plate: CarPlateNumber = new CarPlateNumber(aPlate());

  vin: string = aVin();

  sourceId: VulogCarId = this.id;

  serviceId: VulogServiceId = new VulogServiceId(aUUID());

  serviceType?: VulogFleetServiceType = anEnumValue(VulogFleetServiceType);

  withId(value: VulogCarId): StaticCarBuilder {
    this.id = value;

    return this;
  }

  withModel(value: CarModel): StaticCarBuilder {
    this.model = value;

    return this;
  }

  withPlate(value: CarPlateNumber): StaticCarBuilder {
    this.plate = value;

    return this;
  }

  withVin(value: string): StaticCarBuilder {
    this.vin = value;

    return this;
  }

  withServiceId(value: VulogServiceId): StaticCarBuilder {
    this.serviceId = value;

    return this;
  }

  withServiceType(value: VulogFleetServiceType): StaticCarBuilder {
    this.serviceType = value;

    return this;
  }

  build(): StaticCar {
    return new StaticCar({
      id: this.id,
      model: this.model,
      plate: this.plate,
      vin: this.vin,
      sourceId: this.sourceId,
      serviceId: this.serviceId,
      serviceType: this.serviceType,
    });
  }
}
