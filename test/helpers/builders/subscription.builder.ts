import { BsgUserId } from '../../../src/common/tiny-types';
import { Subscription } from '../../../src/model/uss/subscription';
import { SubscriptionId } from '../../../src/model/uss/subscriptionId';
import { SubscriptionPlanId } from '../../../src/model/uss/subscriptionPlanId';
import { aString, aUUID } from '../random-data.helper';

export class SubscriptionBuilder {
  constructor(
    private id: SubscriptionId = new SubscriptionId(aUUID()),
    private subscriptionPlanId: SubscriptionPlanId = new SubscriptionPlanId(
      aUUID(),
    ),
    private userId: BsgUserId = new BsgUserId(aUUID()),
    private status: string = aString(),
  ) {}

  build(): Subscription {
    return new Subscription(
      this.id,
      this.subscriptionPlanId,
      this.userId,
      this.status,
    );
  }
}
