import { BsgUserId } from '../../../src/common/tiny-types';
import {
  LanguageCodeAlpha3,
  UserStatus,
  UserTitle,
} from '../../../src/external/user-subscription/constants';
import { User } from '../../../src/model/user';
import {
  aDate,
  aFirstName,
  aLastName,
  anEmail,
  anEnumValue,
  aNumber,
  aUUID,
} from '../random-data.helper';
import { SubscriptionBuilder } from './subscription.builder';

export class UserBuilder {
  constructor(
    public id = new BsgUserId(aUUID()),
    public email = anEmail(),
    public status: UserStatus = anEnumValue(UserStatus),
    public firstName = aFirstName(),
    public lastName = aLastName(),
    public dateOfBirth: Date = aDate(),
    public title: UserTitle = anEnumValue(UserTitle),
    public mobile = `${aNumber()}`,
    public preferredLanguage = anEnumValue(
      LanguageCodeAlpha3,
    ) as LanguageCodeAlpha3,
    readonly currentSubscription = new SubscriptionBuilder().build(),
  ) {}

  withId(id: BsgUserId): UserBuilder {
    this.id = id;

    return this;
  }

  build(): User {
    return new User(
      this.id,
      this.email,
      this.status,
      this.firstName,
      this.lastName,
      this.dateOfBirth,
      this.title,
      this.mobile,
      this.preferredLanguage,
      this.currentSubscription,
    );
  }
}
