import {
  VulogCarRealTimeDto,
  VulogRealTimeBookingStatus,
  VulogRealTimeVehicleStatus,
} from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogLocationRealTime } from 'src/model/dtos/vulog-car-real-time/vulog-location-real-time';
import { VulogCarDescription } from 'src/model/dtos/vulog-car/vulog-car-fields/vulog-car-description';
import { VulogCarLocation } from 'src/model/dtos/vulog-car/vulog-car-fields/vulog-car-location';
import { VulogCarStatus } from 'src/model/dtos/vulog-car/vulog-car-fields/vulog-car-status';
import { VulogCarDto } from 'src/model/dtos/vulog-car/vulog-car.dto';
import { VulogBleKey } from 'src/model/dtos/vulog-journey/vulog-journey-fields/vulog-ble-key';
import { VulogPosition } from 'src/model/dtos/vulog-position';
import { VulogLocation } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-location';
import {
  VulogZone,
  VulogZoneType,
} from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { VvgCarAlerts } from 'src/model/dtos/vvg-car/vvg-car-fields/vvg-car-alerts';
import { VvgCarAltTracking } from 'src/model/dtos/vvg-car/vvg-car-fields/vvg-car-alt-tracking';
import { VvgCarInformation } from 'src/model/dtos/vvg-car/vvg-car-fields/vvg-car-information';
import { VvgCarSession } from 'src/model/dtos/vvg-car/vvg-car-fields/vvg-car-session';
import { VvgCarStatus } from 'src/model/dtos/vvg-car/vvg-car-fields/vvg-car-status';
import { VvgCarTracking } from 'src/model/dtos/vvg-car/vvg-car-fields/vvg-car-tracking';
import { VvgCarZones } from 'src/model/dtos/vvg-car/vvg-car-fields/vvg-car-zones';
import { VvgCarDto } from 'src/model/dtos/vvg-car/vvg-car.dto';

import { faker } from '@faker-js/faker';
import { isDefined } from 'class-validator';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { VulogCarModelInfo } from 'src/model/dtos/vulog-car-static-info/vulog-car-static-info-fields/vulog-car-model-info';
import { VulogCarStaticInfoDto } from 'src/model/dtos/vulog-car-static-info/vulog-car-static-info.dto';

export const createTestVulogCarDto: (carId?: string) => VulogCarDto = (
  carId,
) => {
  const newCarDto = new VulogCarDto();
  newCarDto.status = new VulogCarStatus(
    faker.number.int({ min: 0, max: 100 }),
    null,
    faker.datatype.boolean(),
  );

  newCarDto.description = new VulogCarDescription(
    carId || faker.string.uuid(),
    faker.vehicle.model(),
    faker.vehicle.vrm(),
    faker.vehicle.vehicle(),
    faker.string.uuid(),
    [],
  );

  newCarDto.location = new VulogCarLocation();
  newCarDto.location.position = new VulogPosition(
    faker.location.latitude(),
    faker.location.longitude(),
  );

  return newCarDto;
};

export const createTestVvgCarDto: (data?: {
  zones?: VvgCarZones;
}) => VvgCarDto = (data) => {
  const newCarAdminInfoDto = new VvgCarDto();
  newCarAdminInfoDto.id = faker.string.uuid();
  newCarAdminInfoDto.fleetId = faker.string.uuid();
  newCarAdminInfoDto.boxId = faker.string.uuid();
  newCarAdminInfoDto.status = new VvgCarStatus(
    faker.hacker.noun(),
    {
      km: faker.number.int(9999),
      percentage: faker.number.int(100),
    },
    {
      km: faker.number.int(9999),
      percentage: faker.number.int(100),
    },
    {
      km: faker.number.int(9999),
      percentage: faker.number.int(100),
    },
    {
      km: faker.number.int(9999),
      percentage: faker.number.int(100),
    },
    faker.number.float({ max: 99999, precision: 0.1 }),
    faker.number.float({
      min: 10,
      max: 30,
      precision: 0.01,
    }),
    faker.datatype.boolean(),
    faker.datatype.boolean(),
    faker.datatype.boolean(),
    faker.datatype.boolean(),
    faker.datatype.boolean(),
    faker.datatype.boolean(),
    faker.datatype.boolean(),
    {
      frontLeftClosed: faker.datatype.boolean(),
      frontRightClosed: faker.datatype.boolean(),
      rearLeftClosed: faker.datatype.boolean(),
      rearRightClosed: faker.datatype.boolean(),
      trunkClosed: faker.datatype.boolean(),
    },
    {
      frontLeftClosed: faker.datatype.boolean(),
      frontRightClosed: faker.datatype.boolean(),
      rearLeftClosed: faker.datatype.boolean(),
      rearRightClosed: faker.datatype.boolean(),
      trunkClosed: faker.datatype.boolean(),
    },
    faker.datatype.boolean(),
    faker.datatype.boolean(),
    faker.datatype.boolean(),
    faker.datatype.boolean(),
    faker.datatype.boolean(),
    faker.datatype.boolean(),
    faker.datatype.boolean(),
    faker.number.int(99),
    faker.date.recent().toDateString(),
    faker.hacker.noun(),
  );
  newCarAdminInfoDto.sessions = [
    new VvgCarSession(
      faker.string.uuid(),
      faker.string.uuid(),
      faker.string.numeric(8),
      faker.date.recent().toDateString(),
      faker.date.future().toDateString(),
      'en_US',
    ),
  ];
  newCarAdminInfoDto.information = new VvgCarInformation(
    faker.string.alphanumeric(5),
    faker.vehicle.vin(),
    faker.system.semver(),
  );

  newCarAdminInfoDto.tracking = new VvgCarTracking(
    faker.location.latitude(),
    faker.location.longitude(),
    faker.number.int(400),
    faker.number.float({ max: 100, precision: 0.01 }),
    faker.hacker.noun(),
    faker.number.int(100),
    faker.number.float({ max: 360, precision: 0.1 }),
    faker.number.float({ max: 300, precision: 0.1 }),
    faker.date.recent().toDateString(),
  );

  newCarAdminInfoDto.simTracking = new VvgCarAltTracking(
    faker.location.latitude(),
    faker.location.longitude(),
    faker.date.recent().toDateString(),
    faker.location.streetAddress(),
  );

  newCarAdminInfoDto.manualTracking = new VvgCarAltTracking(
    faker.location.latitude(),
    faker.location.longitude(),
    faker.date.recent().toDateString(),
    faker.location.streetAddress(),
  );

  newCarAdminInfoDto.zones =
    data?.zones ??
    new VvgCarZones([[faker.string.uuid(), faker.string.uuid()]]);

  newCarAdminInfoDto.alerts = new VvgCarAlerts(
    faker.datatype.boolean(),
    faker.datatype.boolean(),
    faker.datatype.boolean(),
  );

  return newCarAdminInfoDto;
};

/**
 *
 * @deprecated : Should now use the VulogCarRealTimeDtoBuilder
 */
export const createTestVulogCarRealTimeDto = (
  id?: string,
  vehicle_status?: VulogRealTimeVehicleStatus,
  booking_status?: VulogRealTimeBookingStatus,
  fleetid?: string,
  serviceId?: string,
  zones?: VulogZone[],
  startZones?: VulogZone[],
  vin?: string,
  profileId?: string,
  vulogTripId?: string,
  start_date?: string,
) => {
  const newCarRtDto = new VulogCarRealTimeDto();

  newCarRtDto.id = id ?? faker.string.uuid();
  newCarRtDto.name = faker.string.alphanumeric(10);
  newCarRtDto.plate = faker.vehicle.vrm();
  newCarRtDto.vin = vin || faker.vehicle.vin();
  newCarRtDto.fleetid = fleetid || faker.string.uuid();
  newCarRtDto.boxid = faker.string.uuid();
  newCarRtDto.vehicle_status = isDefined(vehicle_status)
    ? vehicle_status
    : faker.helpers.enumValue(VulogRealTimeVehicleStatus);
  newCarRtDto.zones = zones || [
    new VulogZone(
      faker.string.uuid(),
      faker.number.int(),
      VulogZoneType.Allowed,
      faker.datatype.boolean(),
    ),
  ];
  newCarRtDto.releasable = faker.datatype.boolean();
  newCarRtDto.box_status = faker.number.int(5);
  newCarRtDto.autonomy = faker.number.int(100);
  newCarRtDto.autonomy2 = faker.number.int(100);
  newCarRtDto.isCharging = faker.datatype.boolean();
  newCarRtDto.battery = faker.number.int(30); // auxiliary Battery, in Volt
  newCarRtDto.km = faker.number.int();
  newCarRtDto.speed = faker.number.int(100);
  newCarRtDto.location = new VulogLocationRealTime(
    faker.location.latitude(),
    faker.location.longitude(),
    faker.date.recent().toDateString(),
    faker.date.recent().toDateString(),
    faker.string.alphanumeric(10),
    faker.number.float(10),
  );
  newCarRtDto.endTripLocation = new VulogLocation(
    faker.location.latitude(),
    faker.location.longitude(),
  );
  newCarRtDto.endZoneIds = [faker.string.uuid()];
  newCarRtDto.productIds = [faker.string.uuid()];
  newCarRtDto.isDoorClosed = faker.datatype.boolean();
  newCarRtDto.isDoorLocked = faker.datatype.boolean();
  newCarRtDto.engineOn = faker.datatype.boolean();
  newCarRtDto.immobilizerOn = faker.datatype.boolean();
  newCarRtDto.secureOn = faker.datatype.boolean();
  newCarRtDto.spareLockOn = faker.datatype.boolean();
  newCarRtDto.pileLockOn = faker.datatype.boolean();
  newCarRtDto.userId = faker.string.uuid();
  newCarRtDto.user_locale = 'en_GB';
  newCarRtDto.rfid = faker.string.uuid();
  newCarRtDto.orderId = vulogTripId || faker.string.uuid();
  newCarRtDto.gatewayUrl = faker.internet.url();
  newCarRtDto.booking_status = isDefined(booking_status)
    ? booking_status
    : faker.helpers.enumValue(VulogRealTimeBookingStatus);
  newCarRtDto.booking_date = faker.date.recent().toISOString();
  newCarRtDto.expiresOn = faker.date.recent().toISOString();
  newCarRtDto.last_active_date = faker.date.recent().toISOString();
  newCarRtDto.last_wakeUp_date = faker.date.recent().toISOString();
  newCarRtDto.version = faker.system.semver();
  newCarRtDto.pricingId = faker.string.uuid();
  newCarRtDto.start_date = start_date || faker.date.recent().toISOString();
  newCarRtDto.theorStartDate = faker.date.recent().toISOString();
  newCarRtDto.theorEndDate = faker.date.recent().toISOString();
  newCarRtDto.startZones = startZones || [];
  newCarRtDto.endZones = [];
  newCarRtDto.disabled = faker.datatype.boolean();
  newCarRtDto.outOfServiceReason = '';
  newCarRtDto.cleanlinessStatus = faker.datatype.boolean();
  newCarRtDto.ignitionOffGeohash = '';
  newCarRtDto.geohashNeighbours = [];
  newCarRtDto.needsRedistribution = faker.datatype.boolean();
  newCarRtDto.batteryUnderThreshold = faker.datatype.boolean();
  newCarRtDto.isBeingTowed = faker.datatype.boolean();
  newCarRtDto.automaticallyEnableVehicleAfterRangeRecovery =
    faker.datatype.boolean();
  newCarRtDto.key = new VulogBleKey();
  newCarRtDto.startTripLocation = new VulogLocation(
    faker.location.latitude(),
    faker.location.longitude(),
  );
  newCarRtDto.preAuthStatus = '';
  newCarRtDto.profileId = profileId || faker.string.uuid();
  newCarRtDto.username = faker.string.uuid();
  newCarRtDto.preAuthEnabled = false;
  newCarRtDto.comeFromApp = '';
  newCarRtDto.entityId = faker.string.uuid();
  newCarRtDto.profileType = '';
  newCarRtDto.serviceType = 'FREE_FLOATING';
  newCarRtDto.serviceId = serviceId || faker.string.uuid();
  newCarRtDto.start_mileage = faker.number.float(999999);
  newCarRtDto.lastCleanedDate = faker.date.recent().toISOString();

  return newCarRtDto;
};

export const createTestVulogCarStaticInfoDto = (
  id?: string,
  fleetId?: string,
  carModel?: CarModelEnum,
  vin?: string,
) => {
  const info = new VulogCarStaticInfoDto();

  info.id = id || faker.string.uuid();
  info.vin = vin || faker.vehicle.vin();
  info.name = faker.string.uuid();
  info.plate = faker.vehicle.vrm();

  info.model = new VulogCarModelInfo(
    faker.string.uuid(),
    carModel || CarModelEnum.BlueCar,
    faker.vehicle.manufacturer(),
    faker.string.alpha(5),
    faker.string.alpha(5),
    faker.number.int(9),
    faker.number.int(100),
    faker.date.recent().toISOString(),
    faker.date.recent().toISOString(),
    faker.string.alphanumeric(30),
    faker.string.uuid(),
    faker.vehicle.type(),
    faker.vehicle.type(),
    '',
    '',
    [],
    '',
    faker.datatype.boolean(),
  );

  info.options = [];

  info.createDate = faker.date.recent().toISOString();
  info.updateDate = faker.date.recent().toISOString();
  info.fleetId = fleetId || faker.string.uuid();
  info.vuboxId = faker.string.uuid();
  info.externalId = faker.string.uuid();
  info.wakeupProvider == '';
  info.msisdn = faker.string.uuid();
  info.iccid = faker.string.uuid();
  info.imsi = faker.string.uuid();
  info.published = faker.datatype.boolean();
  info.archived = faker.datatype.boolean();

  return info;
};
