import * as _ from 'lodash';
import {
  InternalParkingLotResponseDtoV1,
  ParkingLotStatus,
} from 'src/external/station/dtos/response/internal.parking-lot.v1.response.dto';
import { InternalStationResponseDtoV1 } from 'src/external/station/dtos/response/internal.station-response.v1.response.dto';
import { MockBuilder } from '../../mock.builder';

export class InternalParkingLotResponseDtoV1Builder extends MockBuilder<InternalParkingLotResponseDtoV1> {
  protected result: InternalParkingLotResponseDtoV1 =
    new InternalParkingLotResponseDtoV1();

  buildStatus(value: ParkingLotStatus) {
    this.result.status = value;

    return this;
  }

  buildStation(value: InternalStationResponseDtoV1) {
    this.result.station = value;

    return this;
  }

  buildIsAvailable(value: boolean) {
    this.result.isAvailable = value;

    return this;
  }

  getResult: () => InternalParkingLotResponseDtoV1 = () => {
    return _.cloneDeep(this.result);
  };
}
