import { config } from 'src/config';
import { VulogUserDto } from 'src/model/dtos/vulog-user/vulog-user.dto';

import { faker } from '@faker-js/faker';

export const createTestVulogUserDto = (id?: string) => {
  const vulogUserDto = new VulogUserDto();

  vulogUserDto.id = id || faker.string.uuid();
  vulogUserDto.fleetId = config.vulogConfig.fleetId.value;
  vulogUserDto.userName = faker.string.uuid();
  vulogUserDto.firstName = faker.person.firstName();
  vulogUserDto.lastName = faker.person.lastName();
  vulogUserDto.email = faker.internet.email();
  vulogUserDto.accountStatus = faker.word.noun();
  vulogUserDto.gender = faker.person.gender();
  vulogUserDto.dataPrivacyConsent = faker.datatype.boolean();
  vulogUserDto.profilingConsent = faker.datatype.boolean();
  vulogUserDto.marketingConsent = faker.datatype.boolean();
  vulogUserDto.membershipNumber = faker.string.uuid();
  vulogUserDto.alipayScore = faker.number.int({ min: 0, max: 9999999 });
  vulogUserDto.birthDate = faker.date.birthdate().toDateString();
  vulogUserDto.businessUser = faker.datatype.boolean();
  vulogUserDto.entityId = faker.string.uuid();
  vulogUserDto.businessEmail = faker.internet.email();
  vulogUserDto.locale = 'en_GB';

  return vulogUserDto;
};
