import { VulogJourneyDto } from '../../../../src/model/dtos/vulog-journey/vulog-journey.dto';
import { VulogJourneyDtoBuilder } from './vulog/vulog-journey.dto.builder';

export const createTestVulogJourneyDto = (
  isRental: boolean,
  isReservation: boolean,
  vehicleId?: string,
  serviceId?: string,
): VulogJourneyDto => {
  const builder = new VulogJourneyDtoBuilder();

  if (vehicleId) builder.withVehicleId(vehicleId);
  if (serviceId) builder.withServiceId(serviceId);
  if (!isReservation) builder.asRental();

  return builder.build();
};
