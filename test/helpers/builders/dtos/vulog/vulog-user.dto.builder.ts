import { config } from '../../../../../src/config';
import { VulogUserDto } from '../../../../../src/model/dtos/vulog-user/vulog-user.dto';
import {
  aBirthDate,
  aBoolean,
  aFirstName,
  aGender,
  aLastName,
  anEmail,
  aNoun,
  aNumber,
  aUUID,
} from '../../../random-data.helper';

export class VulogUserDtoBuilder {
  constructor(
    private id = aUUID(),
    private fleetId = config.vulogConfig.fleetId.value,
    private userName = aUUID(),
    private firstName = aFirstName(),
    private lastName = aLastName(),
    private email = anEmail(),
    private accountStatus = aNoun(),
    private gender = aGender(),
    private dataPrivacyContent = aBoolean(),
    private profilingConsent = aBoolean(),
    private marketingConsent = aBoolean(),
    private membershipNumber = aUUID(),
    private alipayScore = aNumber(),
    private birthDate = aBirthDate().toDateString(),
    private businessUser = aBoolean(),
    private entityId = aUUID(),
    private businessEmail = anEmail(),
    private locale = 'en_GB',
  ) {}

  withId(id: string): VulogUserDtoBuilder {
    this.id = id;
    return this;
  }

  withEmail(email: string): VulogUserDtoBuilder {
    this.email = email;
    return this;
  }

  build() {
    const vulogUserDto = new VulogUserDto();

    vulogUserDto.id = this.id;
    vulogUserDto.fleetId = this.fleetId;
    vulogUserDto.userName = this.userName;
    vulogUserDto.firstName = this.firstName;
    vulogUserDto.lastName = this.lastName;
    vulogUserDto.email = this.email;
    vulogUserDto.accountStatus = this.accountStatus;
    vulogUserDto.gender = this.gender;
    vulogUserDto.dataPrivacyConsent = this.dataPrivacyContent;
    vulogUserDto.profilingConsent = this.profilingConsent;
    vulogUserDto.marketingConsent = this.marketingConsent;
    vulogUserDto.membershipNumber = this.membershipNumber;
    vulogUserDto.alipayScore = this.alipayScore;
    vulogUserDto.birthDate = this.birthDate;
    vulogUserDto.businessUser = this.businessUser;
    vulogUserDto.entityId = this.entityId;
    vulogUserDto.businessEmail = this.businessEmail;
    vulogUserDto.locale = this.locale;

    return vulogUserDto;
  }
}
