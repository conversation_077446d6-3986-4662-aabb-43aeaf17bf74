import {
  VulogCarRealTimeDto,
  VulogRealTimeBookingStatus,
  VulogRealTimeVehicleStatus,
} from '../../../../../src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogLocationRealTime } from '../../../../../src/model/dtos/vulog-car-real-time/vulog-location-real-time';
import { VulogLocation } from '../../../../../src/model/dtos/vulog-trip/vulog-trip-fields/vulog-location';
import {
  VulogZone,
  VulogZoneType,
} from '../../../../../src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import {
  aBoolean,
  aCap,
  aDateISOString,
  aDecimal,
  aLatitude,
  aLongitude,
  anEnumValue,
  aNoun,
  aNumber,
  aPlate,
  aString,
  aUrl,
  aUUID,
  aVersion,
  aVin,
} from '../../../random-data.helper';
import { VulogBleKeyBuilder } from './vulog-journey.dto.builder';

export class VulogZoneBuilder {
  constructor(
    private zoneId = aUUID(),
    private version = aNumber(50),
    private type = anEnumValue(VulogZoneType),
    private sticky = aBoolean(),
  ) {}

  withZoneId(id: string): VulogZoneBuilder {
    this.zoneId = id;
    return this;
  }

  withType(type: VulogZoneType): VulogZoneBuilder {
    this.type = type;
    return this;
  }

  withSticky(value: boolean): VulogZoneBuilder {
    this.sticky = value;

    return this;
  }

  build(): VulogZone {
    return new VulogZone(this.zoneId, this.version, this.type, this.sticky);
  }
}

export class VulogLocationRealTimeBuilder {
  constructor(
    private latitude = aLatitude(),
    private longitude = aLongitude(),
    private gpsDate = aDateISOString(),
    private lastMovingDate = aDateISOString(),
    private geohash = aString(10),
    private cap = aCap(),
  ) {}

  build(): VulogLocationRealTime {
    return new VulogLocationRealTime(
      this.latitude,
      this.longitude,
      this.gpsDate,
      this.lastMovingDate,
      this.geohash,
      this.cap,
    );
  }
}

export class VulogLocationBuilder {
  constructor(
    private latitude = aLatitude(),
    private longitude = aLongitude(),
  ) {}

  build(): VulogLocation {
    return new VulogLocation(this.latitude, this.longitude);
  }
}

export class VulogCarRealTimeDtoBuilder {
  constructor(
    private id = aUUID(),
    private name = aNoun(),
    private plate = aPlate(),
    private vin = aVin(),
    private fleetid = aUUID(),
    private boxid = aUUID(),
    private vehicle_status = anEnumValue(VulogRealTimeVehicleStatus),
    private zones = [
      new VulogZoneBuilder().withType(VulogZoneType.Allowed).build(),
    ],
    private releasable = aBoolean(),
    private box_status = aNumber(5),
    private autonomy = aNumber(),
    private autonomy2 = aNumber(),
    private isCharging = aBoolean(),
    private battery = aNumber(100),
    private km = aNumber(),
    private speed = aNumber(120),
    private location = new VulogLocationRealTimeBuilder().build(),
    private endTripLocation = new VulogLocationBuilder().build(),
    private endZoneIds = [aUUID()],
    private productIds = [aUUID()],
    private isDoorClosed = aBoolean(),
    private isDoorLocked = aBoolean(),
    private engineOn = aBoolean(),
    private immobilizerOn = aBoolean(),
    private secureOn = aBoolean(),
    private spareLockOn = aBoolean(),
    private pileLockOn = aBoolean(),
    private userId = aUUID(),
    private user_locale = 'en_GB',
    private rfid = aUUID(),
    private orderId = aUUID(),
    private gatewayUrl = aUrl(),
    private booking_status = anEnumValue(VulogRealTimeBookingStatus),
    private booking_date = aDateISOString(),
    private expiresOn = aDateISOString(),
    private last_active_date = aDateISOString(),
    private last_wakeUp_date = aDateISOString(),
    private version = aVersion(),
    private pricingId = aUUID(),
    private start_date = aDateISOString(),
    private theorStartDate = aDateISOString(),
    private theorEndDate = aDateISOString(),
    private startZones = [new VulogZoneBuilder().build()],
    private endZones = [],
    private disabled = aBoolean(),
    private outOfServiceReason = '',
    private cleanlinessStatus = aBoolean(),
    private ignitionOffGeohash = '',
    private geohashNeighbours = [],
    private needsRedistribution = aBoolean(),
    private batteryUnderThreshold = aBoolean(),
    private isBeingTowed = aBoolean(),
    private automaticallyEnableVehicleAfterRangeRecovery = aBoolean(),
    private key = new VulogBleKeyBuilder().build(),
    private startTripLocation = new VulogLocationBuilder().build(),
    private preAuthStatus = '',
    private profileId = aUUID(),
    private username = aUUID(),
    private preAuthEnabled = false,
    private comeFromApp = '',
    private entityId = aUUID(),
    private profileType = '',
    private serviceType = 'FREE_FLOATING',
    private serviceId = aUUID(),
    private start_mileage = aDecimal(999999),
    private lastCleanedDate = aDateISOString(),
  ) {}

  withId(id: string): VulogCarRealTimeDtoBuilder {
    this.id = id;
    return this;
  }

  withZones(zones: VulogZone[]): VulogCarRealTimeDtoBuilder {
    this.zones = zones;
    return this;
  }

  withVehicleStatus(
    vehicleStatus: VulogRealTimeVehicleStatus,
  ): VulogCarRealTimeDtoBuilder {
    this.vehicle_status = vehicleStatus;
    return this;
  }

  withBookingStatus(
    bookingStatus: VulogRealTimeBookingStatus,
  ): VulogCarRealTimeDtoBuilder {
    this.booking_status = bookingStatus;
    return this;
  }

  withFleetId(fleetId: string): VulogCarRealTimeDtoBuilder {
    this.fleetid = fleetId;
    return this;
  }

  withServiceId(serviceId: string): VulogCarRealTimeDtoBuilder {
    this.serviceId = serviceId;
    return this;
  }

  withServiceType(serviceType: string): VulogCarRealTimeDtoBuilder {
    this.serviceType = serviceType;
    return this;
  }

  withVin(vin: string): VulogCarRealTimeDtoBuilder {
    this.vin = vin;
    return this;
  }

  withProfileId(profileId: string): VulogCarRealTimeDtoBuilder {
    this.profileId = profileId;
    return this;
  }

  withStartDate(startDate: string): VulogCarRealTimeDtoBuilder {
    this.start_date = startDate;
    return this;
  }

  withBattery(battery: number): VulogCarRealTimeDtoBuilder {
    this.battery = battery;
    return this;
  }

  withAutonomy(autonomy: number): VulogCarRealTimeDtoBuilder {
    this.autonomy = autonomy;
    return this;
  }

  withStartZones(startZones: VulogZone[]): VulogCarRealTimeDtoBuilder {
    this.startZones = startZones;
    return this;
  }

  withVulogTripId(vulogTripId: string): VulogCarRealTimeDtoBuilder {
    this.orderId = vulogTripId;
    return this;
  }

  withPlate(value: string): VulogCarRealTimeDtoBuilder {
    this.plate = value;
    return this;
  }

  withIsCharging(value: boolean): VulogCarRealTimeDtoBuilder {
    this.isCharging = value;
    return this;
  }

  withIsDoorClosed(value: boolean): VulogCarRealTimeDtoBuilder {
    this.isDoorClosed = value;
    return this;
  }

  withIsDoorLocked(value: boolean): VulogCarRealTimeDtoBuilder {
    this.isDoorLocked = value;
    return this;
  }

  withEngineOn(value: boolean): VulogCarRealTimeDtoBuilder {
    this.engineOn = value;
    return this;
  }

  withImmobilizerOn(value: boolean): VulogCarRealTimeDtoBuilder {
    this.immobilizerOn = value;
    return this;
  }

  withDisabled(value: boolean): VulogCarRealTimeDtoBuilder {
    this.disabled = value;
    return this;
  }

  withUserId(value: string): VulogCarRealTimeDtoBuilder {
    this.userId = value;
    return this;
  }

  build(): VulogCarRealTimeDto {
    const dto = new VulogCarRealTimeDto();

    dto.id = this.id;
    dto.name = this.name;
    dto.plate = this.plate;
    dto.vin = this.vin;
    dto.fleetid = this.fleetid;
    dto.boxid = this.boxid;
    dto.vehicle_status = this.vehicle_status;
    dto.zones = this.zones;
    dto.releasable = this.releasable;
    dto.box_status = this.box_status;
    dto.autonomy = this.autonomy;
    dto.autonomy2 = this.autonomy2;
    dto.isCharging = this.isCharging;
    dto.battery = this.battery;
    dto.km = this.km;
    dto.speed = this.speed;
    dto.location = this.location;
    dto.endTripLocation = this.endTripLocation;
    dto.endZoneIds = this.endZoneIds;
    dto.productIds = this.productIds;
    dto.isDoorClosed = this.isDoorClosed;
    dto.isDoorLocked = this.isDoorLocked;
    dto.engineOn = this.engineOn;
    dto.immobilizerOn = this.immobilizerOn;
    dto.secureOn = this.secureOn;
    dto.spareLockOn = this.spareLockOn;
    dto.pileLockOn = this.pileLockOn;
    dto.userId = this.userId;
    dto.user_locale = this.user_locale;
    dto.rfid = this.rfid;
    dto.orderId = this.orderId;
    dto.gatewayUrl = this.gatewayUrl;
    dto.booking_status = this.booking_status;
    dto.booking_date = this.booking_date;
    dto.expiresOn = this.expiresOn;
    dto.last_active_date = this.last_active_date;
    dto.last_wakeUp_date = this.last_wakeUp_date;
    dto.version = this.version;
    dto.pricingId = this.pricingId;
    dto.start_date = this.start_date;
    dto.theorStartDate = this.theorStartDate;
    dto.theorEndDate = this.theorEndDate;
    dto.startZones = this.startZones;
    dto.endZones = this.endZones;
    dto.disabled = this.disabled;
    dto.outOfServiceReason = this.outOfServiceReason;
    dto.cleanlinessStatus = this.cleanlinessStatus;
    dto.ignitionOffGeohash = this.ignitionOffGeohash;
    dto.geohashNeighbours = this.geohashNeighbours;
    dto.needsRedistribution = this.needsRedistribution;
    dto.batteryUnderThreshold = this.batteryUnderThreshold;
    dto.isBeingTowed = this.isBeingTowed;
    dto.automaticallyEnableVehicleAfterRangeRecovery =
      this.automaticallyEnableVehicleAfterRangeRecovery;
    dto.key = this.key;
    dto.startTripLocation = this.startTripLocation;
    dto.preAuthStatus = this.preAuthStatus;
    dto.profileId = this.profileId;
    dto.username = this.username;
    dto.preAuthEnabled = this.preAuthEnabled;
    dto.comeFromApp = this.comeFromApp;
    dto.entityId = this.entityId;
    dto.profileType = this.profileType;
    dto.serviceType = this.serviceType;
    dto.serviceId = this.serviceId;
    dto.start_mileage = this.start_mileage;
    dto.lastCleanedDate = this.lastCleanedDate;

    return dto;
  }
}
