import { config } from 'src/config';
import {
  VulogCarEventDto,
  VulogCarEventType,
} from 'src/model/dtos/vulog-car-event/vulog-car-event.dto';
import {
  aBoolean,
  aDate,
  anEnumValue,
  aString,
  aUUID,
} from 'test/helpers/random-data.helper';

export class VulogCarEventDtoBuilder {
  fleetId: string = config.vulogConfig.fleetId.value;
  vehicleId: string = aUUID();
  origin: string = aString();
  type: VulogCarEventType = anEnumValue(VulogCarEventType);
  date: string = aDate().toISOString();
  failed: boolean = aBoolean();
  originId: string = null;
  insertionDate: string = aDate().toISOString();
  extraInfo?: object;

  withVehicleId(value: string) {
    this.vehicleId = value;

    return this;
  }

  withDate(value: string) {
    this.date = value;

    return this;
  }

  withType(value: VulogCarEventType) {
    this.type = value;

    return this;
  }

  build(): VulogCarEventDto<object> {
    const vulogCarEventDto = new VulogCarEventDto();

    vulogCarEventDto.fleetId = this.fleetId;
    vulogCarEventDto.vehicleId = this.vehicleId;
    vulogCarEventDto.origin = this.origin;
    vulogCarEventDto.type = this.type;
    vulogCarEventDto.date = this.date;
    vulogCarEventDto.failed = this.failed;
    vulogCarEventDto.originId = this.originId;
    vulogCarEventDto.insertionDate = this.insertionDate;
    vulogCarEventDto.extraInfo = this.extraInfo;

    return vulogCarEventDto;
  }
}
