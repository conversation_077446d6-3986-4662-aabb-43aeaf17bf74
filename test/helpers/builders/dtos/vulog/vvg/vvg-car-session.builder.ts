import { VvgCarSession } from 'src/model/dtos/vvg-car/vvg-car-fields/vvg-car-session';
import { aDate, aString, aUUID } from 'test/helpers/random-data.helper';

export class VvgCarSessionBuilder extends VvgCarSession {
  constructor() {
    super(null, null, null, null, null, null);
  }

  public sessionId: string = aUUID();

  public userId: string = aUUID();

  public rfid: string = aUUID();

  public createdDate: string = aDate().toISOString();

  public expirationDate: string = aDate().toISOString();

  public sessionLocale: string = aString();

  withSessionId(value: string): VvgCarSessionBuilder {
    this.sessionId = value;

    return this;
  }

  withUserId(value: string): VvgCarSessionBuilder {
    this.userId = value;

    return this;
  }

  build(): VvgCarSession {
    return new VvgCarSession(
      this.sessionId,
      this.userId,
      this.rfid,
      this.createdDate,
      this.expirationDate,
      this.sessionLocale,
    );
  }
}
