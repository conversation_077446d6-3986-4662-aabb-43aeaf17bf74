import { VulogVehicleStatusTracking } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import {
  aDate,
  aLatitude,
  aLongitude,
  aNumber,
} from 'test/helpers/random-data.helper';

export class VvgVehicleStatusTrackingBuilder extends VulogVehicleStatusTracking {
  latitude: number = aLatitude();

  longitude: number = aLongitude();

  altitude: number = aNumber();

  head: number = aNumber();

  hdop: number = aNumber();

  speed: number = aNumber();

  captureDate: string = aDate().toISOString();

  build(): VulogVehicleStatusTracking {
    const value = new VulogVehicleStatusTracking();

    value.latitude = this.latitude;
    value.longitude = this.longitude;
    value.altitude = this.altitude;
    value.head = this.head;
    value.hdop = this.hdop;
    value.speed = this.speed;
    value.captureDate = this.captureDate;

    return value;
  }
}
