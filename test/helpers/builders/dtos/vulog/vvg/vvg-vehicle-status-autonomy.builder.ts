import { VulogVehicleStatusAutonomy } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { aNumber } from 'test/helpers/random-data.helper';

export class VvgVehicleStatusAutonomyBuilder extends VulogVehicleStatusAutonomy {
  percentage: number = aNumber();

  km: number = aNumber();

  build(): VulogVehicleStatusAutonomy {
    const value = new VulogVehicleStatusAutonomy();

    value.percentage = this.percentage;
    value.km = this.km;

    return value;
  }
}
