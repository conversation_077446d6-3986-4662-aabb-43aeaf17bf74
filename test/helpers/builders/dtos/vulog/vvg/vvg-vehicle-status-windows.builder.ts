import { VulogVehicleStatusWindows } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { aBoolean } from 'test/helpers/random-data.helper';

export class VvgVehicleStatusWindowsBuilder extends VulogVehicleStatusWindows {
  frontLeftClosed: boolean = aBoolean();

  frontRightClosed: boolean = aBoolean();

  rearLeftClosed: boolean = aBoolean();

  rearRightClosed: boolean = aBoolean();

  trunkClosed: boolean = aBoolean();

  build(): VulogVehicleStatusWindows {
    const value = new VulogVehicleStatusWindows();

    value.frontLeftClosed = this.frontLeftClosed;
    value.frontRightClosed = this.frontRightClosed;
    value.rearLeftClosed = this.rearLeftClosed;
    value.rearRightClosed = this.rearRightClosed;
    value.trunkClosed = this.trunkClosed;

    return this;
  }
}
