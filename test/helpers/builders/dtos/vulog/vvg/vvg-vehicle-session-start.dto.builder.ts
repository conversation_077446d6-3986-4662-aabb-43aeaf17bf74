import * as _ from 'lodash';
import { VulogVehicleGatewayOrigin } from 'src/logic/vulog/dto/webhook/vg/vulog-vehicle-gateway-event-notification.dto';
import { VulogVgVehicleSessionStartDto, VulogVgVehicleSessionStartOrigin } from 'src/logic/vulog/dto/webhook/vg/vulog-vg-vehicle-session-start.dto';
import { MockBuilder } from 'test/helpers/builders/mock.builder';
import { aUUID } from 'test/helpers/random-data.helper';

export class VulogVgVehicleSessionStartDtoBuilder extends MockBuilder<VulogVgVehicleSessionStartDto> {
  protected result: VulogVgVehicleSessionStartDto =
    new VulogVgVehicleSessionStartDto();

  constructor() {
    super();
  }

  buildId(value: string = aUUID()) {
    this.result.id = value;

    return this;
  }

  buildOrigin(
    value: VulogVgVehicleSessionStartOrigin = VulogVehicleGatewayOrigin.API,
  ) {
    this.result.origin = value;

    return this;
  }

  buildFleetId(value: string) {
    this.result.fleetId = value;

    return this;
  }

  buildVehicleId(value: string) {
    this.result.vehicleId = value;

    return this;
  }

  buildBoxId(value: string = aUUID()) {
    this.result.boxId = value;

    return this;
  }

  buildDate(value: string) {
    this.result.date = value;

    return this;
  }

  buildSessionId(value: string) {
    this.result.sessionId = value;

    return this;
  }

  buildLatitude(value: number) {
    this.result.latitude = value;

    return this;
  }

  buildLongitude(value: number) {
    this.result.longitude = value;

    return this;
  }

  getResult: () => VulogVgVehicleSessionStartDto = () => {
    return _.cloneDeep(this.result);
  };
}
