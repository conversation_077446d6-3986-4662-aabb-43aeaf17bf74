import * as _ from 'lodash';
import { VulogVehicleGatewayOrigin } from 'src/logic/vulog/dto/webhook/vg/vulog-vehicle-gateway-event-notification.dto';
import { VulogVgVehicleStartTripDto } from 'src/logic/vulog/dto/webhook/vg/vulog-vg-vehicle-start-trip.dto';
import { VulogVgVehicleSessionStartOrigin } from 'src/logic/vulog/dto/webhook/vg/vulog-vg-vehicle-session-start.dto';
import { MockBuilder } from 'test/helpers/builders/mock.builder';
import { aUUID } from 'test/helpers/random-data.helper';
import { faker } from '@faker-js/faker';
import { DateTime } from 'luxon';

export class VulogVgVehicleStartTripDtoBuilder extends MockBuilder<VulogVgVehicleStartTripDto> {
  protected result: VulogVgVehicleStartTripDto = new VulogVgVehicleStartTripDto();

  constructor() {
    super();
  }

  buildId(value: string = aUUID()) {
    this.result.id = value;

    return this;
  }

  buildOrigin(
    value: VulogVgVehicleSessionStartOrigin = VulogVehicleGatewayOrigin.API,
  ) {
    this.result.origin = value;

    return this;
  }

  buildFleetId(value: string) {
    this.result.fleetId = value;

    return this;
  }

  buildVehicleId(value: string) {
    this.result.vehicleId = value;

    return this;
  }

  buildBoxId(value: string = aUUID()) {
    this.result.boxId = value;

    return this;
  }

  buildDate(value: string = DateTime.now().toISO()) {
    this.result.date = value;

    return this;
  }

  buildSessionId(value: string) {
    this.result.sessionId = value;

    return this;
  }

  buildLatitude(value: number = faker.location.latitude()) {
    this.result.latitude = value;

    return this;
  }

  buildLongitude(value: number = faker.location.longitude()) {
    this.result.longitude = value;

    return this;
  }

  getResult: () => VulogVgVehicleStartTripDto = () => {
    return _.cloneDeep(this.result);
  };
}
