import * as _ from 'lodash';
import {
  VulogVehicleGatewayEventNotificationDto,
  VulogVehicleGatewayEventType,
  VulogVehicleGatewayOrigin,
} from 'src/logic/vulog/dto/webhook/vg/vulog-vehicle-gateway-event-notification.dto';
import {
  VulogVgVehicleSessionEndDto,
  VulogVgVehicleSessionEndOrigin,
} from 'src/logic/vulog/dto/webhook/vg/vulog-vg-vehicle-session-end.dto';
import { MockBuilder } from 'test/helpers/builders/mock.builder';
import { aUUID } from 'test/helpers/random-data.helper';

export class VulogVgChargingPluggedDto extends VulogVehicleGatewayEventNotificationDto {
  type: VulogVehicleGatewayEventType =
    VulogVehicleGatewayEventType.VEHICLE_CHARGING_PLUGGED;

  origin: VulogVgVehicleSessionEndOrigin;
}

export class VulogVgChargingPluggedDtoBuilder extends MockBuilder<VulogVgChargingPluggedDto> {
  protected result: VulogVgChargingPluggedDto = new VulogVgChargingPluggedDto();

  constructor() {
    super();
  }

  buildId(value: string = aUUID()) {
    this.result.id = value;

    return this;
  }

  buildOrigin(
    value: VulogVgVehicleSessionEndOrigin = VulogVehicleGatewayOrigin.API,
  ) {
    this.result.origin = value;

    return this;
  }

  buildFleetId(value: string) {
    this.result.fleetId = value;

    return this;
  }

  buildVehicleId(value: string) {
    this.result.vehicleId = value;

    return this;
  }

  buildBoxId(value: string = aUUID()) {
    this.result.boxId = value;

    return this;
  }

  buildDate(value: string) {
    this.result.date = value;

    return this;
  }

  buildSessionId(value: string) {
    this.result.sessionId = value;

    return this;
  }

  buildLatitude(value: number) {
    this.result.latitude = value;

    return this;
  }

  buildLongitude(value: number) {
    this.result.longitude = value;

    return this;
  }

  getResult: () => VulogVgVehicleSessionEndDto = () => {
    return _.cloneDeep(this.result);
  };
}
