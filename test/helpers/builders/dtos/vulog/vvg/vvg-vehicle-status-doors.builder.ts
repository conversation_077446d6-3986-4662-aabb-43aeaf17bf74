import { VulogVehicleStatusDoors } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { aBoolean } from 'test/helpers/random-data.helper';

export class VvgVehicleStatusDoorsBuilder extends VulogVehicleStatusDoors {
  frontLeftClosed: boolean = aBoolean();

  frontRightClosed: boolean = aBoolean();

  rearLeftClosed: boolean = aBoolean();

  rearRightClosed: boolean = aBoolean();

  trunkClosed: boolean = aBoolean();

  build(): VulogVehicleStatusDoors {
    const value = new VulogVehicleStatusDoors();

    value.frontLeftClosed = this.frontLeftClosed;
    value.frontRightClosed = this.frontRightClosed;
    value.rearLeftClosed = this.rearLeftClosed;
    value.rearRightClosed = this.rearRightClosed;
    value.trunkClosed = this.trunkClosed;

    return this;
  }
}
