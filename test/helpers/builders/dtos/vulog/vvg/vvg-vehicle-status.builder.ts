import { VulogBoxStatus } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import {
  VulogVehicleStatus,
  VulogVehicleStatusAutonomy,
  VulogVehicleStatusDoors,
  VulogVehicleStatusWindows,
} from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import {
  aBoolean,
  aDate,
  anEnumValue,
  aNumber,
} from 'test/helpers/random-data.helper';
import { VvgVehicleStatusAutonomyBuilder } from './vvg-vehicle-status-autonomy.builder';
import { VvgVehicleStatusDoorsBuilder } from './vvg-vehicle-status-doors.builder';
import { VvgVehicleStatusWindowsBuilder } from './vvg-vehicle-status-windows.builder';

export class VvgVehicleStatusBuilder extends VulogVehicleStatus {
  boxStatus: string = anEnumValue(VulogBoxStatus).toString();

  mileage: number = aNumber();

  tractionBattery: VulogVehicleStatusAutonomy =
    new VvgVehicleStatusAutonomyBuilder().build();

  auxBatteryVoltage: number = aNumber();

  doors: VulogVehicleStatusDoors = new VvgVehicleStatusDoorsBuilder().build();

  windows: VulogVehicleStatusWindows =
    new VvgVehicleStatusWindowsBuilder().build();

  doorsAndWindowsClosed: boolean = aBoolean();

  locked: boolean = aBoolean();

  engineOn: boolean = aBoolean();

  parkingBrakeOn: boolean = aBoolean(); // This field only exists with the API https://vg-sta.vulog.net/v2/fleets/BLUESG-SINGA/vehicles/:vehecleId/status

  charging: boolean = aBoolean();

  cablePlugged: boolean = aBoolean();

  plugged: boolean = aBoolean();

  immobilizerOn: boolean = aBoolean();

  ecoMode: boolean = aBoolean();

  primaryFuelTank: VulogVehicleStatusAutonomy =
    new VvgVehicleStatusAutonomyBuilder().build();

  secondaryFuelTank: VulogVehicleStatusAutonomy =
    new VvgVehicleStatusAutonomyBuilder().build();

  reverseGear: boolean = aBoolean();

  rssi: number = aNumber();

  secured: boolean = aBoolean();

  updateDate: string = aDate().toISOString();

  build(): VulogVehicleStatus {
    const value = new VulogVehicleStatus();

    value.boxStatus = this.boxStatus;
    value.mileage = this.mileage;
    value.tractionBattery = this.tractionBattery;
    value.auxBatteryVoltage = this.auxBatteryVoltage;
    value.doors = this.doors;
    value.windows = this.windows;
    value.doorsAndWindowsClosed = this.doorsAndWindowsClosed;
    value.locked = this.locked;
    value.engineOn = this.engineOn;
    value.parkingBrakeOn = this.parkingBrakeOn;
    value.charging = this.charging;
    value.cablePlugged = this.cablePlugged;
    value.plugged = this.plugged;
    value.immobilizerOn = this.immobilizerOn;
    value.ecoMode = this.ecoMode;
    value.primaryFuelTank = this.primaryFuelTank;
    value.secondaryFuelTank = this.secondaryFuelTank;
    value.reverseGear = this.reverseGear;
    value.rssi = this.rssi;
    value.secured = this.secured;
    value.updateDate = this.updateDate;

    return value;
  }
}
