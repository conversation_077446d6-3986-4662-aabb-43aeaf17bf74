import { VulogVehicleInformation } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { aString, aVin } from 'test/helpers/random-data.helper';

export class VvgVehicleInformationBuilder extends VulogVehicleInformation {
  vin: string = aVin();

  name: string = aString();

  withVin(value: string) {
    this.vin = value;

    return this;
  }

  build() {
    const value = new VulogVehicleInformation();

    value.vin = this.vin;
    value.name = this.name;

    return value;
  }
}
