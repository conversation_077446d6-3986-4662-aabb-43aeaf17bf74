import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { VulogVehicleZones } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { VulogZoneBuilder } from '../vulog-car.real-time.dto.builder';

export class VvgVehicleZonesBuilder extends VulogVehicleZones {
  current: VulogZone[] = [new VulogZoneBuilder().build()];

  withCurrent(values: VulogZone[]): VvgVehicleZonesBuilder {
    this.current = values;

    return this;
  }

  build(): VulogVehicleZones {
    const value = new VulogVehicleZones();

    value.current = this.current;

    return value;
  }
}
