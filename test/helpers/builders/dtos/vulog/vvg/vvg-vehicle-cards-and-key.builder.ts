import { VulogVehicleCardsAndKey } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { aBoolean } from 'test/helpers/random-data.helper';

export class VvgVehicleCardsAndKeyBuilder extends VulogVehicleCardsAndKey {
  key: boolean = aBoolean();

  card1: boolean = aBoolean();

  card2: boolean = aBoolean();

  build(): VulogVehicleCardsAndKey {
    const value = new VulogVehicleCardsAndKey();

    value.key = this.key;
    value.card1 = this.card1;
    value.card2 = this.card2;

    return value;
  }
}
