import { VulogVehicleSimCard } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { aString } from 'test/helpers/random-data.helper';

export class VvgVehicleSimCardBuilder extends VulogVehicleSimCard {
  imsi: string = aString();

  iccid: string = aString();

  provider: string = aString();

  build(): VulogVehicleSimCard {
    const value = new VulogVehicleSimCard();

    value.imsi = this.imsi;
    value.iccid = this.iccid;
    value.provider = this.provider;

    return value;
  }
}
