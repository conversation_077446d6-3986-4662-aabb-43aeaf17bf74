import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { VvgCarSession } from 'src/model/dtos/vvg-car/vvg-car-fields/vvg-car-session';
import {
  VulogVehicleCardsAndKey,
  VulogVehicleInformation,
  VulogVehicleSimCard,
  VulogVehicleStatus,
  VulogVehicleStatusTracking,
  VulogVehicleZones,
  VvgVehicleStatusDto,
} from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { aBoolean, aUUID } from 'test/helpers/random-data.helper';
import { VvgCarSessionBuilder } from './vvg-car-session.builder';
import { VvgVehicleCardsAndKeyBuilder } from './vvg-vehicle-cards-and-key.builder';
import { VvgVehicleInformationBuilder } from './vvg-vehicle-information.builder';
import { VvgVehicleSimCardBuilder } from './vvg-vehicle-sim-card.builder';
import { VvgVehicleStatusTrackingBuilder } from './vvg-vehicle-status-tracking.builder';
import { VvgVehicleStatusBuilder } from './vvg-vehicle-status.builder';
import { VvgVehicleZonesBuilder } from './vvg-vehicle-zones.builder';

export class VvgVehicleStatusDtoBuilder extends VvgVehicleStatusDto {
  id: string = aUUID();

  fleetId: string = aUUID();

  boxId: string = aUUID();

  hasAlerts: boolean = aBoolean();

  sessions: VvgCarSession[] = [new VvgCarSessionBuilder().build()];

  information: VulogVehicleInformation =
    new VvgVehicleInformationBuilder().build();

  status: VulogVehicleStatus = new VvgVehicleStatusBuilder().build();

  tracking: VulogVehicleStatusTracking =
    new VvgVehicleStatusTrackingBuilder().build();

  cardsAndKey: VulogVehicleCardsAndKey =
    new VvgVehicleCardsAndKeyBuilder().build();

  simCard: VulogVehicleSimCard = new VvgVehicleSimCardBuilder().build();

  zones: VulogVehicleZones = new VvgVehicleZonesBuilder().build();

  withId(value: string): VvgVehicleStatusDtoBuilder {
    this.id = value;

    return this;
  }

  withUserId(value: string): VvgVehicleStatusDtoBuilder {
    this.sessions[0].userId = value;

    return this;
  }

  withVehicleVin(value: string): VvgVehicleStatusDtoBuilder {
    this.information.vin = value;

    return this;
  }

  withLatitude(value: number): VvgVehicleStatusDtoBuilder {
    this.tracking.latitude = value;

    return this;
  }

  withLongitude(value: number): VvgVehicleStatusDtoBuilder {
    this.tracking.longitude = value;

    return this;
  }

  withZone(value: VulogZone): VvgVehicleStatusDtoBuilder {
    this.zones.current = value ? [value] : [];

    return this;
  }

  withPlugged(value: boolean): VvgVehicleStatusDtoBuilder {
    this.status.plugged = value;

    return this;
  }

  withCablePlugged(value: boolean): VvgVehicleStatusDtoBuilder {
    this.status.cablePlugged = value;
    this.status.plugged = value;

    return this;
  }

  withAutonomy(value: number): VvgVehicleStatusDtoBuilder {
    this.status.tractionBattery.percentage = value;

    return this;
  }

  withVin(value: string): VvgVehicleStatusDtoBuilder {
    this.information.vin = value;

    return this;
  }

  withDoorsAndWindowsClosed(value: boolean): VvgVehicleStatusDtoBuilder {
    this.status.doorsAndWindowsClosed = value;

    return this;
  }

  withDoorsLocked(value: boolean): VvgVehicleStatusDtoBuilder {
    this.status.locked = value;

    return this;
  }

  withEngineOn(value: boolean): VvgVehicleStatusDtoBuilder {
    this.status.engineOn = value;

    return this;
  }

  withImmobilizerOn(value: boolean): VvgVehicleStatusDtoBuilder {
    this.status.immobilizerOn = value;

    return this;
  }

  withIsCharging(value: boolean): VvgVehicleStatusDtoBuilder {
    this.status.charging = value;

    return this;
  }

  build(): VvgVehicleStatusDto {
    const value = new VvgVehicleStatusDto();

    value.id = this.id;
    value.fleetId = this.fleetId;
    value.boxId = this.boxId;
    value.hasAlerts = this.hasAlerts;
    value.sessions = this.sessions;
    value.information = this.information;
    value.status = this.status;
    value.tracking = this.tracking;
    value.cardsAndKey = this.cardsAndKey;
    value.simCard = this.simCard;
    value.zones = this.zones;

    return value;
  }
}
