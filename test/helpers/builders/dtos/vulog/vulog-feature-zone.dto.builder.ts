import { VulogFeatureZoneDto } from 'src/model/dtos/vulog-feature/vulog-feature-zone.dto';
import { VulogZoneType } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { aMD5, aNumber, aString } from 'test/helpers/random-data.helper';

export class VulogFeatureZoneDtoBuilder {
  name: string = aString();

  version: number = aNumber(10);

  type: VulogZoneType = VulogZoneType.Allowed;

  zoneId: string = aMD5();

  build(): VulogFeatureZoneDto {
    return new VulogFeatureZoneDto({
      name: this.name,
      type: this.type,
      version: this.version,
      zoneId: this.zoneId,
    });
  }
}
