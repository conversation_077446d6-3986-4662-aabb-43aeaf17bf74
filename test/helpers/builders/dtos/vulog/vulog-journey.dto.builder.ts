import { VulogBle<PERSON>ey } from '../../../../../src/model/dtos/vulog-journey/vulog-journey-fields/vulog-ble-key';
import { VulogBooking } from '../../../../../src/model/dtos/vulog-journey/vulog-journey-fields/vulog-booking';
import { VulogTripField } from '../../../../../src/model/dtos/vulog-journey/vulog-journey-fields/vulog-trip';
import {
  VulogJourneyDto,
  VulogRelActions,
} from '../../../../../src/model/dtos/vulog-journey/vulog-journey.dto';
import { VulogPosition } from '../../../../../src/model/dtos/vulog-position';
import {
  aBoolean,
  aDateISOString,
  aLatitude,
  aLongitude,
  aNumber,
  aUserAgent,
  aUUID,
} from '../../../random-data.helper';

export class VulogBleKeyBuilder {
  constructor(
    private token = aUUID(),
    private sessionKey = aUUID(),
    private deviceName = aUserAgent(),
  ) {}

  build(): VulogBleKey {
    return new VulogBleKey(this.token, this.sessionKey, this.deviceName);
  }
}

export class VulogPositionBuilder {
  constructor(
    private latitude = aLatitude(),
    private longitude = aLongitude(),
  ) {}

  build(): VulogPosition {
    return new VulogPosition(this.latitude, this.longitude);
  }
}

export class VulogBookingBuilder {
  constructor(
    private bookingDate = aDateISOString(),
    private maxTripStartDate = aDateISOString(),
    private serverDate = aDateISOString(),
    private isVehicleInRange = aBoolean(),
  ) {}

  withBookingDate(bookingDate: string): VulogBookingBuilder {
    this.bookingDate = bookingDate;

    return this;
  }

  withMaxTripStartDate(maxTripStartDate: string): VulogBookingBuilder {
    this.maxTripStartDate = maxTripStartDate;

    return this;
  }

  build(): VulogBooking {
    return new VulogBooking(
      this.bookingDate,
      this.maxTripStartDate,
      this.serverDate,
      this.isVehicleInRange,
    );
  }
}
export class VulogJourneyDtoBuilder {
  constructor(
    private id = aUUID(), // the journey id from Vulog
    private energyLevel1 = String(aNumber(100)),
    private energyLevel2 = String(aNumber(100)),
    private inCharge = aBoolean(),
    private vehicleId = aUUID(),
    private key = new VulogBleKeyBuilder().build(),
    private position = new VulogPositionBuilder().build(),
    private booking = new VulogBookingBuilder().build(),
    private trip = new VulogTripField(aBoolean()),
    private isVehicleInRange = aBoolean(),
    private rel: VulogRelActions[] = [
      VulogRelActions.TripCreate,
      VulogRelActions.BookingDelete,
    ],
    private serviceId = aUUID(),
    private pricingId = aUUID(),
    private profileId = aUUID(),
    private preAuthEnabled = aBoolean(),
    private originCancelJourney = aUUID(),
    private vehicleInRange = aBoolean(),
  ) {}

  asRental(): VulogJourneyDtoBuilder {
    this.rel = [VulogRelActions.TripPause];
    return this;
  }

  withId(journeyId: string): VulogJourneyDtoBuilder {
    this.id = journeyId;
    return this;
  }

  withVehicleId(vehicleId: string): VulogJourneyDtoBuilder {
    this.vehicleId = vehicleId;
    return this;
  }

  withServiceId(serviceId: string): VulogJourneyDtoBuilder {
    this.serviceId = serviceId;
    return this;
  }

  withBooking(booking: VulogBooking): VulogJourneyDtoBuilder {
    this.booking = booking;
    return this;
  }

  build(): VulogJourneyDto {
    const vulogJourneyDto = new VulogJourneyDto();

    vulogJourneyDto.id = this.id;
    vulogJourneyDto.energyLevel = this.energyLevel1;
    vulogJourneyDto.energyLevel2 = this.energyLevel2;
    vulogJourneyDto.inCharge = this.inCharge;
    vulogJourneyDto.vehicleId = this.vehicleId;
    vulogJourneyDto.key = this.key;
    vulogJourneyDto.position = this.position;
    vulogJourneyDto.booking = this.booking;
    vulogJourneyDto.trip = this.trip;
    vulogJourneyDto.isVehicleInRange = this.isVehicleInRange;
    vulogJourneyDto.rel = this.rel;
    vulogJourneyDto.serviceId = this.serviceId;
    vulogJourneyDto.pricingId = this.pricingId;
    vulogJourneyDto.profileId = this.profileId;
    vulogJourneyDto.preAuthEnabled = this.preAuthEnabled;
    vulogJourneyDto.originCancelJourney = this.originCancelJourney;
    vulogJourneyDto.vehicleInRange = this.vehicleInRange;

    return vulogJourneyDto;
  }
}
