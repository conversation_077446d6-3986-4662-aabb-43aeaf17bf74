import { cloneDeep } from 'lodash';
import { VulogAdditionalTripInfo } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-additional-trip-info';
import { VulogLocation } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-location';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { aBoolean, aString } from 'test/helpers/random-data.helper';
import {
  VulogLocationBuilder,
  VulogZoneBuilder,
} from './vulog-car.real-time.dto.builder';

export class VulogTripAdditionalInfoBuilder {
  constructor(
    public firstDriveDelayMinutes: string = aString(),
    public bookDuration: string = aString(),
    public startZones: VulogZone[] = [new VulogZoneBuilder().build()],
    public endZones: VulogZone[] = [new VulogZoneBuilder().build()],
    public isAutolock: boolean = aBoolean(),
    public isCancel: boolean = aBoolean(),
    public isReleaseOnServer: boolean = aBoolean(),
    public isBilled: boolean = aBoolean(),
    public suspicious: boolean = aBoolean(),
    public startLocation: VulogLocation = new VulogLocationBuilder().build(),
    public endLocation: VulogLocation = new VulogLocationBuilder().build(),
    public ignoreOdometer: boolean = aBoolean(),
    public originCancelJourney: string = aString(),
  ) {}

  public makeIsCancel(isCancel: boolean): VulogTripAdditionalInfoBuilder {
    this.isCancel = isCancel;

    return this;
  }

  public build(): VulogAdditionalTripInfo {
    return cloneDeep(this);
  }
}
