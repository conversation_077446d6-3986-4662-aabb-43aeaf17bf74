import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { VulogCarModelInfo } from 'src/model/dtos/vulog-car-static-info/vulog-car-static-info-fields/vulog-car-model-info';
import { VulogCarOption } from 'src/model/dtos/vulog-car-static-info/vulog-car-static-info-fields/vulog-car-option';
import { VulogCarStaticInfoDto } from 'src/model/dtos/vulog-car-static-info/vulog-car-static-info.dto';
import {
  aBoolean,
  aDateISOString,
  anAlphaNumeric,
  aPlate,
  aString,
  aUUID,
  aVin,
} from 'test/helpers/random-data.helper';
import { VulogCarModelInfoDtoBuilder } from './vulog-car-model-info.dto.builder';

export class VulogCarStaticInfoDtoBuilder {
  public id: string = aUUID();
  public vin: string = aVin();
  public name: string = anAlphaNumeric();
  public plate: string = aPlate();

  public model: VulogCarModelInfo = new VulogCarModelInfoDtoBuilder().build();

  public options: VulogCarOption[] = null;

  public createDate: string = aDateISOString();
  public updateDate: string = aDateISOString();
  public fleetId: string = aUUID();
  public vuboxId: string = aUUID();
  public externalId: string = aUUID();
  public wakeupProvider: string = anAlphaNumeric();
  public msisdn: string = anAlphaNumeric();
  public iccid: string = anAlphaNumeric();
  public imsi: string = anAlphaNumeric();
  public published: boolean = aBoolean();
  public archived: boolean = aBoolean();
  public serviceId: string = aString();

  withId(id: string): VulogCarStaticInfoDtoBuilder {
    this.id = id;

    return this;
  }

  withModel(model: CarModelEnum): VulogCarStaticInfoDtoBuilder {
    this.model.name = model;

    return this;
  }

  withPlate(plate: string): VulogCarStaticInfoDtoBuilder {
    this.plate = plate;
    return this;
  }

  withVin(value: string): VulogCarStaticInfoDtoBuilder {
    this.vin = value;
    return this;
  }

  withServiceId(value: string): VulogCarStaticInfoDtoBuilder {
    this.serviceId = value;
    return this;
  }

  withArchived(value: boolean): VulogCarStaticInfoDtoBuilder {
    this.archived = value;

    return this;
  }

  build(): VulogCarStaticInfoDto {
    return new VulogCarStaticInfoDto({
      id: this.id,
      vin: this.vin,
      name: this.name,
      plate: this.plate,
      model: this.model,
      options: this.options,
      createDate: this.createDate,
      updateDate: this.updateDate,
      fleetId: this.fleetId,
      vuboxId: this.vuboxId,
      externalId: this.externalId,
      wakeupProvider: this.wakeupProvider,
      msisdn: this.msisdn,
      iccid: this.iccid,
      imsi: this.imsi,
      published: this.published,
      archived: this.archived,
      serviceId: this.serviceId,
    });
  }
}
