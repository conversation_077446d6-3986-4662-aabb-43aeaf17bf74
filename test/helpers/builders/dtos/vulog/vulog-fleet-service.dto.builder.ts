import { config } from 'src/config';
import {
  VulogFleetServiceDto,
  VulogFleetServiceStatus,
  VulogFleetServiceType,
  VulogFleetServiceVisibility,
} from 'src/model/dtos/vulog-fleet-service/vulog-fleet-service.dto';
import { aNumber, aString, aUUID } from 'test/helpers/random-data.helper';

export class VulogFleetServiceDtoBuilder {
  id = aUUID();
  name = aString();
  status = VulogFleetServiceStatus.Active;
  type = VulogFleetServiceType.FreeFloating;
  fleetId = config.vulogConfig.fleetId.value;
  cityId = aUUID();
  zones = [];
  vehicles = [];
  stations = [];

  pois = [];
  bookingValidity = aNumber();
  visibility = VulogFleetServiceVisibility.Public;

  autoApprovalStrategy = aString();
  tripPrivacyStrategy = aString();
  zoneFillColor = aString();
  zoneOutlineColor = aString();
  maxConcurrentTrips = aNumber();
  highestRangeEnabled = false;
  scheduleAtripEnabled = false;
  zoneCheckEnabled = true;
  maxAdditionalDrivers = aNumber();
  nearEndThreshold = aNumber();
  tripDurationStrategy = aString();
  customPricingEnabled = false;

  withId(id: string): VulogFleetServiceDtoBuilder {
    this.id = id;
    return this;
  }

  withType(value: VulogFleetServiceType): VulogFleetServiceDtoBuilder {
    this.type = value;
    return this;
  }

  withZones(zoneIds: string[]): VulogFleetServiceDtoBuilder {
    this.zones = zoneIds;

    return this;
  }

  withVehicles(vehicles: string[]): VulogFleetServiceDtoBuilder {
    this.vehicles = vehicles;

    return this;
  }

  build(): VulogFleetServiceDto {
    const dto = new VulogFleetServiceDto();

    dto.id = this.id;
    dto.name = this.name;
    dto.status = this.status;
    dto.type = this.type;
    dto.fleetId = this.fleetId;
    dto.cityId = this.cityId;
    dto.zones = this.zones;
    dto.vehicles = this.vehicles;
    dto.stations = this.stations;
    dto.pois = this.pois;
    dto.bookingValidity = this.bookingValidity;
    dto.visibility = this.visibility;
    dto.autoApprovalStrategy = this.autoApprovalStrategy;
    dto.tripPrivacyStrategy = this.tripPrivacyStrategy;
    dto.zoneFillColor = this.zoneFillColor;
    dto.zoneOutlineColor = this.zoneOutlineColor;
    dto.maxConcurrentTrips = this.maxConcurrentTrips;
    dto.highestRangeEnabled = this.highestRangeEnabled;
    dto.scheduleAtripEnabled = this.scheduleAtripEnabled;
    dto.zoneCheckEnabled = this.zoneCheckEnabled;
    dto.maxAdditionalDrivers = this.maxAdditionalDrivers;
    dto.nearEndThreshold = this.nearEndThreshold;
    dto.tripDurationStrategy = this.tripDurationStrategy;
    dto.customPricingEnabled = this.customPricingEnabled;

    return dto;
  }
}
