import { cloneDeep } from 'lodash';
import { VulogChargingCablePluggedBodyV1 } from 'src/logic/vulog/dto/webhook/aima/vulog-charging-cable-plugged-webhook.v1.dto';

export class VulogChargingCableUnpluggedBodyV1Builder {
  error = false;

  errorMessage: string = null;

  withError(value: boolean): VulogChargingCableUnpluggedBodyV1Builder {
    this.error = value;

    return this;
  }

  withErrorMessage(value: string): VulogChargingCableUnpluggedBodyV1Builder {
    this.errorMessage = value;

    return this;
  }

  public build(): VulogChargingCablePluggedBodyV1 {
    return cloneDeep(this);
  }
}
