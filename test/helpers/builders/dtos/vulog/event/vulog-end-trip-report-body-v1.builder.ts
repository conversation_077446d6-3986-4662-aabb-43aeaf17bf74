import { cloneDeep } from 'lodash';
import { VulogEndTripReportBodyV1 } from 'src/logic/vulog/dto/webhook/aima/vulog-end-trip-report-webhook.v1.dto';
import {
  aBoolean,
  aDateISOString,
  aNoun,
  aNumber,
  aString,
  aUUID,
} from 'test/helpers/random-data.helper';

export class VulogEndTripReportBodyV1Builder extends VulogEndTripReportBodyV1 {
  constructor() {
    super();
    this.bookingDuration = aNumber();
    this.tripDuration = aNumber();
    this.duration = aNumber();
    this.pauseDuration = aNumber();
    this.startDate = aDateISOString();
    this.endDate = aDateISOString();
    this.theorStartDate = aDateISOString();
    this.theorEndDate = aDateISOString();
    this.mileage = aNumber();
    this.serviceId = aUUID();
    this.serviceType = aNoun();
    this.pricingId = aUUID();
    this.profileId = aUUID();
    this.firstDriveDelayMinutes = 0;
    this.endZones = [];
    this.startZones = [];
    this.isSuspicious = aBoolean();
    this.suspiciousReason = aString();
    this.startLocation = aString();
    this.endLocation = aString();
    this.error = null;
  }

  withError(error: string): VulogEndTripReportBodyV1Builder {
    this.error = error;

    return this;
  }

  withStartZones(startZones: string[]): VulogEndTripReportBodyV1Builder {
    this.startZones = startZones;

    return this;
  }

  withEndZones(endZones: string[]): VulogEndTripReportBodyV1Builder {
    this.endZones = endZones;

    return this;
  }

  public build(): VulogEndTripReportBodyV1 {
    return cloneDeep(this);
  }
}
