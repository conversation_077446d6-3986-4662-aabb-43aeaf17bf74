import { cloneDeep } from 'lodash';
import { VulogVehicleStatusV1 } from 'src/logic/vulog/dto/webhook/aima/vulog-event-notifier-webhook.v1.dto';
import { aBoolean, aNumber } from 'test/helpers/random-data.helper';

export class VulogVehicleStatusV1Builder extends VulogVehicleStatusV1 {
  constructor(
    public energyLevel: number = aNumber(),
    public energyLevel2: number = aNumber(),
    public auxBatLevel: number = aNumber(),
    public doorsClose: boolean = aBoolean(),
    public doorsLock: boolean = aBoolean(),
    public speed: number = aNumber(),
    public odometer: number = aNumber(),
    public engineOn: boolean = aBoolean(),
    public immobilizerOn: boolean = aBoolean(),
    public isCharging: boolean = aBoolean(),
    public boxStatus: number = aNumber(),
  ) {
    super();
  }

  public build(): VulogVehicleStatusV1 {
    return cloneDeep(this);
  }
}
