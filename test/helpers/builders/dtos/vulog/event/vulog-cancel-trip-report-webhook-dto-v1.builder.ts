import { cloneDeep } from 'lodash';
import { VulogEventNotifierType } from 'src/common/constants/vulog-event-type';
import { VulogCancelTripReportBodyV1 } from 'src/logic/vulog/dto/webhook/aima/vulog-cancel-trip-report-webhook.v1.dto';
import { VulogCancelTripReportBodyV1Builder } from './vulog-cancel-trip-report-body-v1.builder';
import { VulogEventDtoBuilder } from './vulog-event-dto-v1.builder';

export class VulogCancelTripWebhookV1Builder extends VulogEventDtoBuilder<VulogCancelTripReportBodyV1> {
  public type: VulogEventNotifierType = VulogEventNotifierType.CancelTripReport;

  constructor(
    public body: VulogCancelTripReportBodyV1 = new VulogCancelTripReportBodyV1Builder().build(),
  ) {
    super();
  }

  public build(): VulogEventDtoBuilder<VulogCancelTripReportBodyV1> {
    return cloneDeep(this);
  }
}
