import * as _ from 'lodash';
import { VulogEndTripReportWebhookDtoV1 } from 'src/logic/vulog/dto/webhook/aima/vulog-end-trip-report-webhook.v1.dto';
import { VulogVehicleStatusV1 } from 'src/logic/vulog/dto/webhook/aima/vulog-event-notifier-webhook.v1.dto';
import { MockBuilder } from 'test/helpers/builders/mock.builder';
import { aString, aUUID } from 'test/helpers/random-data.helper';

export class VulogEndTripReportWebhookDtoV1Builder extends MockBuilder<VulogEndTripReportWebhookDtoV1> {
  protected result: VulogEndTripReportWebhookDtoV1 =
    new VulogEndTripReportWebhookDtoV1();

  constructor() {
    super();

    this.result.body = {} as any;
  }

  buildId(value: string = aUUID()) {
    this.result.id = value;

    return this;
  }

  buildFleetId(value: string) {
    this.result.fleetId = value;

    return this;
  }

  buildVehicleId(value: string) {
    this.result.vehicleId = value;

    return this;
  }

  buildBoxId(value: string = aUUID()) {
    this.result.boxId = value;

    return this;
  }

  buildDate(value: string) {
    this.result.date = value;

    return this;
  }

  buildVehicleName(value: string = aString()) {
    this.result.vehicleName = value;

    return this;
  }

  buildVehicleVin(value: string) {
    this.result.vehicleVin = value;

    return this;
  }

  buildVehiclePlate(value: string) {
    this.result.vehiclePlate = value;

    return this;
  }

  buildUserId(value: string) {
    this.result.userId = value;

    return this;
  }

  buildTripId(value: string) {
    this.result.tripId = value;

    return this;
  }

  buildIsInTrip(value: boolean) {
    this.result.isInTrip = value;

    return this;
  }

  buildIsInZone(value: boolean) {
    this.result.isInZone = value;

    return this;
  }

  buildTrigger(value: string) {
    this.result.trigger = value;

    return this;
  }

  buildOrigin(value: string) {
    this.result.origin = value;

    return this;
  }

  buildVehicleStatus(value: VulogVehicleStatusV1) {
    this.result.vehicleStatus = value;

    return this;
  }

  buildPosition(value: { lat: number; lon: number }) {
    this.result.position = value;

    return this;
  }

  buildBookingDuration(value: number) {
    this.result.body.bookingDuration = value;

    return this;
  }

  buildTripDuration(value: number) {
    this.result.body.tripDuration = value;

    return this;
  }

  buildDuration(value: number) {
    this.result.body.duration = value;

    return this;
  }

  buildPauseDuration(value: number) {
    this.result.body.pauseDuration = value;

    return this;
  }

  buildStartDate(value: string) {
    this.result.body.startDate = value;

    return this;
  }

  buildEndDate(value: string) {
    this.result.body.endDate = value;

    return this;
  }

  buildTheorStartDate(value: string) {
    this.result.body.theorStartDate = value;

    return this;
  }

  buildTheorEndDate(value: string) {
    this.result.body.theorEndDate = value;

    return this;
  }

  buildMileage(value: number) {
    this.result.body.mileage = value;

    return this;
  }

  buildServiceId(value: string) {
    this.result.body.serviceId = value;

    return this;
  }

  buildServiceType(value: string) {
    this.result.body.serviceType = value;

    return this;
  }

  buildPricingId(value: string) {
    this.result.body.pricingId = value;

    return this;
  }

  buildProfileId(value: string) {
    this.result.body.profileId = value;

    return this;
  }

  buildFirstDelayMinutes(value: unknown) {
    this.result.body.firstDriveDelayMinutes = value;

    return this;
  }

  buildEndZones(value: string[]) {
    this.result.body.endZones = value;

    return this;
  }

  buildStartZones(value: string[]) {
    this.result.body.startZones = value;

    return this;
  }

  buildIsSuspicious(value: boolean) {
    this.result.body.isSuspicious = value;

    return this;
  }

  buildSuspiciousReason(value: string) {
    this.result.body.suspiciousReason = value;

    return this;
  }

  buildStartLocation(value: string) {
    this.result.body.startLocation = value;

    return this;
  }

  buildEndLocation(value: string) {
    this.result.body.endLocation = value;

    return this;
  }

  buildError(value: string) {
    this.result.body.error = value;

    return this;
  }

  buildQrCode(value: string) {
    this.result.qrCode = value;

    return this;
  }

  getResult: () => VulogEndTripReportWebhookDtoV1 = () => {
    return _.cloneDeep(this.result);
  };
}
