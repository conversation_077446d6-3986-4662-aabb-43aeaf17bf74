import { cloneDeep } from 'lodash';
import { VulogEventNotifierType } from 'src/common/constants/vulog-event-type';
import { VulogChargingCableUnpluggedBodyV1 } from 'src/logic/vulog/dto/webhook/aima/vulog-charging-cable-unplugged-webhook.v1.dto';
import { VulogChargingCableUnpluggedBodyV1Builder } from './vulog-charging-cable-unplugged-body-v1.builder';
import { VulogEventDtoBuilder } from './vulog-event-dto-v1.builder';

export class VulogChargingCableUnpluggedWebhookV1Builder extends VulogEventDtoBuilder<VulogChargingCableUnpluggedBodyV1> {
  public type: VulogEventNotifierType =
    VulogEventNotifierType.ChargingCableUnplugged;
  tripId = null;
  userId = null;

  constructor(
    public body: VulogChargingCableUnpluggedBodyV1 = new VulogChargingCableUnpluggedBodyV1Builder().build(),
  ) {
    super();
  }

  public build(): VulogEventDtoBuilder<VulogChargingCableUnpluggedBodyV1> {
    return cloneDeep(this);
  }
}
