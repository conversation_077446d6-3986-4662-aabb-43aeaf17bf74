import { cloneDeep } from 'lodash';
import { VulogEventNotifierType } from 'src/common/constants/vulog-event-type';
import { VulogChargingCablePluggedBodyV1 } from 'src/logic/vulog/dto/webhook/aima/vulog-charging-cable-plugged-webhook.v1.dto';
import { VulogChargingCablePluggedBodyV1Builder } from './vulog-charging-cable-plugged-body-v1.builder';
import { VulogEventDtoBuilder } from './vulog-event-dto-v1.builder';

export class VulogChargingCablePluggedWebhookV1Builder extends VulogEventDtoBuilder<VulogChargingCablePluggedBodyV1> {
  public type: VulogEventNotifierType =
    VulogEventNotifierType.ChargingCablePlugged;
  tripId = null;
  userId = null;

  constructor(
    public body: VulogChargingCablePluggedBodyV1 = new VulogChargingCablePluggedBodyV1Builder().build(),
  ) {
    super();
  }

  public build(): VulogEventDtoBuilder<VulogChargingCablePluggedBodyV1> {
    return cloneDeep(this);
  }
}
