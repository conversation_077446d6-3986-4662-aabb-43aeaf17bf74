import { faker } from '@faker-js/faker';
import * as _ from 'lodash';
import { EventbridgeEventDto } from 'src/logic/vulog/dto/webhook/eventbridge-event.dto';
import { MockBuilder } from 'test/helpers/builders/mock.builder';
import { aDateISOString, aString } from 'test/helpers/random-data.helper';

export class EventbridgeEventDtoBuilder extends MockBuilder<EventbridgeEventDto> {
  protected result: EventbridgeEventDto = new EventbridgeEventDto();

  constructor() {
    super();

    this.result.detail = {} as any;
  }

  buildVersion(value: string = aString()) {
    this.result.version = value;

    return this;
  }

  buildId(value: string = aString()) {
    this.result.id = value;

    return this;
  }

  buildDetailType(value: string = aString()) {
    this.result['detail-type'] = value;

    return this;
  }

  buildSource(value: string = faker.internet.ipv4()) {
    this.result.source = value;

    return this;
  }

  buildAccount(value: string = aString()) {
    this.result.account = value;

    return this;
  }

  buildTime(value: string = aDateISOString()) {
    this.result.time = value;

    return this;
  }

  buildRegion(value: string = aString()) {
    this.result.region = value;

    return this;
  }

  buildResources(value: string[] = [aString()]) {
    this.result.resources = value;

    return this;
  }

  buildDetail(value: unknown) {
    this.result.detail = value;

    return this;
  }

  getResult: () => EventbridgeEventDto = () => {
    return _.cloneDeep(this.result);
  };
}
