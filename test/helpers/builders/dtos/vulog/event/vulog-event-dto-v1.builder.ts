import { cloneDeep } from 'lodash';
import { VulogEventNotifierType } from 'src/common/constants/vulog-event-type';
import {
  VulogEventNotifierWebhookDtoV1,
  VulogVehicleStatusV1,
} from 'src/logic/vulog/dto/webhook/aima/vulog-event-notifier-webhook.v1.dto';
import {
  aBoolean,
  aDateISOString,
  aLastName,
  anAlphaNumeric,
  anEnumValue,
  aNoun,
  aNumber,
  aUUID,
} from 'test/helpers/random-data.helper';
import { VulogVehicleStatusV1Builder } from './vulog-vehicle-status-v1.builder';

export class VulogEventDtoBuilder<T = unknown> {
  public body: T;

  constructor(
    public id: string = aUUID(),
    public fleetId: string = aUUID(),
    public vehicleId: string = aUUID(),
    public boxId: string = aUUID(),
    public date: string = aDateISOString(),
    public vehicleName: string = aLastName(),
    public vehicleVin: string = aUUID(),
    public vehiclePlate: string = anAlphaNumeric(),
    public userId: string = aUUID(),
    public tripId: string = aUUID(),
    public isInTrip: boolean = aBoolean(),
    public isInZone: boolean = aBoolean(),
    public trigger: string = aNoun(),
    public origin: string = aNoun(),
    public vehicleStatus: VulogVehicleStatusV1 = new VulogVehicleStatusV1Builder(),
    public position: {
      lat: number;
      lon: number;
    } = {
      lat: aNumber(),
      lon: aNumber(),
    },
    public type: VulogEventNotifierType = anEnumValue(VulogEventNotifierType),
    public qrCode: string = null,
  ) {}

  public makeTripId(val: string) {
    this.tripId = val;
    return this;
  }

  public makeVulogUserId(val: string) {
    this.userId = val;
    return this;
  }

  public makeVehicleId(val: string) {
    this.vehicleId = val;
    return this;
  }

  public makeDate(val: string) {
    this.date = val;
    return this;
  }

  public makeType(val: VulogEventNotifierType) {
    this.type = val;
    return this;
  }

  public makeBody(val: T) {
    this.body = val;
    return this;
  }

  public makeVehicleVin(val: string) {
    this.vehicleVin = val;

    return this;
  }

  public makeQRCode(val: string) {
    this.qrCode = val;
    return this;
  }

  public makeVehiclePlate(val: string) {
    this.vehiclePlate = val;

    return this;
  }

  public makeOrigin(val: string) {
    this.origin = val;

    return this;
  }

  public build(): VulogEventNotifierWebhookDtoV1 {
    return {
      id: this.id,
      fleetId: this.fleetId,
      vehicleId: this.vehicleId,
      boxId: this.boxId,
      date: this.date,
      vehicleName: this.vehicleName,
      vehicleVin: this.vehicleVin,
      vehiclePlate: this.vehiclePlate,
      userId: this.userId,
      tripId: this.tripId,
      isInTrip: this.isInTrip,
      isInZone: this.isInZone,
      trigger: this.trigger,
      origin: this.origin,
      type: this.type,
      vehicleStatus: cloneDeep(this.vehicleStatus),
      position: cloneDeep(this.position),
      body: cloneDeep(this.body),
      qrCode: this.qrCode,
    };
  }
}
