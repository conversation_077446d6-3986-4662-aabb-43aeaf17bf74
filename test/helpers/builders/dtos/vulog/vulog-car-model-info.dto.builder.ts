import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { VulogCarModelInfo } from 'src/model/dtos/vulog-car-static-info/vulog-car-static-info-fields/vulog-car-model-info';
import {
  aBoolean,
  aDateISOString,
  aNumber,
  aString,
  aUUID,
} from 'test/helpers/random-data.helper';

export class VulogCarModelInfoDtoBuilder {
  public id: string = aUUID();
  public name: string = CarModelEnum.BlueCar;
  public brand: string = aString();
  public energyType: string = aString();
  public status: string = aString();
  public nbOfSeats: number = aNumber();
  public autonomy: number = aNumber();
  public creationDate: string = aDateISOString();
  public updateDate: string = aDateISOString();
  public description: string = aString();
  public fleetId: string = aUUID();
  public vehicleType: string = aString();
  public transmission: string = aString();
  public outOfServiceRange: string = aString();
  public recoveryRange: string = aString();
  public incentiveThresholds: number[] = [];
  public redistributeVehicleChargedEnoughRange: string = aString();
  public odometerReliable: boolean = aBoolean();

  build(): VulogCarModelInfo {
    return new VulogCarModelInfo(
      this.id,
      this.name,
      this.brand,
      this.energyType,
      this.status,
      this.nbOfSeats,
      this.autonomy,
      this.creationDate,
      this.updateDate,
      this.description,
      this.fleetId,
      this.vehicleType,
      this.transmission,
      this.outOfServiceRange,
      this.recoveryRange,
      this.incentiveThresholds,
      this.redistributeVehicleChargedEnoughRange,
      this.odometerReliable,
    );
  }
}
