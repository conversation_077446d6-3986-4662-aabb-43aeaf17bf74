import { VulogFeatureCollectionDto } from 'src/model/dtos/vulog-feature/vulog-feature-collection.dto';
import { VulogFeatureZoneDto } from 'src/model/dtos/vulog-feature/vulog-feature-zone.dto';
import { VulogFeatureZoneDtoBuilder } from './vulog-feature-zone.dto.builder';

export class VulogFeatureCollectionDtoBuilder {
  type: string;

  features: VulogFeatureZoneDto[] = Array.from({
    length: 5,
  }).map(() => new VulogFeatureZoneDtoBuilder().build());

  build(): VulogFeatureCollectionDto {
    return {
      type: 'FeatureCollection',
      features: this.features,
    };
  }
}
