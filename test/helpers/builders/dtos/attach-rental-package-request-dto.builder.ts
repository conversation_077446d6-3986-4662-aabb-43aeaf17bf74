import { cloneDeep } from 'lodash';
import { AttachRentalPackageRequestDtoV1 } from 'src/controllers/dtos/rental/request/attach-rental-package.v1.request.dto';
import {
  aNumber,
  anUsername,
  aString,
  aUUID,
} from 'test/helpers/random-data.helper';

export class AttachRentalPackageRequestDtoV1Builder {
  constructor(
    public name: string = anUsername(),
    public packageId: string = aUUID(),
    public title: string = anUsername(),
    public duration: number = aNumber(),
    public price: number = aNumber(),
    public hrid: string = aString(),
    public minAllowedBatteryPercentage: number = aNumber(100),
  ) {}

  withMinAllowedBatteryPercentage(
    minAllowedBatteryPercentage: number,
  ): AttachRentalPackageRequestDtoV1Builder {
    this.minAllowedBatteryPercentage = minAllowedBatteryPercentage;

    return this;
  }

  public build(): AttachRentalPackageRequestDtoV1 {
    return cloneDeep(this);
  }
}
