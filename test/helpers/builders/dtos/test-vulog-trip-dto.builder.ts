import { config } from 'src/config';
import { VulogAdditionalTripInfo } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-additional-trip-info';
import { VulogEventInfo } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-event-info';
import { VulogLocation } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-location';
import { VulogZone } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { VulogTripDto } from 'src/model/dtos/vulog-trip/vulog-trip.dto';

import { faker } from '@faker-js/faker';
import { VulogJourneyOrTripId } from 'src/common/tiny-types';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';

export const createTestVulogTripDto = (
  isRental: boolean,
  isReservation: boolean,
  vehicleId?: string,
  startZones?: VulogZone[],
  fleetId?: string,
  tripId?: VulogJourneyOrTripId,
  endZones?: VulogZone[],
  endDate?: VulogDate,
) => {
  if ((isRental && isReservation) || (!isRental && !isReservation)) {
    throw Error(`isRental and isReservation cannot both be the same value`);
  }

  const testVulogTripDto = new VulogTripDto();

  testVulogTripDto.id = tripId?.value || faker.string.uuid();
  testVulogTripDto.tripId = faker.string.uuid();
  testVulogTripDto.fleetId = fleetId || config.vulogConfig.fleetId.value;
  testVulogTripDto.userId = faker.string.uuid();
  testVulogTripDto.profileId = faker.string.uuid();
  testVulogTripDto.profileType = faker.word.noun();
  testVulogTripDto.vehicleId = vehicleId || faker.string.uuid();
  testVulogTripDto.length = faker.number.int({ min: 0, max: 9999999 });
  testVulogTripDto.duration = faker.number.int({ min: 0, max: 9999999 });
  testVulogTripDto.pauseDuration = faker.number.int({ min: 0, max: 9999999 });
  testVulogTripDto.tripDuration = faker.number.int({ min: 0, max: 9999999 });
  testVulogTripDto.bookingDuration = faker.number.int({ min: 0, max: 9999999 });
  testVulogTripDto.drivingDuration = faker.number.int({ min: 0, max: 9999999 });
  testVulogTripDto.date = faker.date.recent().toISOString();
  testVulogTripDto.endDate =
    endDate?.toString() || faker.date.future().toISOString();

  testVulogTripDto.additionalInfo = new VulogAdditionalTripInfo();
  testVulogTripDto.additionalInfo.firstDriveDelayMinutes = String(
    faker.number.int({ min: 0, max: 9999999 }),
  );
  testVulogTripDto.additionalInfo.bookDuration = String(
    faker.number.int({ min: 0, max: 9999999 }),
  );
  testVulogTripDto.additionalInfo.startZones = startZones || [
    new VulogZone(
      faker.string.uuid(),
      faker.number.int({ min: 1, max: 10 }),
      faker.word.noun(),
      faker.datatype.boolean(),
    ),
  ];
  testVulogTripDto.additionalInfo.endZones = endZones || [
    new VulogZone(
      faker.string.uuid(),
      faker.number.int({ min: 1, max: 10 }),
      faker.word.noun(),
      faker.datatype.boolean(),
    ),
  ];
  testVulogTripDto.additionalInfo.isAutolock = faker.datatype.boolean();
  testVulogTripDto.additionalInfo.isCancel = isRental ? false : true;
  testVulogTripDto.additionalInfo.isReleaseOnServer = faker.datatype.boolean();
  testVulogTripDto.additionalInfo.isBilled = faker.datatype.boolean();
  testVulogTripDto.additionalInfo.suspicious = faker.datatype.boolean();
  testVulogTripDto.additionalInfo.startLocation = new VulogLocation(
    faker.location.latitude(),
    faker.location.longitude(),
  );
  testVulogTripDto.additionalInfo.endLocation = new VulogLocation(
    faker.location.latitude(),
    faker.location.longitude(),
  );
  testVulogTripDto.additionalInfo.originCancelJourney = faker.word.noun();
  testVulogTripDto.additionalInfo.ignoreOdometer = faker.datatype.boolean();

  testVulogTripDto.pricingId = faker.string.uuid();
  testVulogTripDto.productIds = [faker.string.uuid()];
  testVulogTripDto.serviceId = faker.string.uuid();
  testVulogTripDto.serviceType = faker.word.noun();
  testVulogTripDto.tripEvents = [
    new VulogEventInfo(
      faker.word.noun(),
      String(faker.date.recent().getTime()),
      '',
    ),
  ];

  return testVulogTripDto;
};
