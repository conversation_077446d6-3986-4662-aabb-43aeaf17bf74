import { plainToInstance } from 'class-transformer';
import { InternalGetRentalPackageV1ResponseDto } from 'src/external/billing/dtos/response/internal.get-rental-packges.v1.response.dto';
import { aNumber, aString, aUUID } from 'test/helpers/random-data.helper';

export class InternalGetRentalPackageV1ResponseDtoBuilder {
  id: string = aUUID();

  hrid?: string = aString();

  name: string = aString();

  minutes: number = aNumber();

  basePrice: number = aNumber();

  build(): InternalGetRentalPackageV1ResponseDto {
    return plainToInstance(InternalGetRentalPackageV1ResponseDto, this);
  }
}
