import { plainToInstance } from 'class-transformer';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { InternalPricingPolicyResponseDtoV1 } from 'src/external/billing/dtos/response/internal.pricing-policy.v1.response.dto';
import { aBoolean, aNumber, aUUID } from 'test/helpers/random-data.helper';

export class InternalPricingPolicyResponseDtoV1Builder {
  id: string = aUUID();

  carModel: CarModelEnum = CarModelEnum.BlueCar;

  subscriptionPlanId: string = aUUID();

  rentalRate: number = aNumber(100);

  isActive: boolean = aBoolean();

  isApproved: boolean = aBoolean();

  withCarModel(
    carMoel: CarModelEnum,
  ): InternalPricingPolicyResponseDtoV1Builder {
    this.carModel = carMoel;

    return this;
  }

  withSubscriptionPlanId(
    subscriptionPlanId: string,
  ): InternalPricingPolicyResponseDtoV1Builder {
    this.subscriptionPlanId = subscriptionPlanId;

    return this;
  }

  withRentalRate(
    rentalRate: number,
  ): InternalPricingPolicyResponseDtoV1Builder {
    this.rentalRate = rentalRate;

    return this;
  }

  build(): InternalPricingPolicyResponseDtoV1 {
    return plainToInstance(InternalPricingPolicyResponseDtoV1, {
      id: this.id,
      carModel: this.carModel,
      subscriptionPlanId: this.subscriptionPlanId,
      rentalRate: this.rentalRate,
      isActive: this.isActive,
      isApproved: this.isApproved,
    });
  }
}
