import { plainToInstance } from 'class-transformer';
import { InternalStationResponseDtoV1 } from 'src/external/station/dtos/response/internal.station-response.v1.response.dto';
import {
  InternalParkingLotResponseDtoV1,
  ParkingLotStatus,
} from '../../../../src/external/station/dtos/response/internal.parking-lot.v1.response.dto';

export class InternalParkingLotV1ResponseDtoBuilder extends InternalParkingLotResponseDtoV1 {
  public status: ParkingLotStatus;
  public station: InternalStationResponseDtoV1;

  withStation(
    station: InternalStationResponseDtoV1,
  ): InternalParkingLotV1ResponseDtoBuilder {
    this.station = station;
    return this;
  }

  build(): InternalParkingLotResponseDtoV1 {
    return plainToInstance(InternalParkingLotResponseDtoV1, {
      station: this.station,
      status: this.status,
    });
  }
}
