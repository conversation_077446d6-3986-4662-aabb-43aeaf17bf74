import { aString } from 'test/helpers/random-data.helper';

export type VulogAimaErrorPayload = { code: string; message: string };

export class VulogAimaErrorResponseDtoBuilder {
  code = aString();

  message = aString();

  // Vulog AiMA could return error as message only
  isStringOnly = false;

  withCode(value: string): VulogAimaErrorResponseDtoBuilder {
    this.code = value;

    return this;
  }

  withMessage(value: string): VulogAimaErrorResponseDtoBuilder {
    this.message = value;

    return this;
  }

  withIsStringOnly(value: boolean) {
    this.isStringOnly = value;

    return this;
  }

  build() {
    return !this.isStringOnly
      ? ({
          code: this.code,
          message: this.message,
        } as VulogAimaErrorPayload)
      : this.message;
  }
}
