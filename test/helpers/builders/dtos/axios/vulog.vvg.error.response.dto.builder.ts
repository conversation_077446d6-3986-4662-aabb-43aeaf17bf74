import { HttpStatus } from '@nestjs/common';
import { aString } from 'test/helpers/random-data.helper';

export type VVGErrorPayload = {
  code: HttpStatus;
  codeStr: string;
  message: string;
};

export class VVGErrorResponseDtoBuilder {
  code = HttpStatus.GONE;
  codeStr = aString();
  message = aString();

  withCode(value: HttpStatus): VVGErrorResponseDtoBuilder {
    this.code = value;

    return this;
  }

  withCodeStr(value: string): VVGErrorResponseDtoBuilder {
    this.codeStr = value;

    return this;
  }

  withMessage(value: string): VVGErrorResponseDtoBuilder {
    this.message = value;

    return this;
  }

  build(): VVGErrorPayload {
    return {
      code: this.code,
      codeStr: this.codeStr,
      message: this.message,
    };
  }
}
