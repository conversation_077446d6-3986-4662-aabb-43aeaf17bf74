import { config } from 'src/config';
import { VulogCatalog } from 'src/model/dtos/vulog-user/vulog-user-services-fields/vulog-catalog';
import { VulogUserProfile } from 'src/model/dtos/vulog-user/vulog-user-services-fields/vulog-user-profile';
import { VulogUserServicesDto } from 'src/model/dtos/vulog-user/vulog-user-services.dto';

import { faker } from '@faker-js/faker';

export const createTestVulogUserServicesDto = (profileId?: string) => {
  const testVulogUserServicesDto: VulogUserServicesDto =
    new VulogUserServicesDto();
  testVulogUserServicesDto.userId = faker.string.uuid();
  testVulogUserServicesDto.catalog = [
    new VulogCatalog(
      faker.string.uuid(),
      faker.word.noun(),
      faker.word.noun(),
      faker.word.noun(),
      config.vulogConfig.fleetId,
      faker.number.int({ min: -1, max: 10 }),
    ),
  ];
  testVulogUserServicesDto.profiles = [
    new VulogUserProfile(profileId ?? faker.string.uuid(), faker.word.noun(), {
      approved: [],
      incomplete: [],
      pending: [],
      rejected: [],
      suspended: [],
      unregistered: [faker.string.uuid()],
    }),
  ];

  return testVulogUserServicesDto;
};
