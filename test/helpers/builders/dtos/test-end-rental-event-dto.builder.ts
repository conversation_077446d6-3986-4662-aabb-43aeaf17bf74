import {
  CarModelInfoEventDto,
  EndRentalSimulationRequestDto,
  RentalPackageSimulationDto,
  StationInfoEventDto,
  SubscriptionInfoEventDto,
  SubscriptionPlanInfoEventDto,
  TripInfoEventDto,
  UserInfoEventDto,
} from 'src/controllers/dtos/rental/request/end-rental-simulation.request.dto';
import { CarModelName } from 'src/logic/car-availability';
import {
  aDateISOString,
  aNumber,
  aString,
  aUUID,
} from 'test/helpers/random-data.helper';

export class TestEndRentalEventDtoBuilder {
  tripInfo: TripInfoEventDto = new TripInfoEventDto({
    id: aUUID(),
    startingTime: aDateISOString(),
    endingTime: aDateISOString(),
    duration: aNumber(),
  });

  carModelInfo: CarModelInfoEventDto = new CarModelInfoEventDto({
    id: aUUID(),
    model: CarModelName.BlueCar,
  });

  userInfo: UserInfoEventDto = new UserInfoEventDto({ id: aUUID() });

  subscriptionInfo: SubscriptionInfoEventDto = new SubscriptionInfoEventDto({
    id: aUUID(),
    subscriptionPlan: new SubscriptionPlanInfoEventDto({
      id: aUUID(),
      name: aString(),
      description: aString(),
    }),
  });

  startingStationInfo: StationInfoEventDto = new StationInfoEventDto({
    id: aUUID(),
    name: aString(),
  });

  endingStationInfo: StationInfoEventDto = new StationInfoEventDto({
    id: aUUID(),
    name: aString(),
  });

  rentalPackageInfo?: RentalPackageSimulationDto;

  withDurationOfTheTrip(durationOfTheTrip: number): this {
    this.tripInfo.duration = durationOfTheTrip;

    return this;
  }

  build(): EndRentalSimulationRequestDto {
    return new EndRentalSimulationRequestDto({
      tripInfo: this.tripInfo,
      carModelInfo: this.carModelInfo,
      userInfo: this.userInfo,
      subscriptionInfo: this.subscriptionInfo,
      startingStationInfo: this.startingStationInfo,
      endingStationInfo: this.endingStationInfo,
      rentalPackageInfo: this.rentalPackageInfo,
    });
  }
}
