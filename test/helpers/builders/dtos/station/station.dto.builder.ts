import { StationDto } from '../../../../../src/model/dtos/station.dto';
import {
  aDateISOString,
  aLatitude,
  aLocationAddress,
  aLongitude,
  aNoun,
  aNumber,
  aUUID,
} from '../../../random-data.helper';

export class StationDtoBuilder {
  constructor(
    public id = aUUID(),
    public sourceId = aNoun(),
    public source = aNoun(),
    public displayName = aLocationAddress(),
    public latitude = aLatitude() + '',
    public longitude = aLongitude() + '',
    public street = aNoun(),
    public postalCode = aNumber(99999) + '',
    public city = aNoun(),
    public zoneId = aUUID(),
    public createdAt = aDateISOString(),
    public updatedAt = aDateISOString(),
  ) {}

  withId(id: string): StationDtoBuilder {
    this.id = id;

    return this;
  }

  withZoneId(zoneId: string): StationDtoBuilder {
    this.zoneId = zoneId;

    return this;
  }

  build(): StationDto {
    const dto = new StationDto();

    dto.id = this.id;
    dto.sourceId = this.sourceId;
    dto.source = this.source;
    dto.displayName = this.displayName;
    dto.latitude = this.latitude;
    dto.longitude = this.longitude;
    dto.street = this.street;
    dto.postalCode = this.postalCode;
    dto.city = this.city;
    dto.zoneId = this.zoneId;
    dto.createdAt = this.createdAt;
    dto.updatedAt = this.updatedAt;

    return dto;
  }
}
