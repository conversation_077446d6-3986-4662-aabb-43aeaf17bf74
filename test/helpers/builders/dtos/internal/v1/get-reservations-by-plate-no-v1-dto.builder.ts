import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayNotEmpty, IsNotEmpty, ValidateNested } from 'class-validator';

export class RentalPlateNumberAndTimeDto {
  plateNumber: string;
  time: Date;
}

export class GetReservationsByPlateNoAndTimeV1Dto {
  @ApiProperty({
    required: true,
    isArray: true,
    type: RentalPlateNumberAndTimeDto,
  })
  @IsNotEmpty()
  @ArrayNotEmpty()
  @ValidateNested()
  @Type(() => RentalPlateNumberAndTimeDto)
  plateNumberAndTxnDate: RentalPlateNumberAndTimeDto[];
}
