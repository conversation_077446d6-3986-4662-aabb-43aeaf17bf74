import { faker } from '@faker-js/faker';
import { plainToInstance } from 'class-transformer';
import { DateTime } from 'luxon';
import {
  InternalStationResponseDtoV1,
  ProviderName,
} from 'src/external/station/dtos/response/internal.station-response.v1.response.dto';
import {
  anEnumValue,
  aNumber,
  aString,
  aUUID,
} from 'test/helpers/random-data.helper';
import { StationStatus } from '../../../../src/external/station/dtos/station-status';

export class InternalStationResponseDtoV1Builder extends InternalStationResponseDtoV1 {
  public readonly id: string = aUUID();
  public readonly sourceId: string = aUUID();
  public source: ProviderName = ProviderName[anEnumValue(ProviderName)];
  public displayName: string = aString();
  public latitude: string = faker.location.latitude().toString();
  public longitude: string = faker.location.longitude().toString();
  public street: string = aString();
  public postalCode = '100001';
  public city: string = aString();
  public zoneId: string = aUUID();
  public capabilities? = null;
  public readonly createdAt?: string = DateTime.now().toISO();
  public updatedAt?: string = DateTime.now().toISO();
  public parkingSpacesAvailable: number = aNumber();
  public totalParkingSpaces: number = aNumber();
  public status: StationStatus = StationStatus.Open;

  withSource(value: ProviderName): InternalStationResponseDtoV1Builder {
    this.source = value;

    return this;
  }

  withZoneId(zoneId: string): InternalStationResponseDtoV1Builder {
    this.zoneId = zoneId;

    return this;
  }

  withParkingSpacesAvailable(
    value: number,
  ): InternalStationResponseDtoV1Builder {
    this.parkingSpacesAvailable = value;

    return this;
  }

  withStatus(status: StationStatus): InternalStationResponseDtoV1Builder {
    this.status = status;
    return this;
  }

  build(): InternalStationResponseDtoV1 {
    return plainToInstance(InternalStationResponseDtoV1, {
      id: this.id,
      sourceId: this.sourceId,
      source: this.source,
      displayName: this.displayName,
      latitude: this.latitude,
      longitude: this.longitude,
      street: this.street,
      postalCode: this.postalCode,
      city: this.city,
      zoneId: this.zoneId,
      capabilities: this.capabilities,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      parkingSpacesAvailable: this.parkingSpacesAvailable,
      totalParkingSpaces: this.totalParkingSpaces,
      status: this.status,
    });
  }
}
