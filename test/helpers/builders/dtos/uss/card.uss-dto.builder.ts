import { CardUssDto } from '../../../../../src/external/user-subscription/dtos/response/card.uss.dto';
import {
  aBoolean,
  aDate,
  aNumber,
  aString,
  aUUID,
} from '../../../random-data.helper';

export class CardUssDtoBuilder {
  constructor(
    private id = aUUID(),
    private payload = aString(),
    private label = aString(),
    private usage = aString(),
    private ownerId = aUUID(),
    private mayUseRental = aBoolean(),
    private createdAt = aDate(),
    private modifiedAt = aDate(),
    private legacyId = aNumber(),
  ) {}

  build(): CardUssDto {
    return new CardUssDto(
      this.id,
      this.payload,
      this.label,
      this.usage,
      this.ownerId,
      this.mayUseRental,
      this.createdAt,
      this.modifiedAt,
      this.legacyId,
    );
  }
}
