import { cloneDeep } from 'lodash';
import { SubscriptionStatus } from 'src/external/user-subscription/constants';
import { InternalUserDetailResponseDtoV1 } from 'src/external/user-subscription/dtos/response/internal.user-detail.v1.response.dto';
import { SubscriptionUssDto } from 'src/external/user-subscription/dtos/response/subscription.uss.dto';
import { aBoolean, anEnumValue, aUUID } from 'test/helpers/random-data.helper';
import { InternalUserDetailResponseDtoV1Builder } from './internal-user-detail.response.v1.dto.builder';

export class SubscriptionUssDtoBuilder extends SubscriptionUssDto {
  constructor(
    public id: string = aUUID(),
    public subscriptionPlanId: string = aUUID(),
    public nextSubscriptionPlanId: string = aUUID(),
    public userId: string = aUUID(),
    public status: SubscriptionStatus = anEnumValue(SubscriptionStatus),
    public isAutoRenewal: boolean = aBoolean(),
    public user: InternalUserDetailResponseDtoV1 = new InternalUserDetailResponseDtoV1Builder().build(),
  ) {
    super();
  }

  withUserId(val: string): SubscriptionUssDtoBuilder {
    this.userId = val;
    return this;
  }

  withUser(val: InternalUserDetailResponseDtoV1): SubscriptionUssDtoBuilder {
    this.user = val;
    this.userId = val.id;
    return this;
  }

  build(): SubscriptionUssDto {
    return cloneDeep(this);
  }
}
