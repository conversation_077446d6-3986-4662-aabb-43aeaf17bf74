import { plainToInstance } from 'class-transformer';
import { DateTime } from 'luxon';
import {
  LanguageCodeAlpha3,
  SubscriptionStatus,
  UserStatus,
  UserTitle,
} from 'src/external/user-subscription/constants';
import {
  InternalUserDetailResponseDtoV1,
  UserSubscriptionDetail,
} from 'src/external/user-subscription/dtos/response/internal.user-detail.v1.response.dto';
import {
  anEmail,
  anEnumValue,
  aString,
  aUUID,
} from 'test/helpers/random-data.helper';
import { CardUssDto } from '../../../../../src/external/user-subscription/dtos/response/card.uss.dto';
import { CardUssDtoBuilder } from './card.uss-dto.builder';
import { UserSubscriptionDetailBuilder } from './user-subscription-detail.builder';

export class InternalUserDetailResponseDtoV1Builder extends InternalUserDetailResponseDtoV1 {
  id: string = aUUID();

  status: UserStatus = UserStatus[anEnumValue(UserStatus)];

  statusLabel: string = this.status.toString();

  firstName = 'Jon';

  lastName = 'Arrin';

  dateOfBirth: string = DateTime.now().minus({ years: 25 }).toISO();

  title: UserTitle = UserTitle[anEnumValue(UserTitle)];

  titleLabel = 'Bing';

  mobileNo = '1234567890';

  email: string = anEmail();

  preferredLanguage: LanguageCodeAlpha3 =
    LanguageCodeAlpha3[anEnumValue(LanguageCodeAlpha3)];

  preferredLanguageLabel = 'Chilling';

  addressLine1: string = aString();

  addressLine2: string = aString();

  postalCode = '10001';

  currentSubscription: UserSubscriptionDetail =
    new UserSubscriptionDetailBuilder().withUserId(this.id).build();

  cards: CardUssDto[] = [new CardUssDtoBuilder().build()];

  withId(id: string): InternalUserDetailResponseDtoV1Builder {
    this.id = id;

    return this;
  }

  withEmail(email: string): InternalUserDetailResponseDtoV1Builder {
    this.email = email;
    return this;
  }

  withUserStatus(value: UserStatus): InternalUserDetailResponseDtoV1Builder {
    this.status = value;
    return this;
  }

  withLatestSubscriptionStatus(
    value: SubscriptionStatus,
  ): InternalUserDetailResponseDtoV1Builder {
    this.currentSubscription.status = value;
    return this;
  }

  build(): InternalUserDetailResponseDtoV1 {
    return plainToInstance(InternalUserDetailResponseDtoV1, {
      id: this.id,
      status: this.status,
      statusLabel: this.statusLabel,
      firstName: this.firstName,
      lastName: this.lastName,
      dateOfBirth: this.dateOfBirth,
      title: this.title,
      titleLabel: this.titleLabel,
      mobileNo: this.mobileNo,
      email: this.email,
      preferredLanguage: this.preferredLanguage,
      preferredLanguageLabel: this.preferredLanguageLabel,
      addressLine1: this.addressLine1,
      addressLine2: this.addressLine2,
      postalCode: this.postalCode,
      currentSubscription: this.currentSubscription,
      cards: this.cards,
    } as InternalUserDetailResponseDtoV1);
  }
}
