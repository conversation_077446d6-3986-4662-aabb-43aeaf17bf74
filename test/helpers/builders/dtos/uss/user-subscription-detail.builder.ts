import { SubscriptionStatus } from '../../../../../src/external/user-subscription/constants';
import { UserSubscriptionDetail } from '../../../../../src/external/user-subscription/dtos/response/internal.user-detail.v1.response.dto';
import {
  aDateISOString,
  anEnumValue,
  aString,
  aUUID,
} from '../../../random-data.helper';

export class UserSubscriptionDetailBuilder {
  constructor(
    private id = aUUID(),
    private subscriptionPlanId = aUUID(),
    private nextSubscriptionPlanId = aUUID(),
    private userId = aUUID(),
    private status = anEnumValue(SubscriptionStatus),
    private statusLabel = aString(),
    private activatedAt = aDateISOString(),
  ) {}

  withUserId(id: string): UserSubscriptionDetailBuilder {
    this.id = id;
    return this;
  }

  build(): UserSubscriptionDetail {
    return new UserSubscriptionDetail(
      this.id,
      this.subscriptionPlanId,
      this.nextSubscriptionPlanId,
      this.userId,
      this.status,
      this.statusLabel,
      this.activatedAt,
    );
  }
}
