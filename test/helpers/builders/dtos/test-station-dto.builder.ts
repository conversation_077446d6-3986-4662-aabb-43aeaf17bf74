import { StationDto } from 'src/model/dtos/station.dto';

import { faker } from '@faker-js/faker';

/**
 * @deprecated Use the class StationDtoBuilder instead
 */
export const createTestStationDto = (data?: { zoneId?: string }) => {
  const newStationDto = new StationDto();
  newStationDto.id = faker.string.uuid();
  newStationDto.sourceId = faker.string.uuid();
  newStationDto.source = 'LEGACY';
  newStationDto.displayName = faker.location.streetAddress();
  newStationDto.latitude = faker.location.latitude().toString();
  newStationDto.longitude = faker.location.longitude().toString();
  newStationDto.street = faker.location.street();
  newStationDto.postalCode = faker.location.zipCode();
  newStationDto.city = faker.location.city();
  newStationDto.zoneId = data?.zoneId ?? faker.string.uuid();
  newStationDto.createdAt = faker.date.recent().toDateString();
  newStationDto.updatedAt = faker.date.recent().toDateString();

  return newStationDto;
};
