import { EndRentalWithQRRequestDtoV1 } from "src/controllers/dtos/rental/request/end-rental-with-qr.request.dto.v1";

export class EndRentalWithQRRequestDtoV1Builder {
  private qrCode: string;

  constructor() {
    // Initialize with a default QR code URL
    this.qrCode = 'https://qrcode-chargingservices.totalenergies.sg/qrcode?emi3=SG*TCB*E001241';
  }

  withQrCode(qrCode: string): EndRentalWithQRRequestDtoV1Builder {
    this.qrCode = qrCode;
    return this;
  }

  build(): EndRentalWithQRRequestDtoV1 {
    const dto = new EndRentalWithQRRequestDtoV1();
    dto.qrCode = this.qrCode;
    return dto;
  }
}
