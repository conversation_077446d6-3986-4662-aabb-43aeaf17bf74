import { DateTime } from 'luxon';
import { ReservationId, VulogJourneyOrTripId } from 'src/common/tiny-types';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { Car } from 'src/logic/car/domain/car';
import { GpsCoordinates } from 'src/model/gps-coordinates';
import { OnGoingRental } from 'src/model/on-going-rental';
import { RentalPackage } from 'src/model/rental-package';
import { Station } from 'src/model/station';
import { CarRealtimeMetadata } from '../../../src/model/rental-info';
import { aBoolean, aMD5, aNumber, aUUID } from '../random-data.helper';
import { CarRealtimeMetadataBuilder } from './car-realtime.metadata.builder';
import { CarBuilder } from './car.builder';
import { RentalPackageBuilder } from './rental-package.builder';
import { StationBuilder } from './station.builder';

export class OnGoingRentalBuilder {
  id: ReservationId = new ReservationId(aUUID());

  vulogTripId: VulogJourneyOrTripId = new VulogJourneyOrTripId(aMD5());

  startDate: VulogDate = new VulogDate(DateTime.now().toISO());

  endDate: VulogDate;

  duration: number = aNumber(100);

  billedDuration: number = aNumber(100);

  distance: number = aNumber(100);

  price: number = aNumber(100);

  startStation: Station = new StationBuilder().build();

  endStation: Station;

  currentStation?: Station;

  currentGpsCoordinates?: GpsCoordinates;

  car: Car = new CarBuilder().build();

  hasStartedTripInVulog: boolean = aBoolean();

  rentalPackage: RentalPackage = new RentalPackageBuilder().build();

  realtimeInfo: CarRealtimeMetadata = new CarRealtimeMetadataBuilder().build();

  withCurrentStation(value: Station): OnGoingRentalBuilder {
    this.currentStation = value;

    return this;
  }

  build(): OnGoingRental {
    return new OnGoingRental({
      id: this.id,
      vulogTripId: this.vulogTripId,
      startDate: this.startDate,
      endDate: this.endDate,
      duration: this.duration,
      billedDuration: this.billedDuration,
      distance: this.distance,
      price: this.price,
      startStation: this.startStation,
      endStation: this.endStation,
      currentStation: this.currentStation,
      currentGpsCoordinates: this.currentGpsCoordinates,
      car: this.car,
      hasStartedTripInVulog: this.hasStartedTripInVulog,
      rentalPackage: this.rentalPackage,
      realtimeInfo: this.realtimeInfo,
    });
  }
}
