import { VulogCityId, VulogFleetId, VulogServiceId } from "src/common/tiny-types";
import { aString, aUUID } from "../random-data.helper";
import { VulogFleetServiceStatus, VulogFleetServiceType, VulogFleetServiceVisibility } from "src/model/dtos/vulog-fleet-service/vulog-fleet-service.dto";
import { FleetService } from "src/model/fleet-service";
import { MapUtils } from "src/common/utils/map.util";

export class FleetServiceBuilder {
  private id = new VulogServiceId(aUUID());
  private name = aString();
  private status = VulogFleetServiceStatus.Active;
  private type = VulogFleetServiceType.Subscription;
  private fleetId = new VulogFleetId(aUUID());
  private cityId = new VulogCityId(aUUID());
  private zoneIds: string[] = [];
  private vehicleIds: string[] = [];
  private maximumBookingTime = 60;
  private visibility = VulogFleetServiceVisibility.Public;
  private maxConcurrentTrips = 1;

  withId(id: VulogServiceId): FleetServiceBuilder {
    this.id = id;
    return this;
  }

  withName(name: string): FleetServiceBuilder {
    this.name = name;
    return this;
  }

  withStatus(status: VulogFleetServiceStatus): FleetServiceBuilder {
    this.status = status;
    return this;
  }

  withType(type: VulogFleetServiceType): FleetServiceBuilder {
    this.type = type;
    return this;
  }

  withFleetId(fleetId: VulogFleetId): FleetServiceBuilder {
    this.fleetId = fleetId;
    return this;
  }

  withCityId(cityId: VulogCityId): FleetServiceBuilder {
    this.cityId = cityId;
    return this;
  }

  withZoneIds(zoneIds: string[]): FleetServiceBuilder {
    this.zoneIds = zoneIds;
    return this;
  }

  withVehicleIds(vehicleIds: string[]): FleetServiceBuilder {
    this.vehicleIds = vehicleIds;
    return this;
  }

  withMaximumBookingTime(maximumBookingTime: number): FleetServiceBuilder {
    this.maximumBookingTime = maximumBookingTime;
    return this;
  }

  withVisibility(visibility: VulogFleetServiceVisibility): FleetServiceBuilder {
    this.visibility = visibility;
    return this;
  }

  withMaxConcurrentTrips(maxConcurrentTrips: number): FleetServiceBuilder {
    this.maxConcurrentTrips = maxConcurrentTrips;
    return this;
  }

  build(): FleetService {
    return {
      id: this.id,
      name: this.name,
      status: this.status,
      type: this.type,
      fleetId: this.fleetId,
      cityId: this.cityId,
      zoneIds: this.zoneIds,
      vehicleIds: this.vehicleIds,
      maximumBookingTime: this.maximumBookingTime,
      visibility: this.visibility,
      maxConcurrentTrips: this.maxConcurrentTrips,
      getZoneIdsAsMap: function() {
        return MapUtils.build(this.zoneIds, (value) => value);
      }
    };
  }
}
