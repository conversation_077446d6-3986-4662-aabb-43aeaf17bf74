import { Test } from '@nestjs/testing';
import { config } from 'src/config';
import { StationExternalService } from 'src/external/station/station.external.service';
import { InternalStationResponseDtoV1Builder } from 'test/helpers/builders/dtos/internal-station.response.v1.dto.builder';
import { aUUID } from 'test/helpers/random-data.helper';

describe('StationExternalService', () => {
  const mock_StationApiService = {
    get: jest.fn(),
  };

  let stationExternalService: StationExternalService;

  beforeAll(async () => {
    const testModule = await Test.createTestingModule({
      providers: [
        StationExternalService,
        {
          provide: config.httpServiceConfig.stationName,
          useValue: mock_StationApiService,
        },
      ],
    }).compile();

    stationExternalService = testModule.get(StationExternalService);
  });

  describe('getStationDetail', () => {
    const internalStationResponseV1Dto =
      new InternalStationResponseDtoV1Builder().build();

    beforeEach(() => {
      mock_StationApiService.get.mockResolvedValueOnce({
        result: internalStationResponseV1Dto,
      });
    });

    it('Check expectaion', async () => {
      const result = await stationExternalService.internalApi.getStationDetail(
        internalStationResponseV1Dto.id,
      );

      expect(result).toEqual(internalStationResponseV1Dto);
    });
  });

  describe('getStationByZones', () => {
    const wantedUuid = aUUID();

    const internalStationResponseV1Dto =
      new InternalStationResponseDtoV1Builder().withZoneId(wantedUuid).build();

    beforeEach(() => {
      mock_StationApiService.get.mockResolvedValueOnce({
        result: [internalStationResponseV1Dto],
      });
    });

    it('Check expectaion', async () => {
      const result = await stationExternalService.internalApi.getStationByZones(
        [wantedUuid, aUUID()],
      );

      expect(result).toEqual([internalStationResponseV1Dto]);
    });
  });

  describe('exist', () => {
    const internalStationResponseV1Dto =
      new InternalStationResponseDtoV1Builder().build();

    beforeEach(() => {
      mock_StationApiService.get.mockResolvedValueOnce({
        result: internalStationResponseV1Dto,
      });
    });

    it('Check expectaion', async () => {
      const result = await stationExternalService.exist(
        internalStationResponseV1Dto.id,
      );

      expect(result).toBe(true);
    });
  });

  describe('existMany', () => {
    const internalStationResponseV1Dto =
      new InternalStationResponseDtoV1Builder().build();

    beforeEach(() => {
      mock_StationApiService.get.mockResolvedValueOnce({
        result: internalStationResponseV1Dto,
      });
    });

    it('Check expectaion', async () => {
      const result = await stationExternalService.existMany([
        internalStationResponseV1Dto.id,
      ]);

      expect(result).toBe(true);
    });
  });
});
