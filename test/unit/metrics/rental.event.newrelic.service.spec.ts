import { Logger } from '@nestjs/common';
import * as newRelicApmAgent from 'newrelic';
import { EndRentalConditionId } from 'src/common/constants/ending-rental-conditions';
import {
  TestingEndRentalCondition,
  TestingEndRentalConditionResult,
} from 'src/model/testing-end-rental.report';
import { aLastName, aUUID } from 'test/helpers/random-data.helper';
import { NewRelicRentalEventService } from '~shared/newrelic/event/event-type/rental.event.newrelic.service';
import {
  NewRelicMetricCarModel,
  NewRelicMetricRentalType,
  NewRelicMetricTrigger,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';

jest.mock('newrelic', () => ({
  recordCustomEvent: jest.fn(),
}));

jest.mock('@nestjs/common', () => ({
  ...jest.requireActual('@nestjs/common'),
  Logger: {
    error: jest.fn(),
  },
}));

function createTestConditionResults(): TestingEndRentalConditionResult[] {
  return [
    new TestingEndRentalConditionResult(
      new TestingEndRentalCondition(
        EndRentalConditionId.CAR_IN_SERVICE_ZONE,
        'Car in Service Zone',
        'Car is currently in Service Zone',
        'Please drive the car to Service Zone',
        () => true,
        false,
      ),
      true,
    ),
    new TestingEndRentalConditionResult(
      new TestingEndRentalCondition(
        EndRentalConditionId.DOORS_LOCKED,
        'Doors locked',
        'The doors were locked properly',
        'Please lock all doors',
        () => true,
        false,
      ),
      false,
    ),
    new TestingEndRentalConditionResult(
      new TestingEndRentalCondition(
        EndRentalConditionId.ENGINE_OFF,
        'Engine is turned off',
        'The engine is turned off',
        'Please turn the engine off',
        () => true,
        false,
      ),
      false,
    ),
  ];
}

describe(`NewRelicRentalEventService`, () => {
  let nrRentalEventService: NewRelicRentalEventService;

  function clearResource() {
    jest.clearAllMocks();
  }

  describe('🔥 Execute `emitStartedAtBlueSG` function successfully', () => {
    const eventType = 'rental';
    const event = 'startedAtBlueSG';

    beforeAll(() => {
      nrRentalEventService = new NewRelicRentalEventService();
    });

    describe('Admin', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
      ];

      test.each(cases)(
        'With parameters: $expectedTrigger, $expectedRentalType, $expectedCarModel',
        async ({ expectedTrigger, expectedRentalType, expectedCarModel }) => {
          nrRentalEventService.emitStartedAtBlueSG(
            expectedTrigger,
            expectedRentalType,
            expectedCarModel,
          );

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            trigger: expectedTrigger,
            rentalType: expectedRentalType,
            carModel: expectedCarModel,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedTrigger: NewRelicMetricTrigger =
          NewRelicMetricTrigger.Admin;
        const expectedRentalType: NewRelicMetricRentalType =
          NewRelicMetricRentalType.Subscription;
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrRentalEventService.emitStartedAtBlueSG(
          expectedTrigger,
          expectedRentalType,
          expectedCarModel,
        );

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });

    describe('Mobile', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
      ];

      test.each(cases)(
        'With parameters: $expectedTrigger, $expectedRentalType, $expectedCarModel',
        async ({ expectedTrigger, expectedRentalType, expectedCarModel }) => {
          nrRentalEventService.emitStartedAtBlueSG(
            expectedTrigger,
            expectedRentalType,
            expectedCarModel,
          );

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            trigger: expectedTrigger,
            rentalType: expectedRentalType,
            carModel: expectedCarModel,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedTrigger: NewRelicMetricTrigger =
          NewRelicMetricTrigger.Admin;
        const expectedRentalType: NewRelicMetricRentalType =
          NewRelicMetricRentalType.Subscription;
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrRentalEventService.emitStartedAtBlueSG(
          expectedTrigger,
          expectedRentalType,
          expectedCarModel,
        );

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });

    describe('RFID', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
      ];

      test.each(cases)(
        'With parameters: $expectedTrigger, $expectedRentalType, $expectedCarModel',
        async ({ expectedTrigger, expectedRentalType, expectedCarModel }) => {
          nrRentalEventService.emitStartedAtBlueSG(
            expectedTrigger,
            expectedRentalType,
            expectedCarModel,
          );

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            trigger: expectedTrigger,
            rentalType: expectedRentalType,
            carModel: expectedCarModel,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedTrigger: NewRelicMetricTrigger =
          NewRelicMetricTrigger.Admin;
        const expectedRentalType: NewRelicMetricRentalType =
          NewRelicMetricRentalType.Subscription;
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrRentalEventService.emitStartedAtBlueSG(
          expectedTrigger,
          expectedRentalType,
          expectedCarModel,
        );

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });
  });

  describe('🔥 Execute `emitStartedAtVulog` function successfully', () => {
    const eventType = 'rental';
    const event = 'startedAtVulog';

    beforeAll(() => {
      nrRentalEventService = new NewRelicRentalEventService();
    });

    describe('Admin', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
      ];

      test.each(cases)(
        'With parameters: $expectedTrigger, $expectedRentalType, $expectedCarModel',
        async ({ expectedTrigger, expectedRentalType, expectedCarModel }) => {
          nrRentalEventService.emitStartedAtVulog(
            expectedTrigger,
            expectedRentalType,
            expectedCarModel,
          );

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            trigger: expectedTrigger,
            rentalType: expectedRentalType,
            carModel: expectedCarModel,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedTrigger: NewRelicMetricTrigger =
          NewRelicMetricTrigger.Admin;
        const expectedRentalType: NewRelicMetricRentalType =
          NewRelicMetricRentalType.Subscription;
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrRentalEventService.emitStartedAtVulog(
          expectedTrigger,
          expectedRentalType,
          expectedCarModel,
        );

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });

    describe('Mobile', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
      ];

      test.each(cases)(
        'With parameters: $expectedTrigger, $expectedRentalType, $expectedCarModel',
        async ({ expectedTrigger, expectedRentalType, expectedCarModel }) => {
          nrRentalEventService.emitStartedAtVulog(
            expectedTrigger,
            expectedRentalType,
            expectedCarModel,
          );

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            trigger: expectedTrigger,
            rentalType: expectedRentalType,
            carModel: expectedCarModel,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedTrigger: NewRelicMetricTrigger =
          NewRelicMetricTrigger.Admin;
        const expectedRentalType: NewRelicMetricRentalType =
          NewRelicMetricRentalType.Subscription;
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrRentalEventService.emitStartedAtVulog(
          expectedTrigger,
          expectedRentalType,
          expectedCarModel,
        );

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });

    describe('RFID', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
      ];

      test.each(cases)(
        'With parameters: $expectedTrigger, $expectedRentalType, $expectedCarModel',
        async ({ expectedTrigger, expectedRentalType, expectedCarModel }) => {
          nrRentalEventService.emitStartedAtVulog(
            expectedTrigger,
            expectedRentalType,
            expectedCarModel,
          );

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            trigger: expectedTrigger,
            rentalType: expectedRentalType,
            carModel: expectedCarModel,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedTrigger: NewRelicMetricTrigger =
          NewRelicMetricTrigger.Admin;
        const expectedRentalType: NewRelicMetricRentalType =
          NewRelicMetricRentalType.Subscription;
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrRentalEventService.emitStartedAtVulog(
          expectedTrigger,
          expectedRentalType,
          expectedCarModel,
        );

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });
  });

  describe('🔥 Execute `emitFailedToSatisfyEndingConditions` function successfully', () => {
    const eventType = 'rental';
    const event = 'failedToSatisfyEndingConditions';

    beforeAll(() => {
      nrRentalEventService = new NewRelicRentalEventService();
    });

    describe('Admin', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
      ];

      test.each(cases)(
        'With parameters: $expectedTrigger, $expectedRentalType, $expectedCarModel',
        async ({
          expectedTrigger,
          expectedRentalType,
          expectedCarModel,
          expectedEndingRentalConditionResults,
          expectedStationName,
          expectedStationId,
        }) => {
          nrRentalEventService.emitFailedToSatisfyEndingConditions(
            expectedTrigger,
            expectedRentalType,
            expectedCarModel,
            expectedEndingRentalConditionResults,
            expectedStationName,
            expectedStationId,
          );

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            trigger: expectedTrigger,
            rentalType: expectedRentalType,
            carModel: expectedCarModel,
            carInServiceZone: true,
            doorsLocked: false,
            engineOff: false,
            stationName: expectedStationName,
            stationId: expectedStationId,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedTrigger: NewRelicMetricTrigger =
          NewRelicMetricTrigger.Admin;
        const expectedRentalType: NewRelicMetricRentalType =
          NewRelicMetricRentalType.Subscription;
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;
        const expectedEndingRentalConditionResults =
          createTestConditionResults();
        const expectedStationName: string = aLastName();
        const expectedStationId: string = aUUID();

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrRentalEventService.emitFailedToSatisfyEndingConditions(
          expectedTrigger,
          expectedRentalType,
          expectedCarModel,
          expectedEndingRentalConditionResults,
          expectedStationName,
          expectedStationId,
        );

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });

    describe('Mobile', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
      ];

      test.each(cases)(
        'With parameters: $expectedTrigger, $expectedRentalType, $expectedCarModel',
        async ({
          expectedTrigger,
          expectedRentalType,
          expectedCarModel,
          expectedEndingRentalConditionResults,
          expectedStationId,
          expectedStationName,
        }) => {
          nrRentalEventService.emitFailedToSatisfyEndingConditions(
            expectedTrigger,
            expectedRentalType,
            expectedCarModel,
            expectedEndingRentalConditionResults,
            expectedStationName,
            expectedStationId,
          );

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            trigger: expectedTrigger,
            rentalType: expectedRentalType,
            carModel: expectedCarModel,
            carInServiceZone: true,
            doorsLocked: false,
            engineOff: false,
            stationName: expectedStationName,
            stationId: expectedStationId,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedTrigger: NewRelicMetricTrigger =
          NewRelicMetricTrigger.Admin;
        const expectedRentalType: NewRelicMetricRentalType =
          NewRelicMetricRentalType.Subscription;
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;
        const expectedEndingRentalConditionResults =
          createTestConditionResults();
        const expectedStationName: string = aLastName();
        const expectedStationId: string = aUUID();

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrRentalEventService.emitFailedToSatisfyEndingConditions(
          expectedTrigger,
          expectedRentalType,
          expectedCarModel,
          expectedEndingRentalConditionResults,
          expectedStationName,
          expectedStationId,
        );

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });
    });

    describe('RFID', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
          expectedEndingRentalConditionResults: createTestConditionResults(),
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
      ];

      test.each(cases)(
        'With parameters: $expectedTrigger, $expectedRentalType, $expectedCarModel',
        async ({
          expectedTrigger,
          expectedRentalType,
          expectedCarModel,
          expectedEndingRentalConditionResults,
          expectedStationId,
          expectedStationName,
        }) => {
          nrRentalEventService.emitFailedToSatisfyEndingConditions(
            expectedTrigger,
            expectedRentalType,
            expectedCarModel,
            expectedEndingRentalConditionResults,
            expectedStationName,
            expectedStationId,
          );

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            trigger: expectedTrigger,
            rentalType: expectedRentalType,
            carModel: expectedCarModel,
            carInServiceZone: true,
            doorsLocked: false,
            engineOff: false,
            stationName: expectedStationName,
            stationId: expectedStationId,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedTrigger: NewRelicMetricTrigger =
          NewRelicMetricTrigger.Admin;
        const expectedRentalType: NewRelicMetricRentalType =
          NewRelicMetricRentalType.Subscription;
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;
        const expectedEndingRentalConditionResults =
          createTestConditionResults();
        const expectedStationName: string = aLastName();
        const expectedStationId: string = aUUID();

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrRentalEventService.emitFailedToSatisfyEndingConditions(
          expectedTrigger,
          expectedRentalType,
          expectedCarModel,
          expectedEndingRentalConditionResults,
          expectedStationName,
          expectedStationId,
        );

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });
  });

  describe('🔥 Execute `emitTriggeredWaitingForInvoicing` function successfully', () => {
    const eventType = 'rental';
    const event = 'triggeredWaitingForInvoicing';

    beforeAll(() => {
      nrRentalEventService = new NewRelicRentalEventService();
    });

    describe('Admin', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
      ];

      test.each(cases)(
        'With parameters: $expectedTrigger, $expectedRentalType, $expectedCarModel',
        async ({ expectedTrigger, expectedRentalType, expectedCarModel }) => {
          nrRentalEventService.emitTriggeredWaitingForInvoicing(
            expectedTrigger,
            expectedRentalType,
            expectedCarModel,
          );

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            trigger: expectedTrigger,
            rentalType: expectedRentalType,
            carModel: expectedCarModel,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedTrigger: NewRelicMetricTrigger =
          NewRelicMetricTrigger.Admin;
        const expectedRentalType: NewRelicMetricRentalType =
          NewRelicMetricRentalType.Subscription;
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrRentalEventService.emitTriggeredWaitingForInvoicing(
          expectedTrigger,
          expectedRentalType,
          expectedCarModel,
        );

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });

    describe('Mobile', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
      ];

      test.each(cases)(
        'With parameters: $expectedTrigger, $expectedRentalType, $expectedCarModel',
        async ({ expectedTrigger, expectedRentalType, expectedCarModel }) => {
          nrRentalEventService.emitTriggeredWaitingForInvoicing(
            expectedTrigger,
            expectedRentalType,
            expectedCarModel,
          );

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            trigger: expectedTrigger,
            rentalType: expectedRentalType,
            carModel: expectedCarModel,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedTrigger: NewRelicMetricTrigger =
          NewRelicMetricTrigger.Admin;
        const expectedRentalType: NewRelicMetricRentalType =
          NewRelicMetricRentalType.Subscription;
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrRentalEventService.emitTriggeredWaitingForInvoicing(
          expectedTrigger,
          expectedRentalType,
          expectedCarModel,
        );

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });

    describe('RFID', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
      ];

      test.each(cases)(
        'With parameters: $expectedTrigger, $expectedRentalType, $expectedCarModel',
        async ({ expectedTrigger, expectedRentalType, expectedCarModel }) => {
          nrRentalEventService.emitTriggeredWaitingForInvoicing(
            expectedTrigger,
            expectedRentalType,
            expectedCarModel,
          );

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            trigger: expectedTrigger,
            rentalType: expectedRentalType,
            carModel: expectedCarModel,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedTrigger: NewRelicMetricTrigger =
          NewRelicMetricTrigger.Admin;
        const expectedRentalType: NewRelicMetricRentalType =
          NewRelicMetricRentalType.Subscription;
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrRentalEventService.emitTriggeredWaitingForInvoicing(
          expectedTrigger,
          expectedRentalType,
          expectedCarModel,
        );

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });
  });

  describe('🔥 Execute `emitEndedByBlueSG` function successfully', () => {
    const eventType = 'rental';
    const event = 'endedByBlueSG';

    beforeAll(() => {
      nrRentalEventService = new NewRelicRentalEventService();
    });

    describe('Only', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
        {
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
          expectedStationName: aLastName(),
          expectedStationId: aUUID(),
        },
      ];

      test.each(cases)(
        'With parameters: $expectedTrigger, $expectedRentalType, $expectedCarModel',
        async ({
          expectedRentalType,
          expectedCarModel,
          expectedStationId,
          expectedStationName,
        }) => {
          nrRentalEventService.emitEndedByBlueSG(
            expectedRentalType,
            expectedCarModel,
            expectedStationName,
            expectedStationId,
          );

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            rentalType: expectedRentalType,
            carModel: expectedCarModel,
            stationName: expectedStationName,
            stationId: expectedStationId,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedRentalType: NewRelicMetricRentalType =
          NewRelicMetricRentalType.Subscription;
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;
        const expectedStationName: string = aLastName();
        const expectedStationId: string = aUUID();

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrRentalEventService.emitEndedByBlueSG(
          expectedRentalType,
          expectedCarModel,
          expectedStationName,
          expectedStationId,
        );

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });
  });

  describe('🔥 Execute `emitEndedByAiMA` function successfully', () => {
    const eventType = 'rental';
    const event = 'endedByAiMA';

    beforeAll(() => {
      nrRentalEventService = new NewRelicRentalEventService();
    });

    describe('Only', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
        {
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
      ];

      test.each(cases)(
        'With parameters: $expectedRentalType, $expectedCarModel',
        async ({ expectedRentalType, expectedCarModel }) => {
          nrRentalEventService.emitEndedByAiMA(
            expectedRentalType,
            expectedCarModel,
          );

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            rentalType: expectedRentalType,
            carModel: expectedCarModel,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedRentalType: NewRelicMetricRentalType =
          NewRelicMetricRentalType.Subscription;
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrRentalEventService.emitEndedByAiMA(
          expectedRentalType,
          expectedCarModel,
        );

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });
  });

  describe('🔥 Execute `emitRated` function successfully', () => {
    const eventType = 'rental';
    const event = 'rated';

    beforeAll(() => {
      nrRentalEventService = new NewRelicRentalEventService();
    });

    describe('Only', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedRentalType: NewRelicMetricRentalType.RentalPackage,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
        {
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedRentalType: NewRelicMetricRentalType.Subscription,
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
      ];

      test.each(cases)(
        'With parameters: $expectedRentalType, $expectedCarModel',
        async ({ expectedRentalType, expectedCarModel }) => {
          nrRentalEventService.emitRated(expectedRentalType, expectedCarModel);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            rentalType: expectedRentalType,
            carModel: expectedCarModel,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedRentalType: NewRelicMetricRentalType =
          NewRelicMetricRentalType.Subscription;
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrRentalEventService.emitRated(expectedRentalType, expectedCarModel);

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });
  });

  describe('🔥 Execute condition validation events successfully', () => {
    const eventType = 'rental';
    const plateNo = 'ABC123';
    const userId = 'user123';
    const qrCode = 'qr123';

    beforeAll(() => {
      nrRentalEventService = new NewRelicRentalEventService();
    });

    beforeEach(() => {
      clearResource();
    });

    const validationCases = [
      {
        methodName: 'emitCarInServiceZoneValidation',
        eventName: 'carInServiceZoneValidation',
        isValid: true,
      },
      {
        methodName: 'emitDoorsAndWindowsClosedValidation',
        eventName: 'doorsAndWindowsClosedValidation',
        isValid: false,
      },
      {
        methodName: 'emitDoorsLockedValidation',
        eventName: 'doorsLockedValidation',
        isValid: true,
      },
      {
        methodName: 'emitEngineOffValidation',
        eventName: 'engineOffValidation',
        isValid: false,
      },
      {
        methodName: 'emitCarPluggedValidation',
        eventName: 'carPluggedValidation',
        isValid: true,
      },
      {
        methodName: 'emitParkingBrakeValidation',
        eventName: 'parkingBrakeValidation',
        isValid: false,
      },
    ];

    test.each(validationCases)(
      'Should emit $eventName event successfully',
      async ({ methodName, eventName, isValid }) => {
        nrRentalEventService[methodName](plateNo, userId, qrCode, isValid);

        expect(
          (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
        ).toEqual(eventType);

        expect(
          (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
        ).toEqual({
          event: `${eventType}.${eventName}`,
          env: process.env.NODE_ENV,
          plateNo,
          userId,
          qrCode,
          isValid,
        });
      },
    );

    it('❌ Should handle unexpected error in validation event', () => {
      (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(() => {
        throw new Error('dummy');
      });

      nrRentalEventService.emitCarInServiceZoneValidation(
        plateNo,
        userId,
        qrCode,
        true,
      );

      expect((Logger as any).error.mock.calls[0][1]).toEqual({
        eventType: eventType,
        event: 'rental.carInServiceZoneValidation',
        env: process.env.NODE_ENV,
        errorDetails: new Error('dummy'),
      });
    });
  });

  describe('🔥 Execute QR-related events successfully', () => {
    const eventType = 'rental';
    const plateNo = 'ABC123';
    const userId = 'user123';
    const qrCode = 'qr123';

    beforeAll(() => {
      nrRentalEventService = new NewRelicRentalEventService();
    });

    beforeEach(() => {
      clearResource();
    });

    describe('Basic QR events', () => {
      const basicQREventCases = [
        {
          methodName: 'emitQREndRentalAttempt',
          eventName: 'qrEndRentalAttempt',
        },
        {
          methodName: 'emitQREndRentalSuccess',
          eventName: 'qrEndRentalSuccess',
        },
        {
          methodName: 'emitQREndRentalError',
          eventName: 'qrEndRentalError',
        },
      ];

      test.each(basicQREventCases)(
        'Should emit $eventName event successfully',
        async ({ methodName, eventName }) => {
          nrRentalEventService[methodName](plateNo, userId, qrCode);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(eventType);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${eventName}`,
            env: process.env.NODE_ENV,
            plateNo,
            userId,
            qrCode,
          });
        },
      );
    });

    describe('QR validation events', () => {
      const validationQREventCases = [
        {
          methodName: 'emitQRFormatValidation',
          eventName: 'qrFormatValidation',
        },
        {
          methodName: 'emitQRStationValidation',
          eventName: 'qrStationValidation',
        },
        {
          methodName: 'emitQRParkingSpaceValidation',
          eventName: 'qrParkingSpaceValidation',
        },
      ];

      test.each(validationQREventCases)(
        'Should emit $eventName event successfully',
        async ({ methodName, eventName }) => {
          nrRentalEventService[methodName](plateNo, userId, qrCode, true);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(eventType);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${eventName}`,
            env: process.env.NODE_ENV,
            plateNo,
            userId,
            qrCode,
            isValid: true,
          });
        },
      );
    });

    it('❌ Should handle unexpected error in QR event', () => {
      (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(() => {
        throw new Error('dummy');
      });

      nrRentalEventService.emitQREndRentalAttempt(plateNo, userId, qrCode);

      expect((Logger as any).error.mock.calls[0][1]).toEqual({
        eventType: eventType,
        event: 'rental.qrEndRentalAttempt',
        env: process.env.NODE_ENV,
        errorDetails: new Error('dummy'),
      });
    });
  });
});
