import { Logger } from '@nestjs/common';
import * as newRelicApmAgent from 'newrelic';
import { NewRelicReservationEventService } from '~shared/newrelic/event/event-type/reservation.event.newrelic.service';
import {
  NewRelicMetricCarModel,
  NewRelicMetricStepInFlow,
  NewRelicMetricTrigger,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';

jest.mock('newrelic', () => ({
  recordCustomEvent: jest.fn(),
}));

jest.mock('@nestjs/common', () => ({
  ...jest.requireActual('@nestjs/common'),
  Logger: {
    error: jest.fn(),
  },
}));

describe(`NewRelicReservationEventService`, () => {
  let nrReservationEventService: NewRelicReservationEventService;

  function clearResource() {
    jest.clearAllMocks();
  }

  describe('🔥 Execute `emitAllocated` function successfully', () => {
    const eventType = 'reservation';
    const event = 'allocated';

    beforeAll(() => {
      nrReservationEventService = new NewRelicReservationEventService();
    });

    describe('Only', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
      ];

      test.each(cases)(
        'With parameters: $expectedCarModel',
        async ({ expectedCarModel }) => {
          nrReservationEventService.emitAllocated(expectedCarModel);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            carModel: expectedCarModel,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrReservationEventService.emitAllocated(expectedCarModel);

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });
  });

  describe('🔥 Execute `emitSwitched` function successfully', () => {
    const eventType = 'reservation';
    const event = 'switched';

    beforeAll(() => {
      nrReservationEventService = new NewRelicReservationEventService();
    });

    describe('Only', () => {
      beforeEach(() => {
        clearResource();
      });

      it('With no parameters', () => {
        nrReservationEventService.emitSwitched();

        expect(
          (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
        ).toEqual(`${eventType}`);

        expect(
          (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
        ).toEqual({
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
        });
      });

      it('❌ Unexpected error', () => {
        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrReservationEventService.emitSwitched();

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });
  });

  describe('🔥 Execute `emitCanceled` function successfully', () => {
    const eventType = 'reservation';
    const event = 'canceled';

    beforeAll(() => {
      nrReservationEventService = new NewRelicReservationEventService();
    });

    describe('Only', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedTrigger: NewRelicMetricTrigger.Admin,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.Mobile,
        },
        {
          expectedTrigger: NewRelicMetricTrigger.RFID,
        },
      ];

      test.each(cases)(
        'With parameters: $expectedTrigger',
        async ({ expectedTrigger }) => {
          nrReservationEventService.emitCanceled(expectedTrigger);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            trigger: expectedTrigger,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedTrigger: NewRelicMetricTrigger =
          NewRelicMetricTrigger.RFID;

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrReservationEventService.emitCanceled(expectedTrigger);

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });
  });

  describe('🔥 Execute `emitExpired` function successfully', () => {
    const eventType = 'reservation';
    const event = 'expired';

    beforeAll(() => {
      nrReservationEventService = new NewRelicReservationEventService();
    });

    describe('Only', () => {
      beforeEach(() => {
        clearResource();
      });

      const cases = [
        {
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
        },
        {
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
        },
        {
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
        },
      ];

      test.each(cases)(
        'With parameters: $expectedCarModel',
        async ({ expectedCarModel }) => {
          nrReservationEventService.emitExpired(expectedCarModel);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            carModel: expectedCarModel,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrReservationEventService.emitExpired(expectedCarModel);

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });
  });

  describe('🔥 Execute `emitTerminatedByTechnicalError` function successfully', () => {
    const eventType = 'reservation';
    const event = 'terminatedByTechnicalError';

    beforeAll(() => {
      nrReservationEventService = new NewRelicReservationEventService();
    });

    describe('Only', () => {
      beforeEach(() => {
        clearResource();
      });

      const fullSteps = [
        NewRelicMetricStepInFlow.AllocateReservation,
        NewRelicMetricStepInFlow.CancelReservation,
        NewRelicMetricStepInFlow.DisableCar,
        NewRelicMetricStepInFlow.EndRental,
        NewRelicMetricStepInFlow.LockAndImmobilize,
        NewRelicMetricStepInFlow.StartRental,
        NewRelicMetricStepInFlow.StationAttachment,
        NewRelicMetricStepInFlow.StationDetachment,
        NewRelicMetricStepInFlow.TerminateTrip,
        NewRelicMetricStepInFlow.UnlockAndMobilize,
      ];

      const blueCarCases = fullSteps.map((step) => {
        return {
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
          expectedStepInFlow: step,
        };
      });

      const opelCases = fullSteps.map((step) => {
        return {
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
          expectedStepInFlow: step,
        };
      });

      const noCarModelCases = fullSteps.map((step) => {
        return {
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
          expectedStepInFlow: step,
        };
      });

      const cases = [...blueCarCases, ...opelCases, ...noCarModelCases];

      test.each(cases)(
        'With parameters: $expectedCarModel $expectedStepInFlow',
        async ({ expectedCarModel, expectedStepInFlow }) => {
          nrReservationEventService.emitTerminatedByTechnicalError(
            expectedCarModel,
            expectedStepInFlow,
          );

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: `${eventType}.${event}`,
            env: process.env.NODE_ENV,
            carModel: expectedCarModel,
            stepInFlow: expectedStepInFlow,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;
        const expectedStepInFlow: NewRelicMetricStepInFlow =
          NewRelicMetricStepInFlow.AllocateReservation;

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrReservationEventService.emitTerminatedByTechnicalError(
          expectedCarModel,
          expectedStepInFlow,
        );

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });
  });

  describe('🔥 Execute `emitSwitchedFailed` function successfully', () => {
    const eventType = 'reservation';
    const event = 'switchedFailed';

    beforeAll(() => {
      nrReservationEventService = new NewRelicReservationEventService();
    });

    describe('Only', () => {
      beforeEach(() => {
        clearResource();
      });

      it('With no parameters', () => {
        nrReservationEventService.emitSwitchedFailed();

        expect(
          (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
        ).toEqual(`${eventType}`);

        expect(
          (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
        ).toEqual({
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
        });
      });

      it('❌ Unexpected error', () => {
        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrReservationEventService.emitSwitchedFailed();

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: `${eventType}.${event}`,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });
  });
});
