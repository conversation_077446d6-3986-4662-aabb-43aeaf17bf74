import { HttpStatus, Logger } from '@nestjs/common';
import * as newRelicApmAgent from 'newrelic';
import { NewRelicCarConnectionProblemEventService } from '~shared/newrelic/event/event-type/car-connection-problem.event.newrelic.service';
import {
  NewRelicMetricCarConnectionProblemOrigin,
  NewRelicMetricCarModel,
  NewRelicMetricStepInFlow,
} from '~shared/newrelic/metric/enums/newrelic-metric.enum';

jest.mock('newrelic', () => ({
  recordCustomEvent: jest.fn(),
}));

jest.mock('@nestjs/common', () => ({
  ...jest.requireActual('@nestjs/common'),
  Logger: {
    error: jest.fn(),
  },
}));

describe(`NewRelicCarConnectionProblemEventService`, () => {
  let nrCarConnectionProblemEventService: NewRelicCarConnectionProblemEventService;

  function clearResource() {
    jest.clearAllMocks();
  }

  describe('🔥 Execute `emitCarConnectionProblem` function successfully', () => {
    const eventType = 'carConnectionProblem';

    beforeAll(() => {
      nrCarConnectionProblemEventService =
        new NewRelicCarConnectionProblemEventService();
    });

    describe('Only', () => {
      beforeEach(() => {
        clearResource();
      });

      const fullSteps = [
        NewRelicMetricStepInFlow.AllocateReservation,
        NewRelicMetricStepInFlow.CancelReservation,
        NewRelicMetricStepInFlow.DisableCar,
        NewRelicMetricStepInFlow.EndRental,
        NewRelicMetricStepInFlow.LockAndImmobilize,
        NewRelicMetricStepInFlow.StartRental,
        NewRelicMetricStepInFlow.StationAttachment,
        NewRelicMetricStepInFlow.StationDetachment,
        NewRelicMetricStepInFlow.TerminateTrip,
        NewRelicMetricStepInFlow.UnlockAndMobilize,
      ];

      const blueCarCases = fullSteps.map((step) => {
        return {
          expectedCarModel: NewRelicMetricCarModel.Bluecar,
          expectedStepInFlow: step,
          paramHttpStatus: HttpStatus.FORBIDDEN,
          expectedOrigin: NewRelicMetricCarConnectionProblemOrigin.VulogAiMA,
        };
      });

      const opelCases = fullSteps.map((step) => {
        return {
          expectedCarModel: NewRelicMetricCarModel.CorsaE,
          expectedStepInFlow: step,
          paramHttpStatus: HttpStatus.GONE,
          expectedOrigin:
            NewRelicMetricCarConnectionProblemOrigin.VulogVehicleGateway,
        };
      });

      const noCarModelCases = fullSteps.map((step) => {
        return {
          expectedCarModel: NewRelicMetricCarModel.NoCarModel,
          expectedStepInFlow: step,
          paramHttpStatus: HttpStatus.BAD_REQUEST,
          expectedOrigin: NewRelicMetricCarConnectionProblemOrigin.Unknown,
        };
      });

      const cases = [...blueCarCases, ...opelCases, ...noCarModelCases];

      test.each(cases)(
        'With parameters: $expectedCarModel $expectedStepInFlow',
        async ({
          expectedCarModel,
          expectedStepInFlow,
          paramHttpStatus,
          expectedOrigin,
        }) => {
          nrCarConnectionProblemEventService.emitCarConnectionProblem(
            expectedCarModel,
            expectedStepInFlow,
            paramHttpStatus,
          );

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][0],
          ).toEqual(`${eventType}`);

          expect(
            (newRelicApmAgent as any).recordCustomEvent.mock.calls[0][1],
          ).toEqual({
            event: undefined,
            env: process.env.NODE_ENV,
            carModel: expectedCarModel,
            stepInFlow: expectedStepInFlow,
            origin: expectedOrigin,
          });
        },
      );

      it('❌ Unexpected error', () => {
        const expectedCarModel: NewRelicMetricCarModel =
          NewRelicMetricCarModel.CorsaE;
        const expectedStepInFlow: NewRelicMetricStepInFlow =
          NewRelicMetricStepInFlow.AllocateReservation;
        const expectedHttpStatus = HttpStatus.FORBIDDEN;

        (newRelicApmAgent as any).recordCustomEvent.mockImplementationOnce(
          () => {
            throw new Error('dummy');
          },
        );

        nrCarConnectionProblemEventService.emitCarConnectionProblem(
          expectedCarModel,
          expectedStepInFlow,
          expectedHttpStatus,
        );

        expect((Logger as any).error.mock.calls[0][1]).toEqual({
          eventType: eventType,
          event: undefined,
          env: process.env.NODE_ENV,
          errorDetails: new Error('dummy'),
        });

        expect(
          (Logger as any).error.mock.calls[0][1]['errorDetails']['message'],
        ).toEqual('dummy');
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
    });
  });
});
