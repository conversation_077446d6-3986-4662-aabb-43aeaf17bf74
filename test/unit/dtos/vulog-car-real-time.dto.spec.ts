import { plainToInstance } from 'class-transformer';
import { isNotEmptyObject } from 'class-validator';
import { DateTime } from 'luxon';
import {
  CarModel,
  CarPlateNumber,
  VulogCarId,
  VulogJourneyOrTripId,
  VulogServiceId,
} from 'src/common/tiny-types';
import { Latitude } from 'src/common/tiny-types/latitude';
import { Longitude } from 'src/common/tiny-types/longitude';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { VulogMileage } from 'src/common/tiny-types/vulog-mileage.type';
import { bigNumberize } from 'src/common/utils/big-decimal.util';
import { MapUtils } from 'src/common/utils/map.util';
import { config } from 'src/config';
import {
  VulogCarRealTimeDto,
  VulogRealTimeBookingStatus,
  VulogRealTimeVehicleStatus,
} from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VulogFleetServiceType } from 'src/model/dtos/vulog-fleet-service/vulog-fleet-service.dto';
import {
  VulogZone,
  VulogZoneType,
} from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { GpsCoordinates } from 'src/model/gps-coordinates';
import { Location, RealtimeCar } from 'src/model/realtime.car';
import { RentalInfo } from 'src/model/rental-info';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import {
  VulogCarRealTimeDtoBuilder,
  VulogZoneBuilder,
} from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { aMD5, aUUID } from 'test/helpers/random-data.helper';

describe('VulogCarRealTimeDto', () => {
  describe('VulogCarRealTimeDto.isAvailable()', () => {
    const customerUsableFleetZoneIds = [aMD5(), aMD5()];
    const nonCustomerUsableFleetZoneIds = [aMD5()];

    const customerUsableVehicleIds = [aUUID()];

    const mock_vulogCarRealtimeDto = new VulogCarRealTimeDtoBuilder()
      .withId(customerUsableVehicleIds[0])
      .withVehicleStatus(VulogRealTimeVehicleStatus.Available)
      .withBookingStatus(VulogRealTimeBookingStatus.Available)
      .withAutonomy(100)
      .build();

    const mock_currentZones = [
      new VulogZone(
        customerUsableFleetZoneIds[0],
        1,
        VulogZoneType.Allowed,
        true,
      ),
      new VulogZone(
        customerUsableFleetZoneIds[1],
        1,
        VulogZoneType.Allowed,
        false,
      ),
      new VulogZone(
        nonCustomerUsableFleetZoneIds[0],
        1,
        VulogZoneType.Allowed,
        false,
      ),
    ];

    const customerUsableFleetZoneIdMap: Map<string, string> = MapUtils.build(
      customerUsableFleetZoneIds,
      (val) => val,
    );

    const mock_startZones = [new VulogZoneBuilder().build()];
    const mock_endZones = [new VulogZoneBuilder().build()];

    const plainVulogCarRealtimeDto: any = {};

    plainVulogCarRealtimeDto.id = customerUsableVehicleIds[0];
    plainVulogCarRealtimeDto.name = undefined;
    plainVulogCarRealtimeDto.plate = mock_vulogCarRealtimeDto.plate;
    plainVulogCarRealtimeDto.vin = mock_vulogCarRealtimeDto.vin;
    plainVulogCarRealtimeDto.fleetid = config.vulogConfig.fleetId.value;
    plainVulogCarRealtimeDto.boxid = mock_vulogCarRealtimeDto.boxid;
    plainVulogCarRealtimeDto.vehicle_status =
      mock_vulogCarRealtimeDto.vehicle_status;
    plainVulogCarRealtimeDto.zones = mock_currentZones;
    plainVulogCarRealtimeDto.releasable = mock_vulogCarRealtimeDto.releasable;
    plainVulogCarRealtimeDto.box_status = mock_vulogCarRealtimeDto.box_status;
    plainVulogCarRealtimeDto.autonomy = mock_vulogCarRealtimeDto.autonomy;
    plainVulogCarRealtimeDto.autonomy2 = mock_vulogCarRealtimeDto.autonomy2;
    plainVulogCarRealtimeDto.isCharging = mock_vulogCarRealtimeDto.isCharging;
    plainVulogCarRealtimeDto.battery = mock_vulogCarRealtimeDto.battery;
    plainVulogCarRealtimeDto.km = mock_vulogCarRealtimeDto.km;
    plainVulogCarRealtimeDto.speed = mock_vulogCarRealtimeDto.speed;
    plainVulogCarRealtimeDto.location = mock_vulogCarRealtimeDto.location;
    plainVulogCarRealtimeDto.endTripLocation =
      mock_vulogCarRealtimeDto.endTripLocation;
    plainVulogCarRealtimeDto.endZoneIds = mock_endZones.map(
      (zone) => zone.zoneId,
    );
    plainVulogCarRealtimeDto.productIds = mock_vulogCarRealtimeDto.productIds;
    plainVulogCarRealtimeDto.isDoorClosed =
      mock_vulogCarRealtimeDto.isDoorClosed;
    plainVulogCarRealtimeDto.isDoorLocked =
      mock_vulogCarRealtimeDto.isDoorLocked;
    plainVulogCarRealtimeDto.engineOn = mock_vulogCarRealtimeDto.engineOn;
    plainVulogCarRealtimeDto.immobilizerOn =
      mock_vulogCarRealtimeDto.immobilizerOn;
    plainVulogCarRealtimeDto.secureOn = mock_vulogCarRealtimeDto.secureOn;
    plainVulogCarRealtimeDto.spareLockOn = mock_vulogCarRealtimeDto.spareLockOn;
    plainVulogCarRealtimeDto.pileLockOn = mock_vulogCarRealtimeDto.pileLockOn;
    plainVulogCarRealtimeDto.userId = mock_vulogCarRealtimeDto.userId;
    plainVulogCarRealtimeDto.user_locale = mock_vulogCarRealtimeDto.user_locale;
    plainVulogCarRealtimeDto.rfid = mock_vulogCarRealtimeDto.rfid;
    plainVulogCarRealtimeDto.orderId = mock_vulogCarRealtimeDto.orderId;
    plainVulogCarRealtimeDto.gatewayUrl = mock_vulogCarRealtimeDto.gatewayUrl;
    plainVulogCarRealtimeDto.booking_status =
      mock_vulogCarRealtimeDto.booking_status;
    plainVulogCarRealtimeDto.booking_date =
      mock_vulogCarRealtimeDto.booking_date;
    plainVulogCarRealtimeDto.expiresOn = mock_vulogCarRealtimeDto.expiresOn;
    plainVulogCarRealtimeDto.last_active_date =
      mock_vulogCarRealtimeDto.last_active_date;
    plainVulogCarRealtimeDto.last_wakeUp_date =
      mock_vulogCarRealtimeDto.last_wakeUp_date;
    plainVulogCarRealtimeDto.version = mock_vulogCarRealtimeDto.version;
    plainVulogCarRealtimeDto.pricingId = mock_vulogCarRealtimeDto.pricingId;
    plainVulogCarRealtimeDto.start_date = mock_vulogCarRealtimeDto.start_date;
    plainVulogCarRealtimeDto.theorStartDate =
      mock_vulogCarRealtimeDto.theorStartDate;
    plainVulogCarRealtimeDto.theorEndDate =
      mock_vulogCarRealtimeDto.theorEndDate;
    plainVulogCarRealtimeDto.startZones = mock_startZones;
    plainVulogCarRealtimeDto.endZones = mock_endZones;
    plainVulogCarRealtimeDto.disabled = mock_vulogCarRealtimeDto.disabled;
    plainVulogCarRealtimeDto.outOfServiceReason =
      mock_vulogCarRealtimeDto.outOfServiceReason;
    plainVulogCarRealtimeDto.cleanlinessStatus =
      mock_vulogCarRealtimeDto.cleanlinessStatus;
    plainVulogCarRealtimeDto.ignitionOffGeohash =
      mock_vulogCarRealtimeDto.ignitionOffGeohash;
    plainVulogCarRealtimeDto.geohashNeighbours =
      mock_vulogCarRealtimeDto.geohashNeighbours;
    plainVulogCarRealtimeDto.needsRedistribution =
      mock_vulogCarRealtimeDto.needsRedistribution;
    plainVulogCarRealtimeDto.batteryUnderThreshold =
      mock_vulogCarRealtimeDto.batteryUnderThreshold;
    plainVulogCarRealtimeDto.isBeingTowed =
      mock_vulogCarRealtimeDto.isBeingTowed;
    plainVulogCarRealtimeDto.automaticallyEnableVehicleAfterRangeRecovery =
      mock_vulogCarRealtimeDto.automaticallyEnableVehicleAfterRangeRecovery;
    plainVulogCarRealtimeDto.key = mock_vulogCarRealtimeDto.key;
    plainVulogCarRealtimeDto.startTripLocation =
      mock_vulogCarRealtimeDto.startTripLocation;
    plainVulogCarRealtimeDto.preAuthStatus =
      mock_vulogCarRealtimeDto.preAuthStatus;
    plainVulogCarRealtimeDto.profileId = mock_vulogCarRealtimeDto.profileId;
    plainVulogCarRealtimeDto.username = mock_vulogCarRealtimeDto.username;
    plainVulogCarRealtimeDto.preAuthEnabled =
      mock_vulogCarRealtimeDto.preAuthEnabled;
    plainVulogCarRealtimeDto.comeFromApp = mock_vulogCarRealtimeDto.comeFromApp;
    plainVulogCarRealtimeDto.entityId = mock_vulogCarRealtimeDto.entityId;
    plainVulogCarRealtimeDto.profileType = mock_vulogCarRealtimeDto.profileType;
    plainVulogCarRealtimeDto.serviceType = mock_vulogCarRealtimeDto.serviceType;
    plainVulogCarRealtimeDto.serviceId = mock_vulogCarRealtimeDto.serviceId;
    plainVulogCarRealtimeDto.start_mileage =
      mock_vulogCarRealtimeDto.start_mileage;
    plainVulogCarRealtimeDto.lastCleanedDate =
      mock_vulogCarRealtimeDto.lastCleanedDate;

    it('verification', () => {
      expect(
        plainToInstance(
          VulogCarRealTimeDto,
          plainVulogCarRealtimeDto,
        ).isAvailable(
          0,
          customerUsableVehicleIds,
          customerUsableFleetZoneIdMap,
        ),
      ).toEqual(true);
    });
  });

  describe('VulogCarRealTimeDto.toRentalInfo()', () => {
    const customerUsableFleetZoneIds = [aMD5(), aMD5()];
    const nonCustomerUsableFleetZoneIds = [aMD5()];

    const customerUsableVehicleIds = [aUUID()];

    const mock_vulogCarRealtimeDto = new VulogCarRealTimeDtoBuilder()
      .withId(customerUsableVehicleIds[0])
      .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
      .withBookingStatus(VulogRealTimeBookingStatus.Available)
      .withAutonomy(100)
      .build();

    const mock_currentZones = [
      new VulogZone(
        customerUsableFleetZoneIds[0],
        1,
        VulogZoneType.Allowed,
        true,
      ),
      new VulogZone(
        customerUsableFleetZoneIds[1],
        1,
        VulogZoneType.Allowed,
        false,
      ),
      new VulogZone(
        nonCustomerUsableFleetZoneIds[0],
        1,
        VulogZoneType.Allowed,
        false,
      ),
    ];

    const customerUsableFleetZoneIdMap: Map<string, string> = MapUtils.build(
      customerUsableFleetZoneIds,
      (val) => val,
    );

    const expected_validZones: VulogZone[] = mock_currentZones.filter((zone) =>
      customerUsableFleetZoneIdMap.has(zone.zoneId),
    );

    const plainVulogCarRealtimeDto: any = {};

    plainVulogCarRealtimeDto.id = customerUsableVehicleIds[0];
    plainVulogCarRealtimeDto.name = undefined;
    plainVulogCarRealtimeDto.plate = mock_vulogCarRealtimeDto.plate;
    plainVulogCarRealtimeDto.vin = mock_vulogCarRealtimeDto.vin;
    plainVulogCarRealtimeDto.fleetid = config.vulogConfig.fleetId.value;
    plainVulogCarRealtimeDto.boxid = mock_vulogCarRealtimeDto.boxid;
    plainVulogCarRealtimeDto.vehicle_status =
      mock_vulogCarRealtimeDto.vehicle_status;
    plainVulogCarRealtimeDto.zones = mock_currentZones;
    plainVulogCarRealtimeDto.releasable = mock_vulogCarRealtimeDto.releasable;
    plainVulogCarRealtimeDto.box_status = mock_vulogCarRealtimeDto.box_status;
    plainVulogCarRealtimeDto.autonomy = mock_vulogCarRealtimeDto.autonomy;
    plainVulogCarRealtimeDto.autonomy2 = mock_vulogCarRealtimeDto.autonomy2;
    plainVulogCarRealtimeDto.isCharging = mock_vulogCarRealtimeDto.isCharging;
    plainVulogCarRealtimeDto.battery = mock_vulogCarRealtimeDto.battery;
    plainVulogCarRealtimeDto.km = mock_vulogCarRealtimeDto.km;
    plainVulogCarRealtimeDto.speed = mock_vulogCarRealtimeDto.speed;
    plainVulogCarRealtimeDto.location = mock_vulogCarRealtimeDto.location;
    plainVulogCarRealtimeDto.endTripLocation =
      mock_vulogCarRealtimeDto.endTripLocation;
    plainVulogCarRealtimeDto.endZoneIds = mock_currentZones.map(
      (zone) => zone.zoneId,
    );
    plainVulogCarRealtimeDto.productIds = mock_vulogCarRealtimeDto.productIds;
    plainVulogCarRealtimeDto.isDoorClosed =
      mock_vulogCarRealtimeDto.isDoorClosed;
    plainVulogCarRealtimeDto.isDoorLocked =
      mock_vulogCarRealtimeDto.isDoorLocked;
    plainVulogCarRealtimeDto.engineOn = mock_vulogCarRealtimeDto.engineOn;
    plainVulogCarRealtimeDto.immobilizerOn =
      mock_vulogCarRealtimeDto.immobilizerOn;
    plainVulogCarRealtimeDto.secureOn = mock_vulogCarRealtimeDto.secureOn;
    plainVulogCarRealtimeDto.spareLockOn = mock_vulogCarRealtimeDto.spareLockOn;
    plainVulogCarRealtimeDto.pileLockOn = mock_vulogCarRealtimeDto.pileLockOn;
    plainVulogCarRealtimeDto.userId = mock_vulogCarRealtimeDto.userId;
    plainVulogCarRealtimeDto.user_locale = mock_vulogCarRealtimeDto.user_locale;
    plainVulogCarRealtimeDto.rfid = mock_vulogCarRealtimeDto.rfid;
    plainVulogCarRealtimeDto.orderId = mock_vulogCarRealtimeDto.orderId;
    plainVulogCarRealtimeDto.gatewayUrl = mock_vulogCarRealtimeDto.gatewayUrl;
    plainVulogCarRealtimeDto.booking_status =
      mock_vulogCarRealtimeDto.booking_status;
    plainVulogCarRealtimeDto.booking_date =
      mock_vulogCarRealtimeDto.booking_date;
    plainVulogCarRealtimeDto.expiresOn = mock_vulogCarRealtimeDto.expiresOn;
    plainVulogCarRealtimeDto.last_active_date =
      mock_vulogCarRealtimeDto.last_active_date;
    plainVulogCarRealtimeDto.last_wakeUp_date =
      mock_vulogCarRealtimeDto.last_wakeUp_date;
    plainVulogCarRealtimeDto.version = mock_vulogCarRealtimeDto.version;
    plainVulogCarRealtimeDto.pricingId = mock_vulogCarRealtimeDto.pricingId;
    plainVulogCarRealtimeDto.start_date = mock_vulogCarRealtimeDto.start_date;
    plainVulogCarRealtimeDto.theorStartDate =
      mock_vulogCarRealtimeDto.theorStartDate;
    plainVulogCarRealtimeDto.theorEndDate =
      mock_vulogCarRealtimeDto.theorEndDate;
    plainVulogCarRealtimeDto.startZones = mock_currentZones;
    plainVulogCarRealtimeDto.endZones = mock_currentZones;
    plainVulogCarRealtimeDto.disabled = mock_vulogCarRealtimeDto.disabled;
    plainVulogCarRealtimeDto.outOfServiceReason =
      mock_vulogCarRealtimeDto.outOfServiceReason;
    plainVulogCarRealtimeDto.cleanlinessStatus =
      mock_vulogCarRealtimeDto.cleanlinessStatus;
    plainVulogCarRealtimeDto.ignitionOffGeohash =
      mock_vulogCarRealtimeDto.ignitionOffGeohash;
    plainVulogCarRealtimeDto.geohashNeighbours =
      mock_vulogCarRealtimeDto.geohashNeighbours;
    plainVulogCarRealtimeDto.needsRedistribution =
      mock_vulogCarRealtimeDto.needsRedistribution;
    plainVulogCarRealtimeDto.batteryUnderThreshold =
      mock_vulogCarRealtimeDto.batteryUnderThreshold;
    plainVulogCarRealtimeDto.isBeingTowed =
      mock_vulogCarRealtimeDto.isBeingTowed;
    plainVulogCarRealtimeDto.automaticallyEnableVehicleAfterRangeRecovery =
      mock_vulogCarRealtimeDto.automaticallyEnableVehicleAfterRangeRecovery;
    plainVulogCarRealtimeDto.key = mock_vulogCarRealtimeDto.key;
    plainVulogCarRealtimeDto.startTripLocation =
      mock_vulogCarRealtimeDto.startTripLocation;
    plainVulogCarRealtimeDto.preAuthStatus =
      mock_vulogCarRealtimeDto.preAuthStatus;
    plainVulogCarRealtimeDto.profileId = mock_vulogCarRealtimeDto.profileId;
    plainVulogCarRealtimeDto.username = mock_vulogCarRealtimeDto.username;
    plainVulogCarRealtimeDto.preAuthEnabled =
      mock_vulogCarRealtimeDto.preAuthEnabled;
    plainVulogCarRealtimeDto.comeFromApp = mock_vulogCarRealtimeDto.comeFromApp;
    plainVulogCarRealtimeDto.entityId = mock_vulogCarRealtimeDto.entityId;
    plainVulogCarRealtimeDto.profileType = mock_vulogCarRealtimeDto.profileType;
    plainVulogCarRealtimeDto.serviceType = mock_vulogCarRealtimeDto.serviceType;
    plainVulogCarRealtimeDto.serviceId = mock_vulogCarRealtimeDto.serviceId;
    plainVulogCarRealtimeDto.start_mileage =
      mock_vulogCarRealtimeDto.start_mileage;
    plainVulogCarRealtimeDto.lastCleanedDate =
      mock_vulogCarRealtimeDto.lastCleanedDate;

    const expected_rentalInfo = new RentalInfo({
      carId: new VulogCarId(mock_vulogCarRealtimeDto.id),
      sourceCarId: new VulogCarId(mock_vulogCarRealtimeDto.id),
      sourceId: new VulogJourneyOrTripId(mock_vulogCarRealtimeDto.orderId),
      startZones: expected_validZones,
      endZones: expected_validZones,
      currentZones: expected_validZones,
      startDate: mock_vulogCarRealtimeDto.start_date
        ? new VulogDate(mock_vulogCarRealtimeDto.start_date)
        : null,
      endDate: mock_vulogCarRealtimeDto.theorEndDate
        ? new VulogDate(mock_vulogCarRealtimeDto.theorEndDate)
        : null,
      duration: undefined,
      distance: new VulogMileage(
        bigNumberize(mock_vulogCarRealtimeDto.km, 5)
          .minus(bigNumberize(mock_vulogCarRealtimeDto.start_mileage, 5))
          .toNumber(),
      ),
      hasTripStarted: mock_vulogCarRealtimeDto.hasTripStarted(),
      carRealtimeMetadata: {
        battery: mock_vulogCarRealtimeDto.autonomy,
        isEngineOn: mock_vulogCarRealtimeDto.engineOn,
        isCharging: mock_vulogCarRealtimeDto.isCharging,
        isDoorLocked: mock_vulogCarRealtimeDto.isDoorLocked,
        isDoorClosed: mock_vulogCarRealtimeDto.isDoorClosed,
      },
      vuboxId: mock_vulogCarRealtimeDto.boxid,
      currentGpsCoordinates: mock_vulogCarRealtimeDto.location
        ? new GpsCoordinates({
            longitude: new Longitude(
              mock_vulogCarRealtimeDto.location.longitude.toString(),
            ),
            latitude: new Latitude(
              mock_vulogCarRealtimeDto.location.latitude.toString(),
            ),
          })
        : null,
    });

    delete expected_rentalInfo.duration;

    const comparison_rentalInfo = plainToInstance(
      VulogCarRealTimeDto,
      plainVulogCarRealtimeDto,
    ).toRentalInfo(customerUsableFleetZoneIdMap);

    delete comparison_rentalInfo.duration;

    it('verification', () => {
      expect(comparison_rentalInfo).toEqual(expected_rentalInfo);
    });
  });

  describe('VulogCarRealTimeDto.toRealtimeCar()', () => {
    const customerUsableFleetZoneIds = [aMD5(), aMD5()];
    const nonCustomerUsableFleetZoneIds = [aMD5()];

    const customerUsableVehicleIds = [aUUID()];

    const mock_vulogCarRealtimeDto = new VulogCarRealTimeDtoBuilder()
      .withId(customerUsableVehicleIds[0])
      .withVehicleStatus(VulogRealTimeVehicleStatus.InUse)
      .withBookingStatus(VulogRealTimeBookingStatus.Available)
      .withAutonomy(100)
      .build();

    const mock_car = new CarBuilder()
      .withVulogId(new VulogCarId(mock_vulogCarRealtimeDto.id))
      .build();

    const mock_currentZones = [
      new VulogZone(
        customerUsableFleetZoneIds[0],
        1,
        VulogZoneType.Allowed,
        true,
      ),
      new VulogZone(
        customerUsableFleetZoneIds[1],
        1,
        VulogZoneType.Allowed,
        false,
      ),
      new VulogZone(
        nonCustomerUsableFleetZoneIds[0],
        1,
        VulogZoneType.Allowed,
        false,
      ),
    ];

    const customerUsableFleetZoneIdMap: Map<string, string> = MapUtils.build(
      customerUsableFleetZoneIds,
      (val) => val,
    );

    const nonCustomerUsableFleetZoneIdMap: Map<string, string> = MapUtils.build(
      nonCustomerUsableFleetZoneIds,
      (val) => val,
    );

    const expected_validZones: VulogZone[] = mock_currentZones.filter((zone) =>
      customerUsableFleetZoneIdMap.has(zone.zoneId),
    );

    const expected_invalidZones: VulogZone[] = mock_currentZones.filter(
      (zone) => nonCustomerUsableFleetZoneIdMap.has(zone.zoneId),
    );

    const plainVulogCarRealtimeDto: any = {};

    plainVulogCarRealtimeDto.id = customerUsableVehicleIds[0];
    plainVulogCarRealtimeDto.name = undefined;
    plainVulogCarRealtimeDto.plate = mock_vulogCarRealtimeDto.plate;
    plainVulogCarRealtimeDto.vin = mock_vulogCarRealtimeDto.vin;
    plainVulogCarRealtimeDto.fleetid = config.vulogConfig.fleetId.value;
    plainVulogCarRealtimeDto.boxid = mock_vulogCarRealtimeDto.boxid;
    plainVulogCarRealtimeDto.vehicle_status =
      mock_vulogCarRealtimeDto.vehicle_status;
    plainVulogCarRealtimeDto.zones = mock_currentZones;
    plainVulogCarRealtimeDto.releasable = mock_vulogCarRealtimeDto.releasable;
    plainVulogCarRealtimeDto.box_status = mock_vulogCarRealtimeDto.box_status;
    plainVulogCarRealtimeDto.autonomy = mock_vulogCarRealtimeDto.autonomy;
    plainVulogCarRealtimeDto.autonomy2 = mock_vulogCarRealtimeDto.autonomy2;
    plainVulogCarRealtimeDto.isCharging = mock_vulogCarRealtimeDto.isCharging;
    plainVulogCarRealtimeDto.battery = mock_vulogCarRealtimeDto.battery;
    plainVulogCarRealtimeDto.km = mock_vulogCarRealtimeDto.km;
    plainVulogCarRealtimeDto.speed = mock_vulogCarRealtimeDto.speed;
    plainVulogCarRealtimeDto.location = mock_vulogCarRealtimeDto.location;
    plainVulogCarRealtimeDto.endTripLocation =
      mock_vulogCarRealtimeDto.endTripLocation;
    plainVulogCarRealtimeDto.endZoneIds = mock_currentZones.map(
      (zone) => zone.zoneId,
    );
    plainVulogCarRealtimeDto.productIds = mock_vulogCarRealtimeDto.productIds;
    plainVulogCarRealtimeDto.isDoorClosed =
      mock_vulogCarRealtimeDto.isDoorClosed;
    plainVulogCarRealtimeDto.isDoorLocked =
      mock_vulogCarRealtimeDto.isDoorLocked;
    plainVulogCarRealtimeDto.engineOn = mock_vulogCarRealtimeDto.engineOn;
    plainVulogCarRealtimeDto.immobilizerOn =
      mock_vulogCarRealtimeDto.immobilizerOn;
    plainVulogCarRealtimeDto.secureOn = mock_vulogCarRealtimeDto.secureOn;
    plainVulogCarRealtimeDto.spareLockOn = mock_vulogCarRealtimeDto.spareLockOn;
    plainVulogCarRealtimeDto.pileLockOn = mock_vulogCarRealtimeDto.pileLockOn;
    plainVulogCarRealtimeDto.userId = mock_vulogCarRealtimeDto.userId;
    plainVulogCarRealtimeDto.user_locale = mock_vulogCarRealtimeDto.user_locale;
    plainVulogCarRealtimeDto.rfid = mock_vulogCarRealtimeDto.rfid;
    plainVulogCarRealtimeDto.orderId = mock_vulogCarRealtimeDto.orderId;
    plainVulogCarRealtimeDto.gatewayUrl = mock_vulogCarRealtimeDto.gatewayUrl;
    plainVulogCarRealtimeDto.booking_status =
      mock_vulogCarRealtimeDto.booking_status;
    plainVulogCarRealtimeDto.booking_date =
      mock_vulogCarRealtimeDto.booking_date;
    plainVulogCarRealtimeDto.expiresOn = mock_vulogCarRealtimeDto.expiresOn;
    plainVulogCarRealtimeDto.last_active_date =
      mock_vulogCarRealtimeDto.last_active_date;
    plainVulogCarRealtimeDto.last_wakeUp_date =
      mock_vulogCarRealtimeDto.last_wakeUp_date;
    plainVulogCarRealtimeDto.version = mock_vulogCarRealtimeDto.version;
    plainVulogCarRealtimeDto.pricingId = mock_vulogCarRealtimeDto.pricingId;
    plainVulogCarRealtimeDto.start_date = mock_vulogCarRealtimeDto.start_date;
    plainVulogCarRealtimeDto.theorStartDate =
      mock_vulogCarRealtimeDto.theorStartDate;
    plainVulogCarRealtimeDto.theorEndDate =
      mock_vulogCarRealtimeDto.theorEndDate;
    plainVulogCarRealtimeDto.startZones = mock_currentZones;
    plainVulogCarRealtimeDto.endZones = mock_currentZones;
    plainVulogCarRealtimeDto.disabled = mock_vulogCarRealtimeDto.disabled;
    plainVulogCarRealtimeDto.outOfServiceReason =
      mock_vulogCarRealtimeDto.outOfServiceReason;
    plainVulogCarRealtimeDto.cleanlinessStatus =
      mock_vulogCarRealtimeDto.cleanlinessStatus;
    plainVulogCarRealtimeDto.ignitionOffGeohash =
      mock_vulogCarRealtimeDto.ignitionOffGeohash;
    plainVulogCarRealtimeDto.geohashNeighbours =
      mock_vulogCarRealtimeDto.geohashNeighbours;
    plainVulogCarRealtimeDto.needsRedistribution =
      mock_vulogCarRealtimeDto.needsRedistribution;
    plainVulogCarRealtimeDto.batteryUnderThreshold =
      mock_vulogCarRealtimeDto.batteryUnderThreshold;
    plainVulogCarRealtimeDto.isBeingTowed =
      mock_vulogCarRealtimeDto.isBeingTowed;
    plainVulogCarRealtimeDto.automaticallyEnableVehicleAfterRangeRecovery =
      mock_vulogCarRealtimeDto.automaticallyEnableVehicleAfterRangeRecovery;
    plainVulogCarRealtimeDto.key = mock_vulogCarRealtimeDto.key;
    plainVulogCarRealtimeDto.startTripLocation =
      mock_vulogCarRealtimeDto.startTripLocation;
    plainVulogCarRealtimeDto.preAuthStatus =
      mock_vulogCarRealtimeDto.preAuthStatus;
    plainVulogCarRealtimeDto.profileId = mock_vulogCarRealtimeDto.profileId;
    plainVulogCarRealtimeDto.username = mock_vulogCarRealtimeDto.username;
    plainVulogCarRealtimeDto.preAuthEnabled =
      mock_vulogCarRealtimeDto.preAuthEnabled;
    plainVulogCarRealtimeDto.comeFromApp = mock_vulogCarRealtimeDto.comeFromApp;
    plainVulogCarRealtimeDto.entityId = mock_vulogCarRealtimeDto.entityId;
    plainVulogCarRealtimeDto.profileType = mock_vulogCarRealtimeDto.profileType;
    plainVulogCarRealtimeDto.serviceType = mock_vulogCarRealtimeDto.serviceType;
    plainVulogCarRealtimeDto.serviceId = mock_vulogCarRealtimeDto.serviceId;
    plainVulogCarRealtimeDto.start_mileage =
      mock_vulogCarRealtimeDto.start_mileage;
    plainVulogCarRealtimeDto.lastCleanedDate =
      mock_vulogCarRealtimeDto.lastCleanedDate;

    const comparison_rentalInfo = plainToInstance(
      VulogCarRealTimeDto,
      plainVulogCarRealtimeDto,
    ).toRealtimeCar(new CarModel(mock_car.model), customerUsableFleetZoneIdMap);

    let statusLabel;

    switch (mock_vulogCarRealtimeDto.vehicle_status) {
      case VulogRealTimeVehicleStatus.Available:
        statusLabel = 'Available';

        break;

      case VulogRealTimeVehicleStatus.InUse:
        statusLabel = 'Car In Use';

        break;

      case VulogRealTimeVehicleStatus.OutOfService:
        statusLabel = 'Under Maintenance';

        break;

      case VulogRealTimeVehicleStatus.Unsync:
        statusLabel = 'Unsync';

        break;

      default:
        statusLabel = 'Unknown';

        break;
    }

    const expected_allowedStickyZone: VulogZone = expected_validZones.find(
      (zone) => zone.isSticky(),
    );

    const expected_realTimeCar = new RealtimeCar({
      id: new VulogCarId(mock_vulogCarRealtimeDto.id),
      status: mock_vulogCarRealtimeDto.vehicle_status,
      statusLabel,
      model: new CarModel(mock_car.model) || null,
      plate: mock_vulogCarRealtimeDto.plate
        ? new CarPlateNumber(mock_vulogCarRealtimeDto.plate)
        : null,
      vin: mock_vulogCarRealtimeDto.vin,
      sourceId: new VulogCarId(mock_vulogCarRealtimeDto.id),
      energyLevel: mock_vulogCarRealtimeDto.autonomy,
      isCharging: mock_vulogCarRealtimeDto.isCharging,
      isPluggedIn: mock_vulogCarRealtimeDto.isCharging,
      location: isNotEmptyObject(mock_vulogCarRealtimeDto.location)
        ? plainToInstance(Location, {
            latitude: mock_vulogCarRealtimeDto.location.latitude,
            longitude: mock_vulogCarRealtimeDto.location.longitude,
          })
        : null,
      areDoorsAndWindowsClosed: mock_vulogCarRealtimeDto.isDoorClosed,
      alerts: null,
      zoneIds: expected_validZones.map((zone) => zone.vulogZoneId),
      isDoorLocked: mock_vulogCarRealtimeDto.isDoorLocked,
      isDoorClosed: mock_vulogCarRealtimeDto.isDoorClosed,
      isEngineOn: mock_vulogCarRealtimeDto.engineOn,
      vuboxId: mock_vulogCarRealtimeDto.boxid,
      serviceId: mock_vulogCarRealtimeDto.serviceId
        ? new VulogServiceId(mock_vulogCarRealtimeDto.serviceId)
        : null,
      serviceType: mock_vulogCarRealtimeDto.serviceType
        ? (mock_vulogCarRealtimeDto.serviceType as VulogFleetServiceType)
        : null,
      disabled: mock_vulogCarRealtimeDto.disabled,
      outOfServiceReason: mock_vulogCarRealtimeDto.outOfServiceReason,
      allowedStickyZone: expected_allowedStickyZone,
      immobilizerOn: mock_vulogCarRealtimeDto.immobilizerOn,
      lastActiveDate: mock_vulogCarRealtimeDto.last_active_date
        ? DateTime.fromISO(mock_vulogCarRealtimeDto.last_active_date)
        : null,
      invalidZones: expected_invalidZones,
    });

    it('verification', () => {
      expect(comparison_rentalInfo).toEqual(expected_realTimeCar);
    });
  });
});
