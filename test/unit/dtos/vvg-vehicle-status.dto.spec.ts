import { plainToInstance } from 'class-transformer';
import 'reflect-metadata';
import { CarModel, VulogCarId, VulogZoneId } from 'src/common/tiny-types';
import { MapUtils } from 'src/common/utils/map.util';
import { StationDto } from 'src/model/dtos/station.dto';
import {
  VulogZone,
  VulogZoneType,
} from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { VvgVehicleStatusDto } from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';
import { Location } from 'src/model/realtime.car';
import { TelemetryCarInfo } from 'src/model/telemetry-car-info';
import { TelemetryCarStatus } from 'src/model/telemetry-car-status';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { VvgCarSessionBuilder } from 'test/helpers/builders/dtos/vulog/vvg/vvg-car-session.builder';
import { VvgVehicleCardsAndKeyBuilder } from 'test/helpers/builders/dtos/vulog/vvg/vvg-vehicle-cards-and-key.builder';
import { VvgVehicleInformationBuilder } from 'test/helpers/builders/dtos/vulog/vvg/vvg-vehicle-information.builder';
import { VvgVehicleSimCardBuilder } from 'test/helpers/builders/dtos/vulog/vvg/vvg-vehicle-sim-card.builder';
import { VvgVehicleStatusTrackingBuilder } from 'test/helpers/builders/dtos/vulog/vvg/vvg-vehicle-status-tracking.builder';
import { VvgVehicleStatusBuilder } from 'test/helpers/builders/dtos/vulog/vvg/vvg-vehicle-status.builder';
import { VvgVehicleZonesBuilder } from 'test/helpers/builders/dtos/vulog/vvg/vvg-vehicle-zones.builder';
import { aMD5 } from 'test/helpers/random-data.helper';

describe('VvgVehicleStatusDto', () => {
  describe('VvgVehicleStatusDto.toCarStatus() with customer-usable zones and sticky zone', () => {
    const mock_car = new CarBuilder().build();

    const customerUsableFleetZoneIds = [aMD5(), aMD5()];
    const nonCustomerUsableFleetZoneIds = [aMD5()];

    const mock_vvgCarSession = new VvgCarSessionBuilder().build();
    const mock_vvgVehicleInformation = new VvgVehicleInformationBuilder()
      .withVin(mock_car.vin)
      .build();
    const mock_vvgVehicleStatus = new VvgVehicleStatusBuilder().build();
    const mock_vvgVehicleStatusTracking =
      new VvgVehicleStatusTrackingBuilder().build();
    const mock_vvgVehicleCardsAndKeys =
      new VvgVehicleCardsAndKeyBuilder().build();
    const mock_vvgVehicleSimCard = new VvgVehicleSimCardBuilder().build();
    const mock_vvgVehicleZones = new VvgVehicleZonesBuilder()
      .withCurrent([
        new VulogZone(
          customerUsableFleetZoneIds[0],
          1,
          VulogZoneType.Allowed,
          true,
        ),
        new VulogZone(
          customerUsableFleetZoneIds[1],
          1,
          VulogZoneType.Allowed,
          false,
        ),
        new VulogZone(
          nonCustomerUsableFleetZoneIds[0],
          1,
          VulogZoneType.Allowed,
          false,
        ),
      ])
      .build();

    const customerUsableFleetZoneIdMap: Map<string, string> = MapUtils.build(
      customerUsableFleetZoneIds,
      (val) => val,
    );

    const expected_validZones: VulogZone[] =
      mock_vvgVehicleZones.current.filter((zone) =>
        customerUsableFleetZoneIdMap.has(zone.zoneId),
      );
    const expected_allowedStickyZone: VulogZone = expected_validZones.find(
      (zone) => zone.isSticky(),
    );

    const expected_carStatus = new TelemetryCarStatus({
      id: mock_car.vulogId,
      model: new CarModel(mock_car.model),
      plate: mock_car.plate,
      vin: mock_vvgVehicleInformation.vin,
      sourceId: new VulogCarId(mock_car.vulogId.value),
      energyLevel: mock_vvgVehicleStatus.tractionBattery.percentage,
      isCharging: mock_vvgVehicleStatus.charging,
      isPluggedIn:
        mock_vvgVehicleStatus.cablePlugged || mock_vvgVehicleStatus.plugged,
      location: plainToInstance(Location, {
        latitude: mock_vvgVehicleStatusTracking.latitude,
        longitude: mock_vvgVehicleStatusTracking.longitude,
      }),
      areDoorsAndWindowsClosed: mock_vvgVehicleStatus.doorsAndWindowsClosed,
      alerts: null,
      zoneIds: customerUsableFleetZoneIds.map((val) => new VulogZoneId(val)),
      cardsAndKey: mock_vvgVehicleCardsAndKeys,
      mileage: mock_vvgVehicleStatus.mileage,
      simCard: mock_vvgVehicleSimCard,
      engineOn: mock_vvgVehicleStatus.engineOn,
      parkingBrakeOn: mock_vvgVehicleStatus.parkingBrakeOn,
      locked: mock_vvgVehicleStatus.locked,
      immobilizerOn: mock_vvgVehicleStatus.immobilizerOn,
      activeSession: mock_vvgCarSession,
      allowedStickyZone: expected_allowedStickyZone,
    });

    it('Verification', () => {
      expect(
        plainToInstance(VvgVehicleStatusDto, {
          id: mock_car.vulogId.value,
          fleetId: undefined, // Ignore
          boxId: undefined, // Ignore
          hasAlerts: undefined, // Ignore
          sessions: [mock_vvgCarSession],
          information: mock_vvgVehicleInformation,
          status: mock_vvgVehicleStatus,
          tracking: mock_vvgVehicleStatusTracking,
          cardsAndKey: mock_vvgVehicleCardsAndKeys,
          simCard: mock_vvgVehicleSimCard,
          zones: mock_vvgVehicleZones,
        } as VvgVehicleStatusDto).toCarStatus(
          mock_car,
          customerUsableFleetZoneIdMap,
        ),
      ).toEqual(expected_carStatus);
    });
  });

  describe('VvgVehicleStatusDto.toTelemetryCarInfo() with customer-usable zones and sticky zone', () => {
    const mock_car = new CarBuilder().build();

    const customerUsableFleetZoneIds = [aMD5(), aMD5()];
    const nonCustomerUsableFleetZoneIds = [aMD5()];

    const mock_vvgCarSession = new VvgCarSessionBuilder().build();
    const mock_vvgVehicleInformation = new VvgVehicleInformationBuilder()
      .withVin(mock_car.vin)
      .build();
    const mock_vvgVehicleStatus = new VvgVehicleStatusBuilder().build();
    const mock_vvgVehicleStatusTracking =
      new VvgVehicleStatusTrackingBuilder().build();
    const mock_vvgVehicleCardsAndKeys =
      new VvgVehicleCardsAndKeyBuilder().build();
    const mock_vvgVehicleSimCard = new VvgVehicleSimCardBuilder().build();
    const mock_vvgVehicleZones = new VvgVehicleZonesBuilder()
      .withCurrent([
        new VulogZone(
          customerUsableFleetZoneIds[0],
          1,
          VulogZoneType.Allowed,
          true,
        ),
        new VulogZone(
          customerUsableFleetZoneIds[1],
          1,
          VulogZoneType.Allowed,
          false,
        ),
        new VulogZone(
          nonCustomerUsableFleetZoneIds[0],
          1,
          VulogZoneType.Allowed,
          false,
        ),
      ])
      .build();

    const customerUsableFleetZoneIdMap: Map<string, string> = MapUtils.build(
      customerUsableFleetZoneIds,
      (val) => val,
    );

    const expected_validZones: VulogZone[] =
      mock_vvgVehicleZones.current.filter((zone) =>
        customerUsableFleetZoneIdMap.has(zone.zoneId),
      );
    const expected_allowedStickyZone: VulogZone = expected_validZones.find(
      (zone) => zone.isSticky(),
    );

    const expected_carStatus = new TelemetryCarInfo({
      id: mock_car.vulogId,
      model: new CarModel(mock_car.model),
      plate: mock_car.plate,
      vin: mock_vvgVehicleInformation.vin,
      sourceId: new VulogCarId(mock_car.vulogId.value),
      energyLevel: mock_vvgVehicleStatus.tractionBattery.percentage,
      isCharging: mock_vvgVehicleStatus.charging,
      isPluggedIn:
        mock_vvgVehicleStatus.cablePlugged || mock_vvgVehicleStatus.plugged,
      location: plainToInstance(Location, {
        latitude: mock_vvgVehicleStatusTracking.latitude,
        longitude: mock_vvgVehicleStatusTracking.longitude,
      }),
      areDoorsAndWindowsClosed: mock_vvgVehicleStatus.doorsAndWindowsClosed,
      alerts: null,
      zoneIds: customerUsableFleetZoneIds.map((val) => new VulogZoneId(val)),
      cardsAndKey: mock_vvgVehicleCardsAndKeys,
      mileage: mock_vvgVehicleStatus.mileage,
      simCard: mock_vvgVehicleSimCard,
      engineOn: mock_vvgVehicleStatus.engineOn,
      locked: mock_vvgVehicleStatus.locked,
      parkingBrakeOn: false,
      immobilizerOn: mock_vvgVehicleStatus.immobilizerOn,
      currentZones: expected_validZones,
      activeSession: mock_vvgCarSession,
      allowedStickyZone: expected_allowedStickyZone,
      currentStation: null,
    });

    it('Verification', () => {
      expect(
        plainToInstance(VvgVehicleStatusDto, {
          id: mock_car.vulogId.value,
          fleetId: undefined, // Ignore
          boxId: undefined, // Ignore
          hasAlerts: undefined, // Ignore
          sessions: [mock_vvgCarSession],
          information: mock_vvgVehicleInformation,
          status: mock_vvgVehicleStatus,
          tracking: mock_vvgVehicleStatusTracking,
          cardsAndKey: mock_vvgVehicleCardsAndKeys,
          simCard: mock_vvgVehicleSimCard,
          zones: mock_vvgVehicleZones,
        } as VvgVehicleStatusDto).toTelemetryCarInfo(
          mock_car,
          customerUsableFleetZoneIdMap,
        ),
      ).toEqual(expected_carStatus);
    });
  });

  describe('VvgVehicleStatusDto.toTelemetryCarInfo() with customer-usable zones, sticky zone and current location', () => {
    const mock_car = new CarBuilder().build();

    const customerUsableFleetZoneIds = [aMD5(), aMD5()];
    const nonCustomerUsableFleetZoneIds = [aMD5()];

    const mock_vvgCarSession = new VvgCarSessionBuilder().build();
    const mock_vvgVehicleInformation = new VvgVehicleInformationBuilder()
      .withVin(mock_car.vin)
      .build();
    const mock_vvgVehicleStatus = new VvgVehicleStatusBuilder().build();
    const mock_vvgVehicleStatusTracking =
      new VvgVehicleStatusTrackingBuilder().build();
    const mock_vvgVehicleCardsAndKeys =
      new VvgVehicleCardsAndKeyBuilder().build();
    const mock_vvgVehicleSimCard = new VvgVehicleSimCardBuilder().build();
    const mock_vvgVehicleZones = new VvgVehicleZonesBuilder()
      .withCurrent([
        new VulogZone(
          customerUsableFleetZoneIds[0],
          1,
          VulogZoneType.Allowed,
          true,
        ),
        new VulogZone(
          customerUsableFleetZoneIds[1],
          1,
          VulogZoneType.Allowed,
          false,
        ),
        new VulogZone(
          nonCustomerUsableFleetZoneIds[0],
          1,
          VulogZoneType.Allowed,
          false,
        ),
      ])
      .build();
    const mock_knownZoneStationMap: { [zoneId: string]: StationDto } = {
      [customerUsableFleetZoneIds[0]]: new StationDtoBuilder()
        .withZoneId(customerUsableFleetZoneIds[0])
        .build(),
    };

    const customerUsableFleetZoneIdMap: Map<string, string> = MapUtils.build(
      customerUsableFleetZoneIds,
      (val) => val,
    );

    const expected_validZones: VulogZone[] =
      mock_vvgVehicleZones.current.filter((zone) =>
        customerUsableFleetZoneIdMap.has(zone.zoneId),
      );
    const expected_allowedStickyZone: VulogZone = expected_validZones.find(
      (zone) => zone.isSticky(),
    );

    const expected_carStatus = new TelemetryCarInfo({
      id: mock_car.vulogId,
      model: new CarModel(mock_car.model),
      plate: mock_car.plate,
      vin: mock_vvgVehicleInformation.vin,
      sourceId: new VulogCarId(mock_car.vulogId.value),
      energyLevel: mock_vvgVehicleStatus.tractionBattery.percentage,
      isCharging: mock_vvgVehicleStatus.charging,
      isPluggedIn:
        mock_vvgVehicleStatus.cablePlugged || mock_vvgVehicleStatus.plugged,
      location: plainToInstance(Location, {
        latitude: mock_vvgVehicleStatusTracking.latitude,
        longitude: mock_vvgVehicleStatusTracking.longitude,
      }),
      areDoorsAndWindowsClosed: mock_vvgVehicleStatus.doorsAndWindowsClosed,
      alerts: null,
      zoneIds: customerUsableFleetZoneIds.map((val) => new VulogZoneId(val)),
      cardsAndKey: mock_vvgVehicleCardsAndKeys,
      mileage: mock_vvgVehicleStatus.mileage,
      simCard: mock_vvgVehicleSimCard,
      engineOn: mock_vvgVehicleStatus.engineOn,
      locked: mock_vvgVehicleStatus.locked,
      parkingBrakeOn: false,
      immobilizerOn: mock_vvgVehicleStatus.immobilizerOn,
      currentZones: expected_validZones,
      activeSession: mock_vvgCarSession,
      allowedStickyZone: expected_allowedStickyZone,
      currentStation:
        mock_knownZoneStationMap[customerUsableFleetZoneIds[0]].toStation(),
    });

    it('Verification', () => {
      expect(
        plainToInstance(VvgVehicleStatusDto, {
          id: mock_car.vulogId.value,
          fleetId: undefined, // Ignore
          boxId: undefined, // Ignore
          hasAlerts: undefined, // Ignore
          sessions: [mock_vvgCarSession],
          information: mock_vvgVehicleInformation,
          status: mock_vvgVehicleStatus,
          tracking: mock_vvgVehicleStatusTracking,
          cardsAndKey: mock_vvgVehicleCardsAndKeys,
          simCard: mock_vvgVehicleSimCard,
          zones: mock_vvgVehicleZones,
        } as VvgVehicleStatusDto).toTelemetryCarInfo(
          mock_car,
          customerUsableFleetZoneIdMap,
          mock_knownZoneStationMap,
        ),
      ).toEqual(expected_carStatus);
    });
  });
});
