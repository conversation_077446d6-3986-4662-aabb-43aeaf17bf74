import { VulogCarId } from 'src/common/tiny-types';
import { AdminCarsController } from 'src/controllers/admin-cars.controller';
import { CarAdminInfoResponseDto } from 'src/controllers/dtos/car-admin-info.response.dto';
import { createTestVvgCarDto } from 'test/helpers/builders/dtos/test-car-dto.builder';
import { RealtimeCar } from '../../../src/model/realtime.car';

import { AuthorizationService } from '@bluesg-2/bo-auth-guard';
import {
  CustomerAuthModule,
  CustomerJwtGuard,
} from '@bluesg-2/customer-authentication';
import { Test } from '@nestjs/testing';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { VulogTelemetryService } from 'src/logic/telemetry-repository/vulog/vulog-telemetry.service';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';

describe('AdminController', () => {
  const mockCar = createTestVvgCarDto().toRealTimeCar();
  const mockCarUnawake = createTestVvgCarDto().toRealTimeCar();
  const mockCarAwake = createTestVvgCarDto().toRealTimeCar();
  const mockCarsResponseFirstPage = [
    createTestVvgCarDto().toRealTimeCar(),
    createTestVvgCarDto().toRealTimeCar(),
  ];

  const userId = aUUID();

  const stubGetCarInformationFn = () => mockCar;
  const stubCarAwakeFn = (carId: VulogCarId) => {
    if (carId.equals(mockCarUnawake.id)) return { isAwake: false };
    if (carId.equals(mockCarAwake.id)) return { isAwake: true };
  };
  const stubGetAllCarsFn = () => {
    return mockCarsResponseFirstPage;
  };

  const stubEmptySuccessResponseFn = () => ({
    success: true,
  });

  let adminController: AdminCarsController;

  beforeAll(async () => {
    const testModule = await Test.createTestingModule({
      imports: [CustomerAuthModule.register()],
      controllers: [AdminCarsController],
      providers: [
        {
          provide: AuthorizationService,
          useValue: {},
        },
        {
          provide: VulogTelemetryService,
          useValue: {
            getCarInformation: stubGetCarInformationFn,
            wakeUpCar: stubCarAwakeFn,
            getAllCars: stubGetAllCarsFn,
            lockCar: stubEmptySuccessResponseFn,
            unlockCar: stubEmptySuccessResponseFn,
            immobilizeCar: stubEmptySuccessResponseFn,
            mobilizeCar: stubEmptySuccessResponseFn,
            standbyCar: stubCarAwakeFn,
          },
        },
        {
          provide: VulogCarService,
          useValue: {
            getAllStaticCars: stubGetAllCarsFn,
            getCarsInRealtime: stubGetAllCarsFn,
          },
        },
        {
          provide: CustomerJwtGuard,
          useValue: mockCustomerJwtGuard(userId),
        },
      ],
    }).compile();

    adminController = testModule.get<AdminCarsController>(AdminCarsController);
  });

  it('should return the correct response DTO when getting a car information', async () => {
    const response = await adminController.getCarInformationInRealTime(
      mockCarsResponseFirstPage[0].id.value,
    );

    expect(response).toEqual(
      CarAdminInfoResponseDto.from(mockCarsResponseFirstPage[0]),
    );
  });

  it.skip.each([
    [mockCarAwake, true],
    [mockCarUnawake, false],
  ])(
    'should successfully ping a car',
    async (mockCarInput: RealtimeCar, expectedIsCarAwake: boolean) => {
      const response = await adminController.pingCar(mockCarInput.id.value);
      expect(response.isAwake).toEqual(expectedIsCarAwake);
    },
  );

  it.skip('should be able to lock a car', async () => {
    const fnCall = adminController.lockCar(mockCar.id.value);
    expect(fnCall).resolves.not.toThrow();
  });

  it.skip('should be able to unlock a car', async () => {
    const fnCall = adminController.unlockCar(mockCar.id.value);
    expect(fnCall).resolves.not.toThrow();
  });

  it.skip('should be able to immobilize a car', async () => {
    const fnCall = adminController.immobilizeCar(mockCar.id.value);
    expect(fnCall).resolves.not.toThrow();
  });

  it.skip('should be able to mobilize a car', async () => {
    const fnCall = adminController.mobilizeCar(mockCar.id.value);
    expect(fnCall).resolves.not.toThrow();
  });

  it.skip.each([
    [mockCarAwake, true],
    [mockCarUnawake, false],
  ])(
    'should be able to put a car in standby',
    async (mockCarInput: RealtimeCar, expectedIsCarAwake: boolean) => {
      const response = await adminController.standbyCar(mockCarInput.id.value);
      expect(response.isAwake).toEqual(expectedIsCarAwake);
    },
  );
});
