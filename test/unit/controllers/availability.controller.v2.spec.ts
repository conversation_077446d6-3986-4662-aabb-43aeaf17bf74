import { CustomerAuthModule } from '@bluesg-2/customer-authentication';
import { Test } from '@nestjs/testing';
import { AvailabilityControllerV2 } from 'src/controllers/availability.controller.v2';
import { CarAvailabilityResponseDto } from 'src/controllers/dtos/car-availability.response.dto';
import { AvailabilityService } from 'src/logic/availability.service';
import { CarAvailability, CarModelName } from 'src/logic/car-availability';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';

describe('AvailabilityController', () => {
  const mockStation1 = new StationDtoBuilder().build().toStation();
  const mockStation2 = new StationDtoBuilder().build().toStation();
  const mockStation3 = new StationDtoBuilder().build().toStation();

  const mockCarAvailability: CarAvailability = {
    [mockStation1.id.value]: {
      station: mockStation1,
      carAvailability: { [CarModelName.BlueCar]: 2, [CarModelName.CorsaE]: 1 },
    },
    [mockStation2.id.value]: {
      station: mockStation2,
      carAvailability: {},
    },
    [mockStation3.id.value]: {
      station: mockStation3,
      carAvailability: { [CarModelName.CorsaE]: 1 },
    },
  };

  const stubGetCsCarAvailabilityFn = () => mockCarAvailability;

  let availabilityController: AvailabilityControllerV2;

  beforeAll(async () => {
    const testModule = await Test.createTestingModule({
      imports: [CustomerAuthModule.register()],
      controllers: [AvailabilityControllerV2],
      providers: [
        {
          provide: AvailabilityService,
          useValue: {
            getCsCarAvailability: stubGetCsCarAvailabilityFn,
          },
        },
      ],
    }).compile();

    availabilityController = testModule.get<AvailabilityControllerV2>(
      AvailabilityControllerV2,
    );
  });

  it('should return an object with stations and the number of available cars at each station', async () => {
    const response = await availabilityController.getAvailableCars();
    expect(response).toEqual(
      CarAvailabilityResponseDto.from(mockCarAvailability),
    );
  });
});
