import { ReservationController } from 'src/controllers/reservation.controller';
import { ReservationService } from 'src/logic/reservation.service';
import { createTestVulogJourneyDto } from 'test/helpers/builders/dtos/test-vulog-journey-dto.builder';

import {
  CustomerAuthModule,
  CustomerJwtGuard,
  UserInfo,
} from '@bluesg-2/customer-authentication';
import { CorrelationIdService } from '@bluesg-2/monitoring';
import { faker } from '@faker-js/faker';
import { Test } from '@nestjs/testing';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { CarModel, ReservationId } from 'src/common/tiny-types';
import { VulogDate } from 'src/common/tiny-types/vulog-date.type';
import { SwitchCarRequestDtoV1 } from 'src/controllers/dtos/rental/request/switch-car.request.dto.v1';
import { ReservationResponseDtoV1 } from 'src/controllers/dtos/reservation.response.v1.dto';
import { ReservationRequestDto } from 'src/controllers/dtos/reservation/request/reservation.request.dto';
import { BillingExternalService } from 'src/external/billing/billing.external.service';
import { AvailabilityService } from 'src/logic/availability.service';
import { CarsCounter } from 'src/logic/car-availability';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { RentalService } from 'src/logic/rental.service';
import { StationService } from 'src/logic/station/station.service';
import { ReservationInfo } from 'src/model/reservation-info';
import { StartRental } from 'src/model/start-rental';
import { EventBusService } from 'src/shared/event-bus/event-bus.service';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import {
  createTestVulogCarDto,
  createTestVulogCarStaticInfoDto,
} from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestStationDto } from 'test/helpers/builders/dtos/test-station-dto.builder';
import { RealtimeCarBuilder } from 'test/helpers/builders/realtime-car.builder';
import { StationBuilder } from 'test/helpers/builders/station.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aFutureDate, aUUID } from 'test/helpers/random-data.helper';
import { v4 } from 'uuid';
import { CarService } from '../../../src/logic/car/car.service';
import { Reservation } from '../../../src/model/reservation.domain';
import { ReservationEntityBuilder } from '../../helpers/builders/entities/reservation.entity.builder';

describe('ReservationController', () => {
  let reservationController: ReservationController;

  const mock_eventBusService = {
    nonBlockingEmit: jest.fn(),
  };

  const mockReservationResponse = createTestVulogJourneyDto(
    false,
    true,
  ).toReservationInfo();

  const stubAllocateCar = () => mockReservationResponse;

  const stubCancelCurrentReservation = () => [mockReservationResponse];

  const stubGetAllReservationsAndAllocations = () => [mockReservationResponse];

  const stubGetOneNotCanceledNorExpired = () => {
    const fake = new ReservationInfo({
      ...mockReservationResponse,
      expiresAt: new VulogDate(aFutureDate().toISOString()),
      entityId: new ReservationId(aUUID()),
    }).toDomain();
    fake.status = ReservationStatus.Converted;
    return fake;
  };

  const stubSaveOne = () => {
    const fake = new ReservationInfo({
      ...mockReservationResponse,
      entityId: undefined,
    });
    fake.carId = newCar.id;
    return fake;
  };

  const stubSwitchAllocationCar = () => {
    const fake = new ReservationInfo({
      ...mockReservationResponse,
      entityId: undefined,
    });
    fake.carId = newCar.id;
    fake.carModel = newCar.model;
    const car = new RealtimeCarBuilder().build();
    car.id = newCar.id;
    car.model = new CarModel(newCar.model);
    fake.realtimeCar = car;
    return fake;
  };

  const newCar = new CarBuilder().build();

  const userId = aUUID();

  const testVulogJourneyDto = createTestVulogJourneyDto(true, false);

  const testStation = createTestStationDto();

  const testCar = createTestVulogCarDto();

  const testCarStaticInfo = createTestVulogCarStaticInfoDto();

  beforeAll(async () => {
    const testModule = await Test.createTestingModule({
      imports: [CustomerAuthModule.register()],
      controllers: [ReservationController],
      providers: [
        {
          provide: ReservationService,
          useValue: {
            allocateCar: stubAllocateCar,
            cancelCurrentReservation: stubCancelCurrentReservation,
            getOne: stubGetOneNotCanceledNorExpired,
            saveOne: stubSaveOne,
            switchAllocationCar: stubSwitchAllocationCar,
          },
        },
        {
          provide: VulogCarService,
          useValue: {
            getAllReservationsAndAllocations:
              stubGetAllReservationsAndAllocations,
          },
        },
        {
          provide: RentalService,
          useValue: {
            startRental: () =>
              StartRental.from(
                Reservation.from(
                  new ReservationEntityBuilder()
                    .makeCarId(testVulogJourneyDto.vehicleId)
                    .makeVulogTripId(testVulogJourneyDto.id)
                    .makeBsgUserId(userId)
                    .build(),
                ),
                testStation.toStation(),
                testCar.toRealtimeCar().toCar(),
                {
                  station: testStation.toStation(),
                  carAvailability: { [testCarStaticInfo.model.name]: 1 },
                } as CarsCounter,
              ),
          },
        },
        {
          provide: CustomerJwtGuard,
          useValue: mockCustomerJwtGuard(userId),
        },
        {
          provide: BillingExternalService,
          useValue: {},
        },
        {
          provide: AvailabilityService,
          useValue: {},
        },
        {
          provide: StationService,
          useValue: {
            getStationByZones: () => new StationBuilder().build(),
          },
        },
        {
          provide: CarService,
          useValue: {
            getByPlate: () => newCar,
          },
        },
        {
          provide: EventBusService,
          useValue: mock_eventBusService,
        },
        {
          provide: CorrelationIdService,
          useValue: {
            getCorrelationId: () => ({ value: v4() }),
          },
        },
      ],
    }).compile();

    reservationController = testModule.get<ReservationController>(
      ReservationController,
    );
  });

  afterEach(async () => {
    jest.clearAllMocks();
  });

  it.skip('should be able to allocate a car at a staion for a user, and not push message for NS', async () => {
    const testStationId = faker.string.uuid();
    const testUserId = faker.string.uuid();
    const result = await reservationController.allocateCar(
      testStationId,
      new UserInfo(testUserId),
      { carModel: CarModelEnum.OpelCorsaE } as ReservationRequestDto,
    );
    mock_eventBusService.nonBlockingEmit.mockImplementation = () => null;

    const spyOnNonBlockingEmit = jest.spyOn(
      mock_eventBusService,
      'nonBlockingEmit',
    );

    const mappedDto = new ReservationInfo({
      ...mockReservationResponse,
      entityId: undefined,
    });

    expect(result).toEqual(
      ReservationResponseDtoV1.fromReservationInfo(mappedDto),
    );

    expect(spyOnNonBlockingEmit).toBeCalledTimes(0);
  });

  it.skip('should be able to cancel all reservation of an user, and push message for NS', async () => {
    const testUserId = faker.string.uuid();
    const result: Array<ReservationResponseDtoV1> =
      await reservationController.cancelAllReservations(
        new UserInfo(testUserId),
      );
    mock_eventBusService.nonBlockingEmit.mockImplementation = () => null;

    const spyOnNonBlockingEmit = jest.spyOn(
      mock_eventBusService,
      'nonBlockingEmit',
    );

    const mappedDto = new ReservationInfo({
      ...mockReservationResponse,
      entityId: undefined,
    });

    expect(result).toEqual([
      ReservationResponseDtoV1.fromReservationInfo(mappedDto),
    ]);

    expect(spyOnNonBlockingEmit).toBeCalledTimes(0);
  });

  it.skip('should be able to switch station of a reservation and send notification', async () => {
    const testUserId = faker.string.uuid();
    const testTargetSwitchCarDto = new SwitchCarRequestDtoV1();
    testTargetSwitchCarDto.plateNumber = newCar.plate.value;
    testTargetSwitchCarDto.reservationId = 'fake-reservation-id';

    const result: ReservationResponseDtoV1 =
      await reservationController.switchAllocationCar(
        testTargetSwitchCarDto as SwitchCarRequestDtoV1,
        new UserInfo(testUserId),
      );

    mock_eventBusService.nonBlockingEmit.mockImplementation = () => null;

    const spyOnNonBlockingEmit = jest.spyOn(
      mock_eventBusService,
      'nonBlockingEmit',
    );

    const mappedDto = new ReservationInfo({
      ...mockReservationResponse,
      carModel: newCar.model,
      carId: newCar.id,
      entityId: undefined,
    });

    expect(result).toEqual(
      JSON.parse(
        JSON.stringify(ReservationResponseDtoV1.fromReservationInfo(mappedDto)),
      ),
    );

    expect(spyOnNonBlockingEmit).toBeCalledTimes(1);
    expect(spyOnNonBlockingEmit).toHaveBeenCalledWith(
      expect.objectContaining({
        payload: {
          reservation: expect.objectContaining({
            bsgUserId: mappedDto.bsgUserId,
            carId: newCar.id.value,
          }),
        },
      }),
    );
  });
});
