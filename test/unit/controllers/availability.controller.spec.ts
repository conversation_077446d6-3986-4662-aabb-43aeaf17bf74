import {
  CustomerAuthModule,
  CustomerJwtGuard,
} from '@bluesg-2/customer-authentication';
import { Test } from '@nestjs/testing';
import { AvailabilityController } from 'src/controllers/availability.controller';
import { CarAvailabilityResponseDto } from 'src/controllers/dtos/car-availability.response.dto';
import { AvailabilityService } from 'src/logic/availability.service';
import { CarAvailability, CarModelName } from 'src/logic/car-availability';
import { createTestStationDto } from 'test/helpers/builders/dtos/test-station-dto.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';

describe('AvailabilityController', () => {
  const mockStation1 = createTestStationDto().toStation();
  const mockStation2 = createTestStationDto().toStation();
  const mockStation3 = createTestStationDto().toStation();

  const mockCarAvailability: CarAvailability = {
    [mockStation1.id.value]: {
      station: mockStation1,
      carAvailability: { [CarModelName.BlueCar]: 2, [CarModelName.CorsaE]: 1 },
    },
    [mockStation2.id.value]: {
      station: mockStation2,
      carAvailability: {},
    },
    [mockStation3.id.value]: {
      station: mockStation3,
      carAvailability: { [CarModelName.CorsaE]: 1 },
    },
  };

  const userId = aUUID();

  const stubGetCarAvailabilityForAllStationsFn = () => mockCarAvailability;

  let availabilityController: AvailabilityController;

  beforeAll(async () => {
    const testModule = await Test.createTestingModule({
      imports: [CustomerAuthModule.register()],
      controllers: [AvailabilityController],
      providers: [
        {
          provide: AvailabilityService,
          useValue: {
            getCarAvailabilityForAllStations:
              stubGetCarAvailabilityForAllStationsFn,
          },
        },
        {
          provide: CustomerJwtGuard,
          useValue: mockCustomerJwtGuard(userId),
        },
      ],
    }).compile();

    availabilityController = testModule.get<AvailabilityController>(
      AvailabilityController,
    );
  });

  it('should return an object with stations and the number of available cars at each station', async () => {
    const response = await availabilityController.getAvailableCars();
    expect(response).toEqual(
      CarAvailabilityResponseDto.from(mockCarAvailability),
    );
  });
});
