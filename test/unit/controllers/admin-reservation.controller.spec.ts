import { ReservationService } from 'src/logic/reservation.service';
import { createTestVulogJourneyDto } from 'test/helpers/builders/dtos/test-vulog-journey-dto.builder';

import {
  AuthGuardConfigOptions,
  AuthModule,
  JwtGuard,
  PasswordNotChangedGuard,
  RolesGuard,
} from '@bluesg-2/bo-auth-guard';
import { Test } from '@nestjs/testing';
import { config } from 'src/config';
import { AdminReservationController } from 'src/controllers/admin-reservation.contoller';
import { ReservationResponseDto } from 'src/controllers/dtos/reservation/response/reservation.response.dto';
import { BillingExternalService } from 'src/external/billing/billing.external.service';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { mockAdminJwtGuard } from 'test/helpers/admin-jwt-guard.mocker';
import { mockPasswordNotChangedGuard } from 'test/helpers/password-not-changed-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { mockRolesGuard } from 'test/helpers/roles-guard.mocker';

describe('AdminReservationController', () => {
  let adminReservationController: AdminReservationController;

  const mockReservationResponse = createTestVulogJourneyDto(
    false,
    true,
  ).toReservationInfo();

  const stubAllocateCar = () => mockReservationResponse;
  const stubGetAllReservationsAndAllocations = () => [mockReservationResponse];

  const userId = aUUID();

  beforeAll(async () => {
    const testModule = await Test.createTestingModule({
      imports: [
        AuthModule.registerAsync({
          useFactory: async (): Promise<AuthGuardConfigOptions> => ({
            secret: config.jwtConfig.adminSecret,
          }),
        }),
      ],
      controllers: [AdminReservationController],
      providers: [
        {
          provide: ReservationService,
          useValue: {
            allocateCar: stubAllocateCar,
          },
        },
        {
          provide: VulogCarService,
          useValue: {
            getAllFinishedReservationsAndAllocations:
              stubGetAllReservationsAndAllocations,
          },
        },
        {
          provide: JwtGuard,
          useValue: mockAdminJwtGuard(userId),
        },
        {
          provide: PasswordNotChangedGuard,
          useValue: mockPasswordNotChangedGuard(),
        },
        {
          provide: RolesGuard,
          useValue: mockRolesGuard(),
        },
        {
          provide: BillingExternalService,
          useValue: {},
        },
      ],
    }).compile();

    adminReservationController = testModule.get<AdminReservationController>(
      AdminReservationController,
    );
  });

  it('should be able to get all reservations', async () => {
    const result = await adminReservationController.getReservations();

    expect(result).toStrictEqual([
      ReservationResponseDto.from(mockReservationResponse),
    ]);
  });
});
