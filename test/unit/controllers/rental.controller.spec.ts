import {
  CustomerAuthModule,
  CustomerJwtGuard,
  UserInfo,
} from '@bluesg-2/customer-authentication';
import { faker } from '@faker-js/faker';
import { Test } from '@nestjs/testing';
import { RentalResponseDto } from 'src/controllers/dtos/rental.response.dto';
import { RentalController } from 'src/controllers/rental.controller';
import { CarsCounter } from 'src/logic/car-availability';
import { RentalService } from 'src/logic/rental.service';
import { StartRental } from 'src/model/start-rental';
import {
  createTestVulogCarDto,
  createTestVulogCarStaticInfoDto,
} from 'test/helpers/builders/dtos/test-car-dto.builder';
import { TestEndRentalEventDtoBuilder } from 'test/helpers/builders/dtos/test-end-rental-event-dto.builder';
import { createTestStationDto } from 'test/helpers/builders/dtos/test-station-dto.builder';
import { createTestVulogJourneyDto } from 'test/helpers/builders/dtos/test-vulog-journey-dto.builder';
import { createTestVulogTripDto } from 'test/helpers/builders/dtos/test-vulog-trip-dto.builder';
import { OnGoingRentalBuilder } from 'test/helpers/builders/on-going-rental.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { Reservation } from '../../../src/model/reservation.domain';
import { ReservationEntityBuilder } from '../../helpers/builders/entities/reservation.entity.builder';

// TODO: Skip for now. Refactor this test suite. It is not expandable for future testing
describe.skip('RentalController', () => {
  let rentalController: RentalController;

  const testBsgUserId = faker.string.uuid();
  const testVulogJourneyDto = createTestVulogJourneyDto(true, false);
  const testVulogTripDto = createTestVulogTripDto(true, false);
  const testVulogTripDto2 = createTestVulogTripDto(true, false);
  const testRentalInfos = [testVulogTripDto, testVulogTripDto2].map((dto) =>
    dto.toRentalInfo(),
  );

  const testStation = createTestStationDto();

  const testCar = createTestVulogCarDto();

  const testCarStaticInfo = createTestVulogCarStaticInfoDto();

  const userId = aUUID();

  const testEndRentalRequestDto = new TestEndRentalEventDtoBuilder().build();

  beforeAll(async () => {
    const testModule = await Test.createTestingModule({
      imports: [CustomerAuthModule.register()],
      controllers: [RentalController],
      providers: [
        {
          provide: RentalService,
          useValue: {
            startRental: () =>
              StartRental.from(
                Reservation.from(
                  new ReservationEntityBuilder()
                    .makeCarId(testVulogJourneyDto.vehicleId)
                    .makeVulogTripId(testVulogJourneyDto.id)
                    .makeBsgUserId(userId)
                    .build(),
                ),
                testStation.toStation(),
                testCar.toRealtimeCar().toCar(),
                {
                  station: testStation.toStation(),
                  carAvailability: { [testCarStaticInfo.model.name]: 1 },
                } as CarsCounter,
              ),
            getCurrentRentalDetails: () => new OnGoingRentalBuilder().build(),
            getOnGoingRentals: () => testRentalInfos,
            endRental: () => null,
            endRentalSimulation: () => null,
          },
        },
        {
          provide: CustomerJwtGuard,
          useValue: mockCustomerJwtGuard(userId),
        },
      ],
    }).compile();

    rentalController = testModule.get<RentalController>(RentalController);
  });

  it('should be able to get details of an ongoing rental', async () => {
    const response = await rentalController.getOngoingRental(
      new UserInfo(testBsgUserId),
    );
    expect(response).toStrictEqual(
      RentalResponseDto.from(testVulogJourneyDto.toRentalInfo()),
    );
  });

  it('should be able to end a rental', async () => {
    await expect(
      rentalController.endRental(new UserInfo(testBsgUserId)),
    ).resolves.not.toThrow();
  });

  it('should be able to end a rental for simulating data received from VULOG', async () => {
    await expect(
      rentalController.endRentalSimulation(testEndRentalRequestDto),
    ).resolves.not.toThrow();
  });
});
