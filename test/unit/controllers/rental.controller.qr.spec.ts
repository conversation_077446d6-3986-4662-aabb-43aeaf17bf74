import {
  CustomerAuthModule,
  CustomerJwtGuard,
  UserInfo,
} from '@bluesg-2/customer-authentication';
import { faker } from '@faker-js/faker';
import {
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { StationError } from 'src/common/errors/station.error';
import { EndRentalResponseV1Dto } from 'src/controllers/dtos/rental/response/end-rental.response.v1.dto';
import { RentalController } from 'src/controllers/rental.controller';
import { RentalService } from 'src/logic/rental.service';
import { StationService } from 'src/logic/station/station.service';
import { TestingEndRentalReport } from 'src/model/testing-end-rental.report';
import { EndRentalWithQRRequestDtoV1Builder } from 'test/helpers/builders/dtos/end-rental-with-qr.request.dto.v1';
import { OnGoingRentalBuilder } from 'test/helpers/builders/on-going-rental.builder';
import { ReservationBuilder } from 'test/helpers/builders/reservation.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { StationDisabledError } from '../../../src/common/errors/external/station-disabled.error';
import { StationNotFoundNewError } from '../../../src/common/errors/external/station-not-found-new.error';
import { StationStatus } from '../../../src/external/station/dtos/station-status';
import { StationBuilder } from '../../helpers/builders/station.builder';

describe('RentalController', () => {
  let rentalController: RentalController;
  let rentalService: RentalService;
  let stationService: StationService;

  const testBsgUserId = faker.string.uuid();
  const userId = aUUID();
  const testEndRentalWithQRRequestDto = new EndRentalWithQRRequestDtoV1Builder()
    .withQrCode('https://example.com/qr?emi3=123456')
    .build();

  const reservation = new ReservationBuilder().build();

  beforeEach(async () => {
    const testModule = await Test.createTestingModule({
      imports: [CustomerAuthModule.register()],
      controllers: [RentalController],
      providers: [
        {
          provide: RentalService,
          useValue: {
            endRentalUsingQR: jest.fn(),
            getCurrentRentalDetail: jest.fn(),
          },
        },
        {
          provide: StationService,
          useValue: {
            getStationByQRCode: jest.fn(),
          },
        },
        {
          provide: CustomerJwtGuard,
          useValue: mockCustomerJwtGuard(userId),
        },
      ],
    }).compile();

    rentalController = testModule.get<RentalController>(RentalController);
    rentalService = testModule.get<RentalService>(RentalService);
    stationService = testModule.get<StationService>(StationService);
  });

  describe('endRentalWithQR', () => {
    it('should be able to end a rental', async () => {
      const mockEndRentalReport = new TestingEndRentalReport(
        reservation,
        true,
        [],
        [],
        3600,
        25.5,
      );
      const mockOngoingRental = new OnGoingRentalBuilder()
        .withCurrentStation(null)
        .build();

      const mockStation = new StationBuilder()
        .withStatus(StationStatus.Open)
        .build();
      jest
        .spyOn(stationService, 'getStationByQRCode')
        .mockResolvedValue(mockStation);
      jest
        .spyOn(rentalService, 'getCurrentRentalDetail')
        .mockResolvedValue(mockOngoingRental);
      jest
        .spyOn(rentalService, 'endRentalUsingQR')
        .mockResolvedValue(mockEndRentalReport);

      const response = await rentalController.endRentalWithQR(
        new UserInfo(testBsgUserId, null, null),
        testEndRentalWithQRRequestDto,
      );

      expect(response).toBeInstanceOf(EndRentalResponseV1Dto);
      expect(rentalService.getCurrentRentalDetail).toHaveBeenCalledWith(
        expect.any(Object),
      );
      expect(rentalService.endRentalUsingQR).toHaveBeenCalledWith(
        mockOngoingRental,
        expect.any(Object),
        testEndRentalWithQRRequestDto.qrCode,
      );
    });

    it('should throw NotFoundException when no ongoing rental found', async () => {
      const mockStation = new StationBuilder()
        .withStatus(StationStatus.Open)
        .build();
      jest
        .spyOn(stationService, 'getStationByQRCode')
        .mockResolvedValue(mockStation);
      jest
        .spyOn(rentalService, 'getCurrentRentalDetail')
        .mockResolvedValue(null);

      await expect(
        rentalController.endRentalWithQR(
          new UserInfo(testBsgUserId, null, null),
          testEndRentalWithQRRequestDto,
        ),
      ).rejects.toThrow(NotFoundException);

      expect(rentalService.getCurrentRentalDetail).toHaveBeenCalledWith(
        expect.any(Object),
      );
    });

    it('should throw BadRequestException on error with station', async () => {
      const mockStation = new StationBuilder()
        .withStatus(StationStatus.Open)
        .build();
      jest
        .spyOn(stationService, 'getStationByQRCode')
        .mockResolvedValue(mockStation);
      const mockOngoingRental = new OnGoingRentalBuilder()
        .withCurrentStation(null)
        .build();
      jest
        .spyOn(rentalService, 'getCurrentRentalDetail')
        .mockResolvedValue(mockOngoingRental);
      jest
        .spyOn(rentalService, 'endRentalUsingQR')
        .mockRejectedValue(new StationError('Invalid station'));

      await expect(
        rentalController.endRentalWithQR(
          new UserInfo(testBsgUserId, null, null),
          testEndRentalWithQRRequestDto,
        ),
      ).rejects.toThrow(BadRequestException);

      expect(rentalService.getCurrentRentalDetail).toHaveBeenCalledWith(
        expect.any(Object),
      );
      expect(rentalService.endRentalUsingQR).toHaveBeenCalledWith(
        mockOngoingRental,
        expect.any(Object),
        testEndRentalWithQRRequestDto.qrCode,
      );
    });

    it('should throw InternalServerErrorException on unexpected errors', async () => {
      const mockStation = new StationBuilder()
        .withStatus(StationStatus.Open)
        .build();
      jest
        .spyOn(stationService, 'getStationByQRCode')
        .mockResolvedValue(mockStation);
      const mockOngoingRental = new OnGoingRentalBuilder()
        .withCurrentStation(null)
        .build();
      jest
        .spyOn(rentalService, 'getCurrentRentalDetail')
        .mockResolvedValue(mockOngoingRental);
      jest
        .spyOn(rentalService, 'endRentalUsingQR')
        .mockRejectedValue(new Error('Unexpected error'));

      await expect(
        rentalController.endRentalWithQR(
          new UserInfo(testBsgUserId, null, null),
          testEndRentalWithQRRequestDto,
        ),
      ).rejects.toThrow(InternalServerErrorException);

      expect(rentalService.getCurrentRentalDetail).toHaveBeenCalledWith(
        expect.any(Object),
      );
      expect(rentalService.endRentalUsingQR).toHaveBeenCalledWith(
        mockOngoingRental,
        expect.any(Object),
        testEndRentalWithQRRequestDto.qrCode,
      );
    });

    it('should throw StationNotFoundNewError', async () => {
      jest
        .spyOn(stationService, 'getStationByQRCode')
        .mockResolvedValue(undefined);

      await expect(
        rentalController.endRentalWithQR(
          new UserInfo(testBsgUserId, null, null),
          testEndRentalWithQRRequestDto,
        ),
      ).rejects.toThrow(new StationNotFoundNewError());
    });

    it('should throw StationDisabledError', async () => {
      const mockStation = new StationBuilder()
        .withStatus(StationStatus.Closed)
        .build();
      jest
        .spyOn(stationService, 'getStationByQRCode')
        .mockResolvedValue(mockStation);

      await expect(
        rentalController.endRentalWithQR(
          new UserInfo(testBsgUserId, null, null),
          testEndRentalWithQRRequestDto,
        ),
      ).rejects.toThrow(new StationDisabledError());
    });
  });
});
