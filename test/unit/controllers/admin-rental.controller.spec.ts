import {
  AuthGuardConfigOptions,
  AuthModule,
  JwtGuard,
  PasswordNotChangedGuard,
  RolesGuard,
} from '@bluesg-2/bo-auth-guard';
import { Test } from '@nestjs/testing';
import { config } from 'src/config';
import { AdminRentalController } from 'src/controllers/admin/v1/rental/admin-rental.contoller';
import { RentalListResponseDto } from 'src/controllers/dtos/rental-list.response.dto';
import { CarsCounter } from 'src/logic/car-availability';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { RentalService } from 'src/logic/rental.service';
import { StartRental } from 'src/model/start-rental';
import { mockAdminJwtGuard } from 'test/helpers/admin-jwt-guard.mocker';
import {
  createTestVulogCarDto,
  createTestVulogCarStaticInfoDto,
} from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestStationDto } from 'test/helpers/builders/dtos/test-station-dto.builder';
import { createTestVulogJourneyDto } from 'test/helpers/builders/dtos/test-vulog-journey-dto.builder';
import { createTestVulogTripDto } from 'test/helpers/builders/dtos/test-vulog-trip-dto.builder';
import { mockPasswordNotChangedGuard } from 'test/helpers/password-not-changed-guard.mocker';
import { aUUID } from 'test/helpers/random-data.helper';
import { mockRolesGuard } from 'test/helpers/roles-guard.mocker';
import { Reservation } from '../../../src/model/reservation.domain';
import { ReservationEntityBuilder } from '../../helpers/builders/entities/reservation.entity.builder';

describe('AdminRentalController', () => {
  let adminRentalController: AdminRentalController;

  const testVulogJourneyDto = createTestVulogJourneyDto(true, false);
  const testVulogTripDto = createTestVulogTripDto(true, false);
  const testVulogTripDto2 = createTestVulogTripDto(true, false);
  const testRentalInfos = [testVulogTripDto, testVulogTripDto2].map((dto) =>
    dto.toRentalInfo(),
  );

  const testStation = createTestStationDto();

  const testCar = createTestVulogCarDto();

  const testCarStaticInfo = createTestVulogCarStaticInfoDto();

  const userId = aUUID();

  beforeAll(async () => {
    const testModule = await Test.createTestingModule({
      imports: [
        AuthModule.registerAsync({
          useFactory: async (): Promise<AuthGuardConfigOptions> => ({
            secret: config.jwtConfig.adminSecret,
          }),
        }),
      ],
      controllers: [AdminRentalController],
      providers: [
        {
          provide: VulogCarService,
          useValue: {},
        },
        {
          provide: RentalService,
          useValue: {
            startRental: () =>
              StartRental.from(
                Reservation.from(
                  new ReservationEntityBuilder()
                    .makeCarId(testVulogJourneyDto.vehicleId)
                    .makeVulogTripId(testVulogJourneyDto.id)
                    .makeBsgUserId(userId)
                    .build(),
                ),
                testStation.toStation(),
                testCar.toRealtimeCar().toCar(),
                {
                  station: testStation.toStation(),
                  carAvailability: { [testCarStaticInfo.model.name]: 1 },
                } as CarsCounter,
              ),
            getCurrentRentalDetails: () => testVulogJourneyDto.toRentalInfo(),
            adminGetAllFinishedRentals: () => testRentalInfos,
            endRental: () => null,
            endRentalSimulation: () => null,
          },
        },
        {
          provide: JwtGuard,
          useValue: mockAdminJwtGuard(userId),
        },
        {
          provide: PasswordNotChangedGuard,
          useValue: mockPasswordNotChangedGuard(),
        },
        {
          provide: RolesGuard,
          useValue: mockRolesGuard(),
        },
      ],
    }).compile();

    adminRentalController = testModule.get<AdminRentalController>(
      AdminRentalController,
    );
  });

  it('should be able to get all ongoing rentals', async () => {
    const response = await adminRentalController.getAllFinishedRentals();

    expect(response).toStrictEqual(RentalListResponseDto.from(testRentalInfos));
  });
});
