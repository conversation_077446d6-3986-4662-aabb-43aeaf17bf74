import { VulogCarRealTimeDto } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { RealtimeCar } from 'src/model/realtime.car';
import { VulogCarRealTimeDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';

describe('RealtimeCar', () => {
  describe('RealtimeCar.toVulogCarRealTimeDto()', () => {
    const inputVulogCarRealtimeDto: VulogCarRealTimeDto =
      new VulogCarRealTimeDtoBuilder().build();

    let outputVulogCarRealTimeDto: VulogCarRealTimeDto;

    const inputRealTimeCar: RealtimeCar =
      inputVulogCarRealtimeDto.toRealtimeCar();

    beforeAll(async () => {
      outputVulogCarRealTimeDto = inputRealTimeCar.toVulogCarRealTimeDto();
    });

    it('assertion', async () => {
      expect(outputVulogCarRealTimeDto instanceof VulogCarRealTimeDto).toEqual(
        true,
      );
    });
  });
});
