import { AxiosError } from 'axios';
import { VEHICLE_ALREADY_BOOKED } from 'src/common/constants/vulog-error-codes';
import { BsgUserId, VulogCarId } from 'src/common/tiny-types';
import { AvailabilityService } from 'src/logic/availability.service';
import { ReservationService } from 'src/logic/reservation.service';
import { VulogUserRepository } from 'src/model/repositories/vulog-user.repository';
import { createTestVvgCarDto } from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestVulogJourneyDto } from 'test/helpers/builders/dtos/test-vulog-journey-dto.builder';
import { createTestVulogUserEntity } from 'test/helpers/builders/entities/test-vulog-user-entity.builder';

import { faker } from '@faker-js/faker';
import { Test } from '@nestjs/testing';
import { BillingExternalService } from 'src/external/billing/billing.external.service';
import { UserSubscriptionExternalService } from 'src/external/user-subscription/user-subscription.external.service';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { StationService } from 'src/logic/station/station.service';
import { VulogTelemetryService } from 'src/logic/telemetry-repository/vulog/vulog-telemetry.service';
import { VulogAuthService } from 'src/logic/vulog/vulog-auth.service';
import { VvgCarZones } from 'src/model/dtos/vvg-car/vvg-car-fields/vvg-car-zones';
import { ReservationRepository } from 'src/model/repositories/reservation.repository';

import { Reservation } from 'src/model/reservation.domain';

import { EventName } from '@bluesg-2/event-library';
import { CorrelationIdService } from '@bluesg-2/monitoring';
import { QueuePopCarModel } from '@bluesg-2/queue-pop';
import { CarReservationExpiryEventDto } from 'src/event/notification/car-reservation-expiry.event';
import { ReservationCancelationAfterSwitchingCarEventDto } from 'src/event/reservation/reservation-cancelation-after-switching-car.event';
import { CarService } from 'src/logic/car/car.service';
import { ReservationCacheService } from 'src/logic/reservation/reservation.cache.service';
import { EventBusService } from 'src/shared/event-bus/event-bus.service';
import { createTestStationDto } from 'test/helpers/builders/dtos/test-station-dto.builder';
import { createTestVulogTripDto } from 'test/helpers/builders/dtos/test-vulog-trip-dto.builder';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { PreReservationBuilder } from 'test/helpers/builders/pre-reservation/pre-reservation.builder';
import { aDate } from 'test/helpers/random-data.helper';
import { v4 } from 'uuid';
import { ConfigurationService } from '~shared/configuration/configuration.service';
import { NewRelicReservationEventService } from '~shared/newrelic/event/event-type/reservation.event.newrelic.service';

const fakeReservation = Reservation.from(
  new ReservationEntityBuilder().makeExpiresAt(aDate()).build(),
);

// Prioritize for October release
describe.skip('ReservationService', () => {
  const mock_VulogTelemetryService = {
    getCarInformation: jest.fn(),
  };

  const mock_VulogCarService = {
    bookCar: jest.fn(),
    bookCarAsAdmin: jest.fn(),
    getUserCurrentReservations: jest.fn(),
    cancelCarReservationOrAllocation: jest.fn(),
    cancelCarReservationOrAllocationAsAdmin: jest.fn(),
    getAllFinishedReservationsAndAllocations: jest.fn(),
  };

  const mock_AvailabilityService = {
    getAvailableCarsForStation: jest.fn(),
  };

  const mock_VulogUserRepository = {
    findVulogUserByBsgUserId: jest.fn(),
    findVulogUser: jest.fn(),
  };

  const mock_VulogAuthService = {
    createUser: jest.fn(),
    findOrCreateUser: jest.fn(),
  };

  const mock_UserSubscriptionExternalService = {
    internalApi: {
      getUserDetail: jest.fn(),
    },
  };

  const mock_StationService = {
    getStation: jest.fn(),
  };

  const mock_eventBusService = {
    nonBlockingEmit: jest.fn(),
  };

  const mock_newRelicService = {
    emitExpired: jest.fn(),
    emitAllocated: jest.fn(),
    emitSwitched: jest.fn(),
    emitCanceled: jest.fn(),
  };

  let reservationService: ReservationService;

  beforeAll(async () => {
    const testModule = await Test.createTestingModule({
      providers: [
        ReservationService,
        {
          provide: VulogTelemetryService,
          useValue: mock_VulogTelemetryService,
        },
        {
          provide: VulogCarService,
          useValue: mock_VulogCarService,
        },
        {
          provide: AvailabilityService,
          useValue: mock_AvailabilityService,
        },
        {
          provide: VulogUserRepository,
          useValue: mock_VulogUserRepository,
        },
        {
          provide: VulogAuthService,
          useValue: mock_VulogAuthService,
        },
        {
          provide: UserSubscriptionExternalService,
          useValue: mock_UserSubscriptionExternalService,
        },
        {
          provide: StationService,
          useValue: mock_StationService,
        },
        {
          provide: EventBusService,
          useValue: mock_eventBusService,
        },
        {
          provide: NewRelicReservationEventService,
          useValue: mock_newRelicService,
        },
        {
          provide: CorrelationIdService,
          useValue: {
            getCorrelationId: () => ({ value: v4() }),
          },
        },
        {
          provide: ReservationRepository,
          useValue: {
            createReservation: (reservation: Reservation) =>
              Reservation.from(
                new ReservationEntityBuilder()
                  .makeBsgUserId(reservation.bsgUserId.value)
                  .makeStartStationId(reservation.startStationId.value)
                  .build(),
              ),
            updateReservation: (reservation: Reservation) =>
              Reservation.from(
                new ReservationEntityBuilder()
                  .makeReservationId(reservation.getId)
                  .makeBsgUserId(reservation.bsgUserId.value)
                  .makeStartStationId(reservation.startStationId.value)
                  .makeCarId(reservation.carId.value)
                  .makeCarModel(reservation.carModel)
                  .makeVulogTripId(reservation.vulogTripId.value)
                  .build(),
              ),
          },
        },
        {
          provide: BillingExternalService,
          useValue: {},
        },
      ],
    }).compile();

    reservationService = testModule.get<ReservationService>(ReservationService);
  });

  describe.skip('should be able to allocate a car', () => {
    const testCar = createTestVvgCarDto().toRealTimeCar();
    const testVulogUserEntity = createTestVulogUserEntity();
    const alreadyBookedCars = {};
    const testVulogJourneyDto = createTestVulogJourneyDto(false, true);
    const testReservationInfo = testVulogJourneyDto.toReservationInfo();
    testReservationInfo.carId = testCar.id;
    testReservationInfo.sourceCarId = testCar.id;

    let result;

    const stubBookCarFn = (carId: VulogCarId) => {
      if (alreadyBookedCars[carId.value]) {
        const error = new AxiosError();
        error.response = {
          ...error.response,
          data: { message: VEHICLE_ALREADY_BOOKED },
        };
        throw error;
      }
      const reservationInfo = testVulogJourneyDto.toReservationInfo();
      reservationInfo.carId = carId;
      reservationInfo.sourceCarId = carId;
      return reservationInfo;
    };

    beforeEach(() => {
      mock_AvailabilityService.getAvailableCarsForStation.mockResolvedValueOnce(
        [testCar],
      );

      mock_VulogAuthService.findOrCreateUser.mockResolvedValueOnce(
        testVulogUserEntity,
      );

      mock_VulogCarService.bookCar.mockImplementationOnce(stubBookCarFn);
    });

    it('Check expectation', async () => {
      const testVulogUserEntity = createTestVulogUserEntity();

      const testStationId = faker.string.uuid();

      const testUserId = new BsgUserId(testVulogUserEntity.bsgUserId);
      result = await reservationService.allocateCar(
        testStationId,
        testUserId,
        null,
      );

      // Temporarily checking 3 fields only
      // expect(result.carId).toStrictEqual(testReservationInfo.carId.value);
      // expect(result.sourceCarId).toStrictEqual(
      //   testReservationInfo.sourceCarId.value,
      // );
      // expect(result.sourceId).toStrictEqual(testReservationInfo.sourceId.value);
    });
  });

  describe('should be able to switch an allocation to a different car', () => {
    let result;

    const testVulogUserEntity = createTestVulogUserEntity();
    const testTargetSwitchCarDto = createTestVvgCarDto();
    const testVulogJourneyDto = createTestVulogJourneyDto(false, true);
    const existingAllocation = createTestVulogJourneyDto(
      false,
      true,
    ).toReservationInfo();

    let alreadyBookedCars = {};

    const stubBookCarFn = (carId: VulogCarId) => {
      if (alreadyBookedCars[carId.value]) {
        const error = new AxiosError();
        error.response = {
          ...error.response,
          data: { message: VEHICLE_ALREADY_BOOKED },
        };
        throw error;
      }
      const reservationInfo = testVulogJourneyDto.toReservationInfo();
      reservationInfo.carId = carId;
      reservationInfo.sourceCarId = carId;
      return reservationInfo;
    };

    beforeEach(() => {
      mock_VulogUserRepository.findVulogUserByBsgUserId.mockResolvedValueOnce(
        testVulogUserEntity,
      );

      mock_VulogTelemetryService.getCarInformation.mockResolvedValueOnce(
        testTargetSwitchCarDto.toRealTimeCar(),
      );

      mock_VulogCarService.bookCarAsAdmin.mockImplementationOnce(stubBookCarFn);

      mock_VulogCarService.cancelCarReservationOrAllocationAsAdmin.mockImplementationOnce(
        () => {
          alreadyBookedCars = {};
        },
      );
    });

    it('Check expectation', async () => {
      // const expectedReservationInfo = testVulogJourneyDto.toReservationInfo();
      // expectedReservationInfo.carId = testTargetSwitchCarDto.toCar().id;
      // expectedReservationInfo.sourceCarId = testTargetSwitchCarDto.toCar().id;
      // result = await reservationService.switchAllocationCar(
      //   new BsgUserId(testVulogUserEntity.bsgUserId),
      //   existingAllocation,
      //   testTargetSwitchCarDto.toCar().id,
      // );
      // expect(result).toStrictEqual(expectedReservationInfo);
    });
  });

  describe('should be able to switch an allocation to a different car, even if another user has been allocated that car', () => {
    let result;

    const testVulogUserEntity = createTestVulogUserEntity();
    const testTargetSwitchCarDto = createTestVvgCarDto();
    const testVulogJourneyDto = createTestVulogJourneyDto(false, true);
    const existingAllocation = createTestVulogJourneyDto(
      false,
      true,
    ).toReservationInfo();
    const testVulogTripOfTargetCar = createTestVulogTripDto(
      false,
      true,
    ).toVulogTrip();

    const stubBookCarFn = (carId: VulogCarId) => {
      if (alreadyBookedCars[carId.value]) {
        const error = new AxiosError();
        error.response = {
          ...error.response,
          data: { message: VEHICLE_ALREADY_BOOKED },
        };
        throw error;
      }
      const reservationInfo = testVulogJourneyDto.toReservationInfo();
      reservationInfo.carId = carId;
      reservationInfo.sourceCarId = carId;
      return reservationInfo;
    };

    let alreadyBookedCars = {};

    beforeEach(() => {
      mock_VulogUserRepository.findVulogUserByBsgUserId.mockResolvedValueOnce(
        testVulogUserEntity,
      );

      mock_VulogTelemetryService.getCarInformation.mockResolvedValueOnce(
        testTargetSwitchCarDto.toRealTimeCar(),
      );

      mock_VulogCarService.bookCarAsAdmin.mockImplementationOnce(stubBookCarFn);

      alreadyBookedCars[testTargetSwitchCarDto.toRealTimeCar().id.value] = true;

      mock_VulogCarService.getAllFinishedReservationsAndAllocations.mockResolvedValueOnce(
        [testVulogTripOfTargetCar],
      );

      mock_VulogCarService.cancelCarReservationOrAllocationAsAdmin.mockImplementationOnce(
        () => {
          alreadyBookedCars = {};
        },
      );

      mock_VulogCarService.cancelCarReservationOrAllocationAsAdmin.mockImplementationOnce(
        () => {
          alreadyBookedCars = {};
        },
      );

      mock_VulogCarService.bookCarAsAdmin.mockImplementationOnce(stubBookCarFn);

      mock_VulogCarService.bookCarAsAdmin.mockImplementationOnce(stubBookCarFn);
    });

    it('Check expectation', async () => {
      // result = await reservationService.switchAllocationCar(
      //   new BsgUserId(testVulogUserEntity.bsgUserId),
      //   existingAllocation,
      //   new CarId(testTargetSwitchCarDto.id),
      // );
      // const expectedReservationInfo = testVulogJourneyDto.toReservationInfo();
      // expectedReservationInfo.carId = testTargetSwitchCarDto.toCar().id;
      // expectedReservationInfo.sourceCarId = testTargetSwitchCarDto.toCar().id;
      // expect(result).toStrictEqual(expectedReservationInfo);
    });
  });

  describe('should be able to check if cars are available at specific station', () => {
    const zoneId = faker.string.uuid();

    const testCar = createTestVvgCarDto({
      zones: new VvgCarZones([[zoneId]]),
    }).toRealTimeCar();

    const testStation = createTestStationDto({ zoneId }).toStation();

    beforeEach(() => {
      mock_AvailabilityService.getAvailableCarsForStation.mockResolvedValueOnce(
        [testCar],
      );

      mock_StationService.getStation.mockResolvedValueOnce(testStation);
    });

    it('Check expectation', async () => {
      const result = await reservationService.isAvailable(testStation.id.value);

      expect(result).toBe(true);
    });
  });

  describe('should be able to expire on-going reservation', () => {
    const testUser = new InternalUserDetailResponseDtoV1Builder().build();

    const testVulogJourneyDto = createTestVulogJourneyDto(false, true);

    const testReservationInfo = testVulogJourneyDto.toReservationInfo();

    beforeEach(() => {
      mock_UserSubscriptionExternalService.internalApi.getUserDetail.mockResolvedValueOnce(
        { id: testUser.id, email: testUser.email },
      );

      mock_VulogCarService.getUserCurrentReservations.mockResolvedValueOnce([
        testReservationInfo,
      ]);

      mock_VulogCarService.cancelCarReservationOrAllocation.mockResolvedValueOnce(
        true,
      );
    });

    it('Check expectation', async () => {
      await reservationService.expiresOnGoingReservation(testUser.id);

      expect(
        mock_VulogCarService.cancelCarReservationOrAllocation,
      ).toBeCalled();
    });
  });

  describe('should be able to convert pre-reservation to reservation', () => {
    const preReservation = new PreReservationBuilder().build();

    const testCar = createTestVvgCarDto().toRealTimeCar();
    const testVulogUserEntity = createTestVulogUserEntity();
    const alreadyBookedCars = {};
    const testVulogJourneyDto = createTestVulogJourneyDto(false, true);
    const testReservationInfo = testVulogJourneyDto.toReservationInfo();
    testReservationInfo.carId = testCar.id;
    testReservationInfo.sourceCarId = testCar.id;

    const stubBookCarFn = (carId: VulogCarId) => {
      if (alreadyBookedCars[carId.value]) {
        const error = new AxiosError();
        error.response = {
          ...error.response,
          data: { message: VEHICLE_ALREADY_BOOKED },
        };
        throw error;
      }
      const reservationInfo = testVulogJourneyDto.toReservationInfo();
      reservationInfo.carId = carId;
      reservationInfo.sourceCarId = carId;
      return reservationInfo;
    };

    beforeEach(() => {
      mock_AvailabilityService.getAvailableCarsForStation.mockResolvedValueOnce(
        [testCar],
      );

      mock_VulogAuthService.findOrCreateUser.mockResolvedValueOnce(
        testVulogUserEntity,
      );

      mock_VulogCarService.bookCar.mockImplementationOnce(stubBookCarFn);
    });

    it('Check expectation', async () => {
      await expect(
        reservationService.preReservationToReservation(
          preReservation,
          testCar.model.value as QueuePopCarModel,
        ),
      ).resolves.not.toThrow();
    });
  });
});

describe('reservationService', () => {
  let reservationService: ReservationService;
  const mock_VulogTelemetryService = {
    getCarInformation: jest.fn(),
  };

  const mock_VulogCarService = {
    bookCar: jest.fn(),
    bookCarAsAdmin: jest.fn(),
    getUserCurrentReservations: jest.fn(),
    cancelCarReservationOrAllocation: jest.fn(),
    cancelCarReservationOrAllocationAsAdmin: jest.fn(),
    getAllFinishedReservationsAndAllocations: jest.fn(),
  };

  const mock_AvailabilityService = {
    getAvailableCarsForStation: jest.fn(),
  };

  const mock_VulogUserRepository = {
    findVulogUserByBsgUserId: jest.fn(),
    findVulogUser: jest.fn(),
  };

  const mock_VulogAuthService = {
    createUser: jest.fn(),
    findOrCreateUser: jest.fn(),
  };

  const mock_UserSubscriptionExternalService = {
    getUserDetail_Internal: jest.fn(),
  };

  const mock_StationService = {
    getStation: jest.fn(),
  };

  const mock_eventBusService = {
    nonBlockingEmit: jest.fn(),
  };

  const mock_ReservationCacheService = {
    vulogCarSwitchedOnGoingReservationByUserId: {
      retrieve: jest.fn(),
    },
  };

  beforeAll(async () => {
    const testModule = await Test.createTestingModule({
      providers: [
        ReservationService,
        {
          provide: VulogTelemetryService,
          useValue: mock_VulogTelemetryService,
        },
        {
          provide: VulogCarService,
          useValue: mock_VulogCarService,
        },
        {
          provide: AvailabilityService,
          useValue: mock_AvailabilityService,
        },
        {
          provide: VulogUserRepository,
          useValue: mock_VulogUserRepository,
        },
        {
          provide: VulogAuthService,
          useValue: mock_VulogAuthService,
        },
        {
          provide: UserSubscriptionExternalService,
          useValue: mock_UserSubscriptionExternalService,
        },
        {
          provide: StationService,
          useValue: mock_StationService,
        },
        {
          provide: EventBusService,
          useValue: mock_eventBusService,
        },
        {
          provide: CorrelationIdService,
          useValue: {
            getCorrelationId: () => ({ value: v4() }),
          },
        },
        {
          provide: ReservationRepository,
          useValue: {
            createReservation: (reservation: Reservation) =>
              Reservation.from(
                new ReservationEntityBuilder()
                  .makeBsgUserId(reservation.bsgUserId.value)
                  .makeStartStationId(reservation.startStationId.value)
                  .build(),
              ),
            updateReservation: (reservation: Reservation) =>
              Reservation.from(
                new ReservationEntityBuilder()
                  .makeReservationId(reservation.getId)
                  .makeBsgUserId(reservation.bsgUserId.value)
                  .makeStartStationId(reservation.startStationId.value)
                  .makeCarId(reservation.carId.value)
                  .makeCarModel(reservation.carModel)
                  .makeVulogTripId(reservation.vulogTripId.value)
                  .build(),
              ),
            getToBeExpiredReservations: () => [fakeReservation],
            saveOne: () => null,
            updateStatus: () => fakeReservation,
          },
        },
        {
          provide: BillingExternalService,
          useValue: {},
        },
        { provide: ConfigurationService, useValue: { findOne: () => 15 } },
        {
          provide: NewRelicReservationEventService,
          useValue: { emitExpired: () => null },
        },
        {
          provide: ReservationCacheService,
          useValue: mock_ReservationCacheService,
        },
        {
          provide: CarService,
          useValue: {},
        },
      ],
    }).compile();

    reservationService = testModule.get<ReservationService>(ReservationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('when expire reservation it should send message to NS with correct params', async () => {
    mock_ReservationCacheService.vulogCarSwitchedOnGoingReservationByUserId.retrieve.mockResolvedValueOnce(
      true,
    );

    await reservationService.expireReservation();

    const firstNonBlockingEmitCall =
      mock_eventBusService.nonBlockingEmit.mock.calls[0][0];

    delete firstNonBlockingEmitCall.correlationId;
    delete firstNonBlockingEmitCall.payload.correlationId;

    const expected_firstNonBlockingEmitCall =
      new ReservationCancelationAfterSwitchingCarEventDto(
        {
          reservationId: fakeReservation.getId,
          bsgUserId: fakeReservation.bsgUserId.value,
          correlationId: null,
        },
        null,
      );

    delete expected_firstNonBlockingEmitCall.correlationId;
    delete expected_firstNonBlockingEmitCall.payload.correlationId;

    expect(firstNonBlockingEmitCall).toEqual(expected_firstNonBlockingEmitCall);

    const secondNonBlockingEmitCall =
      mock_eventBusService.nonBlockingEmit.mock.calls[1][0];

    delete secondNonBlockingEmitCall.correlationId;

    const expected_secondNonBlockingEmitCall = new CarReservationExpiryEventDto(
      fakeReservation,
      null,
    );

    delete expected_secondNonBlockingEmitCall.correlationId;

    expect(secondNonBlockingEmitCall).toEqual(
      expected_secondNonBlockingEmitCall,
    );

    const thirdNonBlockingEmitCall =
      mock_eventBusService.nonBlockingEmit.mock.calls[2][0];

    expect(thirdNonBlockingEmitCall.event).toBe(
      EventName.RentalReservationExpiredEvent,
    );
    expect(thirdNonBlockingEmitCall.payload.id).toBe(fakeReservation.getId);
    expect(thirdNonBlockingEmitCall.payload.carId).toBe(
      fakeReservation.carId.value,
    );
    expect(thirdNonBlockingEmitCall.payload.createdAt).toBeTruthy();
    expect(thirdNonBlockingEmitCall.payload.reservedAt).toBeTruthy();
    expect(thirdNonBlockingEmitCall.payload.expiredAt).toBeTruthy();
    expect(thirdNonBlockingEmitCall.payload.userId).toBe(
      fakeReservation.bsgUserId.value,
    );
  });
});
