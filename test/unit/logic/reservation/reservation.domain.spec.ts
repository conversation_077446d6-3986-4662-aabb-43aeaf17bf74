
import { ReservationBuilder } from 'test/helpers/builders/reservation.builder';
import { PricingPolicyNotFound } from 'src/common/errors/external/pricing-policy-not-found.error';
import { PricingPolicyBuilder } from 'test/helpers/builders/pricing-policy.builder';
import { PricingPolicyNoRentalRate } from 'src/common/errors/external/pricing-policy-no-rental-rate.error';
import { RentalPackageBuilder } from 'test/helpers/builders/rental-package.builder';
import { RentalPackageInvalidDuration, RentalPackageInvalidPrice } from 'src/common/errors/external/rental-package.error';


describe('Reservation', () => {
  describe('getRentalPrice', () => {
    it('should throw PricingPolicyNotFound if reservation has no pricing policy', () => {
      const reservation = new ReservationBuilder()
      .withDuration(120)
      .withPricingPolicy(null)
      .build();

      expect(() => reservation.getRentalPrice()).toThrow(PricingPolicyNotFound);
    });

    it('should throw PricingPolicyNotFound if pricing policy has no rentalRate', () => {
      const reservation = new ReservationBuilder()
      .withDuration(120)
      .withPricingPolicy(new PricingPolicyBuilder().withRentalRate(null).build())
      .build();

      expect(() => reservation.getRentalPrice()).toThrow(PricingPolicyNoRentalRate);
    });
    
    it('should return 0 if pricing policy rentalRate is 0', () => {
      const reservation = new ReservationBuilder()
      .withDuration(120)
      .withPricingPolicy(new PricingPolicyBuilder().withRentalRate(0.0).build())
      .build();

      const result = reservation.getRentalPrice();

      expect(result).toBe(0.0);
    });

    describe("When rental has no package" , () => {
      it('should calculate rental price correctly when rental duration >= 15 mins', () => {
        const reservation = new ReservationBuilder()
        .withDuration(1200)
        .withPricingPolicy(new PricingPolicyBuilder().withRentalRate(0.36).build())
        .build();
  
        const result = reservation.getRentalPrice();
  
        expect(result).toBe(7.20);
      });

      it('should calculate rental price correctly when rental duration < 15 mins', () => {
        const reservation = new ReservationBuilder()
        .withDuration(600)
        .withPricingPolicy(new PricingPolicyBuilder().withRentalRate(0.36).build())
        .build();
  
        const result = reservation.getRentalPrice();
  
        expect(result).toBe(5.40);
      });

      it('should calculate rental price correctly when rental duration < 15 mins', () => {
        const reservation = new ReservationBuilder()
        .withDuration(600)
        .withPricingPolicy(new PricingPolicyBuilder().withRentalRate(0.36).build())
        .build();
  
        const result = reservation.getRentalPrice();
  
        expect(result).toBe(5.40);
      });
    })

    describe("When rental has a package" , () => {
      it('should throw invalid rental package error if duration is null', () => {
        const reservation = new ReservationBuilder()
        .withDuration(600)
        .withPricingPolicy(new PricingPolicyBuilder().withRentalRate(0.36).build())
        .withRentalPackageData(new RentalPackageBuilder().withDuration(null).withPrice(1.0).build())
        .build();

        expect(() => reservation.getRentalPrice()).toThrow(RentalPackageInvalidDuration);
      });

      it('should throw invalid rental package error if price is null', () => {
        const reservation = new ReservationBuilder()
        .withDuration(600)
        .withPricingPolicy(new PricingPolicyBuilder().withRentalRate(0.36).build())
        .withRentalPackageData(new RentalPackageBuilder().withDuration(1.0).withPrice(null).build())
        .build();

        expect(() => reservation.getRentalPrice()).toThrow(RentalPackageInvalidPrice);
      });

      it('should calculate rental price correctly when rental duration is WITHIN the package duration', () => {
        const reservation = new ReservationBuilder()
        .withDuration(600)
        .withPricingPolicy(new PricingPolicyBuilder().withRentalRate(0.36).build())
        .withRentalPackageData(new RentalPackageBuilder().withDuration(20.0).withPrice(10.0).build()) // 20 min
        .build();

        const result = reservation.getRentalPrice();
  
        expect(result).toBe(10.0);
      });

      it('should calculate rental price correctly when rental duration is MORE than the package duration', () => {
        const reservation = new ReservationBuilder()
        .withDuration(1200) // 20 min
        .withPricingPolicy(new PricingPolicyBuilder().withRentalRate(0.36).build())
        .withRentalPackageData(new RentalPackageBuilder().withDuration(10.0).withPrice(10.0).build()) // 10 min
        .build();

        const result = reservation.getRentalPrice();
  
        expect(result).toBe(13.6);
      });
    })
  });
});
