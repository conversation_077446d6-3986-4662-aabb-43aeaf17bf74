import { HttpStatus, INestApplication } from '@nestjs/common';

import { CustomerJwtGuard } from '@bluesg-2/customer-authentication';
import { faker } from '@faker-js/faker';
import { getRepositoryToken } from '@nestjs/typeorm';
import MockAdapter from 'axios-mock-adapter';
import { BaseResponseDto } from 'src/common/api/base.response.dto';
import { RootRouteSegment } from 'src/common/constants/api-path';
import { ApiVersion } from 'src/common/constants/api-versions';
import {
  BsgUserId,
  CarPlateNumber,
  VulogJourneyOrTripId,
  VulogProfileId,
  VulogUserId,
  VulogZoneId,
} from 'src/common/tiny-types';
import { ApiRouteUtils } from 'src/common/utils/api-route.util';
import { config } from 'src/config';
import {
  SubscriptionStatus,
  UserStatus,
} from 'src/external/user-subscription/constants';
import { Car } from 'src/logic/car/domain/car';
import { CarEntity } from 'src/logic/car/entity/car.entity';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { AppBuilder } from 'test/helpers/app.builder';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { createTestVulogAuthResponse } from 'test/helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from 'test/helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from 'test/helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { InternalUserDetailResponseDtoV1Builder } from 'test/helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { VulogUserBuilder } from 'test/helpers/builders/vulog-user.builder';
import { mockCustomerJwtGuard } from 'test/helpers/customer-jwt-guard.mocker';
import { UssApiMocker } from 'test/helpers/external-services/uss.api-mocker';
import { aMD5, aUUID } from 'test/helpers/random-data.helper';
import { mockVulogCredential } from 'test/helpers/util/mock-vulog-credential.util';
import { Repository } from 'typeorm';

import { DateTime } from 'luxon';
import { ReservationStatus } from 'src/common/enum/reservation.enum';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { VulogCarConnectionProblemService } from 'src/logic/vulog/vulog-car-connection-problem.service';
import { VulogJourneyDto } from 'src/model/dtos/vulog-journey/vulog-journey.dto';
import { ReservationEntity } from 'src/model/repositories/entities/reservation.entity';
import { VulogAimaErrorResponseDtoBuilder } from 'test/helpers/builders/dtos/axios/vulog.aima.error.response.dto.builder';
import { VVGErrorResponseDtoBuilder } from 'test/helpers/builders/dtos/axios/vulog.vvg.error.response.dto.builder';
import { VulogJourneyDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-journey.dto.builder';
import { ReservationEntityBuilder } from 'test/helpers/builders/entities/reservation.entity.builder';
import { ConfigurationRepository } from '~shared/configuration/configuration.repository';
import { ConfigurationKey } from '~shared/configuration/constants/configuration';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { NewRelicMetricStepInFlow } from '~shared/newrelic/metric/enums/newrelic-metric.enum';

const userId = aUUID();

const email = faker.internet.email();

jest.setTimeout(120000);

describe(`[UNIT TEST] VulogCarConnectionProblemService`, () => {
  const appBuilder = new AppBuilder();

  let app: INestApplication;

  let vulogUserRepo: Repository<VulogUserEntity>;
  let vulogCarConnectionProblemService: VulogCarConnectionProblemService;
  let reservationRepo: Repository<ReservationEntity>;
  let carRepo: Repository<CarEntity>;
  let vulogCarService: VulogCarService;
  let configurationRepository: ConfigurationRepository;
  let featureFlagService: FeatureFlagService;
  let spyOn_featureFlagService__shouldCancelReservationAfterBestEffort: jest.SpyInstance;

  let mockOutgoingService: MockAdapter;
  let mockUssApiService: MockAdapter;

  const mockAuthResponse = createTestVulogAuthResponse();
  const mockVulogUser = VulogUserBuilder.creating()
    .withBsgUserId(new BsgUserId(userId))
    .build();

  const ussUserDto = new InternalUserDetailResponseDtoV1Builder()
    .withId(userId)
    .withUserStatus(UserStatus.VERIFIED)
    .withLatestSubscriptionStatus(SubscriptionStatus.ACTIVE)
    .build();

  const userDto = createTestVulogUserDto();
  const userServicesAndProfiles = createTestVulogUserServicesDto();

  beforeAll(async () => {
    app = await appBuilder
      .overrideProvider(CustomerJwtGuard, mockCustomerJwtGuard(aUUID()), true)
      .buildAndStart();

    carRepo = app.get<Repository<CarEntity>>(getRepositoryToken(CarEntity));
    reservationRepo = app.get<Repository<ReservationEntity>>(
      getRepositoryToken(ReservationEntity),
    );
    vulogUserRepo = app.get<Repository<VulogUserEntity>>(
      getRepositoryToken(VulogUserEntity),
    );

    const httpApdaterService = app.get<HttpAdapterService>(HttpAdapterService);
    mockOutgoingService = new MockAdapter(
      httpApdaterService.httpService.axiosRef,
      { onNoMatch: 'throwException' },
    );
    mockUssApiService = new UssApiMocker(app).mock();

    vulogCarConnectionProblemService =
      app.get<VulogCarConnectionProblemService>(
        VulogCarConnectionProblemService,
      );
    vulogCarService = app.get<VulogCarService>(VulogCarService);
    configurationRepository = app.get(ConfigurationRepository);
    featureFlagService = app.get(FeatureFlagService);
    spyOn_featureFlagService__shouldCancelReservationAfterBestEffort =
      jest.spyOn(featureFlagService, 'shouldCancelReservationAfterBestEffort');

    userDto.email = email;

    mockOutgoingService
      .onPost(`${VulogPaths.registerUser(config.vulogConfig.fleetId)}`)
      .reply(200, userDto);

    userServicesAndProfiles.userId = userDto.id;

    mockUssApiService
      .onGet(
        `${ApiRouteUtils.buildApiRouteSegment(
          RootRouteSegment.Internal,
          ApiVersion.V1,
          ['users', userId],
        )}`,
      )
      .reply(200, {
        success: true,
        result: JSON.parse(JSON.stringify(ussUserDto)),
      } as BaseResponseDto);

    await vulogUserRepo.save(VulogUserEntity.from(mockVulogUser));

    const maximumRetryAttemptsConfig =
      await configurationRepository.getOneByKey(
        ConfigurationKey.CAR_CONNECTION_PROBLEM__MAXIMUM_RETRY_ATTEMPTS_FOR_PING,
      );

    maximumRetryAttemptsConfig.value = 2;

    await configurationRepository.saveOne(maximumRetryAttemptsConfig);

    mockVulogCredential(app);
  });

  afterAll(async () => {
    await cleanUpResources();

    await app.close();

    mockOutgoingService.reset();
    mockUssApiService.reset();
  });

  async function cleanUpResources(): Promise<void> {
    await reservationRepo.delete({});
    await carRepo.delete({});
    spyOn_featureFlagService__shouldCancelReservationAfterBestEffort.mockReset();

    mockOutgoingService.reset();
    mockUssApiService.reset();

    /**
     * Doing Vulog authentication mock
     */
    mockOutgoingService
      .onGet(
        `${VulogPaths.userServicesAndProfiles(
          config.vulogConfig.fleetId,
          new VulogUserId(userDto.id),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.registerUserProfileForService(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, userServicesAndProfiles);

    mockOutgoingService
      .onPost(
        `${VulogPaths.userProfile(
          config.vulogConfig.fleetId,
          new VulogProfileId(userServicesAndProfiles.profiles[0].profileId),
        )}`,
      )
      .reply(200, {});

    mockOutgoingService
      .onPost(`${VulogPaths.auth()}`)
      .reply(200, mockAuthResponse);

    jest.clearAllMocks();
  }

  describe('🔥 Success: Run successfully without error', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();

    it('test verification', async () => {
      const result: boolean =
        await vulogCarConnectionProblemService.retryWhenEncounterCarConnectionProblem(
          {
            callback: async () => {
              return true;
            },
            step: NewRelicMetricStepInFlow.AllocateReservation,
            vulogCarId: car.vulogId,
            shouldCancelReservationAfterBestEffort: false,
          },
        );

      expect(result).toEqual(true);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  // Vulog AiMA, Car ID
  describe('🔥 Success: [Status code: 403] Vulog AiMA Error, wake up successfully, vulog car ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const zoneId: VulogZoneId = new VulogZoneId(aMD5());

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            zoneId,
          )}`,
        )
        .replyOnce(
          HttpStatus.FORBIDDEN,
          new VulogAimaErrorResponseDtoBuilder()
            .withCode('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.ACCEPTED);

      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            zoneId,
          )}`,
        )
        .replyOnce(HttpStatus.OK, { statusCode: HttpStatus.OK });
    });

    it('test verification', async () => {
      const success = await vulogCarService.toggleStickyStationWithCar(
        car.vulogId,
        zoneId,
        true,
      );

      expect(success).toEqual(true);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('🔥 Success: [Status code: 403] Vulog AiMA Error, wake up failed, ping car successfully (online), vulog car ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const zoneId: VulogZoneId = new VulogZoneId(aMD5());

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            zoneId,
          )}`,
        )
        .replyOnce(
          HttpStatus.FORBIDDEN,
          new VulogAimaErrorResponseDtoBuilder()
            .withCode('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            zoneId,
          )}`,
        )
        .replyOnce(HttpStatus.OK, { statusCode: HttpStatus.OK });
    });

    it('test verification', async () => {
      const success = await vulogCarService.toggleStickyStationWithCar(
        car.vulogId,
        zoneId,
        true,
      );

      expect(success).toEqual(true);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('🔥 Success: [Status code: 403] Vulog AiMA Error, wake up failed, ping car failed then successfully (online), vulog car ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const zoneId: VulogZoneId = new VulogZoneId(aMD5());

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            zoneId,
          )}`,
        )
        .replyOnce(
          HttpStatus.FORBIDDEN,
          new VulogAimaErrorResponseDtoBuilder()
            .withCode('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      /** START: Retry simulation */
      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.OK);
      /** END: Retry simulation */

      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            zoneId,
          )}`,
        )
        .replyOnce(HttpStatus.OK, { statusCode: HttpStatus.OK });
    });

    it('test verification', async () => {
      const success = await vulogCarService.toggleStickyStationWithCar(
        car.vulogId,
        zoneId,
        true,
      );

      expect(success).toEqual(true);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('💦 Failed: [Status code: 403] Vulog AiMA Error, wake up failed, ping car failed (offline), vulog car ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const zoneId: VulogZoneId = new VulogZoneId(aMD5());

    let error;

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            zoneId,
          )}`,
        )
        .replyOnce(
          HttpStatus.FORBIDDEN,
          new VulogAimaErrorResponseDtoBuilder()
            .withCode('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      /** START: Retry simulation */
      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);
      /** END: Retry simulation */

      try {
        await vulogCarService.toggleStickyStationWithCar(
          car.vulogId,
          zoneId,
          true,
        );
      } catch (err) {
        error = err;
      }
    });

    it('test verification', async () => {
      expect(error.cause).toEqual(
        'Failed to ping vehicle after trying to wake it up',
      );
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('🔥 Success: [Status code: 410] Vulog AiMA Error, wake up failed, ping car failed then successfully (online), vulog car ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const zoneId: VulogZoneId = new VulogZoneId(aMD5());

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            zoneId,
          )}`,
        )
        .replyOnce(
          HttpStatus.GONE,
          new VulogAimaErrorResponseDtoBuilder()
            .withMessage('carConnectionProblem')
            .withIsStringOnly(true)
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      /** START: Retry simulation */
      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.OK);
      /** END: Retry simulation */

      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            zoneId,
          )}`,
        )
        .replyOnce(HttpStatus.OK, { statusCode: HttpStatus.OK });
    });

    it('test verification', async () => {
      const success = await vulogCarService.toggleStickyStationWithCar(
        car.vulogId,
        zoneId,
        true,
      );

      expect(success).toEqual(true);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  // Vulog AiMA, Journey ID
  describe('🔥 Success: [Status code: 403] Vulog AiMA Error, wake up successfully, vulog journey ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(car.vulogId.value)
      .makeCarModel(car.model)
      .makeStatus(ReservationStatus.Reserved)
      .makeBsgUserId(mockVulogUser.bsgUserId.value)
      .makePlateNumber(car.plate.value)
      .build();
    const vulogJourneyDto: VulogJourneyDto = new VulogJourneyDtoBuilder()
      .withId(reservationEntity.vulogTripId)
      .withVehicleId(car.vulogId.value)
      .build();

    let error;

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      await reservationRepo.save([reservationEntity]);

      mockOutgoingService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(vulogJourneyDto.id),
          )}`,
        )
        .replyOnce(
          HttpStatus.FORBIDDEN,
          new VulogAimaErrorResponseDtoBuilder()
            .withCode('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.ACCEPTED);

      mockOutgoingService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(vulogJourneyDto.id),
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      try {
        await vulogCarService.cancelCarReservationOrAllocation(
          new VulogJourneyOrTripId(vulogJourneyDto.id),
          mockVulogUser.bsgUserId,
        );
      } catch (err) {
        error = err;
      }
    });

    it('test verification', async () => {
      expect(!!error).toEqual(false);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('🔥 Success: [Status code: 403] Vulog AiMA Error, wake up failed, ping car successfully (online), vulog journey ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(car.vulogId.value)
      .makeCarModel(car.model)
      .makeStatus(ReservationStatus.Reserved)
      .makeBsgUserId(mockVulogUser.bsgUserId.value)
      .build();
    const vulogJourneyDto: VulogJourneyDto = new VulogJourneyDtoBuilder()
      .withId(reservationEntity.vulogTripId)
      .withVehicleId(car.vulogId.value)
      .build();

    let error;

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      await reservationRepo.save(reservationEntity);

      mockOutgoingService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(vulogJourneyDto.id),
          )}`,
        )
        .replyOnce(
          HttpStatus.FORBIDDEN,
          new VulogAimaErrorResponseDtoBuilder()
            .withCode('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      mockOutgoingService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(vulogJourneyDto.id),
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      try {
        await vulogCarService.cancelCarReservationOrAllocation(
          new VulogJourneyOrTripId(vulogJourneyDto.id),
          mockVulogUser.bsgUserId,
        );
      } catch (err) {
        error = err;
      }
    });

    it('test verification', async () => {
      expect(!!error).toEqual(false);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('🔥 Success: [Status code: 403] Vulog AiMA Error, wake up failed, ping car failed then successfully (online), vulog journey ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(car.vulogId.value)
      .makeCarModel(car.model)
      .makeStatus(ReservationStatus.Reserved)
      .makeBsgUserId(mockVulogUser.bsgUserId.value)
      .build();
    const vulogJourneyDto: VulogJourneyDto = new VulogJourneyDtoBuilder()
      .withId(reservationEntity.vulogTripId)
      .withVehicleId(car.vulogId.value)
      .build();

    let error;

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      await reservationRepo.save(reservationEntity);

      mockOutgoingService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(vulogJourneyDto.id),
          )}`,
        )
        .replyOnce(
          HttpStatus.FORBIDDEN,
          new VulogAimaErrorResponseDtoBuilder()
            .withCode('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      /** START: Retry simulation */
      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.OK);
      /** END: Retry simulation */

      mockOutgoingService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(vulogJourneyDto.id),
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      try {
        await vulogCarService.cancelCarReservationOrAllocation(
          new VulogJourneyOrTripId(vulogJourneyDto.id),
          mockVulogUser.bsgUserId,
        );
      } catch (err) {
        error = err;
      }
    });

    it('test verification', async () => {
      expect(!!error).toEqual(false);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('💦 Failed: [Status code: 403] Vulog AiMA Error, wake up failed, ping car failed (offline), vulog journey ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(car.vulogId.value)
      .makeCarModel(car.model)
      .makeStatus(ReservationStatus.Reserved)
      .makeBsgUserId(mockVulogUser.bsgUserId.value)
      .build();
    const vulogJourneyDto: VulogJourneyDto = new VulogJourneyDtoBuilder()
      .withId(reservationEntity.vulogTripId)
      .withVehicleId(car.vulogId.value)
      .build();

    let error;

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      await reservationRepo.save(reservationEntity);

      mockOutgoingService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(vulogJourneyDto.id),
          )}`,
        )
        .replyOnce(
          HttpStatus.FORBIDDEN,
          new VulogAimaErrorResponseDtoBuilder()
            .withCode('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      /** START: Retry simulation */
      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);
      /** END: Retry simulation */

      try {
        await vulogCarService.cancelCarReservationOrAllocation(
          new VulogJourneyOrTripId(vulogJourneyDto.id),
          mockVulogUser.bsgUserId,
        );
      } catch (err) {
        error = err;
      }
    });

    it('test verification', async () => {
      expect(error.cause).toEqual(
        'Failed to ping vehicle after trying to wake it up',
      );
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('💦 Failed: [Status code: 403] Vulog AiMA Error, wake up failed, ping car failed (offline), vulog journey ID is given for retry logic, cancelation flag is enabled', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(car.vulogId.value)
      .makeCarModel(car.model)
      .makeStatus(ReservationStatus.Reserved)
      .makeBsgUserId(mockVulogUser.bsgUserId.value)
      .makeStartedAt(undefined)
      .build();

    let error;

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      await reservationRepo.save(reservationEntity);

      spyOn_featureFlagService__shouldCancelReservationAfterBestEffort.mockResolvedValueOnce(
        true,
      );

      mockOutgoingService
        .onPost(
          `${VulogPaths.startOrEndTrip(
            new VulogJourneyOrTripId(reservationEntity.vulogTripId),
          )}`,
        )
        .replyOnce(
          HttpStatus.FORBIDDEN,
          new VulogAimaErrorResponseDtoBuilder()
            .withCode('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      /** START: Retry simulation */
      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);
      /** END: Retry simulation */

      mockOutgoingService
        .onPost(
          `${VulogPaths.backOfficeExpert(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
          {
            cmd: 'Trip Termination',
          },
        )
        .replyOnce(HttpStatus.OK);

      try {
        await vulogCarService.startRental(
          mockVulogUser.bsgUserId,
          new VulogJourneyOrTripId(reservationEntity.vulogTripId),
        );
      } catch (err) {
        error = err;
      }
    });

    it('test verification', async () => {
      const updatedReservationEntity = await reservationRepo.findOne({
        where: {
          id: reservationEntity.id,
        },
      });

      expect(updatedReservationEntity.status).toEqual(
        ReservationStatus.Cancelled,
      );
      expect(updatedReservationEntity.canceledAt).toBeDefined();
      expect(updatedReservationEntity.startedAt).toEqual(null);
      expect(updatedReservationEntity.deletedBy).toEqual(
        'The rental was canceled by CRS due to technical problems with Vulog',
      );

      expect(error.cause).toEqual(
        'Due to technical issues, your rental has been cancelled. Please note that your rental is not eligible for billing. Sorry for the inconvenience, you should try to book car again.',
      );
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  // Vulog Vehicle Gateway, Car ID
  describe('🔥 Success: [Status code: 410] VVG Error, wake up successfully, vulog car ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const zoneId: VulogZoneId = new VulogZoneId(aMD5());

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            zoneId,
          )}`,
        )
        .replyOnce(
          HttpStatus.GONE,
          new VVGErrorResponseDtoBuilder()
            .withCodeStr('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.ACCEPTED);

      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            zoneId,
          )}`,
        )
        .replyOnce(HttpStatus.OK, { statusCode: HttpStatus.OK });
    });

    it('test verification', async () => {
      const success = await vulogCarService.toggleStickyStationWithCar(
        car.vulogId,
        zoneId,
        true,
      );

      expect(success).toEqual(true);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('🔥 Success: [Status code: 410] VVG Error, wake up failed, ping car successfully (online), vulog car ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const zoneId: VulogZoneId = new VulogZoneId(aMD5());

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            zoneId,
          )}`,
        )
        .replyOnce(
          HttpStatus.GONE,
          new VVGErrorResponseDtoBuilder()
            .withCodeStr('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            zoneId,
          )}`,
        )
        .replyOnce(HttpStatus.OK, { statusCode: HttpStatus.OK });
    });

    it('test verification', async () => {
      const success = await vulogCarService.toggleStickyStationWithCar(
        car.vulogId,
        zoneId,
        true,
      );

      expect(success).toEqual(true);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('🔥 Success: [Status code: 410] VVG Error, wake up failed, ping car failed then successfully (online), vulog car ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const zoneId: VulogZoneId = new VulogZoneId(aMD5());

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            zoneId,
          )}`,
        )
        .replyOnce(
          HttpStatus.GONE,
          new VVGErrorResponseDtoBuilder()
            .withCodeStr('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      /** START: Retry simulation */
      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.OK);
      /** END: Retry simulation */

      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            zoneId,
          )}`,
        )
        .replyOnce(HttpStatus.OK, { statusCode: HttpStatus.OK });
    });

    it('test verification', async () => {
      const success = await vulogCarService.toggleStickyStationWithCar(
        car.vulogId,
        zoneId,
        true,
      );

      expect(success).toEqual(true);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('💦 Failed: [Status code: 410] VVG Error, wake up failed, ping car failed (offline), vulog car ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const zoneId: VulogZoneId = new VulogZoneId(aMD5());

    let error;

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      mockOutgoingService
        .onPut(
          `${VulogPaths.backOfficeStickStationWithCar(
            config.vulogConfig.fleetId,
            car.vulogId,
            zoneId,
          )}`,
        )
        .replyOnce(
          HttpStatus.GONE,
          new VVGErrorResponseDtoBuilder()
            .withCodeStr('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      /** START: Retry simulation */
      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);
      /** END: Retry simulation */

      try {
        await vulogCarService.toggleStickyStationWithCar(
          car.vulogId,
          zoneId,
          true,
        );
      } catch (err) {
        error = err;
      }
    });

    it('test verification', async () => {
      expect(error.cause).toEqual(
        'Failed to ping vehicle after trying to wake it up',
      );
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  // Vulog Vehicle Gateway, Journey ID
  describe('🔥 Success: [Status code: 410] VVG Error, wake up successfully, vulog journey ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(car.vulogId.value)
      .makeCarModel(car.model)
      .makeStatus(ReservationStatus.Reserved)
      .makeBsgUserId(mockVulogUser.bsgUserId.value)
      .build();
    const vulogJourneyDto: VulogJourneyDto = new VulogJourneyDtoBuilder()
      .withId(reservationEntity.vulogTripId)
      .withVehicleId(car.vulogId.value)
      .build();

    let error;

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      await reservationRepo.save(reservationEntity);

      mockOutgoingService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(vulogJourneyDto.id),
          )}`,
        )
        .replyOnce(
          HttpStatus.GONE,
          new VVGErrorResponseDtoBuilder()
            .withCodeStr('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.ACCEPTED);

      mockOutgoingService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(vulogJourneyDto.id),
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      try {
        await vulogCarService.cancelCarReservationOrAllocation(
          new VulogJourneyOrTripId(vulogJourneyDto.id),
          mockVulogUser.bsgUserId,
        );
      } catch (err) {
        error = err;
      }
    });

    it('test verification', async () => {
      expect(!!error).toEqual(false);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('🔥 Success: [Status code: 410] VVG Error, wake up failed, ping car successfully (online), vulog journey ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(car.vulogId.value)
      .makeCarModel(car.model)
      .makeStatus(ReservationStatus.Reserved)
      .makeBsgUserId(mockVulogUser.bsgUserId.value)
      .build();
    const vulogJourneyDto: VulogJourneyDto = new VulogJourneyDtoBuilder()
      .withId(reservationEntity.vulogTripId)
      .withVehicleId(car.vulogId.value)
      .build();

    let error;

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      await reservationRepo.save(reservationEntity);

      mockOutgoingService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(vulogJourneyDto.id),
          )}`,
        )
        .replyOnce(
          HttpStatus.GONE,
          new VVGErrorResponseDtoBuilder()
            .withCodeStr('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      mockOutgoingService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(vulogJourneyDto.id),
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      try {
        await vulogCarService.cancelCarReservationOrAllocation(
          new VulogJourneyOrTripId(vulogJourneyDto.id),
          mockVulogUser.bsgUserId,
        );
      } catch (err) {
        error = err;
      }
    });

    it('test verification', async () => {
      expect(!!error).toEqual(false);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('🔥 Success: [Status code: 410] VVG Error, wake up failed, ping car failed then successfully (online), vulog journey ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(car.vulogId.value)
      .makeCarModel(car.model)
      .makeStatus(ReservationStatus.Reserved)
      .makeBsgUserId(mockVulogUser.bsgUserId.value)
      .build();
    const vulogJourneyDto: VulogJourneyDto = new VulogJourneyDtoBuilder()
      .withId(reservationEntity.vulogTripId)
      .withVehicleId(car.vulogId.value)
      .build();

    let error;

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      await reservationRepo.save(reservationEntity);

      mockOutgoingService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(vulogJourneyDto.id),
          )}`,
        )
        .replyOnce(
          HttpStatus.GONE,
          new VVGErrorResponseDtoBuilder()
            .withCodeStr('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      /** START: Retry simulation */
      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.OK);
      /** END: Retry simulation */

      mockOutgoingService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(vulogJourneyDto.id),
          )}`,
        )
        .replyOnce(HttpStatus.OK);

      try {
        await vulogCarService.cancelCarReservationOrAllocation(
          new VulogJourneyOrTripId(vulogJourneyDto.id),
          mockVulogUser.bsgUserId,
        );
      } catch (err) {
        error = err;
      }
    });

    it('test verification', async () => {
      expect(!!error).toEqual(false);
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('💦 Failed: [Status code: 410] VVG Error, wake up failed, ping car failed (offline), vulog journey ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(car.vulogId.value)
      .makeCarModel(car.model)
      .makeStatus(ReservationStatus.Reserved)
      .makeBsgUserId(mockVulogUser.bsgUserId.value)
      .build();
    const vulogJourneyDto: VulogJourneyDto = new VulogJourneyDtoBuilder()
      .withId(reservationEntity.vulogTripId)
      .withVehicleId(car.vulogId.value)
      .build();

    let error;

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      await reservationRepo.save(reservationEntity);

      mockOutgoingService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(vulogJourneyDto.id),
          )}`,
        )
        .replyOnce(
          HttpStatus.GONE,
          new VVGErrorResponseDtoBuilder()
            .withCodeStr('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      /** START: Retry simulation */
      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);
      /** END: Retry simulation */

      try {
        await vulogCarService.cancelCarReservationOrAllocation(
          new VulogJourneyOrTripId(vulogJourneyDto.id),
          mockVulogUser.bsgUserId,
        );
      } catch (err) {
        error = err;
      }
    });

    it('test verification', async () => {
      expect(error.cause).toEqual(
        'Failed to ping vehicle after trying to wake it up',
      );
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('💦 Failed: [Status code: 410] VVG Error, wake up failed, ping car failed (offline), vulog journey ID is given for retry logic, cancelation flag is enabled', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(car.vulogId.value)
      .makeCarModel(car.model)
      .makeStatus(ReservationStatus.Reserved)
      .makeBsgUserId(mockVulogUser.bsgUserId.value)
      .makeStartedAt(undefined)
      .build();

    let error;

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      await reservationRepo.save(reservationEntity);

      spyOn_featureFlagService__shouldCancelReservationAfterBestEffort.mockResolvedValueOnce(
        true,
      );

      mockOutgoingService
        .onPost(
          `${VulogPaths.startOrEndTrip(
            new VulogJourneyOrTripId(reservationEntity.vulogTripId),
          )}`,
        )
        .replyOnce(
          HttpStatus.GONE,
          new VVGErrorResponseDtoBuilder()
            .withCodeStr('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      /** START: Retry simulation */
      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);
      /** END: Retry simulation */

      mockOutgoingService
        .onPost(
          `${VulogPaths.backOfficeExpert(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
          {
            cmd: 'Trip Termination',
          },
        )
        .replyOnce(HttpStatus.OK);

      try {
        await vulogCarService.startRental(
          mockVulogUser.bsgUserId,
          new VulogJourneyOrTripId(reservationEntity.vulogTripId),
        );
      } catch (err) {
        error = err;
      }
    });

    it('test verification', async () => {
      const updatedReservationEntity = await reservationRepo.findOne({
        where: {
          id: reservationEntity.id,
        },
      });

      expect(updatedReservationEntity.status).toEqual(
        ReservationStatus.Cancelled,
      );
      expect(updatedReservationEntity.canceledAt).toBeDefined();
      expect(updatedReservationEntity.startedAt).toEqual(null);
      expect(updatedReservationEntity.deletedBy).toEqual(
        'The rental was canceled by CRS due to technical problems with Vulog',
      );

      expect(error.cause).toEqual(
        'Due to technical issues, your rental has been cancelled. Please note that your rental is not eligible for billing. Sorry for the inconvenience, you should try to book car again.',
      );
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });

  describe('💦 Failed: [Status code: 410] VVG Error, wake up failed, ping car failed then execute task again failed, vulog journey ID is given for retry logic', () => {
    const car: Car = new CarBuilder()
      .withPlate(new CarPlateNumber(`plate-${DateTime.now().toMillis()}`))
      .build();
    const reservationEntity: ReservationEntity = new ReservationEntityBuilder()
      .makeCarId(car.vulogId.value)
      .makeCarModel(car.model)
      .makeStatus(ReservationStatus.Reserved)
      .makeBsgUserId(mockVulogUser.bsgUserId.value)
      .build();
    const vulogJourneyDto: VulogJourneyDto = new VulogJourneyDtoBuilder()
      .withId(reservationEntity.vulogTripId)
      .withVehicleId(car.vulogId.value)
      .build();

    let error;

    beforeAll(async () => {
      await carRepo.save(CarEntity.from(car));

      await reservationRepo.save(reservationEntity);

      mockOutgoingService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(vulogJourneyDto.id),
          )}`,
        )
        .replyOnce(
          HttpStatus.GONE,
          new VVGErrorResponseDtoBuilder()
            .withCodeStr('carConnectionProblem')
            .build(),
        );

      mockOutgoingService
        .onPost(
          `${VulogPaths.vvgWakeUpVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GATEWAY_TIMEOUT);

      /** START: Retry simulation */
      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.GONE);

      mockOutgoingService
        .onGet(
          `${VulogPaths.vvgPingVehicle(
            config.vulogConfig.fleetId,
            car.vulogId,
          )}`,
        )
        .replyOnce(HttpStatus.OK);
      /** END: Retry simulation */

      // All retry steps are passed, but retry task failed
      mockOutgoingService
        .onDelete(
          `${VulogPaths.cancelBooking(
            new VulogJourneyOrTripId(vulogJourneyDto.id),
          )}`,
        )
        .replyOnce(
          HttpStatus.GONE,
          new VVGErrorResponseDtoBuilder()
            .withCodeStr('carConnectionProblem')
            .build(),
        );

      try {
        await vulogCarService.cancelCarReservationOrAllocation(
          new VulogJourneyOrTripId(vulogJourneyDto.id),
          mockVulogUser.bsgUserId,
        );
      } catch (err) {
        error = err;
      }
    });

    it('test verification', async () => {
      expect(error.data.codeStr).toEqual('carConnectionProblem');
    });

    afterAll(async () => {
      await cleanUpResources();
    });
  });
});
