import { DateTime } from 'luxon';
import { VulogDate } from '../../../../../src/common/tiny-types/vulog-date.type';

const dtNowFunc = DateTime.now;

describe('VulogDate', () => {
  const spyOnDateTime_Now = jest.spyOn(DateTime, 'now');

  beforeAll(() => {
    spyOnDateTime_Now.mockImplementation(dtNowFunc);
  });

  describe('now()', () => {
    it('should return a VulogDate with the current time', () => {
      const now = DateTime.now();

      spyOnDateTime_Now.mockReturnValue(now);

      expect(VulogDate.now().value).toBe(now.toUTC().toISO());
    });
  });

  afterEach(() => {
    spyOnDateTime_Now.mockImplementation(dtNowFunc);
  });
});
