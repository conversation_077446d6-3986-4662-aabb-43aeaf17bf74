import { VulogApiError } from 'src/common/errors/vulog-api-error';
import {
  BsgUserId,
  CarPlateNumber,
  VulogCarId,
  VulogJourneyOrTripId,
  VulogProfileId,
} from 'src/common/tiny-types';
import { ContinuationTokenManager } from 'src/common/utils/continuation-token-manager';
import { config } from 'src/config';
import { Url } from 'src/logic/url';
import { VulogClientService } from 'src/logic/vulog/vulog-client.service';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';
import {
  VulogRealTimeBookingStatus,
  VulogRealTimeVehicleStatus,
} from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { VvgCarListDto } from 'src/model/dtos/vvg-car/vvg-car-list.dto';
import { VvgCarDto } from 'src/model/dtos/vvg-car/vvg-car.dto';
import {
  createTestVulogCarRealTimeDto,
  createTestVvgCarDto,
} from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestVulogJourneyDto } from 'test/helpers/builders/dtos/test-vulog-journey-dto.builder';
import { createTestVulogTripDto } from 'test/helpers/builders/dtos/test-vulog-trip-dto.builder';

import { faker } from '@faker-js/faker';
import { CacheModule } from '@nestjs/cache-manager';
import { HttpStatus } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { CacheService } from 'src/logic/cache/cache.service';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { VulogTelemetryService } from 'src/logic/telemetry-repository/vulog/vulog-telemetry.service';

// TODO: Refactor this test or remove it in the future
describe.skip('VulogService (VulogCarClient and VulogTelemetryClient)', () => {
  const testVvgCarDto1 = createTestVvgCarDto();
  const testVvgCarDto2 = createTestVvgCarDto();
  const testVvgCarDto3 = createTestVvgCarDto();
  const testVvgCarDtos: VvgCarDto[] = [
    testVvgCarDto1,
    testVvgCarDto2,
    testVvgCarDto3,
  ];

  const testVulogCarRtDto1 = createTestVulogCarRealTimeDto();
  const testVulogCarRtDto2 = createTestVulogCarRealTimeDto();
  const testVulogCarRtDto3 = createTestVulogCarRealTimeDto();
  testVulogCarRtDto1.vehicle_status = VulogRealTimeVehicleStatus.Available;
  testVulogCarRtDto1.booking_status = VulogRealTimeBookingStatus.Available;
  testVulogCarRtDto2.vehicle_status = VulogRealTimeVehicleStatus.Available;
  testVulogCarRtDto2.booking_status = VulogRealTimeBookingStatus.Available;
  testVulogCarRtDto3.vehicle_status = VulogRealTimeVehicleStatus.Available;
  testVulogCarRtDto3.booking_status = VulogRealTimeBookingStatus.Available;
  const testVulogCarRtDtos = [
    testVulogCarRtDto1,
    testVulogCarRtDto2,
    testVulogCarRtDto3,
  ];

  const testDtosNextPage: VvgCarDto[] = [
    createTestVvgCarDto(),
    createTestVvgCarDto(),
  ];

  const fakeCarAdminInfoDto = createTestVvgCarDto();

  const fakeCarId = new VulogCarId(faker.string.uuid());
  const awakeCarId = new VulogCarId(faker.string.uuid());
  const unawakeCarId = new VulogCarId(faker.string.uuid());
  const unexpectedCarId = new VulogCarId(faker.string.uuid());

  const testCarAdminInfoListDto = new VvgCarListDto();
  testCarAdminInfoListDto.content = testVvgCarDtos;
  testCarAdminInfoListDto.continuationToken = faker.string.uuid();

  const testCarAdminInfoListDtoNextPage = new VvgCarListDto();
  testCarAdminInfoListDtoNextPage.content = testDtosNextPage;
  testCarAdminInfoListDtoNextPage.continuationToken = faker.string.uuid();

  const testVulogJourneyDtoReservation = createTestVulogJourneyDto(false, true);
  const testVulogJourneyDtoRental = createTestVulogJourneyDto(true, false);
  const testVulogJourneyDtoRental2 = createTestVulogJourneyDto(true, false);

  const cancelReservationSuccessTestJourneyId = faker.string.uuid();
  const cancelReservationFailureTestJourneyId = faker.string.uuid();

  const testVulogTripDtoReservation1 = createTestVulogTripDto(false, true);
  const testVulogTripDtoReservation2 = createTestVulogTripDto(false, true);
  const testVulogTripDtoRental3 = createTestVulogTripDto(true, false);
  const testVulogTripDtoRental4 = createTestVulogTripDto(true, false);
  const testVulogTripDtoReservation5 = createTestVulogTripDto(false, true);
  const testVulogTripsPg1 = [
    testVulogTripDtoReservation1,
    testVulogTripDtoReservation2,
    testVulogTripDtoRental3,
  ];
  const testVulogTripsPg2 = [
    testVulogTripDtoRental4,
    testVulogTripDtoReservation5,
  ];
  const testVulogTripsCarPlateFilterPg1 = [
    testVulogTripDtoReservation2,
    testVulogTripDtoRental4,
  ];
  const testVulogTripsCarPlateFilterPg2 = [testVulogTripDtoReservation5];
  const testCarPlate = faker.vehicle.vrm();

  const testBsgUserIdReservation = new BsgUserId(faker.string.uuid());
  const testBsgUserIdRental = new BsgUserId(faker.string.uuid());

  const stubGetFn = jest.fn((url: Url, _, __, ___, bsgUserId) => {
    switch (url.value) {
      case VulogPaths.vvgVehicles(config.vulogConfig.fleetId).value:
        return testCarAdminInfoListDto;
      case VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId).value:
        return testVulogCarRtDtos;
      case VulogPaths.vvgVehicles(config.vulogConfig.fleetId, fakeCarId).value:
        return fakeCarAdminInfoDto;
      case VulogPaths.vvgVehicles(config.vulogConfig.fleetId).addQueryParams({
        continuationToken: testCarAdminInfoListDtoNextPage.continuationToken,
      }).value:
        return testCarAdminInfoListDtoNextPage;
      case VulogPaths.currentJourneys().value:
        if (bsgUserId === testBsgUserIdReservation)
          return { journeys: [testVulogJourneyDtoReservation] };
        if (bsgUserId === testBsgUserIdRental)
          return { journeys: [testVulogJourneyDtoRental] };
    }
  });

  const stubGetAllPagesAsArray = jest.fn((url: Url) => {
    if (
      url.value === VulogPaths.finishedTrips(config.vulogConfig.fleetId).value
    ) {
      return [...testVulogTripsPg1, ...testVulogTripsPg2];
    }

    if (
      url.value ===
      VulogPaths.backOfficeVehicles(config.vulogConfig.fleetId).value
    ) {
      return testVulogCarRtDtos;
    }

    if (
      url.value ===
      VulogPaths.finishedTrips(config.vulogConfig.fleetId).addQueryParams({
        plate: testCarPlate,
      }).value
    ) {
      return [
        ...testVulogTripsCarPlateFilterPg1,
        ...testVulogTripsCarPlateFilterPg2,
      ];
    }
  });

  const stubPostFn = jest.fn((url: Url) => {
    switch (url.value) {
      case VulogPaths.vvgWakeUpVehicle(config.vulogConfig.fleetId, unawakeCarId)
        .value:
      case VulogPaths.vvgLockVehicle(config.vulogConfig.fleetId, fakeCarId)
        .value:
      case VulogPaths.vvgUnlockVehicle(config.vulogConfig.fleetId, fakeCarId)
        .value:
      case VulogPaths.vvgImmobilizeVehicle(
        config.vulogConfig.fleetId,
        fakeCarId,
      ).value:
      case VulogPaths.vvgMobilizeVehicle(config.vulogConfig.fleetId, fakeCarId)
        .value:
      case VulogPaths.vvgStandbyVehicle(config.vulogConfig.fleetId, awakeCarId)
        .value:
        return { statusCode: HttpStatus.OK, statusMessage: 'OK' };
      case VulogPaths.vvgWakeUpVehicle(config.vulogConfig.fleetId, awakeCarId)
        .value:
        return { statusCode: HttpStatus.ACCEPTED, statusMessage: 'Accepted' };
      case VulogPaths.vvgStandbyVehicle(
        config.vulogConfig.fleetId,
        unawakeCarId,
      ).value:
        const err = new VulogApiError('Mock error');
        err.statusCode = HttpStatus.GONE;
        throw err;
      case VulogPaths.vvgStandbyVehicle(
        config.vulogConfig.fleetId,
        unexpectedCarId,
      ).value:
        return { statusCode: HttpStatus.ACCEPTED, statusMessage: 'Accepted' };
      case VulogPaths.bookJourney(fakeCarId).value:
      case VulogPaths.vehicleBookingBackOffice(
        config.vulogConfig.fleetId,
        fakeCarId,
      ).value:
        return testVulogJourneyDtoReservation;
      case VulogPaths.startOrEndTrip(
        new VulogJourneyOrTripId(testVulogJourneyDtoRental.id),
      ).value:
        return testVulogJourneyDtoRental;
    }
  });

  const stubDeleteFn = jest.fn((url: Url) => {
    let err;
    switch (url.value) {
      case VulogPaths.cancelBooking(
        new VulogJourneyOrTripId(cancelReservationSuccessTestJourneyId),
      ).value:
      case VulogPaths.vehicleBookingBackOffice(
        config.vulogConfig.fleetId,
        fakeCarId,
      ).value:
      case VulogPaths.startOrEndTrip(
        new VulogJourneyOrTripId(testVulogJourneyDtoRental.id),
      ).value:
        return { statusCode: HttpStatus.OK, statusMessage: 'OK' };
      case VulogPaths.cancelBooking(
        new VulogJourneyOrTripId(cancelReservationFailureTestJourneyId),
      ).value:
        err = new VulogApiError('Mock error');
        err.statusCode = HttpStatus.NOT_ACCEPTABLE;
        throw err;
      case VulogPaths.startOrEndTrip(
        new VulogJourneyOrTripId(testVulogJourneyDtoRental2.id),
      ).value:
        err = new VulogApiError('Mock error');
        err.statusCode = HttpStatus.FORBIDDEN;
        throw err;
    }
  });

  let vulogCarClient: VulogCarService;
  let vulogTelemetryClient: VulogTelemetryService;
  let cache: CacheService;

  beforeAll(async () => {
    const testModule = await Test.createTestingModule({
      imports: [
        CacheModule.register({
          isGlobal: true,
        }),
      ],
      providers: [
        VulogCarService,
        VulogTelemetryService,
        CacheService,
        {
          provide: VulogClientService,
          useValue: {
            get: stubGetFn,
            getAsArray: stubGetFn,
            getAllPagesAsArray: stubGetAllPagesAsArray,
            post: stubPostFn,
            delete: stubDeleteFn,
          },
        },
      ],
    }).compile();

    vulogCarClient = testModule.get<VulogCarService>(VulogCarService);
    vulogTelemetryClient = testModule.get<VulogTelemetryService>(
      VulogTelemetryService,
    );
    cache = testModule.get<CacheService>(CacheService);
  });

  it('should get a list of available vehicles for a city ID from Vulog', async () => {
    const availableCars = await vulogCarClient.getAllAvailableCars();

    expect(availableCars).toEqual(
      testVulogCarRtDtos.map((testDto) => testDto.toRealtimeCar()),
    );
  });

  it('should get admin information about a vehicle from Vulog', async () => {
    const carInfo = await vulogTelemetryClient.getCarInformation(fakeCarId);
    expect(carInfo).toEqual(fakeCarAdminInfoDto.toRealTimeCar());
  });

  it('should get the first page of a full list of vehicles from Vulog', async () => {
    const allCarsFirstPage = await vulogTelemetryClient.getAllCars();
    expect(allCarsFirstPage.content).toEqual(
      testVvgCarDtos.map((testDto) => testDto.toRealTimeCar()),
    );
  });

  it('should get the next page of a full list of vehicles from Vulog, using an internal continuation token', async () => {
    const testInternalContinuationToken =
      ContinuationTokenManager.generateInternalContinuationToken();
    await cache.set(
      testInternalContinuationToken,
      testCarAdminInfoListDtoNextPage.continuationToken,
    );

    const allCarsNextPage = await vulogTelemetryClient.getAllCars(
      testInternalContinuationToken,
    );
    expect(allCarsNextPage.content).toEqual(
      testDtosNextPage.map((testDto) => testDto.toRealTimeCar()),
    );
  });

  it('should send a successful wake up signal to an unawake car', async () => {
    const result = await vulogTelemetryClient.wakeUpCar(unawakeCarId);
    expect(result.isAwake).toEqual(false);
  });

  it('should indicate that a car is already awake on sending a wake up signal to an awake car', async () => {
    const result = await vulogTelemetryClient.wakeUpCar(awakeCarId);
    expect(result.isAwake).toEqual(true);
  });

  it('should be able to successfully send a lock command to a car', async () => {
    const result = await vulogTelemetryClient.lockCar(fakeCarId);
    expect(result.success).toEqual(true);
  });

  it('should be able to successfully send an unlock command to a car', async () => {
    const result = await vulogTelemetryClient.unlockCar(fakeCarId);
    expect(result.success).toEqual(true);
  });

  it('should be able to successfully send an immobilize command to a car', async () => {
    const result = await vulogTelemetryClient.immobilizeCar(fakeCarId);
    expect(result.success).toEqual(true);
  });

  it('should be able to successfully send a mobilize command to a car', async () => {
    const result = await vulogTelemetryClient.mobilizeCar(fakeCarId);
    expect(result.success).toEqual(true);
  });

  it('should be able to successfully send a standby command to an awake car', async () => {
    const result = await vulogTelemetryClient.standbyCar(awakeCarId);
    expect(result.isAwake).toEqual(true);
  });

  it('should be able to indicate that a car is already on standby on sending a standby signal to an unawake car', async () => {
    const result = await vulogTelemetryClient.standbyCar(unawakeCarId);
    expect(result.isAwake).toEqual(false);
  });

  it('should throw an error on receiving an unexpected status code when attempting to put a car on standby', async () => {
    await expect(
      vulogTelemetryClient.standbyCar(unexpectedCarId),
    ).rejects.toThrowError(VulogApiError);
  });

  it('should be able to allocate a vehicle', async () => {
    const result = await vulogCarClient.bookCar(
      fakeCarId,
      new BsgUserId(faker.string.uuid()),
      config.vulogConfig.serviceId,
      new VulogProfileId(faker.string.uuid()),
    );
    expect(result).toStrictEqual(
      testVulogJourneyDtoReservation.toReservationInfo(),
    );
  });

  // Prioritize e2e test for October release
  it.skip('should be able to allocate a vehicle via the Vulog back office API', async () => {
    // const result = await vulogCarClient.bookCarAsAdmin(
    //   fakeCarId,
    //   new VulogUserId(faker.string.uuid()),
    // );
    // expect(result).toStrictEqual(
    //   testVulogJourneyDtoReservation.toReservationInfo(),
    // );
  });

  it('should be able to get the current reservations of a user', async () => {
    const result = await vulogCarClient.getUserCurrentReservations(
      testBsgUserIdReservation,
    );
    expect(result).toHaveLength(1);
    expect(result).toStrictEqual([
      testVulogJourneyDtoReservation.toReservationInfo(),
    ]);
  });

  it('should be able to cancel a car reservation/allocation', async () => {
    await expect(
      vulogCarClient.cancelCarReservationOrAllocation(
        new VulogJourneyOrTripId(cancelReservationSuccessTestJourneyId),
        new BsgUserId(faker.string.uuid()),
      ),
    ).resolves.not.toThrowError();
  });

  it('should be able to cancel a car reservation/allocation via the Vulog back office API', async () => {
    await expect(
      vulogCarClient.cancelCarReservationOrAllocationAsAdmin(fakeCarId),
    ).resolves.not.toThrowError();
  });

  it('should throw an error if the car reservation/allocation to be cancelled cannot be found', async () => {
    await expect(
      vulogCarClient.cancelCarReservationOrAllocation(
        new VulogJourneyOrTripId(cancelReservationFailureTestJourneyId),
        new BsgUserId(faker.string.uuid()),
      ),
    ).rejects.toThrow(VulogApiError);
  });

  // We will only need these tests when we implement admin endpoints that can get a user's history of trips

  // it('should be able to get one page of reservations (Vulog trips)', async () => {
  //   const result = await vulogService.getAllReservationsAndAllocations();
  //   expect(result).toStrictEqual(
  //     testVulogTripsPg1
  //       .filter((dto) => dto.isReservationOrAllocation())
  //       .map((dto) => dto.toReservationAdminInfo()),
  //   );
  // });

  // it('should be able to get one page of rentals (Vulog trips)', async () => {
  //   const result = await vulogService.getAllRentals();
  //   expect(result).toStrictEqual(
  //     testVulogTripsPg1
  //       .filter((dto) => dto.isRental())
  //       .map((dto) => dto.toRentalInfo()),
  //   );
  // });

  it('should be able to get all pages of reservations (Vulog trips)', async () => {
    const result =
      await vulogCarClient.getAllFinishedReservationsAndAllocations();

    const expectedResult = testVulogTripsPg1
      .concat(testVulogTripsPg2)
      .filter((dto) => dto.isReservationOrAllocation())
      .map((dto) => dto.toReservationAdminInfo());

    expect(result).toStrictEqual(expectedResult);
  });

  it('should be able to get all pages of rentals (Vulog trips)', async () => {
    const result = await vulogCarClient.getAllFinishedRentals();

    const expectedResult = testVulogTripsPg1
      .concat(testVulogTripsPg2)
      .filter((dto) => dto.isRental())
      .map((dto) => dto.toRentalInfo());

    expect(result).toStrictEqual(expectedResult);
  });

  it('should be able to get all reservations filtered by car plate', async () => {
    const result =
      await vulogCarClient.getAllFinishedReservationsAndAllocations(
        new CarPlateNumber(testCarPlate),
      );

    const expectedResult = testVulogTripsCarPlateFilterPg1
      .concat(testVulogTripsCarPlateFilterPg2)
      .filter((dto) => dto.isReservationOrAllocation())
      .map((dto) => dto.toReservationAdminInfo());

    expect(result).toStrictEqual(expectedResult);
  });

  it('should be able to get all rentals filtered by car plate', async () => {
    const result = await vulogCarClient.getAllFinishedRentals(testCarPlate);

    const expectedResult = testVulogTripsCarPlateFilterPg1
      .concat(testVulogTripsCarPlateFilterPg2)
      .filter((dto) => dto.isRental())
      .map((dto) => dto.toRentalInfo());

    expect(result).toStrictEqual(expectedResult);
  });

  it('should be able to start a rental trip', async () => {
    const result = await vulogCarClient.startRental(
      new BsgUserId(faker.string.uuid()),
      new VulogJourneyOrTripId(testVulogJourneyDtoRental.id),
    );
    expect(result).toStrictEqual(testVulogJourneyDtoRental.toRentalInfo());
  });

  it('should be able to get the current rentals of a user (Vulog journeys)', async () => {
    const result = await vulogCarClient.getUserCurrentRentals(
      testBsgUserIdRental,
    );
    expect(result).toStrictEqual([testVulogJourneyDtoRental.toRentalInfo()]);
  });

  it('should be able to end a rental for a user', async () => {
    await expect(
      vulogCarClient.endRental(
        new VulogJourneyOrTripId(testVulogJourneyDtoRental.id),
        testBsgUserIdRental,
      ),
    ).resolves.not.toThrow();
  });

  it('should throw an error if a preconditon for ending a rental is unfulfilled', async () => {
    await expect(
      vulogCarClient.endRental(
        new VulogJourneyOrTripId(testVulogJourneyDtoRental2.id),
        testBsgUserIdRental,
      ),
    ).rejects.toThrowError(VulogApiError);
  });
});
