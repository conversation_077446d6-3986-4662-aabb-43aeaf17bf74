import { Test, TestingModule } from '@nestjs/testing';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { Car } from 'src/logic/car/domain/car';
import { FleetService } from 'src/model/fleet-service';
import { CarPlateNumber, VulogCarId, VulogCityId, VulogFleetId, VulogServiceId } from 'src/common/tiny-types';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { VulogCarRealTimeDtoBuilder } from 'test/helpers/builders/dtos/vulog/vulog-car.real-time.dto.builder';
import { VulogRealTimeBookingStatus, VulogRealTimeVehicleStatus } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { config } from 'src/config';
import { VulogClientService } from 'src/logic/vulog/vulog-client.service';
import { CacheService } from 'src/logic/cache/cache.service';
import { ConfigurationService } from '~shared/configuration/configuration.service';
import { CarService } from 'src/logic/car/car.service';
import { VulogFleetService } from 'src/logic/vulog/vulog-fleet-service.service';
import { VulogCarConnectionProblemService } from 'src/logic/vulog/vulog-car-connection-problem.service';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { aUUID } from 'test/helpers/random-data.helper';
import { FleetServiceBuilder } from 'test/helpers/builders/fleet-service.builder';

describe('VulogCarService', () => {
  let service: VulogCarService;
  let mockVulogClientService;
  let mockCacheService;
  let mockConfigurationService;
  let mockCarService;
  let mockVulogFleetService;
  let mockVulogCarConnectionProblemService;
  let mockFeatureFlagService;

  const mockWarningBatteryLevels = {
    [CarModelEnum.BlueCar]: 20,
    [CarModelEnum.OpelCorsaE]: 15,
  };

  const availableCarId = aUUID();

  const mockCar1: Car = new CarBuilder()
    .withVulogId(new VulogCarId(availableCarId))
    .withModel(CarModelEnum.OpelCorsaE)
    .withVin('VIN1')
    .withPlate(new CarPlateNumber('plate-01'))
    .build();

  const unavailableCarId = aUUID();
  const mockCar2: Car = new CarBuilder()
    .withVulogId(new VulogCarId(unavailableCarId))
    .withModel(CarModelEnum.BlueCar)
    .withVin('VIN2')
    .withPlate(new CarPlateNumber('plate-02'))
    .build();

  const mockCarMap = new Map<string, Car>([
    [availableCarId, mockCar1],
    [unavailableCarId, mockCar2],
  ]);

  const mockFleetService: FleetService = new FleetServiceBuilder()
    .withId(new VulogServiceId('test-fleet-1'))
    .withName('Test Fleet')
    .withFleetId(new VulogFleetId('mock-fleet-id'))
    .withCityId(new VulogCityId(aUUID()))
    .withZoneIds(['zone1', 'zone2'])
    .withVehicleIds([availableCarId, unavailableCarId])
    .build();

  const createMockRealTimeDto = (id: string, batteryLevel: number, available = true) => {
    return new VulogCarRealTimeDtoBuilder()
      .withId(id)
      .withAutonomy(100)
      .withBattery(batteryLevel)
      .withBookingStatus(available ? VulogRealTimeBookingStatus.Available : VulogRealTimeBookingStatus.Booked)
      .withVehicleStatus(available ? VulogRealTimeVehicleStatus.Available : VulogRealTimeVehicleStatus.OutOfService)
      .withFleetId(config.vulogConfig.fleetId.value)
      .withId(id)
      .build();
  };

  beforeEach(async () => {
    mockVulogClientService = {
      getAllPagesAsArray: jest.fn(),
      get: jest.fn(),
      getAsArray: jest.fn(),
    };

    mockCacheService = {
      get: jest.fn(),
      set: jest.fn(),
    };

    mockConfigurationService = {
      findOne: jest.fn().mockResolvedValue({
        value: mockWarningBatteryLevels,
      }),
    };

    mockCarService = {
      getMapByVulogId: jest.fn().mockResolvedValue(mockCarMap),
    };

    mockVulogFleetService = {
      getCustomerUsableFleetService: jest.fn().mockResolvedValue(mockFleetService),
    };

    mockVulogCarConnectionProblemService = {
      // Add necessary methods if needed
    };

    mockFeatureFlagService = {
      filterOutNonCustomerZones: jest.fn().mockResolvedValue(false),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VulogCarService,
        {
          provide: VulogClientService,
          useValue: mockVulogClientService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
        {
          provide: ConfigurationService,
          useValue: mockConfigurationService,
        },
        {
          provide: CarService,
          useValue: mockCarService,
        },
        {
          provide: VulogFleetService,
          useValue: mockVulogFleetService,
        },
        {
          provide: VulogCarConnectionProblemService,
          useValue: mockVulogCarConnectionProblemService,
        },
        {
          provide: FeatureFlagService,
          useValue: mockFeatureFlagService,
        },
      ],
    }).compile();

    service = module.get<VulogCarService>(VulogCarService);
  });

  describe('getCarsInRealtime', () => {
    it('should return all cars when availableOnly is false', async () => {
      const mockRealTimeDtos = [
        createMockRealTimeDto(availableCarId, 25, true),
        createMockRealTimeDto(unavailableCarId, 10, false),
      ];

      jest.spyOn(mockVulogClientService, 'getAsArray').mockResolvedValue(mockRealTimeDtos);

      const result = await service.getCarsInRealtime(false);

      expect(result.length).toBe(2);
    });

    it('should return only available cars when availableOnly is true', async () => {
      const mockRealTimeDtos = [
        createMockRealTimeDto(availableCarId, 25, true),
        createMockRealTimeDto(unavailableCarId, 10, false),
      ];

      jest.spyOn(mockVulogClientService, 'getAsArray').mockResolvedValue(mockRealTimeDtos);

      const result = await service.getCarsInRealtime(true);

      expect(result.length).toBe(1);
      expect(result[0].id.value).toMatch(availableCarId);
    });
  });
});
