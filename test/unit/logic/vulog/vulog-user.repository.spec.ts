import { BsgUserId, VulogUserId } from 'src/common/tiny-types';
import { VulogUserEntity } from 'src/model/repositories/entities/vulog-user.entity';
import { VulogUserRepository } from 'src/model/repositories/vulog-user.repository';
import { createTestVulogUserEntity } from 'test/helpers/builders/entities/test-vulog-user-entity.builder';

import { Test } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';

describe('VulogUserRepository', () => {
  let vulogUserRepository: VulogUserRepository;

  const mockVulogUserEntity = createTestVulogUserEntity();

  let mockDatabaseStore = [];

  const repositoryMockFactory = jest.fn(() => ({
    find: jest.fn(() => mockVulogUserEntity),
    findOne: jest.fn(() => mockVulogUserEntity),
    insert: jest.fn((vulogUserEntity: VulogUserEntity) => {
      mockDatabaseStore.push(vulogUserEntity);
    }),
  }));

  beforeAll(async () => {
    const testModule = await Test.createTestingModule({
      providers: [
        VulogUserRepository,
        {
          provide: getRepositoryToken(VulogUserEntity),
          useFactory: repositoryMockFactory,
        },
      ],
    }).compile();

    vulogUserRepository =
      testModule.get<VulogUserRepository>(VulogUserRepository);
  });

  afterEach(async () => {
    mockDatabaseStore = [];
  });

  it('should be able to insert a new user', async () => {
    expect(mockDatabaseStore).toHaveLength(0);

    await vulogUserRepository.insert(mockVulogUserEntity);

    expect(mockDatabaseStore).not.toHaveLength(0);
    expect(mockDatabaseStore[0]).toStrictEqual(mockVulogUserEntity);
  });

  it('should be able to find a Vulog user by the Vulog userId', async () => {
    const result = await vulogUserRepository.findVulogUser(
      new VulogUserId(mockVulogUserEntity.id),
    );
    expect(result).toStrictEqual(mockVulogUserEntity);
  });

  it('should be able to find a Vulog user by the BlueSG userId', async () => {
    const result = await vulogUserRepository.findVulogUserByBsgUserId(
      new BsgUserId(mockVulogUserEntity.bsgUserId),
    );
    expect(result).toStrictEqual(mockVulogUserEntity);
  });
});
