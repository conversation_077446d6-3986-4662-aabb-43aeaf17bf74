import { faker } from '@faker-js/faker';
import { Test } from '@nestjs/testing';
import { Page } from 'src/common/query/page';
import { PaginationQuery } from 'src/common/query/pagination-query';
import { Url } from 'src/logic/url';
import { VulogAdapterService } from 'src/logic/vulog/vulog-adapter.service';
import { VulogAuthService } from 'src/logic/vulog/vulog-auth.service';
import { VulogClientService } from 'src/logic/vulog/vulog-client.service';
import { VulogJourneyDto } from 'src/model/dtos/vulog-journey/vulog-journey.dto';
import { createTestVulogJourneyDto } from 'test/helpers/builders/dtos/test-vulog-journey-dto.builder';

describe('VulogClientService', () => {
  let vulogClientService: VulogClientService;

  const mockUrl = new Url('/mock/gateway/url');
  const testDto = createTestVulogJourneyDto(true, false);
  const testPage = new Page([testDto], 1, true);

  beforeAll(async () => {
    const testModule = await Test.createTestingModule({
      providers: [
        VulogClientService,
        {
          provide: VulogAdapterService,
          useValue: {
            get: () => testDto,
            getOnePage: () => testPage,
            getAllPagesAsArray: () => [testDto],
            getAsArray: () => [testDto],
            post: () => testDto,
            delete: () => testDto,
          },
        },
        {
          provide: VulogAuthService,
          useValue: {
            setupAuthConfig: () => ({
              headers: {
                Authorization: `Bearer ${faker.string.alphanumeric(20)}`,
              },
            }),
          },
        },
      ],
    }).compile();

    vulogClientService = testModule.get<VulogClientService>(VulogClientService);
  });

  it('should be able to execute a GET request to Vulog API', async () => {
    const response = await vulogClientService.get(mockUrl, VulogJourneyDto);
    expect(response).toStrictEqual(testDto);
  });

  it('should be able to execute a GET request, expecting an array, to Vulog API', async () => {
    const response = await vulogClientService.getAsArray(
      mockUrl,
      VulogJourneyDto,
    );
    expect(response).toStrictEqual([testDto]);
  });

  it('should be able to execute a paginated GET request to Vulog mobile API', async () => {
    const response = await vulogClientService.getOnePage(
      mockUrl,
      new PaginationQuery(),
      VulogJourneyDto,
    );
    expect(response).toStrictEqual(testPage);
  });

  it('should be able to execute a paginated GET request for all pages to Vulog mobile API', async () => {
    const response = await vulogClientService.getAllPagesAsArray(
      mockUrl,
      VulogJourneyDto,
    );
    expect(response).toStrictEqual([testDto]);
  });

  it('should be able to execute a POST request to Vulog API', async () => {
    const response = await vulogClientService.post(mockUrl, VulogJourneyDto);
    expect(response).toStrictEqual(testDto);
  });

  it('should be able to execute a DELETE request to Vulog API', async () => {
    const response = await vulogClientService.delete(mockUrl, VulogJourneyDto);
    expect(response).toStrictEqual(testDto);
  });
});
