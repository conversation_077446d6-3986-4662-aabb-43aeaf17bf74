import { Test, TestingModule } from '@nestjs/testing';
import { VulogTelemetryService } from 'src/logic/telemetry-repository/vulog/vulog-telemetry.service';
import { VulogClientService } from 'src/logic/vulog/vulog-client.service';
import { CacheService } from 'src/logic/cache/cache.service';
import { CarService } from 'src/logic/car/car.service';
import { VulogFleetService } from 'src/logic/vulog/vulog-fleet-service.service';
import { StationService } from 'src/logic/station/station.service';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { VvgVehicleStatusDto, VulogVehicleStatus, VulogVehicleZones} from 'src/model/dtos/vvg-vehicle-status/vvg-vehicle-status.dto';

import { Car } from 'src/logic/car/domain/car';
import { StationDto } from 'src/model/dtos/station.dto';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { VulogZone, VulogZoneType } from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { VulogZoneId } from 'src/common/tiny-types';

describe('VulogTelemetryService', () => {
  let service: VulogTelemetryService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VulogTelemetryService,
        { provide: VulogClientService, useValue: {} },
        { provide: CacheService, useValue: {} },
        { provide: CarService, useValue: {} },
        { provide: VulogFleetService, useValue: {} },
        { provide: StationService, useValue: {} },
        { provide: FeatureFlagService, useValue: {} },
      ],
    }).compile();

    service = module.get<VulogTelemetryService>(VulogTelemetryService);
  });

  describe('toTelemetryCarInfo', () => {
    it('should correctly transform VvgVehicleStatusDto to TelemetryCarInfo', () => {
      // Create a VvgVehicleStatusDto instance
      const vvgVehicleStatusDto = new VvgVehicleStatusDto();
      vvgVehicleStatusDto.id = 'test-car-id';
      vvgVehicleStatusDto.fleetId = 'mock-fleet-id';
      vvgVehicleStatusDto.boxId = 'mock-box-id';
      vvgVehicleStatusDto.hasAlerts = false;
      vvgVehicleStatusDto.sessions = [];

      // Set up the status
      const status = new VulogVehicleStatus();
      status.tractionBattery = { percentage: 75, km: 100 };
      status.charging = true;
      status.cablePlugged = true;
      status.doorsAndWindowsClosed = true;
      status.mileage = 50000;
      status.engineOn = true;
      status.locked = true;
      status.immobilizerOn = true;
      vvgVehicleStatusDto.status = status;

      // Set up tracking
      vvgVehicleStatusDto.tracking = { latitude: 1.2345, longitude: 6.7890, altitude: 100, head: 0, hdop: 0, speed: 0, captureDate: new Date().toISOString() };

      // Set up zones
      const zone = new VulogZone('zone1', 1, VulogZoneType.Allowed, true);
      zone.zoneId = 'zone1';
      zone.isSticky = () => true;
      const zones = new VulogVehicleZones();
      zones.current = [zone];
      vvgVehicleStatusDto.zones = zones;

      // Create mock data for parameters
      const staticCarInfo = new Car({
        id: '123456',
        model: CarModelEnum.BlueCar,
        plate: 'ABC123',
        vin: 'ABCDEF1234567890',
        vulogId: 'test-car-id',
        isActive: true,
      });

      const fleetZoneIds = new Map([['zone1', 'vulog-zone1']]);

      // Correctly create StationDto instance
      const stationDto = new StationDto();
      stationDto.id = 'station1';
      stationDto.sourceId = 'source1';
      stationDto.source = 'vulog';
      stationDto.displayName = 'Test Station';
      stationDto.latitude = '1.2345';
      stationDto.longitude = '6.7890';
      stationDto.street = 'Test Street';
      stationDto.postalCode = '12345';
      stationDto.city = 'Test City';
      stationDto.zoneId = 'zone1';

      const knownZoneStationMap = {
        'zone1': stationDto,
      };

      // Call the method
      const result = vvgVehicleStatusDto.toTelemetryCarInfo(staticCarInfo, fleetZoneIds, knownZoneStationMap);

      // Assert the result
      expect(result).toBeDefined();
      expect(result.id.value).toBe('test-car-id');
      expect(result.model.value).toBe(CarModelEnum.BlueCar);
      expect(result.plate.value).toBe('ABC123');
      expect(result.energyLevel).toBe(75);
      expect(result.isCharging).toBe(true);
      expect(result.isPluggedIn).toBe(true);
      expect(result.areDoorsAndWindowsClosed).toBe(true);
      expect(result.zoneIds).toHaveLength(1);
      expect(result.zoneIds[0]).toBeInstanceOf(VulogZoneId);
      expect(result.zoneIds[0].value).toBe('zone1');
      expect(result.mileage).toBe(50000);
      expect(result.engineOn).toBe(true);
      expect(result.locked).toBe(true);
      expect(result.immobilizerOn).toBe(true);
      expect(result.currentZones).toHaveLength(1);
      expect(result.allowedStickyZone).toBeDefined();
      expect(result.currentStation).toBeDefined();
      expect(result.currentStation.id.value).toBe('station1');
      expect(result.currentStation.displayName).toBe('Test Station');
    });
  });
});
