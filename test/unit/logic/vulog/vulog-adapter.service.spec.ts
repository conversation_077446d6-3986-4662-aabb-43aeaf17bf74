import { CorrelationIdService } from '@bluesg-2/monitoring';
import { faker } from '@faker-js/faker';
import { HttpService } from '@nestjs/axios';
import { CacheModule } from '@nestjs/cache-manager';
import { HttpStatus } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import MockAdapter from 'axios-mock-adapter';
import { ClsService } from 'nestjs-cls';
import { CORRELATION_ID_HEADER } from 'src/common/constants/http-headers';
import { VulogApiError } from 'src/common/errors/vulog-api-error';
import { Page } from 'src/common/query/page';
import { PaginationQuery } from 'src/common/query/pagination-query';
import { AuthConfig, HttpAdapterService } from 'src/logic/http-adapter.service';
import { Url } from 'src/logic/url';
import { VulogAdapterService } from 'src/logic/vulog/vulog-adapter.service';
import { VulogCarDto } from 'src/model/dtos/vulog-car/vulog-car.dto';
import { VulogTripDto } from 'src/model/dtos/vulog-trip/vulog-trip.dto';
import { VvgCarListDto } from 'src/model/dtos/vvg-car/vvg-car-list.dto';
import { VvgCarDto } from 'src/model/dtos/vvg-car/vvg-car.dto';
import { HttpLogger } from 'src/shared/http-logger/http-logger';
import {
  createTestVulogCarDto,
  createTestVvgCarDto,
} from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestVulogTripDto } from 'test/helpers/builders/dtos/test-vulog-trip-dto.builder';

describe('VulogAdapterService', () => {
  const httpService = new HttpService();
  const mockAdapter = new MockAdapter(httpService.axiosRef);

  const mockClsService = {
    has: jest.fn(() => CORRELATION_ID_HEADER),
    get: jest.fn(() => CORRELATION_ID_HEADER),
  } as unknown as ClsService;

  const mockGatewayUrl = new Url('http://fake-vulog-url.center');

  let vulogAdapter: VulogAdapterService;

  const testVvgCarDto1 = createTestVvgCarDto();
  const testVvgCarDto2 = createTestVvgCarDto();
  const testVulogCarDto = createTestVulogCarDto();

  const testParsedData1Car = [testVvgCarDto1];
  const testParsedData2Cars = [testVvgCarDto1, testVvgCarDto2];

  const testVulogRawData1Car = testParsedData1Car.map((car) =>
    car.toRawJsonObject(),
  );
  const testVulogRawData2Cars = testParsedData2Cars.map((car) =>
    car.toRawJsonObject(),
  );

  const testVulogTripDto1 = createTestVulogTripDto(true, false);
  const testVulogTripDto2 = createTestVulogTripDto(true, false);
  const testVulogTripDto3 = createTestVulogTripDto(true, false);
  const testVulogTripDto4 = createTestVulogTripDto(true, false);
  const testVulogTripDto5 = createTestVulogTripDto(true, false);
  const testVulogTripsPg1 = [
    testVulogTripDto1,
    testVulogTripDto2,
    testVulogTripDto3,
  ];
  const testVulogTripsPg2 = [testVulogTripDto4, testVulogTripDto5];

  const mockAuthConfig: AuthConfig = {
    headers: {
      Authorization: faker.string.alphanumeric(20),
      'x-api-key': faker.string.uuid(),
    },
  };

  beforeAll(async () => {
    const testModule = await Test.createTestingModule({
      imports: [
        CacheModule.register({
          isGlobal: true,
        }),
      ],
      providers: [
        VulogAdapterService,
        {
          provide: HttpAdapterService,
          useFactory: () =>
            new HttpAdapterService(
              httpService,
              new HttpLogger(),
              new CorrelationIdService(mockClsService),
            ),
        },
        {
          provide: HttpService,
          useFactory: () => httpService,
        },
      ],
    }).compile();

    vulogAdapter = testModule.get<VulogAdapterService>(VulogAdapterService);
  });

  afterEach(async () => {
    mockAdapter.reset();
  });

  it('should throw an error if unable to query the Vulog gateway', async () => {
    mockAdapter.onGet(`${mockGatewayUrl}`).reply(500);
    await expect(
      vulogAdapter.get(mockGatewayUrl, VvgCarDto, mockAuthConfig),
    ).rejects.toThrow(VulogApiError);
  });

  it('should have set the custom user agent', async () => {
    mockAdapter.onGet(`${mockGatewayUrl}`).reply(200);

    await vulogAdapter.get(mockGatewayUrl, VvgCarDto, mockAuthConfig);

    expect(mockAdapter.history.get[0].headers['User-Agent']).toBe(
      'BlueSG/2.0 CRS',
    );
  });

  it.each([
    [VvgCarDto, testVvgCarDto1],
    [VulogCarDto, testVulogCarDto],
  ])(
    'should return data parsed as the correct class',
    async (dtoType: any, expectedDto) => {
      mockAdapter
        .onGet(`${mockGatewayUrl}`)
        .reply(200, expectedDto.toRawJsonObject());

      const response = await vulogAdapter.get(
        mockGatewayUrl,
        dtoType,
        mockAuthConfig,
      );

      expect(response).toStrictEqual(expectedDto);
    },
  );

  it.each([
    [testVulogRawData1Car, testParsedData1Car],
    [testVulogRawData2Cars, testParsedData2Cars],
  ])(
    'should return data in an array parsed as an array of the correct class on successful GET request',
    async (testVulogResponseData, testParsedData) => {
      mockAdapter.onGet(`${mockGatewayUrl}`).reply(200, testVulogResponseData);
      const response = await vulogAdapter.getAsArray(
        mockGatewayUrl,
        VvgCarDto,
        mockAuthConfig,
      );

      expect(response).toHaveLength(testVulogResponseData.length);

      response.forEach((carDto, index) => {
        expect(carDto).toStrictEqual(testParsedData[index]);
      });
    },
  );

  // TODO: fix, and maybe rewrite this test.
  it.skip('should correctly parse an array of vehicles with a continuation token into a CarAdminInfoListDto, on successful GET request', async () => {
    const testVulogCarList = {
      content: testVulogRawData2Cars,
      continuationToken: faker.string.uuid(),
    };

    mockAdapter.onGet(`${mockGatewayUrl}`).reply(200, testVulogCarList);
    const response: VvgCarListDto = await vulogAdapter.get(
      mockGatewayUrl,
      VvgCarListDto,
      mockAuthConfig,
    );

    const content = response.content;
    expect(content).toBeDefined();
    expect(content).toHaveLength(testVulogCarList.content.length);
    expect((response as VvgCarListDto).content).toStrictEqual(
      testParsedData2Cars,
    );
    expect((response as VvgCarListDto).continuationToken).toStrictEqual(
      testVulogCarList.continuationToken,
    );
  });

  it('should correctly return a status code and message with no response data, on successful POST request with empty response data', async () => {
    mockAdapter.onPost(`${mockGatewayUrl}`).reply(200);
    const response = await vulogAdapter.post<null>(
      mockGatewayUrl,
      null,
      mockAuthConfig,
    );

    expect(response.statusCode).toEqual(HttpStatus.OK);
  });

  it('should correctly throw an error, on receiving non-200 response code from the Vulog gateway', async () => {
    const errorData = {
      codeStr: 'carConnectionProblem',
      code: 410,
      message: 'Box not connected wakeup required',
      transactionId: '201bfd75-f826-4dd4-be7d-41f2e19f39cc',
    };

    mockAdapter.onPost(`${mockGatewayUrl}`).reply(410, errorData);

    try {
      await vulogAdapter.post<null>(mockGatewayUrl, null, mockAuthConfig);
    } catch (err) {
      expect(err).toBeInstanceOf(VulogApiError);
      expect(err.statusCode).toEqual(errorData.code);
      expect(err.data).toStrictEqual(errorData);
      return;
    }
    throw new Error('No error thrown on receiving non-200 response code');
  });

  it('should be able to handle fetching 1 page for a Vulog mobile API endpoint', async () => {
    mockAdapter
      .onGet(`${mockGatewayUrl.addQueryParams({ page: '0', size: '200' })}`)
      .reply(200, [...testVulogTripsPg1], { first: true, last: false });

    const response = await vulogAdapter.getOnePage(
      mockGatewayUrl,
      new PaginationQuery(),
      VulogTripDto,
      mockAuthConfig,
    );

    const expectedResponse = new Page(testVulogTripsPg1, 3, false);
    expect(response).toStrictEqual(expectedResponse);
  });

  it('should be able to handle fetching all pages for a Vulog mobile API endpoint', async () => {
    mockAdapter
      .onGet(`${mockGatewayUrl.addQueryParams({ page: '0', size: '200' })}`)
      .reply(200, [...testVulogTripsPg1], { first: true, last: false });

    mockAdapter
      .onGet(`${mockGatewayUrl.addQueryParams({ page: '1', size: '200' })}`)
      .reply(200, [...testVulogTripsPg2], { first: false, last: true });

    const response = await vulogAdapter.getAllPagesAsArray(
      mockGatewayUrl,
      VulogTripDto,
      mockAuthConfig,
    );

    expect(response).toStrictEqual([
      ...testVulogTripsPg1,
      ...testVulogTripsPg2,
    ]);
  });
});
