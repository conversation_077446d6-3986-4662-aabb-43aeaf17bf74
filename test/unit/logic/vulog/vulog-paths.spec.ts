import { faker } from '@faker-js/faker';
import { VulogApiError } from 'src/common/errors/vulog-api-error';
import { Url } from 'src/logic/url';
import { VulogPaths } from 'src/logic/vulog/vulog-paths';

describe('VulogPaths', () => {
  const testPostfix1 =
    '/' + faker.string.alphanumeric({ length: { min: 5, max: 20 } });

  const testPostfix2 =
    '/' +
    faker.string.alphanumeric({ length: { min: 5, max: 20 } }) +
    '/' +
    faker.string.alphanumeric({ length: { min: 5, max: 20 } });

  const testClientUrl = VulogPaths.urlPrefix() + testPostfix1;
  const testAdminUrl = VulogPaths.vvgUrlPrefix() + testPostfix2;

  const testQueryParamKey1 = faker.word.sample();
  const testQueryParamValue1 = faker.string.alphanumeric({
    length: { min: 1, max: 20 },
  });

  const testQueryParamKey2 = faker.word.sample();
  const testQueryParamValue2 = faker.string.alphanumeric({
    length: { min: 1, max: 20 },
  });

  const testQueryParamKey3 = faker.word.sample();
  const testQueryParamValue3 = faker.string.alphanumeric({
    length: { min: 1, max: 20 },
  });

  const testQueryParamsSingle = {
    [testQueryParamKey3]: testQueryParamValue3,
  };

  const testQueryParamsSinglePostfix = `?${testQueryParamKey3}=${testQueryParamValue3}`;

  const testQueryParamsMultiple = {
    [testQueryParamKey1]: testQueryParamValue1,
    [testQueryParamKey2]: testQueryParamValue2,
    [testQueryParamKey3]: testQueryParamValue3,
  };

  const testQueryParamsMultiplePostfix = `?${testQueryParamKey1}=${testQueryParamValue1}&${testQueryParamKey2}=${testQueryParamValue2}&${testQueryParamKey3}=${testQueryParamValue3}`;

  it('should throw an error if an invalid URL is passed into postfixPathOnly', () => {
    expect(() => VulogPaths.postfixPathOnly(faker.internet.url())).toThrow(
      VulogApiError,
    );
  });

  it.each([
    [testClientUrl, testPostfix1],
    [testAdminUrl, testPostfix2],
  ])(
    'should be able to return the postfix path of a Vulog URL',
    (testUrl, testPostfix) => {
      expect(VulogPaths.postfixPathOnly(testUrl).value).toEqual(testPostfix);
    },
  );

  it.each([
    [testQueryParamsSingle, testQueryParamsSinglePostfix],
    [testQueryParamsMultiple, testQueryParamsMultiplePostfix],
  ])(
    'should correctly add query params to a Vulog URL',
    (queryParams: { [paramKey: string]: string }, expectedPostfix: string) => {
      const testUrl = new Url(testClientUrl);
      expect(`${testUrl.addQueryParams(queryParams)}`).toEqual(
        `${testUrl}${expectedPostfix}`,
      );
    },
  );
});
