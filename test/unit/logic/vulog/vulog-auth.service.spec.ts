import { CorrelationIdService } from '@bluesg-2/monitoring';
import { faker } from '@faker-js/faker';
import { HttpService } from '@nestjs/axios';
import { CacheModule } from '@nestjs/cache-manager';
import { HttpStatus } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { aString, aUUID } from 'test/helpers/random-data.helper';
import {
  mockAuthWithPassword,
  mockAuthWithRefreshToken,
  mockGetCachedAccessToken,
  mockVulogCredential,
} from 'test/helpers/util/mock-vulog-credential.util';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import {
  VULOG_ACCESS_TOKEN_KEY,
  VULOG_REFRESH_TOKEN_KEY,
} from '../../../../src/common/constants/cache-keys';
import {
  BsgUserId,
  VulogProfileId,
  VulogUserId,
} from '../../../../src/common/tiny-types';
import { generateEncryptedPasswordFrom } from '../../../../src/common/utils/generate-encrypted-password';
import { config } from '../../../../src/config';
import { UserSubscriptionExternalService } from '../../../../src/external/user-subscription/user-subscription.external.service';
import { CacheService } from '../../../../src/logic/cache/cache.service';
import { HttpAdapterService } from '../../../../src/logic/http-adapter.service';
import { RfidUsageService } from '../../../../src/logic/rfid-usage.service';
import { PostUserVulogRequestDto } from '../../../../src/logic/vulog/dto/request/post.user.vulog.request-dto';
import { VulogAdapterService } from '../../../../src/logic/vulog/vulog-adapter.service';
import { VulogAuthService } from '../../../../src/logic/vulog/vulog-auth.service';
import { VulogProfileService } from '../../../../src/logic/vulog/vulog-profile.service';
import { VulogUserService } from '../../../../src/logic/vulog/vulog-user.service';
import { VulogUserEntity } from '../../../../src/model/repositories/entities/vulog-user.entity';
import { VulogUserRepository } from '../../../../src/model/repositories/vulog-user.repository';
import { VulogUser, VulogUserStatus } from '../../../../src/model/vulog-user';
import { createTestVulogAuthResponse } from '../../../helpers/builders/dtos/test-vulog-auth-response.builder';
import { createTestVulogUserDto } from '../../../helpers/builders/dtos/test-vulog-user-dto.builder';
import { createTestVulogUserServicesDto } from '../../../helpers/builders/dtos/test-vulog-user-services-dto.builder';
import { InternalUserDetailResponseDtoV1Builder } from '../../../helpers/builders/dtos/uss/internal-user-detail.response.v1.dto.builder';
import { createTestVulogUserEntity } from '../../../helpers/builders/entities/test-vulog-user-entity.builder';
import { UserBuilder } from '../../../helpers/builders/user.builder';
import { VulogUserBuilder } from '../../../helpers/builders/vulog-user.builder';

jest.mock('../../../../src/logic/http-adapter.service');
jest.mock('@bluesg-2/monitoring');

describe('VulogAuthService', () => {
  let cache: CacheService;
  let vulogAuthService: VulogAuthService;
  let testModule: TestingModule;

  const mock_HttpService = {
    axiosRef: {
      post: jest.fn(),
    },
  };

  const mock_VulogAdapterService = {
    get: jest.fn(),
    post: jest.fn(),
  };

  const mock_VulogUserRepository = {
    findVulogUserByBsgUserId: jest.fn(),
    insert: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
  };

  const mock_UserSubscriptionExternalService = {
    internalApi: {
      getUserDetail: jest.fn(),
    },
  };

  const mock_HttpAdapterService = {
    axiosInstance: {
      post: jest.fn(),
      get: jest.fn(),
      delete: jest.fn(),
      patch: jest.fn(),
      put: jest.fn(),
    },
  };

  const mock_FeatureFlagService = {
    disableRfidUsageOfCustomer: jest.fn(),
  };

  beforeAll(async () => {
    testModule = await Test.createTestingModule({
      imports: [
        CacheModule.register({
          isGlobal: true,
        }),
      ],
      providers: [
        VulogAuthService,
        VulogUserService,
        CacheService,
        {
          provide: HttpService,
          useValue: mock_HttpService,
        },
        {
          provide: VulogAdapterService,
          useValue: mock_VulogAdapterService,
        },
        {
          provide: VulogUserRepository,
          useValue: mock_VulogUserRepository,
        },
        {
          provide: UserSubscriptionExternalService,
          useValue: mock_UserSubscriptionExternalService,
        },
        {
          provide: HttpAdapterService,
          useValue: mock_HttpAdapterService,
        },
        {
          provide: 'NO_RATE_LIMIT_SERVICE',
          useValue: mock_HttpAdapterService,
        },
        {
          provide: CorrelationIdService,
          useClass: CorrelationIdService,
        },
        {
          provide: FeatureFlagService,
          useValue: mock_FeatureFlagService,
        },
        {
          provide: VulogProfileService,
          useClass: VulogProfileService,
        },
        {
          provide: RfidUsageService,
          useClass: RfidUsageService,
        },
      ],
    }).compile();

    vulogAuthService = testModule.get<VulogAuthService>(VulogAuthService);
    cache = testModule.get<CacheService>(CacheService);
  });

  afterEach(async () => {
    await cache.clearAll();
    jest.resetAllMocks();
  });

  describe('should be able to retrieve the admin cached access token', () => {
    const testAuthResponse = createTestVulogAuthResponse();
    const testToken = testAuthResponse.access_token;

    beforeEach(async () => {
      const systemUser = vulogAuthService.getSystemUser();
      const tokenKey = vulogAuthService.generateTokenKey(
        VULOG_ACCESS_TOKEN_KEY,
        '',
        systemUser,
      );

      await cache.set(tokenKey, testToken);
    });

    it('Check expectation', async () => {
      const response = await vulogAuthService.getAccessToken();

      expect(response).toEqual(testToken);
    });
  });

  describe('should be able to retrieve a Vulog user cached access token', () => {
    const testAuthResponse = createTestVulogAuthResponse();
    const testToken = testAuthResponse.access_token;
    const vulogUser = VulogUserBuilder.allSync().build();

    beforeEach(async () => {
      await cache.set(
        vulogAuthService.generateTokenKey(
          VULOG_ACCESS_TOKEN_KEY,
          vulogUser.bsgUserId.value,
          { username: '' },
        ),
        testToken,
      );
    });

    it('Check expectation', async () => {
      const response = await vulogAuthService.getAccessToken(
        vulogUser.bsgUserId,
      );

      expect(response).toEqual(testToken);
    });
  });

  describe('should be able to authenticate and retrieve the new admin access token using the cached admin refresh token', () => {
    let accessTokenKey;
    let refreshTokenKey;

    const testAuthResponse = createTestVulogAuthResponse();
    const testToken = testAuthResponse.access_token;
    const testRefreshToken = testAuthResponse.refresh_token;

    beforeEach(async () => {
      const systemUser = vulogAuthService.getSystemUser();

      accessTokenKey = vulogAuthService.generateTokenKey(
        VULOG_ACCESS_TOKEN_KEY,
        '',
        systemUser,
      );
      refreshTokenKey = vulogAuthService.generateTokenKey(
        VULOG_REFRESH_TOKEN_KEY,
        '',
        systemUser,
      );

      await cache.set(refreshTokenKey, testRefreshToken);

      mockGetCachedAccessToken(testModule, null);
      mockAuthWithRefreshToken(testModule, testAuthResponse);
    });

    it('Check expectation', async () => {
      const existingAccessToken = await cache.get(accessTokenKey);
      expect(existingAccessToken).toBeUndefined();

      const response = await vulogAuthService.getAccessToken();

      expect(response).toEqual(testToken);
      const newAccessToken = await cache.get(accessTokenKey);
      const newRefreshToken = await cache.get(refreshTokenKey);
      expect(newAccessToken).toStrictEqual(testToken);
      expect(newRefreshToken).toStrictEqual(testRefreshToken);
    });
  });

  describe('should be able to authenticate and retrieve a new Vulog user access token using a cached Vulog user refresh token', () => {
    let vulogUserAccessTokenKey;
    let vulogUserRefreshTokenKey;

    const testAuthResponse = createTestVulogAuthResponse();
    const testToken = testAuthResponse.access_token;
    const testRefreshToken = testAuthResponse.refresh_token;
    const vulogUser = VulogUserBuilder.allSync().build();

    beforeEach(async () => {
      const systemUser = vulogAuthService.getSystemUser();

      vulogUserAccessTokenKey = vulogAuthService.generateTokenKey(
        VULOG_ACCESS_TOKEN_KEY,
        vulogUser.bsgUserId.value,
        systemUser,
      );
      vulogUserRefreshTokenKey = vulogAuthService.generateTokenKey(
        VULOG_REFRESH_TOKEN_KEY,
        vulogUser.bsgUserId.value,
        systemUser,
      );

      await cache.set(vulogUserRefreshTokenKey, testRefreshToken);

      mockGetCachedAccessToken(testModule, null);
      mockAuthWithRefreshToken(testModule, testAuthResponse);
    });

    it('Check expectation', async () => {
      const existingAccessToken = await cache.get(vulogUserAccessTokenKey);
      expect(existingAccessToken).toBeUndefined();

      const response = await vulogAuthService.getAccessToken(
        vulogUser.bsgUserId,
      );
      expect(response).toEqual(testToken);
      const newAccessToken = await cache.get(vulogUserAccessTokenKey);
      const newRefreshToken = await cache.get(vulogUserRefreshTokenKey);
      expect(newAccessToken).toStrictEqual(testToken);
      expect(newRefreshToken).toStrictEqual(testRefreshToken);
    });
  });

  describe('should be able to authenticate and retrieve the new admin access token and admin refresh token from scratch', () => {
    let accessTokenKey;
    let refreshTokenKey;

    const testAuthResponse = createTestVulogAuthResponse();
    const testToken = testAuthResponse.access_token;
    const testRefreshToken = testAuthResponse.refresh_token;

    beforeEach(async () => {
      const systemUser = vulogAuthService.getSystemUser();

      accessTokenKey = vulogAuthService.generateTokenKey(
        VULOG_ACCESS_TOKEN_KEY,
        '',
        systemUser,
      );
      refreshTokenKey = vulogAuthService.generateTokenKey(
        VULOG_REFRESH_TOKEN_KEY,
        '',
        systemUser,
      );

      mockGetCachedAccessToken(testModule, null);
      mockAuthWithPassword(testModule, testAuthResponse);
    });

    it('Check expectation', async () => {
      const existingAccessToken = await cache.get(accessTokenKey);
      const existingRefreshToken = await cache.get(refreshTokenKey);
      expect(existingAccessToken).toBeUndefined();
      expect(existingRefreshToken).toBeUndefined();

      const response = await vulogAuthService.getAccessToken();

      expect(response).toEqual(testToken);
      const newAccessToken = await cache.get(accessTokenKey);
      const newRefreshToken = await cache.get(refreshTokenKey);
      expect(newAccessToken).toStrictEqual(testToken);
      expect(newRefreshToken).toStrictEqual(testRefreshToken);
    });
  });

  describe('should be able to authenticate and retrieve a new Vulog user access token and Vulog user refresh token from scratch', () => {
    const vulogUser = VulogUserBuilder.allSync().build();
    let vulogUserAccessTokenKey: string;
    let vulogUserRefreshTokenKey: string;

    const testAuthResponse = createTestVulogAuthResponse();
    const testToken = testAuthResponse.access_token;
    const testRefreshToken = testAuthResponse.refresh_token;

    beforeEach(async () => {
      const systemUser = vulogAuthService.getSystemUser();

      vulogUserAccessTokenKey = vulogAuthService.generateTokenKey(
        VULOG_ACCESS_TOKEN_KEY,
        vulogUser.bsgUserId.value,
        systemUser,
      );
      vulogUserRefreshTokenKey = vulogAuthService.generateTokenKey(
        VULOG_REFRESH_TOKEN_KEY,
        vulogUser.bsgUserId.value,
        systemUser,
      );

      await cache.set(
        vulogAuthService.generateTokenKey(
          VULOG_ACCESS_TOKEN_KEY,
          '',
          systemUser,
        ),
        'dummy_access_token',
      );
      await cache.set(
        vulogAuthService.generateTokenKey(
          VULOG_REFRESH_TOKEN_KEY,
          '',
          systemUser,
        ),
        'dummy_refresh_token',
      );

      mock_VulogUserRepository.findVulogUserByBsgUserId.mockResolvedValue(
        VulogUserEntity.from(vulogUser),
      );
      mock_VulogUserRepository.save.mockResolvedValue([
        VulogUserEntity.from(vulogUser),
      ]);

      mockAuthWithPassword(testModule, testAuthResponse);
    });

    it('Check expectation', async () => {
      const existingAccessToken = await cache.get(vulogUserAccessTokenKey);
      const existingRefreshToken = await cache.get(vulogUserRefreshTokenKey);
      expect(existingAccessToken).toBeUndefined();
      expect(existingRefreshToken).toBeUndefined();

      const response = await vulogAuthService.getAccessToken(
        vulogUser.bsgUserId,
      );

      expect(response).toEqual(testToken);
      const newAccessToken = await cache.get(vulogUserAccessTokenKey);
      const newRefreshToken = await cache.get(vulogUserRefreshTokenKey);
      expect(newAccessToken).toStrictEqual(testToken);
      expect(newRefreshToken).toStrictEqual(testRefreshToken);
    });
  });

  describe('should be able to detect if a user does not have a Vulog user account, and create a Vulog user and log into Vulog auth with the new user credentials', () => {
    const testBsgUserIdNoVulogUser = new BsgUserId(faker.string.uuid());
    const vulogUser = new VulogUserBuilder()
      .withBsgUserId(testBsgUserIdNoVulogUser)
      .withStatus(VulogUserStatus.CREATING)
      .build();

    const mockVulogUserProfileId = vulogUser.profileId;

    const testAuthResponse = createTestVulogAuthResponse();

    const email = vulogUser.email;

    const mockVulogUserDto = createTestVulogUserDto(vulogUser.entityId);

    const mockVulogUserServicesDto = createTestVulogUserServicesDto(
      mockVulogUserProfileId.value,
    );

    const password = aString();

    beforeEach(async () => {
      // NOTE: mock to not let return unauthorized vulog service
      mockGetCachedAccessToken(testModule, null);

      // NOTE: mock to make the user request logging in
      mockAuthWithRefreshToken(testModule, null);

      // NOTE: make the return null to force create user
      mock_VulogUserRepository.findVulogUserByBsgUserId.mockResolvedValue(null);

      // NOTE: mock user info to start generate user
      mock_UserSubscriptionExternalService.internalApi.getUserDetail.mockResolvedValueOnce(
        new InternalUserDetailResponseDtoV1Builder().withEmail(email).build(),
      );

      mock_VulogUserRepository.save.mockResolvedValue([
        VulogUserEntity.from(vulogUser),
      ]);

      // NOTE: mock authWithPassword after create new
      // mock_HttpService.axiosRef.post.mockResolvedValueOnce({
      //   data: testAuthResponse,
      // });
      mockAuthWithPassword(testModule, testAuthResponse);

      mock_VulogAdapterService.post.mockResolvedValueOnce(mockVulogUserDto);

      mock_VulogUserRepository.save.mockResolvedValue([
        VulogUserEntity.from(vulogUser),
      ]);

      mock_HttpService.axiosRef.post.mockResolvedValueOnce({
        data: testAuthResponse,
      });

      mock_VulogAdapterService.get.mockResolvedValueOnce(
        mockVulogUserServicesDto,
      );

      mock_VulogUserRepository.save.mockResolvedValue([
        VulogUserEntity.from(
          mockVulogUserDto.toVulogUser(
            vulogUser.bsgUserId,
            mockVulogUserServicesDto.profiles[0].profileId,
            vulogUser.email,
            VulogUserStatus.SYNCHRONIZED,
            vulogUser.id,
            vulogUser.entityId,
            password,
          ),
        ),
      ]);

      mock_HttpService.axiosRef.post.mockResolvedValueOnce({
        data: testAuthResponse,
      });

      mock_VulogAdapterService.post.mockResolvedValueOnce({
        statusCode: HttpStatus.OK,
        statusMessage: 'OK',
      });

      const result = VulogUserEntity.from(
        mockVulogUserDto.toVulogUser(
          vulogUser.bsgUserId,
          mockVulogUserServicesDto.profiles[0].profileId,
          vulogUser.email,
          VulogUserStatus.SYNCHRONIZED,
          vulogUser.id,
          vulogUser.entityId,
          password,
        ),
      );

      result.isRegistered = true;

      mock_VulogUserRepository.save.mockResolvedValue([result]);
    });

    it('Check expectation', async () => {
      const response = await vulogAuthService.getAccessToken(
        testBsgUserIdNoVulogUser,
      );

      expect(response).toEqual(testAuthResponse.access_token);
    });
  });

  describe('should be able to create a new Vulog user', () => {
    const mockVulogUserProfileId = faker.string.uuid();

    const user = new UserBuilder().withId(new BsgUserId(aUUID())).build();

    const testAuthResponse = createTestVulogAuthResponse();

    const mockVulogUserServicesDto = createTestVulogUserServicesDto(
      mockVulogUserProfileId,
    );

    const entityId = aUUID();

    const vulogUser = VulogUser.generateFrom(user);
    vulogUser.id = new VulogUserId(aUUID());

    const mockVulogUserDto = createTestVulogUserDto(vulogUser.id.value);

    const password = aString();

    beforeEach(async () => {
      mockVulogCredential(testModule);

      mock_VulogUserRepository.findVulogUserByBsgUserId.mockResolvedValue(null);

      mock_UserSubscriptionExternalService.internalApi.getUserDetail.mockResolvedValueOnce(
        new InternalUserDetailResponseDtoV1Builder()
          .withId(vulogUser.bsgUserId.value)
          .withEmail(vulogUser.email)
          .build(),
      );

      mock_VulogUserRepository.save.mockResolvedValue([
        VulogUserEntity.from(vulogUser),
      ]);

      mock_VulogAdapterService.post.mockResolvedValueOnce(mockVulogUserDto);

      mock_HttpService.axiosRef.post.mockResolvedValueOnce({
        data: testAuthResponse,
      });

      mock_VulogUserRepository.save.mockResolvedValue([
        VulogUserEntity.from(vulogUser),
      ]);

      mock_HttpService.axiosRef.post.mockResolvedValueOnce({
        data: testAuthResponse,
      });

      mock_VulogAdapterService.get.mockResolvedValueOnce(
        mockVulogUserServicesDto,
      );

      mock_VulogUserRepository.save.mockResolvedValue([
        VulogUserEntity.from(
          mockVulogUserDto.toVulogUser(
            vulogUser.bsgUserId,
            mockVulogUserServicesDto.profiles[0].profileId,
            vulogUser.email,
            VulogUserStatus.SYNCHRONIZED,
            vulogUser.id,
            entityId,
            password,
          ),
        ),
      ]);

      mock_HttpService.axiosRef.post.mockResolvedValueOnce({
        data: testAuthResponse,
      });

      mock_VulogAdapterService.post.mockResolvedValueOnce({
        statusCode: HttpStatus.OK,
        statusMessage: 'OK',
      });

      const result = VulogUserEntity.from(
        mockVulogUserDto.toVulogUser(
          vulogUser.bsgUserId,
          mockVulogUserServicesDto.profiles[0].profileId,
          vulogUser.email,
          VulogUserStatus.SYNCHRONIZED,
          vulogUser.id,
          entityId,
          password,
        ),
      );

      result.isRegistered = true;

      mock_VulogUserRepository.save.mockResolvedValue([result]);
    });

    it('Check expectation', async () => {
      const result = await vulogAuthService.findOrCreateUser(user.id);

      expect(result).toStrictEqual(
        new VulogUser(
          user.id,
          vulogUser.email,
          VulogUserStatus.SYNCHRONIZED,
          result.getPassword(),
          new VulogProfileId(mockVulogUserProfileId),
          vulogUser.id,
          true,
          undefined,
          undefined,
          entityId,
        ),
      );
    });
  });

  describe('should be able to prepare an object for registering a Vulog user with all the correct fields', () => {
    const mockVulogUserDto = createTestVulogUserDto();

    const mockVulogUserEntity = createTestVulogUserEntity();

    const testBsgUserIdNoVulogUser = new BsgUserId(faker.string.uuid());
    const mockVulogUserProfileId = faker.string.uuid();

    const email = faker.internet.email();

    const password = aString();

    const mockVulogUser = mockVulogUserDto.toVulogUser(
      testBsgUserIdNoVulogUser,
      mockVulogUserProfileId,
      email,
      undefined,
      new VulogUserId(aUUID()),
      mockVulogUserEntity.id,
      password,
    );

    it('Check expectation', async () => {
      const mockPassword = faker.string.uuid();

      const result = PostUserVulogRequestDto.fromUserInfo(
        mockVulogUser.bsgUserId,
        mockVulogUser.email,
        mockPassword,
      );

      expect(result).toMatchObject({
        birthDate: new Date(
          config.vulogConfig.birthdatePlaceholder,
        ).toISOString(),
        dataPrivacyConsent: true,
        email: `vulog-${mockVulogUser.email}`,
        emailConsent: false,
        firstName: config.vulogConfig.firstNamePlaceholder,
        lastName: config.vulogConfig.lastNamePlaceholder,
        locale: config.vulogConfig.locale,
        membershipNumber: config.vulogConfig.membershipNumberPlaceholder,
        password: mockPassword,
        phoneNumber: config.vulogConfig.phoneNumberPlaceholder,
        userName: mockVulogUser.bsgUserId.value,
      } as PostUserVulogRequestDto);
    });
  });

  describe('should be able to generate a hashed password to register the Vulog user', () => {
    it('Check expectation', async () => {
      const mockPasswordToEncrypt = faker.internet.email();

      const result = await generateEncryptedPasswordFrom(mockPasswordToEncrypt);

      // Ensure that encrypted result is the same both times
      const expectedResult = await generateEncryptedPasswordFrom(
        mockPasswordToEncrypt,
      );

      expect(result).toEqual(expectedResult);
      expect(result).not.toEqual(mockPasswordToEncrypt);
    });
  });

  describe('💳 toggle RFID usage: customer does not have status of `SYNC`', () => {
    const testAuthResponse = createTestVulogAuthResponse();
    const testToken = testAuthResponse.access_token;
    const vulogUser = VulogUserBuilder.allSync()
      .withRfid(null)
      .withStatus(VulogUserStatus.UPDATING)
      .build();

    beforeEach(async () => {
      await cache.set(
        vulogAuthService.generateTokenKey(
          VULOG_ACCESS_TOKEN_KEY,
          vulogUser.bsgUserId.value,
          { username: '' },
        ),
        testToken,
      );

      mock_VulogUserRepository.findOne.mockResolvedValueOnce(
        VulogUserEntity.from(vulogUser),
      );

      await vulogAuthService.setupAuthConfig(vulogUser.bsgUserId);
    });

    it('Check expectation', async () => {
      expect(mock_VulogAdapterService.post.mock.calls.length).toEqual(0);

      expect(mock_VulogUserRepository.save.mock.calls.length).toEqual(0);
    });
  });

  describe('💳 toggle RFID usage: customer does not have RFID number, no actions should be performed', () => {
    const testAuthResponse = createTestVulogAuthResponse();
    const testToken = testAuthResponse.access_token;
    const vulogUser = VulogUserBuilder.allSync().withRfid(null).build();

    beforeEach(async () => {
      await cache.set(
        vulogAuthService.generateTokenKey(
          VULOG_ACCESS_TOKEN_KEY,
          vulogUser.bsgUserId.value,
          { username: '' },
        ),
        testToken,
      );

      mock_FeatureFlagService.disableRfidUsageOfCustomer.mockResolvedValueOnce(
        true,
      );

      mock_VulogUserRepository.findOne.mockResolvedValueOnce(
        VulogUserEntity.from(vulogUser),
      );

      await vulogAuthService.setupAuthConfig(vulogUser.bsgUserId);
    });

    it('Check expectation', async () => {
      expect(mock_VulogAdapterService.post.mock.calls.length).toEqual(0);

      expect(mock_VulogUserRepository.save.mock.calls.length).toEqual(0);
    });
  });

  describe.skip('💳 toggle RFID usage: customer does have RFID number (enabled), flag `disableRfidUsageOfCustomer` is true', () => {
    const testAuthResponse = createTestVulogAuthResponse();
    const testToken = testAuthResponse.access_token;
    const vulogUser = VulogUserBuilder.allSync().withRfid('123456').build();

    beforeEach(async () => {
      await cache.set(
        vulogAuthService.generateTokenKey(
          VULOG_ACCESS_TOKEN_KEY,
          vulogUser.bsgUserId.value,
          { username: '' },
        ),
        testToken,
      );

      await cache.set(
        vulogAuthService.generateTokenKey(
          VULOG_ACCESS_TOKEN_KEY,
          config.vulogConfig.defaultUsers[0].username,
          { username: '' },
        ),
        testToken,
      );

      mock_FeatureFlagService.disableRfidUsageOfCustomer.mockResolvedValueOnce(
        true,
      );

      mock_VulogUserRepository.findOne.mockResolvedValueOnce(
        VulogUserEntity.from(vulogUser),
      );

      mock_VulogUserRepository.save.mockResolvedValueOnce([
        VulogUserEntity.from(vulogUser),
      ]);

      await vulogAuthService.setupAuthConfig(vulogUser.bsgUserId);
    });

    it('Check expectation', async () => {
      expect(mock_VulogAdapterService.post.mock.calls[0][4]).toEqual({
        email: vulogUser.email,
        rfid: `${vulogUser.rfid}--DISABLED`,
      });

      expect(mock_VulogUserRepository.save.mock.calls[0][0][0].status).toEqual(
        VulogUserStatus.SYNCHRONIZED,
      );
    });
  });

  describe.skip('💳 toggle RFID usage: customer does have RFID number (enabled), flag `disableRfidUsageOfCustomer` is false', () => {
    const testAuthResponse = createTestVulogAuthResponse();
    const testToken = testAuthResponse.access_token;
    const vulogUser = VulogUserBuilder.allSync().withRfid('123456').build();

    beforeEach(async () => {
      await cache.set(
        vulogAuthService.generateTokenKey(
          VULOG_ACCESS_TOKEN_KEY,
          vulogUser.bsgUserId.value,
          { username: '' },
        ),
        testToken,
      );

      await cache.set(
        vulogAuthService.generateTokenKey(
          VULOG_ACCESS_TOKEN_KEY,
          config.vulogConfig.defaultUsers[0].username,
          { username: '' },
        ),
        testToken,
      );

      mock_FeatureFlagService.disableRfidUsageOfCustomer.mockResolvedValueOnce(
        false,
      );

      mock_VulogUserRepository.findOne.mockResolvedValueOnce(
        VulogUserEntity.from(vulogUser),
      );

      mock_VulogUserRepository.save.mockResolvedValueOnce([
        VulogUserEntity.from(vulogUser),
      ]);

      await vulogAuthService.setupAuthConfig(vulogUser.bsgUserId);
    });

    it('Check expectation', async () => {
      expect(mock_VulogAdapterService.post.mock.calls.length).toEqual(0);

      expect(mock_VulogUserRepository.save.mock.calls.length).toEqual(0);
    });
  });

  describe.skip('💳 toggle RFID usage: customer does have RFID number (disabled), flag `disableRfidUsageOfCustomer` is false', () => {
    const testAuthResponse = createTestVulogAuthResponse();
    const testToken = testAuthResponse.access_token;
    const vulogUser = VulogUserBuilder.allSync()
      .withRfid('123456--DISABLED')
      .build();

    beforeEach(async () => {
      await cache.set(
        vulogAuthService.generateTokenKey(
          VULOG_ACCESS_TOKEN_KEY,
          vulogUser.bsgUserId.value,
          { username: '' },
        ),
        testToken,
      );

      await cache.set(
        vulogAuthService.generateTokenKey(
          VULOG_ACCESS_TOKEN_KEY,
          config.vulogConfig.defaultUsers[0].username,
          { username: '' },
        ),
        testToken,
      );

      mock_FeatureFlagService.disableRfidUsageOfCustomer.mockResolvedValueOnce(
        false,
      );

      mock_VulogUserRepository.findOne.mockResolvedValueOnce(
        VulogUserEntity.from(vulogUser),
      );

      mock_VulogUserRepository.save.mockResolvedValueOnce([
        VulogUserEntity.from(vulogUser),
      ]);

      await vulogAuthService.setupAuthConfig(vulogUser.bsgUserId);
    });

    it('Check expectation', async () => {
      expect(mock_VulogAdapterService.post.mock.calls[0][4]).toEqual({
        email: vulogUser.email,
        rfid: `123456`,
      });

      expect(mock_VulogUserRepository.save.mock.calls[0][0][0].status).toEqual(
        VulogUserStatus.SYNCHRONIZED,
      );
    });
  });

  describe('💳 toggle RFID usage: customer does have RFID number (disabled), flag `disableRfidUsageOfCustomer` is true', () => {
    const testAuthResponse = createTestVulogAuthResponse();
    const testToken = testAuthResponse.access_token;
    const vulogUser = VulogUserBuilder.allSync()
      .withRfid('123456--DISABLED')
      .build();

    beforeEach(async () => {
      await cache.set(
        vulogAuthService.generateTokenKey(
          VULOG_ACCESS_TOKEN_KEY,
          vulogUser.bsgUserId.value,
          { username: '' },
        ),
        testToken,
      );

      await cache.set(
        vulogAuthService.generateTokenKey(
          VULOG_ACCESS_TOKEN_KEY,
          config.vulogConfig.defaultUsers[0].username,
          { username: '' },
        ),
        testToken,
      );

      mock_FeatureFlagService.disableRfidUsageOfCustomer.mockResolvedValueOnce(
        true,
      );

      mock_VulogUserRepository.findOne.mockResolvedValueOnce(
        VulogUserEntity.from(vulogUser),
      );

      mock_VulogUserRepository.save.mockResolvedValueOnce([
        VulogUserEntity.from(vulogUser),
      ]);

      await vulogAuthService.setupAuthConfig(vulogUser.bsgUserId);
    });

    it('Check expectation', async () => {
      expect(mock_VulogAdapterService.post.mock.calls.length).toEqual(0);

      expect(mock_VulogUserRepository.save.mock.calls.length).toEqual(0);
    });
  });
});
