import { AvailabilityService } from 'src/logic/availability.service';
import { CacheService } from 'src/logic/cache/cache.service';
import { StationService } from 'src/logic/station/station.service';
import { Station } from 'src/model/station';
import { createTestVvgCarDto } from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestStationDto } from 'test/helpers/builders/dtos/test-station-dto.builder';
import { RealtimeCar } from '../../../../src/model/realtime.car';

import { AppLogger, MonitoringModule } from '@bluesg-2/monitoring';
import { CacheModule } from '@nestjs/cache-manager';
import { Test } from '@nestjs/testing';
import { ClsModule } from 'nestjs-cls';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import {
  VulogZone,
  VulogZoneType,
} from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { StaticCar } from 'src/model/static.car';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { StationBuilder } from 'test/helpers/builders/station.builder';
import { CarModel, VulogCarId } from '../../../../src/common/tiny-types';
import { CarService } from '../../../../src/logic/car/car.service';
import { Car } from '../../../../src/logic/car/domain/car';
import { FleetService } from 'src/model/fleet-service';
import { Configuration } from 'src/model/configuration';
import { VulogCarRealTimeDto } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import { EventBusService } from '~shared/event-bus/event-bus.service';
import { MessageEventPattern } from 'src/common/constants/event';

describe('AvailabilityService', () => {
  const setupTestingAvailabilityService = async (
      stubGetAllStationsFn?: () => Station[],
      stubGetAllAvailableCarsFn?: () => RealtimeCar[],
      stubGetStationFn?: () => Station,
      stubGetStaticInformationOfAllCars?: () => StaticCar[],
      stubGetCarMap?: () => Map<string, Car>,
      stubGetCarsInRealtime?: () => Promise<RealtimeCar[]> ,
      stubGetCustomerUsableFleetService?: () => Promise<FleetService>,
      stubGetWarningLowBatteryLevel?: () => Promise<Configuration>,
      stubFilterAvailableCars?: () => Promise<VulogCarRealTimeDto[]>,
      stubNonBlockingEmit?: jest.Mock,
    ) => {
      const temp = await Test.createTestingModule({
        imports: [
          CacheModule.register({
            isGlobal: true,
          }),
          ClsModule.forRoot({
            global: true,
          }),
          MonitoringModule.registerAsync({
            imports: [],
            useFactory: async () => ({}),
            inject: [],
          }),
        ],
        providers: [
          AvailabilityService,
          CacheService,
          {
            provide: CarService,
            useValue: {
              getMapByVulogId: stubGetCarMap,
            },
          },
          {
            provide: StationService,
            useValue: {
              getAllStations: stubGetAllStationsFn,
              getStation: stubGetStationFn,
            },
          },
          {
            provide: VulogCarService,
            useValue: {
              getAllAvailableCars: stubGetAllAvailableCarsFn,
              getStaticInformationOfAllCars: stubGetStaticInformationOfAllCars,
              getCarsInRealtime: stubGetCarsInRealtime,
              getCustomerUsableFleetService: stubGetCustomerUsableFleetService,
              getWarningLowBatteryLevel: stubGetWarningLowBatteryLevel,
              filterAvailableCars: stubFilterAvailableCars,
            },
          },
          {
            provide: EventBusService,
            useValue: {
              nonBlockingEmit: stubNonBlockingEmit,
            },
          }
        ],
      }).compile();
      temp.useLogger(temp.get(AppLogger));
      return temp;
  };

  describe('Edge Cases', () => {
    it('should correctly count multiple cars at the same station', async () => {
      const stationZoneId = 'test-zone-123';
      const testStation = createTestStationDto().toStation();
      testStation.zoneId = stationZoneId;

      // Create two cars with different models
      const testCar1Id = 'test-car-1';
      const vvgCar1 = createTestVvgCarDto();
      vvgCar1.information.name = CarModelEnum.OpelCorsaE;
      const testCar1 = vvgCar1.toRealTimeCar();
      testCar1.sourceId = new VulogCarId(testCar1Id);
      testCar1.id = new VulogCarId(testCar1Id);
      testCar1.allowedStickyZone = new VulogZone(
        stationZoneId,
        1,
        VulogZoneType.Allowed,
        true,
      );
      testCar1.zoneIds = [new VulogCarId(stationZoneId)];
      testCar1.zoneMaps = new Map([
        [stationZoneId, new VulogCarId(stationZoneId)],
      ]);

      const testCar2Id = 'test-car-2';
      const vvgCar2 = createTestVvgCarDto();
      vvgCar2.information.name = CarModelEnum.BlueCar;
      const testCar2 = vvgCar2.toRealTimeCar();
      testCar2.sourceId = new VulogCarId(testCar2Id);
      testCar2.id = new VulogCarId(testCar2Id);
      testCar2.allowedStickyZone = new VulogZone(
        stationZoneId,
        1,
        VulogZoneType.Allowed,
        true,
      );
      testCar2.zoneIds = [new VulogCarId(stationZoneId)];
      testCar2.zoneMaps = new Map([
        [stationZoneId, new VulogCarId(stationZoneId)],
      ]);

      // Create car map with two different car models
      const carMap = new Map<string, Car>();
      carMap.set(
        testCar1.sourceId.value,
        new CarBuilder()
          .withVulogId(new VulogCarId(testCar1Id))
          .withModel(CarModelEnum.OpelCorsaE)
          .build(),
      );
      carMap.set(
        testCar2.sourceId.value,
        new CarBuilder()
          .withVulogId(new VulogCarId(testCar2Id))
          .withModel(CarModelEnum.BlueCar)
          .build(),
      );

      const mockNonBlockingEmit = jest.fn();
      const availabilityService = (
        await setupTestingAvailabilityService(
          () => [testStation],
          undefined,
          undefined,
          undefined,
          () => carMap,
          () => Promise.resolve([testCar1, testCar2]), // Corrected stubGetCarsInRealtime
          () => Promise.resolve(null),
          () => Promise.resolve(null),
          () => Promise.resolve([testCar1, testCar2].map((car) => car.toVulogCarRealTimeDto())),
          mockNonBlockingEmit,
        )
      ).get<AvailabilityService>(AvailabilityService);

      const result =
        await availabilityService.getCarAvailabilityForAllStations();

      expect(result[testStation.id.value]).toBeDefined();
      expect(
        result[testStation.id.value].carAvailability[CarModelEnum.OpelCorsaE],
      ).toBe(1);
      expect(
        result[testStation.id.value].carAvailability[CarModelEnum.BlueCar],
      ).toBe(1);

      expect(mockNonBlockingEmit).toHaveBeenCalledTimes(1);
      expect(mockNonBlockingEmit).toHaveBeenCalledWith(
        expect.objectContaining({
          event: MessageEventPattern.CarAvailabilityUpdated,
          payload: expect.objectContaining({
            stations: expect.arrayContaining([
              expect.objectContaining({
                stationId: testStation.id.value,
                carAvailability: expect.objectContaining({
                  byModel: expect.any(Object),
                  total: expect.any(Number),
                }),
              }),
            ]),
            timestamp: expect.any(String),
          }),
        }),
      );
    });

    it('should handle cars with no model information', async () => {
      const stationZoneId = 'test-zone-123';
      const testStation = createTestStationDto().toStation();
      testStation.zoneId = stationZoneId;
    
      const testCarId = 'test-car-123';
      const testCar = createTestVvgCarDto().toRealTimeCar();
      testCar.sourceId = new VulogCarId(testCarId);
      testCar.id = new VulogCarId(testCarId);
      testCar.allowedStickyZone = new VulogZone(
        stationZoneId,
        1,
        VulogZoneType.Allowed,
        true,
      );
      testCar.zoneIds = [new VulogCarId(stationZoneId)];
      testCar.zoneMaps = new Map([[stationZoneId, new VulogCarId(stationZoneId)]]);
    
      // Create car map with undefined model
      const carMap = new Map<string, Car>();
      const carWithoutModel = new CarBuilder()
        .withVulogId(new VulogCarId(testCarId))
        .build();
      delete carWithoutModel.model; // Explicitly remove model
      carMap.set(testCar.sourceId.value, carWithoutModel);
      const mockNonBlockingEmit = jest.fn();
      const availabilityService = (
        await setupTestingAvailabilityService(
          () => [testStation],
          undefined,
          undefined,
          undefined,
          () => carMap,
          () => Promise.resolve([testCar]),
          () => Promise.resolve(null),
          () => Promise.resolve(null),
          () => Promise.resolve([testCar].map(car => car.toVulogCarRealTimeDto())),
          mockNonBlockingEmit,
        )
      ).get<AvailabilityService>(AvailabilityService);
    
      const result = await availabilityService.getCarAvailabilityForAllStations();
    
      expect(result[testStation.id.value]).toBeDefined();
      expect(Object.keys(result[testStation.id.value].carAvailability)).toHaveLength(0);
      expect(mockNonBlockingEmit).toHaveBeenCalledTimes(1);
    });
    
    it('should handle station with no zones configured', async () => {
      const testStation = new StationBuilder().withZoneId(null).build();
      
      const testCarId = 'test-car-123';
      const vvgCar = createTestVvgCarDto();
      const testCar = vvgCar.toRealTimeCar();
      testCar.sourceId = new VulogCarId(testCarId);
      testCar.id = new VulogCarId(testCarId);
      testCar.zoneIds = [new VulogCarId('some-zone')];
    
      const carMap = new Map<string, Car>();
      carMap.set(
        testCar.sourceId.value,
        new CarBuilder()
          .withVulogId(new VulogCarId(testCarId))
          .withModel(CarModelEnum.OpelCorsaE)
          .build(),
      );
      const mockNonBlockingEmit = jest.fn();
      const availabilityService = (
        await setupTestingAvailabilityService(
          () => [testStation],
          undefined,
          undefined,
          undefined,
          () => carMap,
          () => Promise.resolve([testCar]),
          () => Promise.resolve(null),
          () => Promise.resolve(null),
          () => Promise.resolve([testCar].map(car => car.toVulogCarRealTimeDto())),
          mockNonBlockingEmit,
        )
      ).get<AvailabilityService>(AvailabilityService);
    
      const result = await availabilityService.getCarAvailabilityForAllStations();
    
      expect(result[testStation.id.value]).toBeDefined();
      expect(result[testStation.id.value].carAvailability).toEqual({});
      expect(mockNonBlockingEmit).toHaveBeenCalledTimes(1);
    });
    it('should properly transform zones through class transformations', async () => {
      const stationZoneId = 'test-zone-123';
      const testStation = createTestStationDto().toStation();
      testStation.zoneId = stationZoneId;
    
      const testCarId = 'test-car-123';
      const testCar = createTestVvgCarDto().toRealTimeCar();
      testCar.sourceId = new VulogCarId(testCarId);
      testCar.id = new VulogCarId(testCarId);
      
      // Set up valid zones
      const validZoneId = new VulogCarId(stationZoneId);
      testCar.zoneIds = [validZoneId];
      testCar.zoneMaps = new Map([[stationZoneId, validZoneId]]);
    
      // Create car map with proper model
      const carMap = new Map<string, Car>();
      carMap.set(
        testCar.sourceId.value,
        new CarBuilder()
          .withVulogId(new VulogCarId(testCarId))
          .withModel(CarModelEnum.OpelCorsaE)
          .build(),
      );
    
      const customerUsableFleetService = await (
        await setupTestingAvailabilityService(
          () => [testStation],
          undefined,
          undefined,
          undefined,
          () => carMap,
          () => Promise.resolve([testCar]),
          () => Promise.resolve(null),
          () => Promise.resolve(null),
          () => Promise.resolve([testCar].map(car => car.toVulogCarRealTimeDto())),
        )
      ).get<VulogCarService>(VulogCarService).getCustomerUsableFleetService();
    
      // Transform to DTO and back
      const vulogDto = testCar.toVulogCarRealTimeDto();
      const transformedCar = vulogDto.toRealtimeCar(
        new CarModel(CarModelEnum.OpelCorsaE),
        customerUsableFleetService?.getZoneIdsAsMap()
      );
    
      // Verify zones are properly preserved through transformation
      expect(transformedCar.zoneIds.map(z => z.value)).toContain(stationZoneId);
      expect(transformedCar.zoneMaps.has(stationZoneId)).toBeTruthy();
      expect(transformedCar.zoneMaps.get(stationZoneId).value).toBe(stationZoneId);
    });    
  });

  describe('Sticky Zone Handling', () => {
    it('should assign car to station based on sticky zone', async () => {
      const stationZoneId = 'test-zone-123';
      const testStation = createTestStationDto().toStation();
      testStation.zoneId = stationZoneId;
    
      const testCarId = 'test-car-123';
      const testCar = createTestVvgCarDto().toRealTimeCar();
      testCar.sourceId = new VulogCarId(testCarId);
      testCar.id = new VulogCarId(testCarId);
    
      // Set up proper sticky zone configuration
      testCar.allowedStickyZone = new VulogZone(
        stationZoneId,
        1,
        VulogZoneType.Allowed,
        true,
      );
    
      // Properly set up zoneIds and zoneMaps
      const zoneId = new VulogCarId(stationZoneId);
      testCar.zoneIds = [zoneId];
      testCar.zoneMaps = new Map([[stationZoneId, zoneId]]);
    
      // Create car map with proper model
      const car = new CarBuilder()
        .withVulogId(new VulogCarId(testCarId))
        .withModel(CarModelEnum.OpelCorsaE)
        .build();
    
      const carMap = new Map<string, Car>();
      carMap.set(testCar.sourceId.value, car);
      const mockNonBlockingEmit = jest.fn();
      const availabilityService = (
        await setupTestingAvailabilityService(
          () => [testStation],
          undefined,
          undefined,
          undefined,
          () => carMap,
          () => Promise.resolve([testCar]), // Add getCarsInRealtime stub
          () => Promise.resolve(null),
          () => Promise.resolve(null),
          () => Promise.resolve([testCar].map(car => car.toVulogCarRealTimeDto())),
          mockNonBlockingEmit,
        )
      ).get<AvailabilityService>(AvailabilityService);
    
      const result = await availabilityService.getCarAvailabilityForAllStations();
    
      expect(result[testStation.id.value]).toBeDefined();
      expect(
        result[testStation.id.value].carAvailability[CarModelEnum.OpelCorsaE],
      ).toBe(1);
      expect(mockNonBlockingEmit).toHaveBeenCalledTimes(1);
    });

    it('should fallback to regular zones when sticky zone is invalid', async () => {
      const stationZoneId = 'test-zone-123';
      const testStation = createTestStationDto().toStation();
      testStation.zoneId = stationZoneId;
    
      const testCarId = 'test-car-123';
      const testCar = createTestVvgCarDto().toRealTimeCar();
      testCar.sourceId = new VulogCarId(testCarId);
      testCar.id = new VulogCarId(testCarId);
      
      // Set invalid sticky zone
      testCar.allowedStickyZone = new VulogZone(
        'invalid-zone',
        1,
        VulogZoneType.Allowed,
        true,
      );
    
      // Set valid regular zone
      testCar.zoneIds = [new VulogCarId(stationZoneId)];
      testCar.zoneMaps = new Map([[stationZoneId, new VulogCarId(stationZoneId)]]);
    
      const carMap = new Map<string, Car>();
      carMap.set(
        testCar.sourceId.value,
        new CarBuilder()
          .withVulogId(new VulogCarId(testCarId))
          .withModel(CarModelEnum.OpelCorsaE)
          .build(),
      );
      const mockNonBlockingEmit = jest.fn();
      const availabilityService = (
        await setupTestingAvailabilityService(
          () => [testStation],
          undefined,
          undefined,
          undefined,
          () => carMap,
          () => Promise.resolve([testCar]),
          () => Promise.resolve(null),
          () => Promise.resolve(null),
          () => Promise.resolve([testCar].map(car => car.toVulogCarRealTimeDto())),
          mockNonBlockingEmit,
        )
      ).get<AvailabilityService>(AvailabilityService);

      const result = await availabilityService.getCarAvailabilityForAllStations();
    
      expect(result[testStation.id.value]).toBeDefined();
      expect(result[testStation.id.value].carAvailability[CarModelEnum.OpelCorsaE]).toBe(1);
      expect(mockNonBlockingEmit).toHaveBeenCalledTimes(1);
    });    
  });

  it('should throw an error if there are no stations', async () => {
    const availabilityService = (
      await setupTestingAvailabilityService(
        () => [], // Return empty stations array
        undefined,
        undefined,
        undefined,
        () => new Map<string, Car>(),
        () => Promise.resolve([]),
        () => Promise.resolve(null),
        () => Promise.resolve(null),
        () => Promise.resolve([]),
      )
    ).get<AvailabilityService>(AvailabilityService);
  
    const result = await availabilityService.getCarAvailabilityForAllStations();
    expect(result).toEqual({});
  });

  it('should handle no cars scenario', async () => {
    const testStation = createTestStationDto().toStation();
    const mockNonBlockingEmit = jest.fn();
    const availabilityService = (
      await setupTestingAvailabilityService(
        () => [testStation],
        undefined,
        undefined,
        undefined,
        () => new Map<string, Car>(),
        () => Promise.resolve([]),
        () => Promise.resolve(null),
        () => Promise.resolve(null),
        () => Promise.resolve([]),
        mockNonBlockingEmit,
      )
    ).get<AvailabilityService>(AvailabilityService);
  
    const result = await availabilityService.getCarAvailabilityForAllStations();
  
    expect(result[testStation.id.value]).toBeDefined();
    expect(result[testStation.id.value].carAvailability).toEqual({});
    expect(mockNonBlockingEmit).toHaveBeenCalledTimes(1);
  });
});
