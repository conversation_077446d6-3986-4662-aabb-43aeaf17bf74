import { AvailabilityService } from 'src/logic/availability.service';
import { CacheService } from 'src/logic/cache/cache.service';
import { StationService } from 'src/logic/station/station.service';
import { Station } from 'src/model/station';
import { createTestVvgCarDto } from 'test/helpers/builders/dtos/test-car-dto.builder';
import { createTestStationDto } from 'test/helpers/builders/dtos/test-station-dto.builder';
import { RealtimeCar } from '../../../../src/model/realtime.car';

import { AppLogger, MonitoringModule } from '@bluesg-2/monitoring';
import { CacheModule } from '@nestjs/cache-manager';
import { Test } from '@nestjs/testing';
import { ClsModule } from 'nestjs-cls';
import { MessageEventPattern } from 'src/common/constants/event';
import { CarModelEnum } from 'src/common/enum/car-model.enum';
import { CarExternalService } from 'src/external/car/car.external.service';
import { InternalAvailabilityMapResponseV1 } from 'src/external/car/dtos/internal.availability-map.response.v1.dto';
import { InternalCarResponseV1 } from 'src/external/car/dtos/internal.cars.response.v1.dto';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { Configuration } from 'src/model/configuration';
import { VulogCarRealTimeDto } from 'src/model/dtos/vulog-car-real-time/vulog-car-real-time.dto';
import {
  VulogZone,
  VulogZoneType,
} from 'src/model/dtos/vulog-trip/vulog-trip-fields/vulog-zone';
import { FleetService } from 'src/model/fleet-service';
import { StaticCar } from 'src/model/static.car';
import { CarBuilder } from 'test/helpers/builders/car.builder';
import { StationDtoBuilder } from 'test/helpers/builders/dtos/station/station.dto.builder';
import { StationBuilder } from 'test/helpers/builders/station.builder';
import { aUUID } from 'test/helpers/random-data.helper';
import { EventBusService } from '~shared/event-bus/event-bus.service';
import { FeatureFlagService } from '~shared/feature-flag/feature-flag.service';
import { CarModel, VulogCarId } from '../../../../src/common/tiny-types';
import { CarService } from '../../../../src/logic/car/car.service';
import { Car } from '../../../../src/logic/car/domain/car';

describe('AvailabilityService', () => {
  const setupTestingAvailabilityService = async (props: {
    stubGetAllStationsFn?: () => Station[];
    stubGetAllAvailableCarsFn?: () => RealtimeCar[];
    stubGetStationFn?: () => Station;
    stubGetStaticInformationOfAllCars?: () => StaticCar[];
    stubGetCarMap?: () => Map<string, Car>;
    stubGetCarsInRealtime?: () => Promise<RealtimeCar[]>;
    stubGetCustomerUsableFleetService?: () => Promise<FleetService>;
    stubGetWarningLowBatteryLevel?: () => Promise<Configuration>;
    stubFilterAvailableCars?: () => Promise<VulogCarRealTimeDto[]>;
    stubGetAvailabilityMap?: () => Promise<InternalAvailabilityMapResponseV1>;
    stubGetCars?: () => Promise<InternalCarResponseV1[]>;
    stubIsEnableCsCarAvailability?: () => Promise<boolean>;
    stubNonBlockingEmit?: jest.Mock;
  }) => {
    const temp = await Test.createTestingModule({
      imports: [
        CacheModule.register({
          isGlobal: true,
        }),
        ClsModule.forRoot({
          global: true,
        }),
        MonitoringModule.registerAsync({
          imports: [],
          useFactory: async () => ({}),
          inject: [],
        }),
      ],
      providers: [
        AvailabilityService,
        CacheService,
        {
          provide: CarService,
          useValue: {
            getMapByVulogId: props.stubGetCarMap,
          },
        },
        {
          provide: StationService,
          useValue: {
            getAllStations: props.stubGetAllStationsFn,
            getStation: props.stubGetStationFn,
          },
        },
        {
          provide: CarExternalService,
          useValue: {
            internalApi: {
              getAvailabilityMap: props.stubGetAvailabilityMap,
              getCars: props.stubGetCars,
            },
          },
        },
        {
          provide: VulogCarService,
          useValue: {
            getAllAvailableCars: props.stubGetAllAvailableCarsFn,
            getStaticInformationOfAllCars:
              props.stubGetStaticInformationOfAllCars,
            getCarsInRealtime: props.stubGetCarsInRealtime,
            getCustomerUsableFleetService:
              props.stubGetCustomerUsableFleetService,
            getWarningLowBatteryLevel: props.stubGetWarningLowBatteryLevel,
            filterAvailableCars: props.stubFilterAvailableCars,
          },
        },
        {
          provide: EventBusService,
          useValue: {
            nonBlockingEmit: props.stubNonBlockingEmit,
          },
        },
        {
          provide: FeatureFlagService,
          useValue: {
            isEnableCsCarAvailability: props.stubIsEnableCsCarAvailability,
          },
        },
      ],
    }).compile();
    temp.useLogger(temp.get(AppLogger));
    return temp;
  };

  describe('CS Car Availability', () => {
    it('should return empty if no station is available', async () => {
      const availabilityService = (
        await setupTestingAvailabilityService({
          stubGetAllStationsFn: () => [],
        })
      ).get<AvailabilityService>(AvailabilityService);

      const result = await availabilityService.getCsCarAvailability();

      expect(result).toEqual({});
    });

    it('should return availability map', async () => {
      const testStation1 = new StationDtoBuilder().build().toStation();
      const testStation2 = new StationDtoBuilder().build().toStation();
      const testStation3 = new StationDtoBuilder().build().toStation();

      const fakeStationId = aUUID();

      const lowBatteryLevelConfig = {
        [CarModelEnum.BlueCar]: 30,
        [CarModelEnum.OpelCorsaE]: 24,
      };

      const availabilityMap: InternalAvailabilityMapResponseV1 = {
        [testStation1.id.value]: {
          id: testStation1.id.value,
          cars: {
            [CarModelEnum.BlueCar]: {
              available: 2,
              reserved: 1,
            },
            [CarModelEnum.OpelCorsaE]: {
              available: 0,
              reserved: 1,
            },
          },
        },
        [testStation2.id.value]: {
          id: testStation2.id.value,
          cars: {
            [CarModelEnum.BlueCar]: {
              available: 1,
              reserved: 0,
            },
            [CarModelEnum.OpelCorsaE]: {
              available: 1,
              reserved: 2,
            },
          },
        },
        [fakeStationId]: {
          id: fakeStationId,
          cars: {},
        },
      };

      const availabilityService = (
        await setupTestingAvailabilityService({
          stubGetAllStationsFn: () => [
            testStation1,
            testStation2,
            testStation3,
          ],
          stubGetWarningLowBatteryLevel: () =>
            Promise.resolve(lowBatteryLevelConfig as unknown as Configuration),
          stubGetAvailabilityMap: () => Promise.resolve(availabilityMap),
        })
      ).get<AvailabilityService>(AvailabilityService);

      const result = await availabilityService.getCsCarAvailability();

      expect(result[fakeStationId]).toBeUndefined();
      expect(result[testStation1.id.value].carAvailability).toEqual({
        [CarModelEnum.BlueCar]: 2,
        [CarModelEnum.OpelCorsaE]: 0,
      });
      expect(result[testStation1.id.value].station.displayName).toEqual(
        testStation1.displayName,
      );
      expect(result[testStation2.id.value].carAvailability).toEqual({
        [CarModelEnum.BlueCar]: 1,
        [CarModelEnum.OpelCorsaE]: 1,
      });
      expect(result[testStation2.id.value].station.displayName).toEqual(
        testStation2.displayName,
      );
      expect(result[testStation3.id.value].carAvailability).toEqual({});
      expect(result[testStation3.id.value].station.displayName).toEqual(
        testStation3.displayName,
      );
    });
  });

  describe('Edge Cases', () => {
    it('should correctly count multiple cars at the same station', async () => {
      const stationZoneId = 'test-zone-123';
      const testStation = createTestStationDto().toStation();
      testStation.zoneId = stationZoneId;

      // Create two cars with different models
      const testCar1Id = 'test-car-1';
      const vvgCar1 = createTestVvgCarDto();
      vvgCar1.information.name = CarModelEnum.OpelCorsaE;
      const testCar1 = vvgCar1.toRealTimeCar();
      testCar1.sourceId = new VulogCarId(testCar1Id);
      testCar1.id = new VulogCarId(testCar1Id);
      testCar1.allowedStickyZone = new VulogZone(
        stationZoneId,
        1,
        VulogZoneType.Allowed,
        true,
      );
      testCar1.zoneIds = [new VulogCarId(stationZoneId)];
      testCar1.zoneMaps = new Map([
        [stationZoneId, new VulogCarId(stationZoneId)],
      ]);

      const testCar2Id = 'test-car-2';
      const vvgCar2 = createTestVvgCarDto();
      vvgCar2.information.name = CarModelEnum.BlueCar;
      const testCar2 = vvgCar2.toRealTimeCar();
      testCar2.sourceId = new VulogCarId(testCar2Id);
      testCar2.id = new VulogCarId(testCar2Id);
      testCar2.allowedStickyZone = new VulogZone(
        stationZoneId,
        1,
        VulogZoneType.Allowed,
        true,
      );
      testCar2.zoneIds = [new VulogCarId(stationZoneId)];
      testCar2.zoneMaps = new Map([
        [stationZoneId, new VulogCarId(stationZoneId)],
      ]);

      // Create car map with two different car models
      const carMap = new Map<string, Car>();
      carMap.set(
        testCar1.sourceId.value,
        new CarBuilder()
          .withVulogId(new VulogCarId(testCar1Id))
          .withModel(CarModelEnum.OpelCorsaE)
          .build(),
      );
      carMap.set(
        testCar2.sourceId.value,
        new CarBuilder()
          .withVulogId(new VulogCarId(testCar2Id))
          .withModel(CarModelEnum.BlueCar)
          .build(),
      );

      const mockNonBlockingEmit = jest.fn();
      const availabilityService = (
        await setupTestingAvailabilityService({
          stubGetAllStationsFn: () => [testStation],
          stubGetCarMap: () => carMap,
          stubGetCarsInRealtime: () => Promise.resolve([testCar1, testCar2]),
          stubGetCustomerUsableFleetService: () => Promise.resolve(null),
          stubGetWarningLowBatteryLevel: () => Promise.resolve(null),
          stubFilterAvailableCars: () =>
            Promise.resolve(
              [testCar1, testCar2].map((car) => car.toVulogCarRealTimeDto()),
            ),
          stubNonBlockingEmit: mockNonBlockingEmit,
        })
      ).get<AvailabilityService>(AvailabilityService);

      const result =
        await availabilityService.getCarAvailabilityForAllStations();

      expect(result[testStation.id.value]).toBeDefined();
      expect(
        result[testStation.id.value].carAvailability[CarModelEnum.OpelCorsaE],
      ).toBe(1);
      expect(
        result[testStation.id.value].carAvailability[CarModelEnum.BlueCar],
      ).toBe(1);

      expect(mockNonBlockingEmit).toHaveBeenCalledTimes(1);
      expect(mockNonBlockingEmit).toHaveBeenCalledWith(
        expect.objectContaining({
          event: MessageEventPattern.CarAvailabilityUpdated,
          payload: expect.objectContaining({
            stations: expect.arrayContaining([
              expect.objectContaining({
                stationId: testStation.id.value,
                carAvailability: expect.objectContaining({
                  byModel: expect.any(Object),
                  total: expect.any(Number),
                }),
              }),
            ]),
            timestamp: expect.any(String),
          }),
        }),
      );
    });

    it('should handle cars with no model information', async () => {
      const stationZoneId = 'test-zone-123';
      const testStation = createTestStationDto().toStation();
      testStation.zoneId = stationZoneId;

      const testCarId = 'test-car-123';
      const testCar = createTestVvgCarDto().toRealTimeCar();
      testCar.sourceId = new VulogCarId(testCarId);
      testCar.id = new VulogCarId(testCarId);
      testCar.allowedStickyZone = new VulogZone(
        stationZoneId,
        1,
        VulogZoneType.Allowed,
        true,
      );
      testCar.zoneIds = [new VulogCarId(stationZoneId)];
      testCar.zoneMaps = new Map([
        [stationZoneId, new VulogCarId(stationZoneId)],
      ]);

      // Create car map with undefined model
      const carMap = new Map<string, Car>();
      const carWithoutModel = new CarBuilder()
        .withVulogId(new VulogCarId(testCarId))
        .build();
      delete carWithoutModel.model; // Explicitly remove model
      carMap.set(testCar.sourceId.value, carWithoutModel);
      const mockNonBlockingEmit = jest.fn();
      const availabilityService = (
        await setupTestingAvailabilityService({
          stubGetAllStationsFn: () => [testStation],
          stubGetCarMap: () => carMap,
          stubGetCarsInRealtime: () => Promise.resolve([testCar]),
          stubGetCustomerUsableFleetService: () => Promise.resolve(null),
          stubGetWarningLowBatteryLevel: () => Promise.resolve(null),
          stubFilterAvailableCars: () =>
            Promise.resolve(
              [testCar].map((car) => car.toVulogCarRealTimeDto()),
            ),
          stubNonBlockingEmit: mockNonBlockingEmit,
        })
      ).get<AvailabilityService>(AvailabilityService);

      const result =
        await availabilityService.getCarAvailabilityForAllStations();

      expect(result[testStation.id.value]).toBeDefined();
      expect(
        Object.keys(result[testStation.id.value].carAvailability),
      ).toHaveLength(0);
      expect(mockNonBlockingEmit).toHaveBeenCalledTimes(1);
    });

    it('should handle station with no zones configured', async () => {
      const testStation = new StationBuilder().withZoneId(null).build();

      const testCarId = 'test-car-123';
      const vvgCar = createTestVvgCarDto();
      const testCar = vvgCar.toRealTimeCar();
      testCar.sourceId = new VulogCarId(testCarId);
      testCar.id = new VulogCarId(testCarId);
      testCar.zoneIds = [new VulogCarId('some-zone')];

      const carMap = new Map<string, Car>();
      carMap.set(
        testCar.sourceId.value,
        new CarBuilder()
          .withVulogId(new VulogCarId(testCarId))
          .withModel(CarModelEnum.OpelCorsaE)
          .build(),
      );
      const mockNonBlockingEmit = jest.fn();
      const availabilityService = (
        await setupTestingAvailabilityService({
          stubGetAllStationsFn: () => [testStation],
          stubGetCarMap: () => carMap,
          stubGetCarsInRealtime: () => Promise.resolve([testCar]),
          stubGetCustomerUsableFleetService: () => Promise.resolve(null),
          stubGetWarningLowBatteryLevel: () => Promise.resolve(null),
          stubFilterAvailableCars: () =>
            Promise.resolve(
              [testCar].map((car) => car.toVulogCarRealTimeDto()),
            ),
          stubNonBlockingEmit: mockNonBlockingEmit,
        })
      ).get<AvailabilityService>(AvailabilityService);

      const result =
        await availabilityService.getCarAvailabilityForAllStations();

      expect(result[testStation.id.value]).toBeDefined();
      expect(result[testStation.id.value].carAvailability).toEqual({});
      expect(mockNonBlockingEmit).toHaveBeenCalledTimes(1);
    });
    it('should properly transform zones through class transformations', async () => {
      const stationZoneId = 'test-zone-123';
      const testStation = createTestStationDto().toStation();
      testStation.zoneId = stationZoneId;

      const testCarId = 'test-car-123';
      const testCar = createTestVvgCarDto().toRealTimeCar();
      testCar.sourceId = new VulogCarId(testCarId);
      testCar.id = new VulogCarId(testCarId);

      // Set up valid zones
      const validZoneId = new VulogCarId(stationZoneId);
      testCar.zoneIds = [validZoneId];
      testCar.zoneMaps = new Map([[stationZoneId, validZoneId]]);

      // Create car map with proper model
      const carMap = new Map<string, Car>();
      carMap.set(
        testCar.sourceId.value,
        new CarBuilder()
          .withVulogId(new VulogCarId(testCarId))
          .withModel(CarModelEnum.OpelCorsaE)
          .build(),
      );

      const customerUsableFleetService = await (
        await setupTestingAvailabilityService({
          stubGetAllStationsFn: () => [testStation],
          stubGetCarMap: () => carMap,
          stubGetCarsInRealtime: () => Promise.resolve([testCar]),
          stubGetCustomerUsableFleetService: () => Promise.resolve(null),
          stubGetWarningLowBatteryLevel: () => Promise.resolve(null),
          stubFilterAvailableCars: () =>
            Promise.resolve(
              [testCar].map((car) => car.toVulogCarRealTimeDto()),
            ),
        })
      )
        .get<VulogCarService>(VulogCarService)
        .getCustomerUsableFleetService();

      // Transform to DTO and back
      const vulogDto = testCar.toVulogCarRealTimeDto();
      const transformedCar = vulogDto.toRealtimeCar(
        new CarModel(CarModelEnum.OpelCorsaE),
        customerUsableFleetService?.getZoneIdsAsMap(),
      );

      // Verify zones are properly preserved through transformation
      expect(transformedCar.zoneIds.map((z) => z.value)).toContain(
        stationZoneId,
      );
      expect(transformedCar.zoneMaps.has(stationZoneId)).toBeTruthy();
      expect(transformedCar.zoneMaps.get(stationZoneId).value).toBe(
        stationZoneId,
      );
    });
  });

  describe('Sticky Zone Handling', () => {
    it('should assign car to station based on sticky zone', async () => {
      const stationZoneId = 'test-zone-123';
      const testStation = createTestStationDto().toStation();
      testStation.zoneId = stationZoneId;

      const testCarId = 'test-car-123';
      const testCar = createTestVvgCarDto().toRealTimeCar();
      testCar.sourceId = new VulogCarId(testCarId);
      testCar.id = new VulogCarId(testCarId);

      // Set up proper sticky zone configuration
      testCar.allowedStickyZone = new VulogZone(
        stationZoneId,
        1,
        VulogZoneType.Allowed,
        true,
      );

      // Properly set up zoneIds and zoneMaps
      const zoneId = new VulogCarId(stationZoneId);
      testCar.zoneIds = [zoneId];
      testCar.zoneMaps = new Map([[stationZoneId, zoneId]]);

      // Create car map with proper model
      const car = new CarBuilder()
        .withVulogId(new VulogCarId(testCarId))
        .withModel(CarModelEnum.OpelCorsaE)
        .build();

      const carMap = new Map<string, Car>();
      carMap.set(testCar.sourceId.value, car);
      const mockNonBlockingEmit = jest.fn();
      const availabilityService = (
        await setupTestingAvailabilityService({
          stubGetAllStationsFn: () => [testStation],
          stubGetCarMap: () => carMap,
          stubGetCarsInRealtime: () => Promise.resolve([testCar]),
          stubGetCustomerUsableFleetService: () => Promise.resolve(null),
          stubGetWarningLowBatteryLevel: () => Promise.resolve(null),
          stubFilterAvailableCars: () =>
            Promise.resolve(
              [testCar].map((car) => car.toVulogCarRealTimeDto()),
            ),
          stubNonBlockingEmit: mockNonBlockingEmit,
        })
      ).get<AvailabilityService>(AvailabilityService);

      const result =
        await availabilityService.getCarAvailabilityForAllStations();

      expect(result[testStation.id.value]).toBeDefined();
      expect(
        result[testStation.id.value].carAvailability[CarModelEnum.OpelCorsaE],
      ).toBe(1);
      expect(mockNonBlockingEmit).toHaveBeenCalledTimes(1);
    });

    it('should fallback to regular zones when sticky zone is invalid', async () => {
      const stationZoneId = 'test-zone-123';
      const testStation = createTestStationDto().toStation();
      testStation.zoneId = stationZoneId;

      const testCarId = 'test-car-123';
      const testCar = createTestVvgCarDto().toRealTimeCar();
      testCar.sourceId = new VulogCarId(testCarId);
      testCar.id = new VulogCarId(testCarId);

      // Set invalid sticky zone
      testCar.allowedStickyZone = new VulogZone(
        'invalid-zone',
        1,
        VulogZoneType.Allowed,
        true,
      );

      // Set valid regular zone
      testCar.zoneIds = [new VulogCarId(stationZoneId)];
      testCar.zoneMaps = new Map([
        [stationZoneId, new VulogCarId(stationZoneId)],
      ]);

      const carMap = new Map<string, Car>();
      carMap.set(
        testCar.sourceId.value,
        new CarBuilder()
          .withVulogId(new VulogCarId(testCarId))
          .withModel(CarModelEnum.OpelCorsaE)
          .build(),
      );
      const mockNonBlockingEmit = jest.fn();
      const availabilityService = (
        await setupTestingAvailabilityService({
          stubGetAllStationsFn: () => [testStation],
          stubGetCarMap: () => carMap,
          stubGetCarsInRealtime: () => Promise.resolve([testCar]),
          stubGetCustomerUsableFleetService: () => Promise.resolve(null),
          stubGetWarningLowBatteryLevel: () => Promise.resolve(null),
          stubFilterAvailableCars: () =>
            Promise.resolve(
              [testCar].map((car) => car.toVulogCarRealTimeDto()),
            ),
          stubNonBlockingEmit: mockNonBlockingEmit,
        })
      ).get<AvailabilityService>(AvailabilityService);

      const result =
        await availabilityService.getCarAvailabilityForAllStations();

      expect(result[testStation.id.value]).toBeDefined();
      expect(
        result[testStation.id.value].carAvailability[CarModelEnum.OpelCorsaE],
      ).toBe(1);
      expect(mockNonBlockingEmit).toHaveBeenCalledTimes(1);
    });
  });

  it('should throw an error if there are no stations', async () => {
    const availabilityService = (
      await setupTestingAvailabilityService({
        stubGetAllStationsFn: () => [],
        stubGetCarMap: () => new Map<string, Car>(),
        stubGetCarsInRealtime: () => Promise.resolve([]),
        stubGetCustomerUsableFleetService: () => Promise.resolve(null),
        stubGetWarningLowBatteryLevel: () => Promise.resolve(null),
        stubFilterAvailableCars: () => Promise.resolve([]),
      })
    ).get<AvailabilityService>(AvailabilityService);

    const result = await availabilityService.getCarAvailabilityForAllStations();
    expect(result).toEqual({});
  });

  it('should handle no cars scenario', async () => {
    const testStation = createTestStationDto().toStation();
    const mockNonBlockingEmit = jest.fn();
    const availabilityService = (
      await setupTestingAvailabilityService({
        stubGetAllStationsFn: () => [testStation],
        stubGetCarMap: () => new Map<string, Car>(),
        stubGetCarsInRealtime: () => Promise.resolve([]),
        stubGetCustomerUsableFleetService: () => Promise.resolve(null),
        stubGetWarningLowBatteryLevel: () => Promise.resolve(null),
        stubFilterAvailableCars: () => Promise.resolve([]),
        stubNonBlockingEmit: mockNonBlockingEmit,
      })
    ).get<AvailabilityService>(AvailabilityService);

    const result = await availabilityService.getCarAvailabilityForAllStations();

    expect(result[testStation.id.value]).toBeDefined();
    expect(result[testStation.id.value].carAvailability).toEqual({});
    expect(mockNonBlockingEmit).toHaveBeenCalledTimes(1);
  });

  describe('getAvailableCarsForSpecificStation', () => {
    it('should filter cars by model and battery level when using CS car availability', async () => {
      const testStation = new StationBuilder().build();
      const testStationId = testStation.id;
      const expectedModel = CarModelEnum.BlueCar;

      // Mock InternalCarResponseV1 DTOs
      const csCarDtoGood: InternalCarResponseV1 = {
        id: 'car-1',
        carModel: { id: 'model-1', name: expectedModel, hrid: 'hrid-1' },
        plateNumber: 'SGX1234A',
        vin: 'VIN1',
        externalFleetId: 'fleet-1',
        externalCarId: 'car-1',
        stationId: testStationId.value,
        state: {
          locked: true,
          batteryLevel: 50,
          allDoorsWindowsClosed: true,
          engineOn: false,
          cablePlugged: false,
          charging: false,
        },
      } as InternalCarResponseV1;

      const csCarDtoLow: InternalCarResponseV1 = {
        id: 'car-2',
        carModel: { id: 'model-1', name: expectedModel, hrid: 'hrid-1' },
        plateNumber: 'SGX5678B',
        vin: 'VIN2',
        externalFleetId: 'fleet-1',
        externalCarId: 'car-2',
        stationId: testStationId.value,
        state: {
          locked: true,
          batteryLevel: 10,
          allDoorsWindowsClosed: true,
          engineOn: false,
          cablePlugged: false,
          charging: false,
        },
      } as InternalCarResponseV1;

      const csCarDtoOther: InternalCarResponseV1 = {
        id: 'car-3',
        carModel: {
          id: 'model-2',
          name: CarModelEnum.OpelCorsaE,
          hrid: 'hrid-2',
        },
        plateNumber: 'SGX9999C',
        vin: 'VIN3',
        externalFleetId: 'fleet-1',
        externalCarId: 'car-3',
        stationId: testStationId.value,
        state: {
          locked: true,
          batteryLevel: 80,
          allDoorsWindowsClosed: true,
          engineOn: false,
          cablePlugged: false,
          charging: false,
        },
      } as InternalCarResponseV1;

      // Battery threshold config
      const batteryConfig = {
        [CarModelEnum.BlueCar]: 30,
        [CarModelEnum.OpelCorsaE]: 24,
      };

      // Setup service with proper stubs
      const availabilityService = (
        await setupTestingAvailabilityService({
          stubGetCars: () =>
            Promise.resolve([
              csCarDtoGood,
              csCarDtoLow,
              csCarDtoOther,
            ] as InternalCarResponseV1[]),
          stubIsEnableCsCarAvailability: () => Promise.resolve(true),
          stubGetWarningLowBatteryLevel: () =>
            Promise.resolve(batteryConfig as any),
        })
      ).get<AvailabilityService>(AvailabilityService);

      const result =
        await availabilityService.getAvailableCarsForSpecificStation(
          testStationId,
          { id: 'user-1' } as any,
          expectedModel,
        );

      // Only csCarDtoGood should be returned
      expect(result).toHaveLength(1);
      expect(result[0].model).toBe(expectedModel);
      expect(result[0].realtimeMetadata.batteryPercentage).toBe(50);
    });
  });
});
