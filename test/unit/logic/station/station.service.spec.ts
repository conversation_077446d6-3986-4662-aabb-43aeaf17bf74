import { CacheModule } from '@nestjs/cache-manager';
import { Test } from '@nestjs/testing';
import { StationId } from 'src/common/tiny-types';
import { CacheService } from 'src/logic/cache/cache.service';
import { StationAdapterService } from 'src/logic/station/station-adapter.service';
import { StationPaths } from 'src/logic/station/station-paths';
import { StationService } from 'src/logic/station/station.service';
import { Url } from 'src/logic/url';
import { createTestStationDto } from 'test/helpers/builders/dtos/test-station-dto.builder';
import { StationExternalService } from '../../../../src/external/station/station.external.service';
import { InternalStationResponseDtoV1Builder } from '../../../helpers/builders/dtos/internal-station.response.v1.dto.builder';
import { InternalParkingLotV1ResponseDtoBuilder } from '../../../helpers/builders/dtos/internal.parking-lot.v1.response.dto.builder';

describe('StationService', () => {
  const mockStation1 = createTestStationDto();
  const mockStation2 = createTestStationDto();
  const mockStations = [mockStation1, mockStation2];

  let stationService: StationService;
  let stationExternalService: StationExternalService;

  beforeAll(async () => {
    const testModule = await Test.createTestingModule({
      imports: [
        CacheModule.register({
          isGlobal: true,
        }),
      ],
      providers: [
        CacheService,
        StationService,
        {
          provide: StationExternalService,
          useValue: {
            internalApi: {
              getParkingLotByQR: jest.fn(),
            },
          },
        },
        {
          provide: StationAdapterService,
          useValue: {
            get: (url: Url) => {
              if (url.value === StationPaths.stations().value) {
                return { result: mockStations };
              }
              if (
                url.value === StationPaths.getStation(mockStation2.id).value
              ) {
                return mockStation2;
              }
            },
            getAll: () => mockStations,
          },
        },
      ],
    }).compile();

    stationService = testModule.get<StationService>(StationService);
    stationExternalService = testModule.get<StationExternalService>(
      StationExternalService,
    );
  });

  it('should get a list of all stations', async () => {
    const stations = await stationService.getAllStations();

    expect(stations).toEqual(mockStations.map((stnDto) => stnDto.toStation()));
  });

  it('should get details of a specified station', async () => {
    const station = await stationService.getStation(
      new StationId(mockStation2.id),
    );

    expect(station).toStrictEqual(mockStation2.toStation());
  });

  it('should return the station by QR code', async () => {
    const qrCode = 'BSG-123456-01';
    const mockStation = new InternalStationResponseDtoV1Builder().build();

    const mockParkingLotResponse = new InternalParkingLotV1ResponseDtoBuilder()
      .withStation(mockStation)
      .build();

    jest
      .spyOn(stationExternalService.internalApi, 'getParkingLotByQR')
      .mockResolvedValue(mockParkingLotResponse);

    const result = await stationService.getStationByQRCode(qrCode);

    expect(result).toStrictEqual(mockStation.toStation());
    expect(
      stationExternalService.internalApi.getParkingLotByQR,
    ).toHaveBeenCalledWith(qrCode);
  });
});
