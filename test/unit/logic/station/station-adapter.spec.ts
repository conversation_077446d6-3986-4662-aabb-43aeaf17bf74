import { CorrelationIdService } from '@bluesg-2/monitoring';
import { HttpService } from '@nestjs/axios';
import MockAdapter from 'axios-mock-adapter';
import { ClsService } from 'nestjs-cls';
import * as QueryString from 'qs';
import { CORRELATION_ID_HEADER } from 'src/common/constants/http-headers';
import { StationApiError } from 'src/common/errors/station-api-error';
import { HttpAdapterService } from 'src/logic/http-adapter.service';
import { StationAdapterService } from 'src/logic/station/station-adapter.service';
import { Url } from 'src/logic/url';
import { StationDto } from 'src/model/dtos/station.dto';
import { HttpLogger } from 'src/shared/http-logger/http-logger';
import { createTestStationDto } from 'test/helpers/builders/dtos/test-station-dto.builder';

describe('StationAdapterService', () => {
  const httpService = new HttpService();
  const mockAdapter = new MockAdapter(httpService.axiosRef);
  const mockStationsUrl = new Url('http://fake-sts.dev/stationsSvc');

  const mockClsService = {
    has: jest.fn(() => CORRELATION_ID_HEADER),
    get: jest.fn(() => CORRELATION_ID_HEADER),
  } as unknown as ClsService;

  const httpAdapter = new HttpAdapterService(
    httpService,
    new HttpLogger(),
    new CorrelationIdService(mockClsService),
  );

  const stationAdapter = new StationAdapterService(httpAdapter);

  const testStationDto1 = createTestStationDto();
  const testStationDto2 = createTestStationDto();

  const test1StationList = [testStationDto1];
  const test2StationList = [testStationDto1, testStationDto2];

  afterEach(() => {
    mockAdapter.reset();
  });

  it('should throw an error if unable to query the BlueSG Station Service API', async () => {
    mockAdapter.onGet(`${mockStationsUrl}`).reply(500);
    await expect(
      stationAdapter.get(mockStationsUrl, StationDto),
    ).rejects.toThrow(StationApiError);
  });

  it('should return data parsed as the correct class', async () => {
    mockAdapter
      .onGet(`${mockStationsUrl}`)
      .reply(200, testStationDto1.toRawJsonObject());
    const response = await stationAdapter.get(mockStationsUrl, StationDto);

    expect(response).toStrictEqual(testStationDto1);
  });

  it.each([
    [
      test1StationList.map((stnDto) => stnDto.toRawJsonObject()),
      test1StationList,
    ],
    [
      test2StationList.map((stnDto) => stnDto.toRawJsonObject()),
      test2StationList,
    ],
  ])(
    'should return data in an array parsed as an array of the correct class',
    async (testStationResponseData, testParsedData) => {
      mockAdapter
        .onGet(
          `${mockStationsUrl}?${QueryString.stringify({
            page: 1,
            pageSize: 200,
          })}`,
        )
        .reply(200, testStationResponseData);

      mockAdapter
        .onGet(
          `${mockStationsUrl}?${QueryString.stringify({
            page: 2,
            pageSize: 200,
          })}`,
        )
        .reply(200, []); // Breaking loop

      const response = await stationAdapter.getAll(mockStationsUrl, StationDto);

      // expect(response.success).toBe(true);
      expect(response).toHaveLength(testStationResponseData.length);

      response.forEach((carDto, index) => {
        expect(carDto).toStrictEqual(testParsedData[index]);
      });
    },
  );
});
