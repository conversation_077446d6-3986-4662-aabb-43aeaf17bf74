import 'reflect-metadata';
import {
  carInServiceZoneCond,
  carPluggedCond,
  doorsAndWindowsClosedCond,
  doorsLockedCond,
  engineOffCond,
  parkingEngageCond,
} from 'src/common/constants/ending-rental-conditions';
import { OnGoingRentalBuilder } from 'test/helpers/builders/on-going-rental.builder';
import { StationBuilder } from 'test/helpers/builders/station.builder';
import { TelemetryCarInfoBuilder } from 'test/helpers/builders/telemetry-car-info.builder';
import { TelemetryCarStatusBuilder } from 'test/helpers/builders/telemetry-car-status.builder';

describe('TestingEndRentalCondition', () => {
  function cleanResources() {
    jest.resetAllMocks();
  }

  afterAll(cleanResources);

  describe.each([
    {
      caseName: '✅ satisfied',
      onGoingRental: new OnGoingRentalBuilder()
        .withCurrentStation(new StationBuilder().build()) // Current Station is existing
        .build(),
      fulfilled: true,
    },
    {
      caseName: '❌ unsatisfied',
      onGoingRental: new OnGoingRentalBuilder()
        .withCurrentStation(null)
        .build(),
      fulfilled: false,
    },
  ])(
    'Condition is $caseName: Car In Service Zone',
    ({ onGoingRental, fulfilled }) => {
      it('Verification', async () => {
        const result = carInServiceZoneCond.validate({
          onGoingRental,
        });

        expect(result.fulfilled).toEqual(fulfilled);
      });

      afterAll(cleanResources);
    },
  );

  describe.each([
    {
      caseName: '✅ satisfied',
      telemetryCarInfo: new TelemetryCarInfoBuilder()
        .withAreDoorsAndWindowsClosed(true)
        .withImmobilizerOn(true)
        .build(),
      telemetryCarStatus: new TelemetryCarStatusBuilder()
        .withAreDoorsAndWindowsClosed(true)
        .withImmobilizerOn(true)
        .build(),
      fulfilled: true,
    },
    {
      caseName: '❌ unsatisfied',
      telemetryCarInfo: new TelemetryCarInfoBuilder()
        .withAreDoorsAndWindowsClosed(false)
        .withImmobilizerOn(false)
        .build(),
      telemetryCarStatus: new TelemetryCarStatusBuilder()
        .withAreDoorsAndWindowsClosed(false)
        .withImmobilizerOn(false)
        .build(),
      fulfilled: false,
    },
  ])(
    'Condition is $caseName: Doors and windows closed',
    ({ telemetryCarInfo, telemetryCarStatus, fulfilled }) => {
      it('Verification', async () => {
        const result = doorsAndWindowsClosedCond.validate({
          telemetryCarInfo,
          telemetryCarStatus,
        });

        expect(result.fulfilled).toEqual(fulfilled);
      });

      afterAll(cleanResources);
    },
  );

  describe.each([
    {
      caseName: '✅ satisfied',
      telemetryCarInfo: new TelemetryCarInfoBuilder()
        .withLocked(true)
        .withImmobilizerOn(true)
        .build(),
      telemetryCarStatus: new TelemetryCarStatusBuilder()
        .withLocked(true)
        .withImmobilizerOn(true)
        .build(),
      fulfilled: true,
    },
    {
      caseName: '❌ unsatisfied',
      telemetryCarInfo: new TelemetryCarInfoBuilder().withLocked(false).build(),
      telemetryCarStatus: new TelemetryCarStatusBuilder()
        .withLocked(false)
        .build(),
      fulfilled: false,
    },
  ])(
    'Condition is $caseName: Doors locked',
    ({ telemetryCarInfo, telemetryCarStatus, fulfilled }) => {
      it('Verification', async () => {
        const result = doorsLockedCond.validate({
          telemetryCarInfo,
          telemetryCarStatus,
        });

        expect(result.fulfilled).toEqual(fulfilled);
      });

      afterAll(cleanResources);
    },
  );

  describe.each([
    {
      caseName: '✅ satisfied',
      telemetryCarInfo: new TelemetryCarInfoBuilder()
        .withEngineOn(false)
        .build(),
      telemetryCarStatus: new TelemetryCarStatusBuilder()
        .withEngineOn(false)
        .build(),
      fulfilled: true,
    },
    {
      caseName: '❌ unsatisfied',
      telemetryCarInfo: new TelemetryCarInfoBuilder()
        .withEngineOn(true)
        .build(),
      telemetryCarStatus: new TelemetryCarStatusBuilder()
        .withEngineOn(true)
        .build(),
      fulfilled: false,
    },
  ])(
    'Condition is $caseName: Engine off',
    ({ telemetryCarInfo, telemetryCarStatus, fulfilled }) => {
      it('Verification', async () => {
        const result = engineOffCond.validate({
          telemetryCarInfo,
          telemetryCarStatus,
        });

        expect(result.fulfilled).toEqual(fulfilled);
      });

      afterAll(cleanResources);
    },
  );

  describe.each([
    {
      caseName: '✅ satisfied',
      telemetryCarInfo: new TelemetryCarInfoBuilder()
        .withIsPluggedIn(true)
        .withIsCharging(true)
        .build(),
      telemetryCarStatus: new TelemetryCarStatusBuilder()
        .withIsPluggedIn(true)
        .withIsCharging(true)
        .build(),
      fulfilled: true,
    },
    {
      caseName: '❌ unsatisfied',
      telemetryCarInfo: new TelemetryCarInfoBuilder()
        .withIsPluggedIn(false)
        .withIsCharging(false)
        .build(),
      telemetryCarStatus: new TelemetryCarStatusBuilder()
        .withIsPluggedIn(false)
        .withIsCharging(false)
        .build(),
      fulfilled: false,
    },
  ])(
    'Condition is $caseName: Car plugged',
    ({ telemetryCarInfo, telemetryCarStatus, fulfilled }) => {
      it('Verification', async () => {
        const result = carPluggedCond.validate({
          telemetryCarInfo,
          telemetryCarStatus,
        });

        expect(result.fulfilled).toEqual(fulfilled);
      });

      afterAll(cleanResources);
    },
  );

  describe.each([
    {
      caseName: '✅ satisfied',
      telemetryCarInfo: new TelemetryCarInfoBuilder()
        .withParkingBrakeOn(true)
        .build(),
      telemetryCarStatus: new TelemetryCarStatusBuilder()
        .withParkingBrakeOn(true)
        .build(),
      fulfilled: true,
    },
    {
      caseName: '❌ unsatisfied',
      telemetryCarInfo: new TelemetryCarInfoBuilder()
        .withParkingBrakeOn(false)
        .build(),
      telemetryCarStatus: new TelemetryCarStatusBuilder()
        .withParkingBrakeOn(false)
        .build(),
      fulfilled: false,
    },
  ])(
    'Condition is $caseName: Parking engaged',
    ({ telemetryCarInfo, telemetryCarStatus, fulfilled }) => {
      it('Verification', async () => {
        const result = parkingEngageCond.validate({
          telemetryCarInfo,
          telemetryCarStatus,
        });

        expect(result.fulfilled).toEqual(fulfilled);
      });

      afterAll(cleanResources);
    },
  );
});
