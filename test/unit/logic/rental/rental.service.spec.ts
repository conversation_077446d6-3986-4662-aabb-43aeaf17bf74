import { RentalError } from 'src/common/errors/rental-error';
import { BsgUserId, VulogJourneyOrTripId } from 'src/common/tiny-types';
import { RentalService } from 'src/logic/rental.service';
import { ReservationService } from 'src/logic/reservation.service';
import { createTestVulogJourneyDto } from 'test/helpers/builders/dtos/test-vulog-journey-dto.builder';
import { createTestVulogTripDto } from 'test/helpers/builders/dtos/test-vulog-trip-dto.builder';

import { CorrelationIdService } from '@bluesg-2/monitoring';
import { faker } from '@faker-js/faker';
import { Test } from '@nestjs/testing';
import { Page } from 'src/common/query/page';
import { BillingExternalService } from 'src/external/billing/billing.external.service';
import { StationExternalService } from 'src/external/station/station.external.service';
import { UserSubscriptionExternalService } from 'src/external/user-subscription/user-subscription.external.service';
import { AvailabilityService } from 'src/logic/availability.service';
import { VulogCarService } from 'src/logic/car-repository/vulog/vulog-car.service';
import { VulogUserService } from 'src/logic/vulog/vulog-user.service';
import { EventBusService } from 'src/shared/event-bus/event-bus.service';
import {
  createTestVulogCarDto,
  createTestVulogCarRealTimeDto,
} from 'test/helpers/builders/dtos/test-car-dto.builder';
import { TestEndRentalEventDtoBuilder } from 'test/helpers/builders/dtos/test-end-rental-event-dto.builder';
import { createTestStationDto } from 'test/helpers/builders/dtos/test-station-dto.builder';
import { createTestVulogUserEntity } from 'test/helpers/builders/entities/test-vulog-user-entity.builder';
import { OnGoingRentalBuilder } from 'test/helpers/builders/on-going-rental.builder';
import { v4 } from 'uuid';
import { VulogEventNotifierService } from '../../../../src/logic/vulog/vulog-event-notifier.service';

jest.mock('../../../../src/logic/vulog/vulog-event-notifier.service');

// TODO: Skip for now. Refactor this test suite. Start Rental needs Reservation instead of Car ID
describe.skip('RentalService', () => {
  let rentalService: RentalService;

  const testBsgUserId = new BsgUserId(faker.string.uuid());
  const testBsgUserIdMoreThanOneRentalOrReservation = new BsgUserId(
    faker.string.uuid(),
  );
  const testBsgUserIdNoRentalOrReservation = new BsgUserId(faker.string.uuid());
  const testVulogJourneyDtoReservation = createTestVulogJourneyDto(false, true);

  const testVulogJourneyDtoReservation2 = createTestVulogJourneyDto(
    false,
    true,
  );
  const testVulogJourneyDtoRental = createTestVulogJourneyDto(true, false);
  const testRentalInfo = testVulogJourneyDtoRental.toRentalInfo();

  const testVulogTripDtoRental1 = createTestVulogTripDto(true, false);
  const testVulogTripDtoRental2 = createTestVulogTripDto(true, false);
  const testVulogTripDtoRental3 = createTestVulogTripDto(true, false);
  const testVulogTripDtos = [
    testVulogTripDtoRental1,
    testVulogTripDtoRental2,
    testVulogTripDtoRental3,
  ];

  const testCar = createTestVulogCarDto(
    testVulogJourneyDtoRental.vehicleId,
  ).toRealtimeCar();

  const testVulogRealtimeTripDtos = [
    createTestVulogCarRealTimeDto(testCar.id.value),
  ];

  const testEndRentalRequestDto = new TestEndRentalEventDtoBuilder().build();

  const testVulogUser = createTestVulogUserEntity();

  const testStation = createTestStationDto();

  const testVulogTripDtoRental4 = createTestVulogTripDto(
    true,
    false,
    testCar.id.value,
  );

  const testCarsCounter = {
    station: testStation.toStation(),
    carAvailability: { [testCar.model.value]: 1 },
  };

  beforeAll(async () => {
    const testModule = await Test.createTestingModule({
      providers: [
        RentalService,
        {
          provide: VulogCarService,
          useValue: {
            getUserCurrentReservations: (bsgUserId) => {
              if (bsgUserId === testBsgUserIdMoreThanOneRentalOrReservation)
                return [
                  testVulogJourneyDtoReservation.toReservationInfo(),
                  testVulogJourneyDtoReservation.toReservationInfo(),
                ];
              if (bsgUserId === testBsgUserIdNoRentalOrReservation) return [];
              return [testVulogJourneyDtoReservation.toReservationInfo()];
            },
            startRental: (_bsgUserId, vulogJourneyId: VulogJourneyOrTripId) => {
              const journey = testVulogJourneyDtoRental;
              journey.id = vulogJourneyId.value;
              return journey.toRentalInfo();
            },
            getUserCurrentRentals: (bsgUserId) => {
              if (bsgUserId === testBsgUserId) return [testRentalInfo];
              if (bsgUserId === testBsgUserIdMoreThanOneRentalOrReservation)
                return [testRentalInfo, testRentalInfo];
              if (bsgUserId === testBsgUserIdNoRentalOrReservation) return [];
            },
            getOnGoingRentals: () =>
              testVulogRealtimeTripDtos.map((dto) =>
                dto.toRentalInfo(new Map()),
              ),
            endRental: () => null,
            getStaticCar: () => testCar,
            getAllVulogTrips: () =>
              new Page([testVulogTripDtoRental4], 1, true),
          },
        },
        {
          provide: ReservationService,
          useValue: {
            switchAllocationCar: () =>
              testVulogJourneyDtoReservation2.toReservationInfo(),
          },
        },
        {
          provide: EventBusService,
          useValue: {
            nonBlockingEmit: () => null,
          },
        },
        {
          provide: CorrelationIdService,
          useValue: {
            getCorrelationId: () => ({ value: v4() }),
          },
        },
        {
          provide: StationExternalService,
          useValue: {
            getStationByZones: () => [testStation],
          },
        },
        {
          provide: BillingExternalService,
          useValue: {},
        },
        {
          provide: UserSubscriptionExternalService,
          useValue: {},
        },
        {
          provide: VulogUserService,
          useValue: {
            findOneByBsgUserId: () => testVulogUser.toVulogUser(),
          },
        },
        {
          provide: AvailabilityService,
          useValue: {
            getCarsCounterForStation: () => testCarsCounter,
          },
        },
        {
          provide: VulogEventNotifierService,
          useClass: VulogEventNotifierService,
        },
      ],
    }).compile();

    rentalService = testModule.get<RentalService>(RentalService);
  });

  it('should be able to get details of a ongoing rental for a user', async () => {
    const rental = await rentalService.getCurrentRentalDetail(testBsgUserId);

    expect(rental).toStrictEqual(new OnGoingRentalBuilder().build());
  });

  it('should throw an error if there is more than one ongoing rental for a user', async () => {
    await expect(
      rentalService.getCurrentRentalDetail(
        testBsgUserIdMoreThanOneRentalOrReservation,
      ),
    ).rejects.toThrowError(RentalError);
  });

  it('should throw an error if attempting to retrieve ongoing rental details when there is no ongoing rental', async () => {
    await expect(
      rentalService.getCurrentRentalDetail(testBsgUserIdNoRentalOrReservation),
    ).rejects.toThrowError(RentalError);
  });

  it('should be able to get all ongoing rentals', async () => {
    const ongoingRentals = await rentalService.adminGetAllFinishedRentals();

    expect(ongoingRentals).toStrictEqual(
      testVulogTripDtos.map((dto) => dto.toRentalInfo()),
    );
  });

  it('should be able to end a rental', async () => {
    await expect(
      rentalService.endRentalTesting(testBsgUserId),
    ).resolves.not.toThrow();
  });

  it('should be able to end a rental for simulating data received from VULOG ', async () => {
    await expect(
      rentalService.endRentalSimulation(testEndRentalRequestDto),
    ).resolves.not.toThrow();
  });
});
