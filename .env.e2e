NODE_ENV=e2e
PORT=8881
CRON_PORT=8890
NEW_RELIC_LICENSE_KEY=new_relic_license_key
NEW_RELIC_ENABLED=false

DB_TYPE=postgres
DB_HOST=db
DB_PORT=5432
DB_USERNAME=test
DB_PASSWORD=test-pwd
DB_NAME=test-db

VULOG_URL=http://localhost:3000
VULOG_ADMIN_URL=http://localhost:3000
VULOG_CLIENT_ID=client-id
VULOG_CLIENT_SECRET=client-secret
VULOG_FLEET_ID=checkInTheWikiDoc
VULOG_CITY_ID=123
VULOG_SERVICE_ID=mock-service-id
VULOG_PRICING_ID=mock-pricing-id
VULOG_DEFAULT_USERNAME=testUsername
VULOG_DEFAULT_PASSWORD=testUsername
VULOG_DEFAULT_USERS='[{"username":"testUsername","password":"testUsername"}]'
VULOG_CONTINUATION_TOKEN_TTL=86400
VULOG_API_KEY=fake-api-key
VULOG_CENTER_URL=https://bluesg-test.vulog.center/BLUESG-SINGA/app

REDIS_HOST=redis
REDIS_PORT=6379

STATION_SERVICE_HOST=mock-station
STATION_SERVICE_PORT=8889
STATION_SERVICE_URL=http://mock-station:8889

USER_SUBSCRIPTION_SERVICE_URL=http://mock-user-subscription:88

JWT_SECRET=secret
JWT_SECRET_ADMIN=secret

# Fake SDK key with valid format, so that it wont be rejected by validation of ConfigCat
CONFIG_CAT_MAIN_CONFIG_KEY=3IHbCFPzjkqEnCW9xvj8kA/2ObIHlGaPE6OKFCna_FAKE
CONFIG_CAT_MIGRATION_CONFIG_KEY=3IHbCFPzjkqEnCW9xvj8kA/3UnukXGPD0udM5dY1_FAKE
CONFIG_CAT_RELEASE_CONFIG_KEY=3IHbCFPzjkqEnCW9xvj8kA/3UnukXGPD0udM5dY2_FAKE

ENCRYPT_STRING=secretToBeRotated

AWS_ACCESS_KEY_ID=mockKey1
AWS_SECRET_ACCESS_KEY=mockKey2
AWS_SESSION_TOKEN=

AWS_REGION=ap-southeast-1
USING_CREDENTIAL=true

SNS_TOPIC_ARN=mockSnsArn

CRON_EXPRESSION_EXPIRE_PRE_RESERVATIONS="0 */10 * * * *"
BILLING_SERVICE_URL=http://localhost:3003
CRON_EXPRESSION_POP_PRE_RESERVATIONS="0 */10 * * * *"

CAR_SERVICE_URL=foobar

SQS_QUEUE_URL=sqs.url

VULOG_DEFAULT_USERNAME_1=testUsername
VULOG_DEFAULT_PASSWORD_1=testUsername

VULOG_DEFAULT_USERNAME_2=testUsername
VULOG_DEFAULT_PASSWORD_2=testUsername

CRON_EXPIRE_RESERVATIONS_ENABLED=false

LOG_ENABLE=true
LOG_LEVEL=debug

# lib-bo-auth-guard config
AUTH0_SERVER=https://bluesg.auth0.com
AUTHENTICATION_CONFIG_KEY=3IHbCFPzjkqEnCW9xvj8kA/3UnukXGPD0ud4avqA_FAKE
