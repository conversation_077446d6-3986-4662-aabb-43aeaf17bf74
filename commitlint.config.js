module.exports = {
  extends: ['@commitlint/config-angular'],
  rules: {
    'subject-case': [0],
    'scope-case': [0],
    'jira-ticket-required': [2, 'always'], // Custom rule for JIRA ticket,
  },
  plugins: [
    {
      rules: {
        'jira-ticket-required': ({ header }) => {
          const jiraRegex = /[A-Z]{2,}-\d+/;
          return [
            jiraRegex.test(header),
            'Commit message must include a JIRA ticket id. (E.g: CS-78, GS-120)'
          ];
        }
      }
    }
  ]
};