# Rental Service

BlueSG service that manages all cars, reservations and rentals.

## Installation/Setup

To get started with local setup, refer to [setup/README.md](setup/README.md)

### Install packages and dependencies

```bash
$ yarn install
```

### Generate a database migration file

```bash
yarn migration:dev:generate
```

### Run docker-compose to spin up Redis and Postgres containers

```bash
yarn local:container:start
```

### Running migrations for your local database

```bash
# run migrations
yarn migration:dev:run

# revert latest migration
yarn migration:dev:revert

# show all migrations and whether they have been run or not
yarn migration:dev:show
```

## Running the app

```bash
# development
$ yarn start

# watch mode
$ yarn start:crons:dev // handle foreground tasks, need to run this command first
$ yarn start:dev // handle web requests

# production mode
$ yarn start:prod
```

## Running migrations

```
$ yarn typeorm migration:run
# if you see errors related to geometry undefined, run this command in your database to have necessary extension installed:
$ CREATE EXTENSION IF NOT EXISTS postgis;
```

## Test

```bash
# unit tests
$ yarn test

# e2e tests in docker
$ yarn container:build
$ yarn container:start
$ yarn container:exec rental yarn migration:e2e:run
$ yarn container:exec rental yarn test:e2e

# e2e tests local
# need .env.e2e.local, copied from .env.e2e with db & redis hosts replaced with localhost
$ yarn container:build
$ yarn container:start
$ yarn container:exec rental yarn migration:e2e:run
$ yarn test:e2e

# test coverage
$ yarn test:cov
```
