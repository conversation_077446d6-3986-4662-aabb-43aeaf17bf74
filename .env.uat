CRON_PORT=8890

VULOG_URL=https://java-sta.vulog.com
VULOG_ADMIN_URL=http://fakevulog.int-dev:3000
VULOG_CLIENT_ID=BLUESG-SINGA_secure
VULOG_CLIENT_SECRET=58643b5e-6c78-4877-8dfd-69568dba0f0f
VULOG_FLEET_ID=BLUESG-SINGA
VULOG_CITY_ID=123
VULOG_SERVICE_ID=1fb65e82-69ca-4278-b890-9d48ebb8e93a
VULOG_PRICING_ID=mock-pricing-id
VULOG_DEFAULT_USERNAME=testUsername
VULOG_DEFAULT_PASSWORD=testUsername
VULOG_CONTINUATION_TOKEN_TTL=86400
VULOG_CENTER_URL=https://bluesg.vulog.center/BLUESG-SINGA/app

STATION_SERVICE_HOST=http://sts.int-dev
STATION_SERVICE_PORT=3006

SENTRY_DSN=https://<EMAIL>/4505362339266560
SENTRY_ENVIRONMENT=uat

AWS_REGION=ap-southeast-1

SNS_TOPIC_ARN=mockSnsArn

CRON_EXPRESSION_EXPIRE_PRE_RESERVATIONS=0 */5 * * * *
CRON_EXPRESSION_POP_PRE_RESERVATIONS=0 */5 * * * *

END_RENTAL_IN_AIMA_SQS_QUEUE_URL=https://sqs.ap-southeast-1.amazonaws.com/331142702324/uat-end-rental-aima