{"branches": [{"name": "main", "prerelease": false}], "tagFormat": "v${version}", "plugins": [["@semantic-release/commit-analyzer", {"preset": "angular", "releaseRules": [{"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "refactor", "release": "patch"}, {"type": "perf", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}]}], ["semantic-release-jira-notes", {"jiraHost": "bluesg.atlassian.net", "ticketPrefixes": ["BAU", "UIUX", "BLUES", "CS", "CRC", "DE", "DEL", "DO", "DP", "FA", "GS", "PER", "PDY", "QS", "TECH"]}], ["semantic-release-slack-bot", {"onSuccessTemplate": {"text": "*$package_name* released *$npm_package_version*\n$release_notes"}, "markdownReleaseNotes": true, "branchesConfig": [{"pattern": "main", "notifyOnSuccess": true, "notifyOnFail": true}]}], "@semantic-release/git", "@semantic-release/github", {"assets": [{"path": "./*", "label": "Source code"}]}]}