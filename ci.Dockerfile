FROM node:18.16.0 as builder

<PERSON>N<PERSON> NODE_ENV build
ARG GITHUB_TOKEN

ENV GITHUB_TOKEN $GITHUB_TOKEN

WORKDIR /usr/src/app

COPY . .
COPY .npmrc.ci ./.npmrc

RUN yarn install
RUN yarn build


FROM node:18.16.0-alpine

ARG BUILD_ROOT=/usr/src/app
ARG GITHUB_TOKEN

WORKDIR /usr/src/app

## New Relic Configuration
ENV NEW_RELIC_NO_CONFIG_FILE true
ENV NEW_RELIC_DISTRIBUTED_TRACING_ENABLED true
ENV NEW_RELIC_LOG stdout
ENV NEW_RELIC_ALLOW_ALL_HEADERS true
ENV NEW_RELIC_ATTRIBUTES_EXCLUDE ["request.headers.cookie","request.headers.authorization","request.headers.proxyAuthorization","request.headers.setCookie*","request.headers.x*","response.headers.cookie","response.headers.authorization","response.headers.proxyAuthorization","response.headers.setCookie*","response.headers.x*"]


# set environment values
ENV GITHUB_TOKEN $GITHUB_TOKEN

COPY --from=builder $BUILD_ROOT/.npmrc .
COPY --from=builder $BUILD_ROOT/package.json .
COPY --from=builder $BUILD_ROOT/yarn.lock .
COPY --from=builder $BUILD_ROOT/patches/ ./patches/

RUN yarn install --production

COPY --from=builder $BUILD_ROOT/dist/ ./dist/
COPY --from=builder $BUILD_ROOT/migrations/ ./migrations/
COPY --from=builder $BUILD_ROOT/.env* .
COPY --from=builder $BUILD_ROOT/entrypoint.sh .

RUN ["chmod", "+x", "./entrypoint.sh"]

ENTRYPOINT [ "./entrypoint.sh" ]

CMD ["npm", "run", "start:prod"]
