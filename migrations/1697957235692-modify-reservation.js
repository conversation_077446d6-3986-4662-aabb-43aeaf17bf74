module.exports = class ModifyReservation1697957235692 {
  name = 'ModifyReservation1697957235692';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "station_id"
        `);
    await queryRunner.query(`
            CREATE TYPE "public"."reservation_car_model_enum" AS ENUM('Opel Corsa-e', 'Bluecar')
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "car_model" "public"."reservation_car_model_enum" NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "start_station_id" uuid NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "local" boolean NOT NULL DEFAULT false
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "started_at" TIMESTAMP WITH TIME ZONE
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "bsg_user_id"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "bsg_user_id" uuid NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "car_id"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "car_id" uuid
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "status"
        `);
    await queryRunner.query(`
            CREATE TYPE "public"."reservation_status_enum" AS ENUM(
                'CREATING',
                'RESERVED',
                'FAILED',
                'CANCELLED',
                'CONVERTED'
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "status" "public"."reservation_status_enum" NOT NULL
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "status"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."reservation_status_enum"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "status" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "car_id"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "car_id" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "bsg_user_id"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "bsg_user_id" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "started_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "local"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "start_station_id"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "car_model"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."reservation_car_model_enum"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "station_id" character varying
        `);
  }
};
