module.exports = class AddIndexForReservation1703137323076 {
  name = 'AddIndexForReservation1703137323076';

  async up(queryRunner) {
    await queryRunner.query(`
            CREATE INDEX "IDX_29be1e5ad1e641d6966b30aa9b" ON "reservation" ("status")
        `);
    await queryRunner.query(`
            CREATE INDEX "IDX_485e25a3b41f54bdc12225dc5a" ON "reservation" ("reserved_at")
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            DROP INDEX "public"."IDX_485e25a3b41f54bdc12225dc5a"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."IDX_29be1e5ad1e641d6966b30aa9b"
        `);
  }
};
