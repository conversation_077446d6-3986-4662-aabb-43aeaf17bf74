module.exports = class UpdateVulogEvent1741601439506 {
  name = 'UpdateVulogEvent1741601439506';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "vulog_event"
            ADD "source" character varying NOT NULL DEFAULT 'AIMA'
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "vulog_event"."source" IS 'The source of the event (AiMA or Vehicle Gateway)'
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "vulog_event" DROP COLUMN "source"
        `);
  }
};
