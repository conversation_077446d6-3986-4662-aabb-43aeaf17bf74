module.exports = class UpdateReservationStatus1699424616779 {
  name = 'UpdateReservationStatus1699424616779';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TYPE "public"."reservation_status_enum"
            RENAME TO "reservation_status_enum_old"
        `);
    await queryRunner.query(`
            CREATE TYPE "public"."reservation_status_enum" AS ENUM(
                'CREATING',
                'RESERVED',
                'FAILED',
                'CANCELLED',
                'CONVERTED',
                'END_TRIP',
                'EXPIRED'
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ALTER COLUMN "status" TYPE "public"."reservation_status_enum" USING "status"::"text"::"public"."reservation_status_enum"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."reservation_status_enum_old"
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            CREATE TYPE "public"."reservation_status_enum_old" AS ENUM(
                'CREATING',
                'RESERVED',
                'FAILED',
                'CANCELLED',
                'CONVERTED',
                'END_TRIP'
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ALTER COLUMN "status" TYPE "public"."reservation_status_enum_old" USING "status"::"text"::"public"."reservation_status_enum_old"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."reservation_status_enum"
        `);
    await queryRunner.query(`
            ALTER TYPE "public"."reservation_status_enum_old"
            RENAME TO "reservation_status_enum"
        `);
  }
};
