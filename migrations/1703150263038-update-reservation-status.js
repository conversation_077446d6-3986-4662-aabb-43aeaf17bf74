// eslint-disable-next-line @typescript-eslint/no-unused-vars,@typescript-eslint/no-var-requires
const { MigrationInterface, QueryRunner } = require('typeorm');

module.exports = class UpdateReservationStatus1703150263038 {
  name = 'UpdateReservationStatus1703150263038';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TYPE "public"."reservation_status_enum"
            RENAME TO "reservation_status_enum_old"
        `);
    await queryRunner.query(`
            CREATE TYPE "public"."reservation_status_enum" AS ENUM(
                'CREATING',
                'RESERVED',
                'FAILED',
                'CANCELLED',
                'CONVERTED',
                'END_TRIP_TO_REVIEW',
                'ENDED_WAITING_FOR_INVOICING',
                'END_TRIP',
                'EXPIRED',
                'ENDED'
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ALTER COLUMN "status" TYPE "public"."reservation_status_enum" USING "status"::"text"::"public"."reservation_status_enum"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."reservation_status_enum_old"
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            CREATE TYPE "public"."reservation_status_enum_old" AS ENUM(
                'CREATING',
                'RESERVED',
                'FAILED',
                'CANCELLED',
                'CONVERTED',
                'ENDED_WAITING_FOR_INVOICING',
                'END_TRIP',
                'EXPIRED',
                'ENDED'
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ALTER COLUMN "status" TYPE "public"."reservation_status_enum_old" USING "status"::"text"::"public"."reservation_status_enum_old"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."reservation_status_enum"
        `);
    await queryRunner.query(`
            ALTER TYPE "public"."reservation_status_enum_old"
            RENAME TO "reservation_status_enum"
        `);
  }
};
