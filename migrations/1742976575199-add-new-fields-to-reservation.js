module.exports = class AddNewFieldsToReservation1742976575199 {
  name = 'AddNewFieldsToReservation1742976575199';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "user_full_name" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "user_email" character varying
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "user_email"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "user_full_name"
        `);
  }
};
