module.exports = class AddLinkedReservation1702522848564 {
  constructor() {
    this.name = 'AddLinkedReservation1702522848564';
  }
  async up(queryRunner) {
    await queryRunner.query(
      `ALTER TABLE "pre_reservation" ADD "linked_reservation_id" character varying`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "pre_reservation"."linked_reservation_id" IS 'If a pre-reservation is converted, it should linked to an on-going reservation'`,
    );
  }
  async down(queryRunner) {
    await queryRunner.query(
      `COMMENT ON COLUMN "pre_reservation"."linked_reservation_id" IS 'If a pre-reservation is converted, it should linked to an on-going reservation'`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre_reservation" DROP COLUMN "linked_reservation_id"`,
    );
  }
};
