module.exports = class UpdateReservation1697962800302 {
  name = 'UpdateReservation1697962800302';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "end_station_id" uuid
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "ended_at" TIMESTAMP WITH TIME ZONE
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "reserved_at" TIMESTAMP WITH TIME ZONE
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "expires_at" TIMESTAMP WITH TIME ZONE
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ALTER COLUMN "local" DROP NOT NULL
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ALTER COLUMN "local"
            SET NOT NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "expires_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "reserved_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "ended_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "end_station_id"
        `);
  }
};
