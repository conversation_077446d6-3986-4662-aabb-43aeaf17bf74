module.exports = class AddNewConfig1720683073123 {
  name = 'AddNewConfig1720683073123';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TYPE "public"."configuration_key_enum"
            RENAME TO "configuration_key_enum_old"
        `);
    await queryRunner.query(`
            CREATE TYPE "public"."configuration_key_enum" AS ENUM(
                'WARNING_LOW_BATTERY_LEVEL_PER_CAR_MODEL',
                'GRACE_PERIOD_IN_MINUTES',
                'MAXIMUM_ITEMS_PER_QUEUE',
                'QUEUE_POP_TIMEOUT_IN_MINUTES',
                'CAR_CONNECTION_PROBLEM__MAXIMUM_RETRY_ATTEMPTS_FOR_PING'
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "configuration"
            ALTER COLUMN "key" TYPE "public"."configuration_key_enum" USING "key"::"text"::"public"."configuration_key_enum"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."configuration_key_enum_old"
        `);
    await queryRunner.query(`
        INSERT INTO public."configuration"
        (id, created_at, created_by, modified_at, modified_by, deleted_at, deleted_by, "key", "type", value, description, note)
        VALUES('e4c9783a-89ed-4126-8c80-d2f321656a9f'::uuid, '2024-07-11 07:26:11.815', NULL, '2023-12-06 10:26:11.815', NULL, NULL, NULL, 'CAR_CONNECTION_PROBLEM__MAXIMUM_RETRY_ATTEMPTS_FOR_PING'::public."configuration_key_enum", 'NUMBER'::public."configuration_type_enum", '6', 'Maximum retry attempts for ping after first failed attempt to wake car up', NULL);
    `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            CREATE TYPE "public"."configuration_key_enum" AS ENUM(
                'WARNING_LOW_BATTERY_LEVEL_PER_CAR_MODEL',
                'GRACE_PERIOD_IN_MINUTES',
                'MAXIMUM_ITEMS_PER_QUEUE',
                'QUEUE_POP_TIMEOUT_IN_MINUTES'
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "configuration"
            ALTER COLUMN "key" TYPE "public"."configuration_key_enum_old" USING "key"::"text"::"public"."configuration_key_enum_old"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."configuration_key_enum"
        `);
    await queryRunner.query(`
            ALTER TYPE "public"."configuration_key_enum_old"
            RENAME TO "configuration_key_enum"
        `);
  }
};
