// eslint-disable-next-line @typescript-eslint/no-unused-vars,@typescript-eslint/no-var-requires
const { MigrationInterface, QueryRunner } = require('typeorm');

module.exports = class CreateRfidColumn1700647723904 {
  name = 'CreateRfidColumn1700647723904';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "vulog_user"
            ADD IF NOT EXISTS "rfid" text NULL
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."started_at" IS 'The time that the rental starts'
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."ended_at" IS 'The time that the rental ends'
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."reserved_at" IS 'The time that reservation is confirmed by VULOG'
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."expires_at" IS 'The time that reservation is expired based on VULOG config'
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."expires_at" IS NULL
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."reserved_at" IS NULL
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."ended_at" IS NULL
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."started_at" IS NULL
        `);
    await queryRunner.query(`
            ALTER TABLE "vulog_user" DROP COLUMN "rfid"
        `);
  }
};
