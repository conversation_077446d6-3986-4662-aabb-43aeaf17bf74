module.exports = class BigChange1701767836108 {
  constructor() {
    this.name = 'BigChange1701767836108';
  }
  async up(queryRunner) {
    await queryRunner.query(
      `ALTER TABLE "pre_reservation" DROP COLUMN "station_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre_reservation" ADD "station_ids" uuid array NOT NULL DEFAULT '{}'`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "pre_reservation"."station_ids" IS 'Station IDs in queue'`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre_reservation" ADD "pop_station_id" uuid`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "pre_reservation"."pop_station_id" IS 'Station that is popped'`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre_reservation" ADD "cancel_reservation" boolean DEFAULT false`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "pre_reservation"."cancel_reservation" IS 'Should cancel on-going reservations if this flag is true'`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre_reservation" ADD "car_categories" character varying array NOT NULL DEFAULT '{}'`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "pre_reservation"."car_categories" IS 'Expected car models when a pre-reservation is popped'`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre_reservation" ALTER COLUMN "sent_location" TYPE geometry`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d92a8435c2a429a63d33c223e7" ON "pre_reservation" USING GIN("station_ids") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_33f394a1cc6302efb4838b6b39" ON "pre_reservation" ("reserved_at") `,
    );
  }
  async down(queryRunner) {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_33f394a1cc6302efb4838b6b39"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_d92a8435c2a429a63d33c223e7"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre_reservation" ALTER COLUMN "sent_location" TYPE geometry(GEOMETRY,0)`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "pre_reservation"."car_categories" IS 'Expected car models when a pre-reservation is popped'`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre_reservation" DROP COLUMN "car_categories"`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "pre_reservation"."cancel_reservation" IS 'Should cancel on-going reservations if this flag is true'`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre_reservation" DROP COLUMN "cancel_reservation"`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "pre_reservation"."pop_station_id" IS 'Station that is popped'`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre_reservation" DROP COLUMN "pop_station_id"`,
    );
    await queryRunner.query(
      `COMMENT ON COLUMN "pre_reservation"."station_ids" IS 'Station IDs in queue'`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre_reservation" DROP COLUMN "station_ids"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre_reservation" ADD "station_id" uuid NOT NULL`,
    );
  }
};
//# sourceMappingURL=1701767836108-big-change.js.map
