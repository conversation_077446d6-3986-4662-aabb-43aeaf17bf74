module.exports = class AddGpsCoordinates1706772305260 {
  name = 'AddGpsCoordinates1706772305260';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "vulog_end_gps_coordinates" geometry
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "mobile_end_gps_coordinates" geometry
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."mobile_end_gps_coordinates" IS 'Point geometry object. \`coordinates\` should be [longitude, latitude]'
        `);
    await queryRunner.query(`
        COMMENT ON COLUMN "reservation"."vulog_end_gps_coordinates" IS 'Point geometry object. \`coordinates\` should be [longitude, latitude]'
    `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "mobile_end_gps_coordinates"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "vulog_end_gps_coordinates"
        `);
  }
};
