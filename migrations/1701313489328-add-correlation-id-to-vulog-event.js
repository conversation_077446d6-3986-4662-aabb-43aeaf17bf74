module.exports = class AddCorrelationIdToVulogEvent1701313489328 {
  name = 'AddCorrelationIdToVulogEvent1701313489328';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "vulog_event"
            ADD "correlation_id" character varying
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "vulog_event"."correlation_id" IS 'The request ID to trace the requests going through internal services and external ones'
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            COMMENT ON COLUMN "vulog_event"."correlation_id" IS 'The request ID to trace the requests going through internal services and external ones'
        `);
    await queryRunner.query(`
            ALTER TABLE "vulog_event" DROP COLUMN "correlation_id"
        `);
  }
};
