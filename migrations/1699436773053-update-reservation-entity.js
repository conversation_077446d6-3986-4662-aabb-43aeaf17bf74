module.exports = class UpdateReservationEntity1699436773053 {
  name = 'UpdateReservationEntity1699436773053';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "vulog_user" DROP COLUMN "uss_updated_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "vulog_user" DROP COLUMN "user_info_hash"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "amount" numeric(14, 2)
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "duration" numeric(14, 2)
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "pricing_policy" jsonb
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "pricing_policy"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "duration"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "amount"
        `);
    await queryRunner.query(`
            ALTER TABLE "vulog_user"
            ADD "user_info_hash" text
        `);
    await queryRunner.query(`
            ALTER TABLE "vulog_user"
            ADD "uss_updated_at" TIMESTAMP WITH TIME ZONE
        `);
  }
};
