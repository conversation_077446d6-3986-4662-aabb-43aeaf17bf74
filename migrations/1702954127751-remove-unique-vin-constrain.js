// eslint-disable-next-line @typescript-eslint/no-var-requires,@typescript-eslint/no-unused-vars
const { MigrationInterface, QueryRunner } = require('typeorm');

module.exports = class RemoveTheUniqueConstrainOnVin1702954127751 {
  name = 'RemoveTheUniqueConstrainOnVin1702954127751';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "vulog_user"
            ALTER COLUMN "uss_updated_at"
            SET DEFAULT '"1999-12-31T16:00:00.000Z"'
        `);
    await queryRunner.query(`
            ALTER TABLE "car" DROP CONSTRAINT "UQ_e917bf949f80860e817b74006e8"
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "car"
            ADD CONSTRAINT "UQ_e917bf949f80860e817b74006e8" UNIQUE ("vin")
        `);
    await queryRunner.query(`
            ALTER TABLE "vulog_user"
            ALTER COLUMN "uss_updated_at"
            SET DEFAULT '1999-12-31 16:00:00+00'
        `);
  }
};
