module.exports = class AddNewConfig1701832994131 {
  name = 'AddNewConfig1701832994131';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TYPE "public"."configuration_key_enum"
            RENAME TO "configuration_key_enum_old"
        `);
    await queryRunner.query(`
            CREATE TYPE "public"."configuration_key_enum" AS ENUM(
                'WARNING_LOW_BATTERY_LEVEL_PER_CAR_MODEL',
                'GRACE_PERIOD_IN_MINUTES',
                'MAXIMUM_ITEMS_PER_QUEUE',
                'QUEUE_POP_TIMEOUT_IN_MINUTES'
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "configuration"
            ALTER COLUMN "key" TYPE "public"."configuration_key_enum" USING "key"::"text"::"public"."configuration_key_enum"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."configuration_key_enum_old"
        `);
    await queryRunner.query(`
        INSERT INTO public."configuration"
        (id, created_at, created_by, modified_at, modified_by, deleted_at, deleted_by, "key", "type", value, description, note)
        VALUES('f217a572-4108-4c75-95e0-cf7e3d2b85e4'::uuid, '2023-12-06 10:26:11.815', NULL, '2023-12-06 10:26:11.815', NULL, NULL, NULL, 'MAXIMUM_ITEMS_PER_QUEUE'::public."configuration_key_enum", 'NUMBER'::public."configuration_type_enum", '5', 'Maximum items per queue', NULL);
    `);
    await queryRunner.query(`
        INSERT INTO public."configuration"
        (id, created_at, created_by, modified_at, modified_by, deleted_at, deleted_by, "key", "type", value, description, note)
        VALUES('af0bea8c-6b2a-4286-93a0-73ce5d8701be'::uuid, '2023-12-06 10:26:11.815', NULL, '2023-12-06 10:26:11.815', NULL, NULL, NULL, 'QUEUE_POP_TIMEOUT_IN_MINUTES'::public."configuration_key_enum", 'NUMBER'::public."configuration_type_enum", '30', 'Q-Pop timeout in minutes', NULL);
    `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            CREATE TYPE "public"."configuration_key_enum_old" AS ENUM(
                'WARNING_LOW_BATTERY_LEVEL_PER_CAR_MODEL',
                'GRACE_PERIOD_IN_MINUTES'
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "configuration"
            ALTER COLUMN "key" TYPE "public"."configuration_key_enum_old" USING "key"::"text"::"public"."configuration_key_enum_old"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."configuration_key_enum"
        `);
    await queryRunner.query(`
            ALTER TYPE "public"."configuration_key_enum_old"
            RENAME TO "configuration_key_enum"
        `);
  }
};
