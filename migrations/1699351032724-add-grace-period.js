module.exports = class AddGracePeriod1699351032724 {
  name = 'AddGracePeriod1699351032724';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "abuse_releases_at" TIMESTAMP WITH TIME ZONE
        `);
    await queryRunner.query(`
            ALTER TYPE "public"."configuration_key_enum"
            RENAME TO "configuration_key_enum_old"
        `);
    await queryRunner.query(`
            CREATE TYPE "public"."configuration_key_enum" AS ENUM(
                'WARNING_LOW_BATTERY_LEVEL_PER_CAR_MODEL',
                'GRACE_PERIOD_IN_MINUTES'
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "configuration"
            ALTER COLUMN "key" TYPE "public"."configuration_key_enum" USING "key"::"text"::"public"."configuration_key_enum"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."configuration_key_enum_old"
        `);
    await queryRunner.query(`
            INSERT INTO public."configuration" (id,created_at,created_by,modified_at,modified_by,deleted_at,deleted_by,"key","type",value,description,note) VALUES
                ('09620679-fd97-44d3-80af-2db562c0aee8','2023-11-07 17:00:17.816',NULL,'2023-11-07 17:00:17.816',NULL,NULL,NULL,'GRACE_PERIOD_IN_MINUTES','NUMBER','10','Grace period in minutes',NULL);
    `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            CREATE TYPE "public"."configuration_key_enum_old" AS ENUM('WARNING_LOW_BATTERY_LEVEL_PER_CAR_MODEL')
        `);
    await queryRunner.query(`
            ALTER TABLE "configuration"
            ALTER COLUMN "key" TYPE "public"."configuration_key_enum_old" USING "key"::"text"::"public"."configuration_key_enum_old"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."configuration_key_enum"
        `);
    await queryRunner.query(`
            ALTER TYPE "public"."configuration_key_enum_old"
            RENAME TO "configuration_key_enum"
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "abuse_releases_at"
        `);
  }
};
