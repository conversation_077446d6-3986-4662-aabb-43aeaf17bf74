module.exports = class AddFlagForEndingRentalData1701423294215 {
  name = 'AddFlagForEndingRentalData1701423294215';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "is_ending_rental_data_added" boolean DEFAULT false
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."is_ending_rental_data_added" IS 'Mark as true when the data for ending rental data is added'
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."is_ending_rental_data_added" IS 'Mark as true when the data for ending rental data is added'
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "is_ending_rental_data_added"
        `);
  }
};
