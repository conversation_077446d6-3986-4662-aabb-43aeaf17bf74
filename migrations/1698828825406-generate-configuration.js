module.exports = class GenerateConfiguration1698828825406 {
  name = 'GenerateConfiguration1698828825406';

  async up(queryRunner) {
    await queryRunner.query(`
            CREATE TYPE "public"."configuration_key_enum" AS ENUM('WARNING_LOW_BATTERY_LEVEL_PER_CAR_MODEL')
        `);
    await queryRunner.query(`
            CREATE TYPE "public"."configuration_type_enum" AS ENUM('JSON', 'PLAIN', 'NUMBER')
        `);
    await queryRunner.query(`
            CREATE TABLE "configuration" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "created_by" character varying,
                "modified_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "modified_by" character varying,
                "deleted_at" TIMESTAMP WITH TIME ZONE,
                "deleted_by" character varying,
                "key" "public"."configuration_key_enum" NOT NULL,
                "type" "public"."configuration_type_enum" NOT NULL,
                "value" character varying NOT NULL,
                "description" character varying,
                "note" character varying,
                CONSTRAINT "PK_03bad512915052d2342358f0d8b" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            INSERT INTO public."configuration" (created_at,created_by,modified_at,modified_by,deleted_at,deleted_by,"key","type",value,description,note) VALUES
                ('2023-11-01 15:57:02.576',NULL,'2023-11-01 15:57:02.576',NULL,NULL,NULL,'WARNING_LOW_BATTERY_LEVEL_PER_CAR_MODEL','JSON','{"Bluecar":24,"Opel Corsa-e":30}','Warning Low Battery Level for each car model',NULL);
    `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            DROP TABLE "configuration"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."configuration_type_enum"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."configuration_key_enum"
        `);
    await queryRunner.query(`
            DELETE FROM public."configuration" where "key" = 'WARNING_LOW_BATTERY_LEVEL_PER_CAR_MODEL'
        `);
  }
};
