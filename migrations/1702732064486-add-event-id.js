// eslint-disable-next-line @typescript-eslint/no-var-requires,@typescript-eslint/no-unused-vars
const { MigrationInterface, QueryRunner } = require('typeorm');

module.exports = class AddEventId1702732064486 {
  name = 'AddEventId1702732064486';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "vulog_event"
            ADD "event_id" text
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "vulog_event"."event_id" IS 'The id from the event sent by Vulog'
        `);
    await queryRunner.query(`
            DROP INDEX "public"."IDX_5a8cf0f7dcd7fecdb0d1674970"
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "vulog_event"."type" IS 'the type of event from Vulog'
        `);
    await queryRunner.query(`
            ALTER TABLE "vulog_user"
            ALTER COLUMN "uss_updated_at"
            SET DEFAULT '"1999-12-31T16:00:00.000Z"'
        `);
    await queryRunner.query(`
            CREATE INDEX "IDX_6fd6f1b69e1c507ea011a2c8a9" ON "vulog_event" ("event_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "IDX_5a8cf0f7dcd7fecdb0d1674970" ON "vulog_event" ("received_at", "type")
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            DROP INDEX "public"."IDX_5a8cf0f7dcd7fecdb0d1674970"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."IDX_6fd6f1b69e1c507ea011a2c8a9"
        `);
    await queryRunner.query(`
            ALTER TABLE "vulog_user"
            ALTER COLUMN "uss_updated_at"
            SET DEFAULT '1999-12-31 16:00:00+00'
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "vulog_event"."type" IS NULL
        `);
    await queryRunner.query(`
            CREATE INDEX "IDX_5a8cf0f7dcd7fecdb0d1674970" ON "vulog_event" ("type", "received_at")
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "vulog_event"."event_id" IS 'The id from the event sent by Vulog'
        `);
    await queryRunner.query(`
            ALTER TABLE "vulog_event" DROP COLUMN "event_id"
        `);
  }
};
