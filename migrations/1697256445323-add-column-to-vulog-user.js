/* eslint-disable @typescript-eslint/no-unused-vars */
// noinspection JSUnusedLocalSymbols
// eslint-disable-next-line @typescript-eslint/no-var-requires
const { MigrationInterface, QueryRunner } = require('typeorm');

module.exports = class AddColumnToVulogUser1697256445323 {
  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "vulog_user"
            ADD "status" character varying,
            ADD "password" character varying,
            ADD "is_registered" boolean;
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "vulog_user" DROP COLUMN "status";
            ALTER TABLE "vulog_user" DROP COLUMN "password";
            ALTER TABLE "vulog_user" DROP COLUMN "is_registered";
        `);
  }
};
