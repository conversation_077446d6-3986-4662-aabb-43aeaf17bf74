module.exports = class AddVulogEvent1701071513774 {
  name = 'AddVulogEvent1701071513774';

  async up(queryRunner) {
    await queryRunner.query(`
            CREATE TABLE "vulog_event" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "type" character varying NOT NULL,
                "origin" character varying NOT NULL,
                "trigger" character varying NOT NULL,
                "payload" jsonb NOT NULL,
                "received_at" TIMESTAMP WITH TIME ZONE NOT NULL,
                CONSTRAINT "PK_36539bcf62c2c92fbdb47b0b2e9" PRIMARY KEY ("id")
            );
            COMMENT ON COLUMN "vulog_event"."origin" IS 'Actor sends the event (Vubox, API, etc)';
            COMMENT ON COLUMN "vulog_event"."trigger" IS 'Is the event triggered periodically or realtime?';
            COMMENT ON COLUMN "vulog_event"."payload" IS 'Raw webhook response from Vulog';
            COMMENT ON COLUMN "vulog_event"."received_at" IS 'The time when BlueSG receives webhook response from Vulog'
        `);
    await queryRunner.query(`
            CREATE INDEX "IDX_79752b3d4244f5cec18499f175" ON "vulog_event" ("type")
        `);
    await queryRunner.query(`
            CREATE INDEX "IDX_06a0d7040a13cadebaed4309f8" ON "vulog_event" ("origin")
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            DROP INDEX "public"."IDX_06a0d7040a13cadebaed4309f8"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."IDX_79752b3d4244f5cec18499f175"
        `);
    await queryRunner.query(`
            DROP TABLE "vulog_event"
        `);
  }
};
