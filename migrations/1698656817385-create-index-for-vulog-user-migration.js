module.exports = class CreateIndexForVulogUserMigration1698656817385 {
  name = 'CreateIndexForVulogUserMigration1698656817385';

  async up(queryRunner) {
    await queryRunner.query(
      'ALTER TABLE "vulog_user" ADD COLUMN "user_info_hash" text',
    );
    await queryRunner.query(
      'ALTER TABLE "vulog_user" ADD COLUMN "uss_updated_at" TIMESTAMP WITH TIME ZONE',
    );

    await queryRunner.query(
      `CREATE INDEX "IDX_ea5858448688755dc1ccd0b0a5" ON "vulog_user" ("bsg_user_id") `,
    );
  }

  async down(queryRunner) {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_ea5858448688755dc1ccd0b0a5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vulog_user" DROP COLUMN "uss_updated_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vulog_user" DROP COLUMN "user_info_hash"`,
    );
  }
};
