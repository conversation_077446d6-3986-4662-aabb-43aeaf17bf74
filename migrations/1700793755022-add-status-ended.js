module.exports = class AddStatusEnded1700793755022 {
  name = 'AddStatusEnded1700793755022';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TYPE "public"."reservation_status_enum"
            RENAME TO "reservation_status_enum_old"
        `);
    await queryRunner.query(`
            CREATE TYPE "public"."reservation_status_enum" AS ENUM(
                'CREATING',
                'RESERVED',
                'FAILED',
                'CANCELLED',
                'CONVERTED',
                'ENDED_WAITING_FOR_INVOICING',
                'END_TRIP',
                'EXPIRED',
                'ENDED'
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ALTER COLUMN "status" TYPE "public"."reservation_status_enum" USING "status"::"text"::"public"."reservation_status_enum"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."reservation_status_enum_old"
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."started_at" IS 'The time that the rental starts'
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."ended_at" IS 'The time that the rental ends'
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."reserved_at" IS 'The time that reservation is confirmed by VULOG'
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."expires_at" IS 'The time that reservation is expired based on VULOG config'
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."expires_at" IS NULL
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."reserved_at" IS NULL
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."ended_at" IS NULL
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."started_at" IS NULL
        `);
    await queryRunner.query(`
            CREATE TYPE "public"."reservation_status_enum_old" AS ENUM(
                'CREATING',
                'RESERVED',
                'FAILED',
                'CANCELLED',
                'CONVERTED',
                'END_TRIP',
                'EXPIRED'
            )
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ALTER COLUMN "status" TYPE "public"."reservation_status_enum_old" USING "status"::"text"::"public"."reservation_status_enum_old"
        `);
    await queryRunner.query(`
            DROP TYPE "public"."reservation_status_enum"
        `);
    await queryRunner.query(`
            ALTER TYPE "public"."reservation_status_enum_old"
            RENAME TO "reservation_status_enum"
        `);
  }
};
