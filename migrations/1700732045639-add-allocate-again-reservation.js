module.exports = class AddAllocateAgainReservation1700732045639 {
  name = 'AddAllocateAgainReservation1700732045639';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "reservation"
            ADD "allocate_again" boolean DEFAULT false
        `);
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."allocate_again" IS 'In case, the same reservation is to be allocated again once more time (keep the same reservation ID)'
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            COMMENT ON COLUMN "reservation"."allocate_again" IS 'In case, the same reservation is to be allocated again once more time (keep the same reservation ID)'
        `);
    await queryRunner.query(`
            ALTER TABLE "reservation" DROP COLUMN "allocate_again"
        `);
  }
};
