module.exports = class ModifyVulogUser1697766876673 {
  name = 'ModifyVulogUser1697766876673';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "vulog_user"
            ADD "vulog_user_id" character varying
        `);
    await queryRunner.query(`
            ALTER TABLE "vulog_user"
            ADD CONSTRAINT "UQ_196f413e773a9838a069714ee03" UNIQUE ("vulog_user_id")
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "vulog_user" DROP CONSTRAINT "UQ_196f413e773a9838a069714ee03"
        `);
    await queryRunner.query(`
            ALTER TABLE "vulog_user" DROP COLUMN "vulog_user_id"
        `);
  }
};
