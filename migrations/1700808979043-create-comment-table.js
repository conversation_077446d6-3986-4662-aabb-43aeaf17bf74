module.exports = class Dev1700808979043 {
  name = 'CreateTableComment1700808979043';

  async up(queryRunner) {
    await queryRunner.query(`
            CREATE TABLE "comment" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "created_by" character varying,
                "modified_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "modified_by" character varying,
                "deleted_at" TIMESTAMP WITH TIME ZONE,
                "deleted_by" character varying,
                "bsg_user_id" uuid NOT NULL,
                "comment" text NOT NULL,
                "is_anonymous" boolean NOT NULL DEFAULT false,
                CONSTRAINT "PK_0b0e4bbc8415ec426f87f3a88e2" PRIMARY KEY ("id")
            )
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            DROP TABLE "comment"
        `);
  }
};
