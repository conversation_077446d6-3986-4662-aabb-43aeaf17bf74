module.exports = class Dev1687413291113 {
  name = 'Dev1687413291113';

  async up(queryRunner) {
    await queryRunner.query(`
            CREATE TABLE "vulog_user" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "bsg_user_id" character varying NOT NULL,
                "password_hash" character varying NOT NULL,
                "profile_id" character varying NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_ea5858448688755dc1ccd0b0a5a" UNIQUE ("bsg_user_id"),
                CONSTRAINT "PK_53ed0a64d29989131216a5c1aa5" PRIMARY KEY ("id")
            )
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            DROP TABLE "vulog_user"
        `);
  }
};
