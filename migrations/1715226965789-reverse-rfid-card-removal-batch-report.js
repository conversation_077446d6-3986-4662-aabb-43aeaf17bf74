module.exports = class ReverseRfidCardRemovalBatchReport1715226965789 {
  name = 'ReverseRfidCardRemovalBatchReport1715226965789';

  async up(queryRunner) {
    await queryRunner.query(`
            CREATE TABLE "reverse_rfid_card_removal_batch_report" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "created_by" character varying,
                "modified_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "modified_by" character varying,
                "deleted_at" TIMESTAMP WITH TIME ZONE,
                "deleted_by" character varying,
                "executed_at" TIMESTAMP WITH TIME ZONE NOT NULL,
                "total" integer NOT NULL DEFAULT '0',
                "processed" integer NOT NULL DEFAULT '0',
                "errors" character varying,
                CONSTRAINT "PK_f656ba42d8c6f24cc2b40e22fee" PRIMARY KEY ("id")
            );
            COMMENT ON COLUMN "reverse_rfid_card_removal_batch_report"."executed_at" IS 'The timestamp that the batch executed';
            COMMENT ON COLUMN "reverse_rfid_card_removal_batch_report"."total" IS 'Total of customers having RFID card removed';
            COMMENT ON COLUMN "reverse_rfid_card_removal_batch_report"."processed" IS 'Number of customers having RFID card enabled / Total of customers having RFID card removed';
            COMMENT ON COLUMN "reverse_rfid_card_removal_batch_report"."errors" IS 'List of customer IDs having error, timestamp and correlated reason in raw text for debugging'
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            DROP TABLE "reverse_rfid_card_removal_batch_report"
        `);
  }
};
