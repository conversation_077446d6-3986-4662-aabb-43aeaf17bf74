module.exports = class init1694513885562 {
  constructor() {
    this.name = 'init1694513885562';
  }
  async up(queryRunner) {
    await queryRunner.query(
      `CREATE TYPE "public"."pre_reservation_status_enum" AS ENUM('RESERVED', 'FULFILLED', 'CANCELED', 'EXPIRED')`,
    );
    await queryRunner.query(
      `CREATE TABLE "pre_reservation" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "modified_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "user_id" uuid NOT NULL, "station_id" uuid NOT NULL, "status" "public"."pre_reservation_status_enum" NOT NULL, "reserved_at" TIMESTAMP WITH TIME ZONE NOT NULL, "fulfilled_at" TIMESTAMP WITH TIME ZONE, "canceled_at" TIMESTAMP WITH TIME ZONE, "expires_at" TIMESTAMP WITH TIME ZONE NOT NULL, "sent_location" geometry, CONSTRAINT "PK_cff2795cc632b94c71458717c31" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0b29aaca1d85f21c987cf15bd1" ON "pre_reservation" ("user_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_22372c9195d0eb1988a2c7284e" ON "pre_reservation" ("expires_at") `,
    );
  }
  async down(queryRunner) {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_22372c9195d0eb1988a2c7284e"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_0b29aaca1d85f21c987cf15bd1"`,
    );
    await queryRunner.query(`DROP TABLE "pre_reservation"`);
    await queryRunner.query(`DROP TYPE "public"."pre_reservation_status_enum"`);
  }
};
//# sourceMappingURL=1694513885562-init.js.map
