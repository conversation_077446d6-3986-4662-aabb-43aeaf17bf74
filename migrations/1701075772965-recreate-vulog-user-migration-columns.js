// eslint-disable-next-line @typescript-eslint/no-unused-vars,@typescript-eslint/no-var-requires
const { MigrationInterface, QueryRunner } = require('typeorm');

module.exports = class RecreateVulogUserMigrationColumns1701075772965 {
  name = 'RecreateVulogUserMigrationColumns1701075772965';

  async up(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "vulog_user"
            ADD IF NOT EXISTS "user_info_hash" text NOT NULL DEFAULT 'TBD'
        `);
    await queryRunner.query(`
            ALTER TABLE "vulog_user"
            ADD IF NOT EXISTS "uss_updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT '"1999-12-31T16:00:00.000Z"'
        `);

    await queryRunner.query(`
            update vulog_user v
            set 
            user_info_hash= md5(substring(email,7) || rfid),
            uss_updated_at='2023-07-01'
            where starts_with(email, 'vulog-') AND user_info_hash = 'TBD'
        `);

    await queryRunner.query(`
            update vulog_user v
            set 
            user_info_hash= md5(email || rfid),
            uss_updated_at='2023-07-01'
            where not starts_with(email, 'vulog-') AND user_info_hash = 'TBD'
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            ALTER TABLE "vulog_user" DROP COLUMN "uss_updated_at"
        `);
    await queryRunner.query(`
            ALTER TABLE "vulog_user" DROP COLUMN "user_info_bash"
        `);
  }
};
