// eslint-disable-next-line @typescript-eslint/no-var-requires,@typescript-eslint/no-unused-vars
const { MigrationInterface, QueryRunner } = require('typeorm');

module.exports = class CreateCarTable1699599202707 {
  name = 'CreateCarTable1699599202707';

  async up(queryRunner) {
    await queryRunner.query(`
            CREATE TABLE "car" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "vulog_id" character varying NOT NULL,
                "model" text NOT NULL,
                "plate" character varying NOT NULL,
                "vin" character varying NOT NULL,
                CONSTRAINT "UQ_a1cf42546a1a2563f982cc3a77c" UNIQUE ("vulog_id"),
                CONSTRAINT "UQ_ce862bc6271a8d79fef1c845a03" UNIQUE ("plate"),
                CONSTRAINT "UQ_e917bf949f80860e817b74006e8" UNIQUE ("vin"),
                CONSTRAINT "PK_55bbdeb14e0b1d7ab417d11ee6d" PRIMARY KEY ("id")
            )
        `);
    await queryRunner.query(`
            CREATE INDEX "IDX_a1cf42546a1a2563f982cc3a77" ON "car" ("vulog_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "IDX_ce862bc6271a8d79fef1c845a0" ON "car" ("plate")
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            DROP INDEX "public"."IDX_ce862bc6271a8d79fef1c845a0"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."IDX_a1cf42546a1a2563f982cc3a77"
        `);
    await queryRunner.query(`
            DROP TABLE "car"
        `);
  }
};
