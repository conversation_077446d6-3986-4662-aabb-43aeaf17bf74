module.exports = class Dev1697009797115 {
  name = 'CreateReservationTable1697009797115';

  async up(queryRunner) {
    await queryRunner.query(`
            CREATE TABLE "reservation" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "created_by" character varying,
                "modified_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "modified_by" character varying,
                "deleted_at" TIMESTAMP WITH TIME ZONE,
                "deleted_by" character varying,
                "bsg_user_id" character varying,
                "car_id" character varying,
                "station_id" character varying,
                "vulog_trip_id" character varying,
                "status" character varying,
                "rental_package_data" jsonb,
                CONSTRAINT "PK_48b1f9922368359ab88e8bfa525" PRIMARY KEY ("id")
            )
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            DROP TABLE "reservation"
        `);
  }
};
