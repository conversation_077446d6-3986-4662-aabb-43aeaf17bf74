module.exports = class ReservationIndexing1706172662349 {
  name = 'ReservationIndexing1706172662349';

  async up(queryRunner) {
    await queryRunner.query(`
            CREATE INDEX "IDX_7c74ae5e5fe032e7f394b535df" ON "reservation" ("bsg_user_id")
        `);
    await queryRunner.query(`
            CREATE INDEX "IDX_462502d2b351421d1ffb48968a" ON "reservation" ("expires_at")
        `);
  }

  async down(queryRunner) {
    await queryRunner.query(`
            DROP INDEX "public"."IDX_462502d2b351421d1ffb48968a"
        `);
    await queryRunner.query(`
            DROP INDEX "public"."IDX_7c74ae5e5fe032e7f394b535df"
        `);
  }
};
