diff --git a/node_modules/nest-transact/dist/lib/with-transaction.js b/node_modules/nest-transact/dist/lib/with-transaction.js
index 306d75b..f9ca997 100644
--- a/node_modules/nest-transact/dist/lib/with-transaction.js
+++ b/node_modules/nest-transact/dist/lib/with-transaction.js
@@ -43,7 +43,15 @@ let TransactionFor = class TransactionFor {
             return this.moduleRef;
         }
         if (isExcluded) {
-            return this.moduleRef.get(id, { strict: false });
+            let provider;
+
+            try {
+                provider = this.moduleRef.get(id, { strict: false });
+            } catch(err) {
+                provider = this.moduleRef.get(param, { strict: false }); // In case we could not get the provider by function name
+            }
+
+            return provider;
         }
         let argument;
         if (this.cache.has(id)) {
@@ -61,8 +69,11 @@ let TransactionFor = class TransactionFor {
                 dependency = this.moduleRef.get(param, { strict: false });
             }
             if (dependency instanceof typeorm_1.Repository || canBeRepository) {
-                const entity = dependency.metadata.target;
-                argument = manager.getRepository(entity);
+                const wrappedRepoInTxn = manager.getRepository(dependency.genericRepository.target || dependency.genericRepository.metadata.target); // To adapt custom repository pattern in typeorm@0.3.0
+
+                dependency.genericRepository = wrappedRepoInTxn;
+
+                argument = dependency;
             }
             else {
                 if (!dependency) {
@@ -80,53 +91,30 @@ let TransactionFor = class TransactionFor {
     findArgumentsForProvider(constructor, manager, excluded) {
         const args = [];
         const keys = Reflect.getMetadataKeys(constructor);
-        const missingParams = [];
+
+        let paramTypes;
+        let selfParamTypes;
+      
         keys.forEach((key) => {
             if (key === constants_1.PARAMTYPES_METADATA) {
-                const paramTypes = Reflect.getMetadata(key, constructor);
-                const selfParamTypes = Reflect.getMetadata(constants_1.SELF_DECLARED_DEPS_METADATA, constructor);
-                for (const param of paramTypes) {
-                    if (!param) {
-                        const paramTypeNameMap = paramTypes
-                            .filter((item) => !!item)
-                            .reduce((acc, currVal, currIdx) => {
-                            acc.set(currVal.name, currIdx);
-                            return acc;
-                        }, new Map());
-                        const selfParamTypeMap = selfParamTypes
-                            .filter((item) => !!item)
-                            .reduce((acc, currVal, currIdx) => {
-                            if (typeof currVal.param === 'string') {
-                                acc.set(currVal.param, currIdx);
-                            }
-                            else {
-                                acc.set(currVal.param.name, currIdx);
-                            }
-                            return acc;
-                        }, new Map());
-                        const exclusion = (0, lodash_xor_1.default)(Array.from(paramTypeNameMap.keys()), Array.from(selfParamTypeMap.keys()));
-                        exclusion.forEach((item) => {
-                            var _a;
-                            if (paramTypeNameMap.has(item)) {
-                                const val = paramTypes[paramTypeNameMap.get(item)];
-                                missingParams.push(val.name);
-                            }
-                            if (selfParamTypeMap.has(item)) {
-                                const val = selfParamTypes[selfParamTypeMap.get(item)];
-                                missingParams.push(typeof (val === null || val === void 0 ? void 0 : val.param) === 'object' ? (_a = val === null || val === void 0 ? void 0 : val.param) === null || _a === void 0 ? void 0 : _a.name : val.param);
-                            }
-                        });
-                        return;
-                    }
-                    const argument = this.getArgument(param, manager, excluded);
-                    args.push(argument);
-                }
+                    paramTypes = Reflect.getMetadata(key, constructor);
+
+                    selfParamTypes = Reflect.getMetadata(constants_1.SELF_DECLARED_DEPS_METADATA, constructor);
+
+                    paramTypes.forEach((param, index) => {
+                        let decisionParam = param;
+
+                        if (!decisionParam) {
+                            decisionParam = selfParamTypes.find(selfParam => selfParam.index === index).param;
+                        }
+
+                        const argument =  this.getArgument(decisionParam, manager, excluded);
+
+                        args.push(argument);
+                    })
             }
         });
-        missingParams.forEach((item) => {
-            const argument = this.getArgument(item, manager, excluded);
-            args.push(argument);
-        });
+
         return new constructor(...args);
     }
 };
