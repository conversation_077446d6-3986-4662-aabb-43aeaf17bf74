diff --git a/node_modules/typeorm/entity-manager/EntityManager.js b/node_modules/typeorm/entity-manager/EntityManager.js
index e4a6e1a..c0b974a 100644
--- a/node_modules/typeorm/entity-manager/EntityManager.js
+++ b/node_modules/typeorm/entity-manager/EntityManager.js
@@ -98,13 +98,15 @@ class EntityManager {
      * Creates a new query builder that can be used to build a SQL query.
      */
     createQueryBuilder(entityClass, alias, queryRunner) {
+        const isSelfQueryRunnerReleased = !this.queryRunner ? true : this.queryRunner.isReleased;
+
+        const chosenQueryRunner = isSelfQueryRunnerReleased ? queryRunner : this.queryRunner;
+
         if (alias) {
-            return this.connection.createQueryBuilder(entityClass, alias, queryRunner || this.queryRunner);
+            return this.connection.createQueryBuilder(entityClass, alias, chosenQueryRunner);
         }
         else {
-            return this.connection.createQueryBuilder(entityClass ||
-                queryRunner ||
-                this.queryRunner);
+            return this.connection.createQueryBuilder(entityClass || chosenQueryRunner);
         }
     }
     /**
diff --git a/node_modules/typeorm/persistence/EntityPersistExecutor.js b/node_modules/typeorm/persistence/EntityPersistExecutor.js
index c9430e7..0c81c3a 100644
--- a/node_modules/typeorm/persistence/EntityPersistExecutor.js
+++ b/node_modules/typeorm/persistence/EntityPersistExecutor.js
@@ -40,7 +40,9 @@ class EntityPersistExecutor {
         await Promise.resolve();
         // if query runner is already defined in this class, it means this entity manager was already created for a single connection
         // if its not defined we create a new query runner - single connection where we'll execute all our operations
-        const queryRunner = this.queryRunner || this.connection.createQueryRunner();
+        const isSelfQueryRunnerReleased = !this.queryRunner ? true : this.queryRunner.isReleased;
+        
+        const queryRunner = isSelfQueryRunnerReleased ? this.connection.createQueryRunner() : this.queryRunner;
         // save data in the query runner - this is useful functionality to share data from outside of the world
         // with third classes - like subscribers and listener methods
         let oldQueryRunnerData = queryRunner.data;
diff --git a/node_modules/typeorm/query-builder/QueryBuilder.js b/node_modules/typeorm/query-builder/QueryBuilder.js
index 0176a13..957c414 100644
--- a/node_modules/typeorm/query-builder/QueryBuilder.js
+++ b/node_modules/typeorm/query-builder/QueryBuilder.js
@@ -1118,7 +1118,11 @@ class QueryBuilder {
      * Creates a query builder used to execute sql queries inside this query builder.
      */
     obtainQueryRunner() {
-        return this.queryRunner || this.connection.createQueryRunner();
+        const isSelfQueryRunnerReleased = !this.queryRunner ? true : this.queryRunner.isReleased;
+        
+        const chosenQueryRunner = isSelfQueryRunnerReleased ? this.connection.createQueryRunner() : this.queryRunner;
+
+        return chosenQueryRunner;
     }
     hasCommonTableExpressions() {
         return this.expressionMap.commonTableExpressions.length > 0;
diff --git a/node_modules/typeorm/query-builder/SelectQueryBuilder.js b/node_modules/typeorm/query-builder/SelectQueryBuilder.js
index 9b5f08b..c9af275 100644
--- a/node_modules/typeorm/query-builder/SelectQueryBuilder.js
+++ b/node_modules/typeorm/query-builder/SelectQueryBuilder.js
@@ -2222,7 +2222,11 @@ class SelectQueryBuilder extends QueryBuilder_1.QueryBuilder {
      * Creates a query builder used to execute sql queries inside this query builder.
      */
     obtainQueryRunner() {
-        return this.queryRunner || this.connection.createQueryRunner("slave");
+        const isSelfQueryRunnerReleased = !this.queryRunner ? true : this.queryRunner.isReleased;
+        
+        const chosenQueryRunner = isSelfQueryRunnerReleased ? this.connection.createQueryRunner("slave") : this.queryRunner;
+
+        return chosenQueryRunner;
     }
     buildSelect(select, metadata, alias, embedPrefix) {
         for (let key in select) {
