{"name": "rental-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "start:update-cars": "node dist/batch/update-cars/update-cars.main", "start:update-cars:dev": "yarn build && node dist/batch/update-cars/update-cars.main", "start:reverse-rfid-card-removal": "node dist/batch/reverse-rfid-card-removal/reverse-rfid-card-removal.main", "start:reverse-rfid-card-removal:dev": "yarn build && node dist/batch/reverse-rfid-card-removal/reverse-rfid-card-removal.main", "start:crons": "node dist/crons.main", "start:crons:dev": "nest start --watch --entryFile crons.main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config=./jest.e2e-config.js --runInBand", "test:e2e:ci": "yarn test:e2e --coverage --ci --runInBand", "local:setup": "sh ./setup/setup.sh", "local:docker": "docker compose -f ./docker-compose.local.yml --env-file .env.local", "local:container:start": "yarn local:docker up -d", "local:container:stop": "yarn local:docker stop", "local:container:restart": "yarn local:container:stop && yarn local:container:start", "local:container:exec": "yarn local:docker exec", "local:container:logs": "yarn local:docker logs", "typeorm": "npx typeorm -d dist/database/data-source.js", "typeorm:dev": "yarn build && dotenv -e .env.local -- npx typeorm -d dist/database/data-source.js", "migration:dev:generate": "cross-env yarn typeorm:dev migration:generate -o -p migrations/dev", "migration:dev:create": "cross-env typeorm-ts-node-commonjs migration:create -o migrations/dev", "migration:dev:run": "yarn typeorm:dev migration:run", "migration:dev:revert": "yarn typeorm:dev migration:revert", "migration:dev:show": "yarn typeorm:dev migration:show", "migration:e2e:run": "NODE_ENV=e2e yarn typeorm:dev migration:run", "migration:run": "yarn typeorm migration:run", "migration:revert": "yarn typeorm migration:revert", "migration:show": "yarn typeorm migration:show", "container:build": "docker-compose -f ./docker-compose.test.yml build", "container:start": "docker-compose -f ./docker-compose.test.yml up -d", "container:stop": "docker-compose -f ./docker-compose.test.yml down", "container:restart": "yarn container:stop && yarn container:start", "container:exec": "docker-compose -f docker-compose.test.yml exec", "container:logs": "docker-compose -f docker-compose.test.yml logs", "postinstall": "patch-package", "prepare": "husky"}, "dependencies": {"@aws-sdk/client-sns": "^3.216.0", "@aws-sdk/client-sqs": "^3.342.0", "@aws-sdk/rds-signer": "^3.351.0", "@bluesg-2/bo-auth-guard": "^2.2.1", "@bluesg-2/customer-authentication": "0.1.7", "@bluesg-2/event-library": "^1.0.9", "@bluesg-2/monitoring": "0.5.0", "@bluesg-2/queue-pop": "^2.3.10", "@bluesg-2/sqs-event": "^0.8.1", "@commitlint/cli": "^19.5.0", "@commitlint/config-angular": "^19.5.0", "@faker-js/faker": "^8.1.0", "@nestjs/axios": "^2.0.0", "@nestjs/cache-manager": "^1.0.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^9.0.0", "@nestjs/event-emitter": "^2.0.2", "@nestjs/jwt": "^10.1.0", "@nestjs/microservices": "^9.4.2", "@nestjs/platform-express": "^9.0.0", "@nestjs/schedule": "^3.0.4", "@nestjs/swagger": "^6.3.0", "@nestjs/terminus": "^9.2.2", "@nestjs/typeorm": "^9.0.1", "@ntegral/nestjs-sentry": "^4.0.0", "@sentry/node": "^7.55.2", "@types/cache-manager": "^4.0.2", "@types/json-stable-stringify": "^1.0.36", "@types/lodash": "^4.14.200", "argon2": "^0.30.3", "async-retry": "^1.3.3", "axios": "^1.4.0", "axios-rate-limit": "^1.3.0", "cache-manager": "^4.0.1", "cache-manager-ioredis": "^2.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "configcat-node": "^11.3.0", "cronstrue": "^2.47.0", "dotenv": "^16.0.3", "dotenv-cli": "^7.2.1", "dotenv-expand": "^10.0.0", "husky": "^9.1.6", "ioredis": "^5.3.2", "jest-junit": "^16.0.0", "json-stable-stringify": "^1.1.1", "lodash": "^4.17.21", "luxon": "^3.4.3", "nest-transact": "^9.1.2", "nestjs-cls": "^3.3.1", "newrelic": "^12.5.0", "patch-package": "^8.0.0", "pg": "^8.11.0", "postinstall-postinstall": "^2.1.0", "qs": "^6.11.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.2.0", "tiny-types": "^1.20.0", "ts-md5": "^1.3.1", "typeorm": "0.3.17", "typeorm-naming-strategies": "^4.1.0", "uuid": "^9.0.1", "winston": "3.10.0"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/async-retry": "^1.4.8", "@types/express": "^4.17.17", "@types/jest": "29.5.0", "@types/luxon": "^3.3.2", "@types/newrelic": "^9.14.3", "@types/node": "18.15.11", "@types/supertest": "^2.0.11", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "axios-mock-adapter": "^1.21.4", "cross-env": "^7.0.3", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "29.5.0", "prettier": "^2.3.2", "prettier-plugin-organize-imports": "^3.2.3", "source-map-support": "^0.5.20", "supertest": "^6.3.3", "ts-jest": "29.0.5", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.2.0", "typescript": "^4.7.4"}}