version: '3'
services:
  rental:
    container_name: rental
    restart: always
    build:
      context: .
      dockerfile: ./test/config/rental/Dockerfile
      args:
        GITHUB_TOKEN: ${GITHUB_TOKEN}
        NPMRC_FILE: .npmrc.ci
    environment:
      JEST_JUNIT_OUTPUT_DIR: './reports/junit/test-result.xml'
      NODE_ENV: e2e
    env_file:
      - .env.e2e
    depends_on:
      - db
      - redis
  redis:
    container_name: redis
    image: redis:6-alpine
    ports:
      - 6379:6379
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
  db:
    container_name: db
    ports:
      - 5432:5432
    build:
      context: .
      dockerfile: ./test/config/db/Dockerfile
    restart: always
    environment:
      POSTGRES_USER: test
      POSTGRES_PASSWORD: test-pwd
      POSTGRES_DB: test-db
