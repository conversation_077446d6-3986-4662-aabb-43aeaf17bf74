version: 2.1

orbs:
  bluesg: bluesg/bluesg-orb@0.7.56

filters: &filters
  tags:
    only: /^v.*/
  branches:
    ignore: /.*/

parameters:
  node-version:
    type: string
    default: '18.16.0'
  release:
    type: string
    default: << pipeline.git.revision >>
  slack-channel:
    type: string
    default: C056LFKFDAT
  jira-board:
    type: string
    default: CORE

executors:
  node-executor:
    docker:
      - image: cimg/node:<< pipeline.parameters.node-version >>

jobs:
  test:
    executor: node-executor
    resource_class: medium
    steps:
      - checkout
      - restore_cache:
          name: Restore Yarn Package Cache
          keys:
            - v1-deps-yarn-packages-{{ checksum "yarn.lock" }}
      - run:
          name: Install Dependencies
          command: |
            mv .npmrc.ci ./.npmrc
            yarn install
      - run:
          name: Test
          command: yarn test:cov --ci --reporters=default --reporters=jest-junit --maxWorkers=2
          environment:
            JEST_JUNIT_OUTPUT_DIR: ./reports/junit/test-result.xml
      - store_test_results:
          path: ./reports/
      - persist_to_workspace:
          root: coverage
          paths:
            - lcov.info
      - save_cache:
          name: Save Yarn Package Cache
          key: v1-deps-yarn-packages-{{ checksum "yarn.lock" }}
          paths:
            - ~/.cache/yarn
      - bluesg/notify-failure:
          slack-channel: << pipeline.parameters.slack-channel >>
          branch_pattern: main

  test-e2e:
    executor: node-executor
    docker:
      - image: cimg/node:<< pipeline.parameters.node-version >>
      - image: cimg/postgres:16.7-postgis
        name: db
        environment:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test-pwd
          POSTGRES_DB: test-db
      - image: cimg/redis:6.2
        name: redis
        environment:
          ALLOW_EMPTY_PASSWORD: yes
    parallelism: 6
    resource_class: medium
    steps:
      - checkout
      - restore_cache:
          name: Restore Yarn Package Cache
          keys:
            - v1-deps-yarn-packages-{{ checksum "yarn.lock" }}
      - run:
          name: Install Dependencies and build
          environment:
            NPM_CONFIG_USERCONFIG: ./npmrc.ci
          command: |
            yarn install
            yarn build
      - run:
          name: DB Migrations
          environment:
            NODE_ENV: e2e
          command: yarn typeorm:dev migration:run
      - run:
          name: Test
          command: yarn test:e2e:ci --reporters=default --reporters=jest-junit --shard=$((CIRCLE_NODE_INDEX + 1))/$CIRCLE_NODE_TOTAL
          environment:
            NODE_ENV: e2e
            JEST_JUNIT_OUTPUT_DIR: ./reports/junit/test-result.xml
      - store_test_results:
          path: ./reports/
      - run:
          name: Preparing Coverage report
          command: mv coverage-e2e/lcov.info "coverage-e2e/lcov_$((CIRCLE_NODE_INDEX + 1))_$CIRCLE_NODE_TOTAL.info"
      - persist_to_workspace:
          root: .
          paths:
            - coverage-e2e/lcov_*.info
      - save_cache:
          name: Save Yarn Package Cache
          key: v1-deps-yarn-packages-{{ checksum "yarn.lock" }}
          paths:
            - ~/.cache/yarn
      - bluesg/notify-failure:
          slack-channel: << pipeline.parameters.slack-channel >>
          branch_pattern: develop

workflows:
  test-and-build:
    jobs:
      - test:
          context:
            - github-context

      - test-e2e:
          context:
            - github-context

      - bluesg/code-scan:
          requires:
            - test
            - test-e2e
          name: code-scan
          report-paths: /tmp/workspace/lcov.info,/tmp/workspace/coverage-e2e/lcov_1_6.info,/tmp/workspace/coverage-e2e/lcov_2_6.info,/tmp/workspace/coverage-e2e/lcov_3_6.info,/tmp/workspace/coverage-e2e/lcov_4_6.info,/tmp/workspace/coverage-e2e/lcov_5_6.info,/tmp/workspace/coverage-e2e/lcov_6_6.info
          fail-with-quality-gate: 'true'
          revision: << pipeline.parameters.release >>
          project-version: << pipeline.parameters.release >>
          context:
            - sonar-context
            - slack-context

      - bluesg/build:
          requires:
            - code-scan
            - test
          filters:
            branches:
              only:
                - main
          name: build-and-push
          dockerfile: ci.Dockerfile
          jira-board: << pipeline.parameters.jira-board >>
          release: << pipeline.parameters.release >>
          registry-id: ECR_V2_REGISTRY_ID
          build-args: '--build-arg GITHUB_TOKEN=${GITHUB_TOKEN}'
          aws-access-key: AWS_BUILD_ACCESS_KEY_ID
          aws-secret-key: AWS_BUILD_SECRET_ACCESS_KEY
          aws-region: AWS_BUILD_DEFAULT_REGION
          use-jira: false
          context:
            - aws-arti-context
            - slack-context
            - github-context

      - bluesg/tag-version:
          requires:
            - build-and-push
          filters:
            branches:
              only:
                - main
          name: tag-version
          image-tag: << pipeline.git.revision >>
          context:
            - semantic-release
            - aws-arti-context

  promote-and-deploy:
    jobs:
      - bluesg/trigger-deployment:
          name: start-staging-deployment
          filters: *filters
          context:
            - staging-context

      - bluesg/trigger-deployment: # This job only does tagging
          name: start-cron-staging-deployment
          environment: CRON_ENVIRONMENT_NAME # Later the output temporary deployment tag "deploy-cron-staging-xxxx" will be deleted by "Remove deploy tag" command in `bluesg/deploy` job
          filters: *filters
          context:
            - staging-context

      - approve-uat-deployment:
          requires:
            - start-staging-deployment
            - start-cron-staging-deployment
          type: approval
          filters: *filters

      - bluesg/trigger-deployment:
          requires:
            - approve-uat-deployment
          name: start-uat-deployment
          filters: *filters
          context:
            - uat-context

      - bluesg/trigger-deployment: # This job only does tagging
          requires:
            - approve-uat-deployment
          name: start-cron-uat-deployment
          environment: CRON_ENVIRONMENT_NAME # Later the output temporary deployment tag "deploy-cron-uat-xxxx" will be deleted by "Remove deploy tag" command in `bluesg/deploy` job
          filters: *filters
          context:
            - uat-context

      - approve-prod-deployment:
          requires:
            - start-uat-deployment
            - start-cron-uat-deployment
          type: approval
          filters: *filters

      - bluesg/trigger-deployment:
          requires:
            - approve-prod-deployment
          name: start-prod-deployment
          filters: *filters
          context:
            - prod-context

      - bluesg/trigger-deployment: # This job only does tagging
          requires:
            - approve-prod-deployment
          name: start-cron-prod-deployment
          environment: CRON_ENVIRONMENT_NAME # Later the output temporary deployment tag "deploy-cron-prod-xxxx" will be deleted by "Remove deploy tag" command in `bluesg/deploy` job
          filters: *filters
          context:
            - prod-context

  deploy-to-staging:
    jobs:
      - bluesg/deploy:
          name: deploy-staging
          filters:
            tags:
              only: /^deploy-staging.*/
            branches:
              ignore: /.*/
          service: STAGING_SERVICE
          task-definition: STAGING_TASK_DEFINITION
          cluster: V2_PRIVATE_CLUSTER
          container: STAGING_CONTAINER
          registry-id: ECR_V2_REGISTRY_ID
          aws-tag-key: AWS_BUILD_ACCESS_KEY_ID
          aws-tag-secret: AWS_BUILD_SECRET_ACCESS_KEY
          aws-tag-region: $AWS_BUILD_DEFAULT_REGION
          image: << pipeline.parameters.release >>
          repository: CIRCLE_PROJECT_REPONAME
          use-jira: false
          newrelic-app-id: STAGING_NEW_RELIC_APP_ID
          git-tag: << pipeline.git.tag >>
          slack-channel: << pipeline.parameters.slack-channel >>
          context:
            - aws-dev-context
            - aws-arti-context
            - slack-context
            - staging-context
            - github-context
            - jira-context
            - linearb-context

      - bluesg/deploy:
          name: deploy-cron-staging
          filters:
            tags:
              only: /^deploy-cron-staging.*/
            branches:
              ignore: /.*/
          service: CRON_STAGING_SERVICE
          environment: CRON_STAGING_ENVIRONMENT
          task-definition: CRON_STAGING_TASK_DEFINITION
          cluster: V2_PRIVATE_CLUSTER
          container: CRON_STAGING_CONTAINER
          registry-id: ECR_V2_REGISTRY_ID
          aws-tag-key: AWS_BUILD_ACCESS_KEY_ID
          aws-tag-secret: AWS_BUILD_SECRET_ACCESS_KEY
          aws-tag-region: $AWS_BUILD_DEFAULT_REGION
          image: << pipeline.parameters.release >>
          repository: CIRCLE_PROJECT_REPONAME
          use-jira: false
          newrelic-app-id: CRON_STAGING_NEW_RELIC_APP_ID
          git-tag: << pipeline.git.tag >>
          slack-channel: << pipeline.parameters.slack-channel >>
          context:
            - aws-dev-context
            - aws-arti-context
            - slack-context
            - staging-context
            - github-context
            - jira-context
            - linearb-context

  deploy-to-uat:
    jobs:
      - bluesg/deploy:
          name: deploy-uat
          filters:
            tags:
              only: /^deploy-uat.*/
            branches:
              ignore: /.*/
          registry-id: ECR_V2_REGISTRY_ID
          service: UAT_SERVICE
          task-definition: UAT_TASK_DEFINITION
          cluster: V2_PRIVATE_CLUSTER
          container: UAT_CONTAINER
          aws-tag-key: AWS_BUILD_ACCESS_KEY_ID
          aws-tag-secret: AWS_BUILD_SECRET_ACCESS_KEY
          aws-tag-region: $AWS_BUILD_DEFAULT_REGION
          image: << pipeline.parameters.release >>
          repository: CIRCLE_PROJECT_REPONAME
          use-jira: false
          newrelic-app-id: UAT_NEW_RELIC_APP_ID
          git-tag: << pipeline.git.tag >>
          slack-channel: << pipeline.parameters.slack-channel >>
          context:
            - aws-uat-context
            - aws-arti-context
            - slack-context
            - jira-context
            - uat-context
            - github-context
            - linearb-context

      - bluesg/deploy:
          name: deploy-cron-uat
          filters:
            tags:
              only: /^deploy-cron-uat.*/
            branches:
              ignore: /.*/
          service: CRON_UAT_SERVICE
          environment: CRON_UAT_ENVIRONMENT
          task-definition: CRON_UAT_TASK_DEFINITION
          cluster: V2_PRIVATE_CLUSTER
          container: CRON_UAT_CONTAINER
          registry-id: ECR_V2_REGISTRY_ID
          aws-tag-key: AWS_BUILD_ACCESS_KEY_ID
          aws-tag-secret: AWS_BUILD_SECRET_ACCESS_KEY
          aws-tag-region: $AWS_BUILD_DEFAULT_REGION
          image: << pipeline.parameters.release >>
          repository: CIRCLE_PROJECT_REPONAME
          use-jira: false
          newrelic-app-id: CRON_UAT_NEW_RELIC_APP_ID
          git-tag: << pipeline.git.tag >>
          slack-channel: << pipeline.parameters.slack-channel >>
          context:
            - aws-uat-context
            - aws-arti-context
            - sentry-context
            - slack-context
            - jira-context
            - uat-context
            - github-context
            - linearb-context

  deploy-to-prod:
    jobs:
      - bluesg/deploy:
          name: deploy-prod
          filters:
            tags:
              only: /^deploy-prod.*/
            branches:
              ignore: /.*/
          registry-id: ECR_V2_REGISTRY_ID
          service: PROD_SERVICE
          task-definition: PROD_TASK_DEFINITION
          cluster: V2_PRIVATE_CLUSTER
          container: PROD_CONTAINER
          aws-tag-key: AWS_BUILD_ACCESS_KEY_ID
          aws-tag-secret: AWS_BUILD_SECRET_ACCESS_KEY
          aws-tag-region: $AWS_BUILD_DEFAULT_REGION
          image: << pipeline.parameters.release >>
          repository: CIRCLE_PROJECT_REPONAME
          jira-board: << pipeline.parameters.jira-board >>
          jira-env-type: production
          newrelic-app-id: PROD_NEW_RELIC_APP_ID
          git-tag: << pipeline.git.tag >>
          slack-channel: << pipeline.parameters.slack-channel >>
          use-jira: false
          context:
            - aws-prod-context
            - aws-arti-context
            - slack-context
            - jira-context
            - prod-context
            - github-context
            - linearb-context

      - bluesg/deploy:
          name: deploy-cron-prod
          filters:
            tags:
              only: /^deploy-cron-prod.*/
            branches:
              ignore: /.*/
          service: CRON_PROD_SERVICE
          environment: CRON_PROD_ENVIRONMENT
          task-definition: CRON_PROD_TASK_DEFINITION
          cluster: V2_PRIVATE_CLUSTER
          container: CRON_PROD_CONTAINER
          registry-id: ECR_V2_REGISTRY_ID
          aws-tag-key: AWS_BUILD_ACCESS_KEY_ID
          aws-tag-secret: AWS_BUILD_SECRET_ACCESS_KEY
          aws-tag-region: $AWS_BUILD_DEFAULT_REGION
          image: << pipeline.parameters.release >>
          repository: CIRCLE_PROJECT_REPONAME
          use-jira: false
          newrelic-app-id: CRON_PROD_NEW_RELIC_APP_ID
          git-tag: << pipeline.git.tag >>
          slack-channel: << pipeline.parameters.slack-channel >>
          context:
            - aws-prod-context
            - aws-arti-context
            - sentry-context
            - slack-context
            - jira-context
            - prod-context
            - github-context
            - linearb-context
